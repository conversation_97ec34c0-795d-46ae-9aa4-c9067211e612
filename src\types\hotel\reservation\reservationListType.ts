interface RoomAllocationTypes {
  roomAllocation: string;
  room: string;
  allocationDate: string;
  serviceFee: number;
  roomPrice: number;
  totalPrice: number;
  currency: string;
  _id: string;
}

interface ImageType {
  img800: Img800;
  img400: Img400;
  img200: Img200;
  img100: Img100;
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  tags: string;
  createdDate: string;
}

interface Img800 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img400 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img200 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img100 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface PetTypes {
  age: string;
  allergicTo: string[];
  breed: string;
  color: string;
  createdAt: string;
  description: string;
  documentPhotos: ImageType[];
  externalParasiteTreatment: boolean;
  externalTreatmentDate: string | null;
  feedingHabits: string;
  gender: string;
  hereditaryDiseases: string[];
  images: ImageType[];
  infertile: boolean;
  internalParasiteTreatment: boolean;
  internalTreatmentDate: string | null;
  isDeleted: boolean;
  kind: string;
  medicine: string;
  microChipNumber: string;
  name: string;
  operationHistory: string[];
  passive: boolean;
  petOwner: string;
  specified: string;
  updatedAt: string;
  vaccines: string[];
  _id: string;
}

interface PetOwnerTypes {
  availableCampaigns: string[];
  bio: string;
  createdAt: string;
  dateOfBirth: string;
  email: string;
  firstName: string;
  fullName: string;
  gender: string;
  image: string;
  lastName: string;
  passive: boolean;
  phone: string;
  pushTokenList: (string | null)[];
  role: string;
  updatedAt: string;
  username: string;
  _id: string;
}

export interface HotelCustomerTypes {
  _id: string;
  hotel: string;
  fullName: string;
  email: string;
  phone: string;
  createdAt: string;
  updatedAt: string;
}

export interface HotelPetTypes {
  age: string;
  allergicTo: string[];
  breed: string;
  color: string;
  createdAt: string;
  description: string;
  documentPhotos: ImageType[];
  externalParasiteTreatment: boolean;
  externalTreatmentDate: string | null;
  feedingHabits: string;
  gender: string;
  hereditaryDiseases: string[];
  images: ImageType[];
  infertile: boolean;
  internalParasiteTreatment: boolean;
  internalTreatmentDate: string | null;
  isDeleted: boolean;
  kind: string;
  medicine: string;
  microChipNumber: string;
  name: string;
  operationHistory: string[];
  passive: boolean;
  owner: string;
  specified: string;
  updatedAt: string;
  vaccines: string[];
  _id: string;
}

interface RoomTypes {
  createdAt: string;
  hotel: string;
  images: ImageType[];
  nonAcceptedBreedTypes: string[];
  passive: boolean;
  petType: string[];
  reviews: string[];
  roomCapacity: number;
  roomDescription: string;
  roomFeatures: string[];
  roomGroup: {
    _id: string;
    roomGroupName: string;
    roomCount: number;
    hotel: string;
    description: string;
  };
  roomName: string;
  roomNameStartingNumber: number;
  updatedAt: string;
  _id: string;
}

export interface ReservationListApiTypes {
  allocations: RoomAllocationTypes[];
  avgPrice: number;
  channel: string;
  checkedIn: string | null;
  checkedOut: string | null;
  createdAt: string;
  currency: string;
  endDate: string;
  hash: string;
  hotel: string;
  hotelCustomer: HotelCustomerTypes;
  hotelPet: HotelPetTypes;
  hotelPetHistory: string | null;
  hotelPetOwnerHistory: string | null;
  hotelSubMerchantGuid: string;
  issueDate: string;
  nights: number;
  note: string;
  paymentStatus: string;
  paymentType: string;
  pet: PetTypes;
  petOwner: PetOwnerTypes;
  receiptNo: string;
  reservationType: string;
  room: RoomTypes;
  roomPriceTotal: number;
  services: string[];
  startDate: string;
  status: string;
  subscriptionApplied: boolean;
  total: number;
  totalHotel: number;
  totalParam: number;
  updatedAt: string;
  _id: string;
}
