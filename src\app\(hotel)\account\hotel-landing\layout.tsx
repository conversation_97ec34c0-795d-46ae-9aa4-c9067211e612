import type { FC } from "react";
import React from "react";
import HotelLandingNav from "./(components)/HotelLandingNav";
import getHotelLanding from "@/actions/(protected)/hotel/hotelLanding/getHotelLanding";

export interface HotelLandingPagesLayoutProps {
  children?: React.ReactNode;
}

const HotelLandingPagesLayout: FC<HotelLandingPagesLayoutProps> = async ({
  children,
}) => {
  const hotelLandingData = await getHotelLanding();

  return (
    <div className="container">
      {hotelLandingData !== undefined && <HotelLandingNav />}
      {children}
    </div>
  );
};

export default HotelLandingPagesLayout;
