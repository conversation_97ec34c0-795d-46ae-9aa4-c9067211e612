"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import Checkbox from "@/shared/Checkbox";
import { Button } from "@/components/ui/button";
import { usePetTaxiInformationsActions } from "@/hooks/taxi/useTaxiInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";

interface PetTaxiFeaturesProps {
  petTaxiToken: string | undefined;
  taxiData: PetTaxiDataApiTypes;
}

interface Feature {
  label: string;
  name: string;
  id: string;
}

const PetTaxiFeatures: FC<PetTaxiFeaturesProps> = ({
  taxiData,
  petTaxiToken,
}) => {
  const [petTaxiFeatures, setPetTaxiFeatures] = useState<string[]>(
    taxiData.petTaxiFeatures || []
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handlePetTaxiFeatures } = usePetTaxiInformationsActions();

  const features: {
    category: string;
    description: string;
    features: Feature[];
  }[] = [
    {
      category: "Araç Özellikleri",
      description:
        "Evcil hayvanların konforu ve güvenliği için özel olarak donatılmış araçlara ait teknik ve yapısal özellikler.",
      features: [
        {
          label: "Klimalı araç",
          name: "airConditionedVehicle",
          id: "air-conditioned-vehicle",
        },
        {
          label: "Havalandırma sistemi",
          name: "ventilationSystem",
          id: "ventilation-system",
        },
        {
          label: "Kafesli taşıma alanı",
          name: "cageTransportArea",
          id: "cage-transport-area",
        },
        {
          label: "Araç içi kamera sistemi",
          name: "inVehicleCamera",
          id: "in-vehicle-camera",
        },
        {
          label: "Isı kontrolü (termometreli)",
          name: "temperatureControl",
          id: "temperature-control",
        },
        {
          label: "Dezenfekte edilmiş araç",
          name: "sanitizedVehicle",
          id: "sanitized-vehicle",
        },
        { label: "GPS takip sistemi", name: "gpsTracking", id: "gps-tracking" },
        {
          label: "Yolculuk canlı izleme",
          name: "liveTravelMonitoring",
          id: "live-travel-monitoring",
        },
        {
          label: "Kaymaz zemin / izole zemin",
          name: "nonSlipFloor",
          id: "non-slip-floor",
        },
        {
          label: "Evcil hayvan dostu koltuk düzeni",
          name: "petFriendlySeats",
          id: "pet-friendly-seats",
        },
        { label: "Yedek kafes", name: "spareCage", id: "spare-cage" },
        { label: "İlk yardım kiti", name: "firstAidKit", id: "first-aid-kit" },
      ],
    },
    {
      category: "Taşıma Özellikleri",
      description:
        "Taşıma sürecine dair seçenekler ve evcil hayvanların farklı ihtiyaçlarına yönelik taşımacılık hizmetleri.",
      features: [
        {
          label: "Evden alım / adrese teslim",
          name: "pickupAndDropoff",
          id: "pickup-and-dropoff",
        },
        {
          label: "Farklı türleri birlikte taşıyabilme",
          name: "multiSpeciesTransport",
          id: "multi-species-transport",
        },
        {
          label: "Veteriner kliniğine taşıma",
          name: "toVeterinaryClinic",
          id: "to-veterinary-clinic",
        },
        {
          label: "Havalimanı transferi",
          name: "airportTransfer",
          id: "airport-transfer",
        },
        {
          label: "Şehir içi taşıma",
          name: "inCityTransport",
          id: "in-city-transport",
        },
        {
          label: "Şehirler arası taşıma",
          name: "intercityTransport",
          id: "intercity-transport",
        },
        { label: "Gece taşıma", name: "nightTransport", id: "night-transport" },
        {
          label: "Aynı anda birden fazla evcil hayvan taşıma",
          name: "multiplePetsTransport",
          id: "multiple-pets-transport",
        },
        {
          label: "Sahipli taşıma",
          name: "ownerAccompaniedTransport",
          id: "owner-accompanied-transport",
        },
        { label: "VIP taşıma", name: "vipTransport", id: "vip-transport" },
        {
          label: "Canlı konum paylaşımı",
          name: "liveLocationSharing",
          id: "live-location-sharing",
        },
      ],
    },
    {
      category: "Operasyonel Özellikler",
      description:
        "Taşıma sürecinin nasıl organize edildiğini ve kullanıcıya sağlanan hizmet seviyesini belirten operasyonel ayrıcalıklar.",
      features: [
        {
          label: "Rezervasyonla çalışma",
          name: "reservationRequired",
          id: "reservation-required",
        },
        {
          label: "Aynı gün hizmet",
          name: "sameDayService",
          id: "same-day-service",
        },
        {
          label: "Acil taşıma desteği",
          name: "emergencyTransportSupport",
          id: "emergency-transport-support",
        },
        { label: "7/24 hizmet", name: "service24_7", id: "service-24-7" },
        { label: "Canlı destek", name: "liveSupport", id: "live-support" },
        {
          label: "Saat garantili teslim",
          name: "onTimeDelivery",
          id: "on-time-delivery",
        },
        {
          label: "Sigortalı taşıma",
          name: "insuredTransport",
          id: "insured-transport",
        },
      ],
    },
  ];

  // adds pet taxi features to an array and removes
  const petTaxiFeaturesHandler = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      setPetTaxiFeatures([...petTaxiFeatures, name]);
    } else {
      setPetTaxiFeatures(petTaxiFeatures.filter((feature) => feature !== name));
    }
  };

  const sortedInitial = [...taxiData.petTaxiFeatures].sort();
  const sortedCurrent = [...petTaxiFeatures].sort();

  return (
    <form
      className="mt-4 md:mt-8"
      onSubmit={(event) => {
        handlePetTaxiFeatures(
          event,
          petTaxiToken,
          petTaxiFeatures,
          setLoading,
          setDisabled
        );
      }}
    >
      <div className="space-y-6">
        <div>
          <label className="block mb-8">
            <div className="">
              <h2 className="text-2xl font-semibold leading-8">
                Pet Taksi Özellikleri
              </h2>
              <span className="text-sm text-neutral-500 dark:text-neutral-300">
                Bu alanda seçilen pet taksi özellikleri pet taksi detay
                sayfanızda barındırdığınız özellikler olarak gösterilecektir.
              </span>
            </div>
          </label>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
            {features?.map((feature) => (
              <div
                key={feature.category}
                className="space-y-4 bg-white dark:bg-neutral-800 shadow-md rounded-lg px-8 py-6"
              >
                <div className="">
                  <h2 className="text-[18px] font-semibold leading-8">
                    {feature?.category}
                  </h2>
                  <span className="text-sm text-neutral-500 dark:text-neutral-300">
                    {feature?.description}
                  </span>
                </div>

                <div className="space-y-3">
                  {feature.features.map((item) => (
                    <Checkbox
                      key={item.id}
                      id={item.id}
                      label={item.label}
                      name={item.name}
                      checked={petTaxiFeatures.includes(item.name)}
                      onChange={petTaxiFeaturesHandler}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            disabled ||
            JSON.stringify(sortedInitial) === JSON.stringify(sortedCurrent)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default PetTaxiFeatures;
