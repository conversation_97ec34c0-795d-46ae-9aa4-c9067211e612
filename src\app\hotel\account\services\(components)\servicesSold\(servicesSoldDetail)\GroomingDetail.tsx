import React from "react";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface GroomingDetailProps {
  servicesSoldData: any;
}

const GroomingDetail: FC<GroomingDetailProps> = ({ servicesSoldData }) => {
  return (
    <>
      <section>
        <h3 className="text-lg font-medium mb-2">✂️ Hizmet Bilgileri</h3>
        <ul className="space-y-1">
          <li>
            Hizmet Adı:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceName || "-"}
            </span>
          </li>
          <li>
            Hizmet Tarihi:{" "}
            <span className="font-semibold">
              {formatDateToDayMonthYear(servicesSoldData?.serviceDate) || "-"}
            </span>
          </li>
          <li>
            Hizmet Süresi:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.duration
                ? `${servicesSoldData.serviceDetails.duration} dakika`
                : "-"}
            </span>
          </li>
          <li>
            Not:{" "}
            <span className="font-semibold">
              {servicesSoldData?.note || "-"}
            </span>
          </li>
        </ul>
      </section>
      <Separator />
    </>
  );
};

export default GroomingDetail;
