"use client";
import type React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export const usePartnerSignupActions = () => {
  const router = useRouter();
  const { toast } = useToast();
  const owner = useSelector((state: RootState) => state.partnerAuth.owner);
  const partner = useSelector((state: RootState) => state.partnerAuth.partner);
  const hotelFeatures = useSelector(
    (state: RootState) => state.partnerAuth.hotelFeatures
  );
  const registerId = useSelector(
    (state: RootState) => state.partnerAuth.registerId
  );

  const handleLastStep = async () => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/finish`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          registerId: registerId,
          propertyType: partner.propertyType,
        }),
      }
    );
    const data = await response.json();

    if (!response.ok || !data.success) {
      const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
      toast({
        variant: "error",
        duration: 5000,
        title: "Hata",
        description: `${errorMessage}`,
      });
      throw new Error("Network response was not ok");
    }
    router.push(`/success?propertyType=${partner.propertyType}`);
  };

  const handlePartnerInfo = async (
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading(true);

    const isPetTaxi = partner.propertyType === "petTaxi";
    const isPetHotel = partner.propertyType === "petHotel";

    const requestBody = {
      registerId: registerId,
      username: owner.userName,
      password: owner.password,
      firstName: owner.firstName?.trim(),
      lastName: owner.lastName?.trim(),
      dateOfBirth: owner.dateOfBirth,
      propertyType: partner.propertyType,
      gender: owner.gender,
      ...(isPetHotel && {
        hotelName: partner.partnerName,
        hotelPhoneNumber: partner.partnerPhoneNumber,
        hotelDescription: partner.partnerDescription,
        hotelFeatures: hotelFeatures.hotelFeaturesArray,
        careServices: hotelFeatures.hotelCareServices,
      }),

      ...(isPetTaxi && {
        petTaxiName: partner.partnerName,
        petTaxiPhoneNumber: partner.partnerPhoneNumber,
        petTaxiDescription: partner.partnerDescription,
        petTaxiFeatures: hotelFeatures.hotelFeaturesArray,
      }),
      address: {
        streetName: partner.streetName,
        buildingName: partner.buildingName,
        buildingNumber: partner.buildingNumber,
        district: partner.district,
        cityName: partner.cityName,
        region: partner.citySubdivisionName,
        postalZone: partner.postbox,
        country: {
          name: "Türkiye",
          identificationCode: "TR",
        },
      },
      acceptedPetTypes: hotelFeatures.acceptedPetTypes,
    };

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/step5`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }
      handleLastStep();
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  return { handlePartnerInfo };
};
