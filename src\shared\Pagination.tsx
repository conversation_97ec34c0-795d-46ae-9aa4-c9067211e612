"use client";
import type { <PERSON> } from "react";
import React from "react";
import twFocusClass from "@/utils/twFocusClass";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { useSearchParams } from "next/navigation";

export interface PaginationProps {
  className?: string;
  totalPage: number;
}

const Pagination: FC<PaginationProps> = ({ className = "", totalPage }) => {
  const searchParams = useSearchParams();
  const petParams = searchParams.get("pet");
  const cityParams = searchParams.get("city");
  const pageParams = searchParams.get("page") || 1;

  const totalPageCount = [];
  for (let i = 1; i <= totalPage; i++) {
    totalPageCount.push(i);
  }

  const renderItem = (_: any, index: number) => {
    // if (index === 0) {
    //   // RETURN ACTIVE PAGINATION
    //   return (
    //     <span
    //       key={index}
    //       className={`inline-flex w-11 h-11 items-center justify-center rounded-full bg-secondary-6000 text-white ${twFocusClass()}`}
    //     >
    //       {pag.label}
    //     </span>
    //   );
    // }
    // RETURN UNACTIVE PAGINATION
    return (
      <Link
        key={index}
        className={`inline-flex size-11 items-center justify-center rounded-full border border-neutral-200 text-neutral-6000 dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-400 dark:hover:bg-neutral-800 ${twFocusClass()} ${
          index + 1 === Number(pageParams)
            ? "bg-secondary-6000 text-white"
            : "bg-white hover:bg-neutral-100"
        }`}
        href={
          `/hotel-search${
            cityParams && petParams
              ? `?city=${cityParams}&pet=${petParams}`
              : cityParams && !petParams
                ? `?city=${cityParams}`
                : !cityParams && petParams
                  ? `?pet=${petParams}`
                  : ""
          }${
            !cityParams && !petParams
              ? `?page=${index + 1}`
              : `&page=${index + 1}`
          }` as Route
        }
      >
        {index + 1}
      </Link>
    );
  };

  return (
    <nav
      className={`nc-Pagination inline-flex space-x-1 text-base font-medium ${className}`}
    >
      {totalPageCount.map(renderItem)}
    </nav>
  );
};

export default Pagination;
