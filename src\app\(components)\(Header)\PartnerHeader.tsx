import LoggedHotelHeader from "./LoggedHotelHeader";
import LoggedTaxiHeader from "./LoggedTaxiHeader";
import UnloggedHeader from "./UnloggedHeader";
import { cookies } from "next/headers";

const PartnerHeader = () => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value || undefined;
  const pathname = cookieStore.get("pathname")?.value || "/";

  return (
    <>
      {token ? (
        pathname.includes("/petTaxi") ? (
          <LoggedTaxiHeader />
        ) : (
          <LoggedHotelHeader />
        )
      ) : (
        <UnloggedHeader />
      )}
    </>
  );
};

export default PartnerHeader;
