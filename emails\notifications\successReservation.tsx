import React from "react";
import Layout from "emails/_components/Layout";

export interface Allocation {
  roomAllocation: string;
  room: string;
  allocationDate: string;
  serviceFee: number;
  roomPrice: number;
  totalPrice: number;
  currency: string;
  _id: string;
}

export interface Reservation {
  _id: string;
  hotel: string;
  hotelName: string; // Yeni eklendi
  petOwner: string;
  petOwnerName: string; // Yeni eklendi
  pet: string;
  petName: string; // Yeni eklendi
  room: string;
  startDate: string;
  endDate: string;
  issueDate: string;
  checkedIn: string | null;
  checkedOut: string | null;
  nights: number;
  hotelPetOwnerHistory: string | null;
  hotelPetHistory: string | null;
  allocations: Allocation[];
  status: string;
  hotelSubMerchantGuid: string;
  avgPrice: number;
  total: number;
  currency: string;
  paymentStatus: string;
  channel: string;
  note: string;
  hash: string;
  createdAt: string;
  updatedAt: string;
  receiptNo: string | null;
  additionalServices: any[];
  transitions: any[];
}

interface Props {
  reservation: Reservation;
}

const ReservationDetails: React.FC<Props> = ({ reservation }) => {
  return (
    <Layout>
      <div className="container mx-auto p-6 bg-white shadow-lg rounded-lg">
        {/* Genel Bilgiler */}
        <h2 className="text-2xl font-semibold text-gray-800">
          Rezervasyon Detayları
        </h2>
        <p className="text-gray-600 text-sm">
          Rezervasyon ID: {reservation?._id}
        </p>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <h3 className="text-lg font-medium text-gray-700">
              Otel Bilgileri
            </h3>
            <p className="text-gray-600">Otel: {reservation?.hotelName}</p>
            <p className="text-gray-600">
              Pet Sahibi: {reservation?.petOwnerName}
            </p>
            <p className="text-gray-600">
              Evcil Hayvan: {reservation?.petName}
            </p>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-700">
              Rezervasyon Tarihleri
            </h3>
            <p className="text-gray-600">Başlangıç: {reservation?.startDate}</p>
            <p className="text-gray-600">Bitiş: {reservation?.endDate}</p>
            <p className="text-gray-600">Gece Sayısı: {reservation?.nights}</p>
          </div>
        </div>

        {/* Oda ve Tahsis Detayları */}
        <h3 className="text-lg font-medium text-gray-700 mt-6">
          Oda Tahsisleri
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 mt-2">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border">Tarih</th>
                <th className="py-2 px-4 border">Oda ID</th>
                <th className="py-2 px-4 border">Hizmet Bedeli</th>
                <th className="py-2 px-4 border">Oda Ücreti</th>
                <th className="py-2 px-4 border">Toplam Fiyat</th>
              </tr>
            </thead>
            <tbody>
              {reservation?.allocations.map((alloc, index) => (
                <tr key={index} className="border">
                  <td className="py-2 px-4">{alloc?.allocationDate}</td>
                  <td className="py-2 px-4">{alloc?.room}</td>
                  <td className="py-2 px-4">
                    {alloc?.serviceFee} {alloc.currency}
                  </td>
                  <td className="py-2 px-4">
                    {alloc?.roomPrice} {alloc?.currency}
                  </td>
                  <td className="py-2 px-4 font-semibold">
                    {alloc?.totalPrice} {alloc?.currency}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Ödeme ve Durum */}
        <h3 className="text-lg font-medium text-gray-700 mt-6">
          Ödeme ve Rezervasyon Bilgileri
        </h3>
        <p className="text-gray-600">
          Toplam Tutar: {reservation?.total} {reservation?.currency}
        </p>
        <p className="text-gray-600">
          Ödeme Durumu: {reservation?.paymentStatus || "Belirtilmemiş"}
        </p>
        <p className="text-gray-600">Kanal: {reservation?.channel}</p>
        <p className="text-gray-600">Not: {reservation?.note || "Yok"}</p>
        <p className="text-gray-600">
          Durum: <span className="font-bold">{reservation?.status}</span>
        </p>

        {/* İşlem Zamanları */}
        <div className="mt-4 text-sm text-gray-500">
          <p>
            Oluşturulma: {new Date(reservation?.createdAt).toLocaleString()}
          </p>
          <p>
            Güncellenme: {new Date(reservation?.updatedAt).toLocaleString()}
          </p>
        </div>
      </div>
    </Layout>
  );
};

export default ReservationDetails;
