"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import IconDelete from "@/shared/icons/Delete";
import { useDeleteRoom } from "@/hooks/hotel/rooms/useDeleteRoom";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import type { DeleteRoomModalProps } from "@/types/hotel/rooms/deleteRoomTypes";

const DeleteRoomModal: FC<DeleteRoomModalProps> = ({
  roomGroup,
  hotelToken,
}) => {
  const translate = useTranslations("DeleteRoomModal");
  const { roomGroupName, _id } = roomGroup;
  const [deleteRoomIsOpen, setDeleteRoomIsOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { deleteRoomHandler } = useDeleteRoom();

  function openModal() {
    setDeleteRoomIsOpen(true);
  }

  function closeModal() {
    setDeleteRoomIsOpen(false);
    setLoading(false);
  }

  return (
    <>
      <IconDelete
        onClick={openModal}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />
      <Dialog open={deleteRoomIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>{translate("RemoveRoom")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteRoomHandler(
                event,
                hotelToken,
                _id,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="capitalize text-gray-500 dark:text-neutral-200">
                {roomGroupName} {translate("RemoveRoomConfirm")}
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                {translate("cancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
                disabled={disabled}
              >
                {loading ? <LoadingSpinner /> : translate("confirm")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteRoomModal;
