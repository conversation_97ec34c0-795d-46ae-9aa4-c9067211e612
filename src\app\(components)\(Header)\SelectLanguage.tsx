import Select from "@/shared/Select";
import type { FC } from "react";
import React, { useEffect, useState } from "react";
import { LanguageIcon } from "@heroicons/react/24/outline";

export interface SelectLanguageProps {
  // onChangeLang?:any
  // defaultValue?:string
  className?: string;
}

const SelectLanguage: FC<SelectLanguageProps> = ({ className = "" }) => {
  return (
    <div className="flex items-center">
      <select
        className={`nc-Select block h-11 w-[160px] rounded-[4px] border-neutral-200 bg-white text-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-neutral-700 dark:bg-neutral-900 dark:focus:ring-secondary-6000 dark:focus:ring-opacity-25 ${className}`}
      >
        <option value="en">English</option>
        <option value="tr">Türkçe</option>
        {/* <option value="ru">Русский</option>
        <option value="ar">العربية</option>
        <option value="de">Deutsch</option> */}
      </select>
    </div>
  );
};

export default SelectLanguage;
