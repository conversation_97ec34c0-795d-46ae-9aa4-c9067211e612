"use client";
import React from "react";
import { motion } from "framer-motion";
import Iphone15Pro from "@/components/ui/iphone-15-pro";

export default function Iphone15ProComponent({ image }: { image: string }) {
  return (
    <div className="relative overflow-hidden drop-shadow-2xl">
      <motion.div
        initial={{ y: "100%" }}
        animate={{ y: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="relative"
      >
        <Iphone15Pro className="size-full" src={image} />
      </motion.div>
    </div>
  );
}
