"use client";
import React from "react";
import type { FC } from "react";
import { CircleX, FileText } from "lucide-react";

interface PreviewUploadImageContainerProps {
  pdfFileNames: string[] | [];
  setPdfFileNames: React.Dispatch<React.SetStateAction<string[] | []>>;
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  liveChatInputRef: React.RefObject<HTMLInputElement | null>;
}

const PreviewUploadFileContainer: FC<PreviewUploadImageContainerProps> = ({
  pdfFileNames,
  setPdfFileNames,
  setPhotoFileObject,
  liveChatInputRef,
}) => {
  return (
    <>
      {pdfFileNames.length > 0 && (
        <div className="mt-2 flex gap-5 flex-wrap">
          {pdfFileNames.map((file: any, index: number) => {
            return (
              <div key={index} className="relative text-sm">
                <div className="flex gap-1 items-center">
                  <FileText className="size-4" />
                  {file}
                </div>
                <button
                  className="absolute top-0.5 -right-5"
                  type="button"
                  onClick={() => {
                    setPdfFileNames((prevImages: any) =>
                      prevImages.filter((_: any, i: any) => i !== index)
                    );
                    setPhotoFileObject((prevFileObject) => {
                      if (prevFileObject) {
                        // Convert FileList to Array
                        const prevFilesArray = Array.from(prevFileObject);
                        // Perform the delete operation
                        const updatedFiles = prevFilesArray.filter(
                          (_, i) => i !== index
                        );
                        // Convert File[] back to FileList
                        const dataTransfer = new DataTransfer();
                        updatedFiles.forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                        return dataTransfer.files; // Updated FileList
                      }
                      return undefined; // Return undefined instead of null
                    });
                    if (liveChatInputRef.current) {
                      liveChatInputRef.current.value = ""; // Clear the input
                    }
                  }}
                >
                  <CircleX className="size-4 rounded-full bg-white" />
                </button>
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

export default PreviewUploadFileContainer;
