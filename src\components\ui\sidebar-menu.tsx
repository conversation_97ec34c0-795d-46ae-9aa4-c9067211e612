"use client";
import { cn } from "@/lib/utils";
import React, { useState, createContext, useContext } from "react";
import { AnimatePresence, motion } from "motion/react";
import { IconMenu2, IconX } from "@tabler/icons-react";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";

interface Links {
  label: string;
  href: string;
  icon: React.JSX.Element | React.ReactNode;
  subItems?: {
    label: string;
    href: string;
    icon?: React.JSX.Element | React.ReactNode;
  }[];
  isActive?: boolean;
}

interface SidebarContextProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  animate: boolean;
}

const SidebarContext = createContext<SidebarContextProps | undefined>(
  undefined
);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};

export const SidebarProvider = ({
  children,
  open: openProp,
  setOpen: setOpenProp,
  animate = true,
}: {
  children: React.ReactNode;
  open?: boolean;
  setOpen?: (open: boolean) => void;
  animate?: boolean;
}) => {
  const [openState, setOpenState] = useState(false);

  const open = openProp !== undefined ? openProp : openState;
  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;

  return (
    <SidebarContext.Provider value={{ open, setOpen, animate: animate }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const Sidebar = ({
  children,
  open,
  setOpen,
  animate,
}: {
  children: React.ReactNode;
  open?: boolean;
  setOpen?: (open: boolean) => void;
  animate?: boolean;
}) => {
  return (
    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>
      {children}
    </SidebarProvider>
  );
};

export const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {
  return (
    <>
      <DesktopSidebar {...props} />
      <MobileSidebar {...(props as React.ComponentProps<"div">)} />
    </>
  );
};

export const DesktopSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<typeof motion.div>) => {
  const { open, setOpen, animate } = useSidebar();
  return (
    <>
      <motion.div
        className={cn(
          "h-full py-4 hidden md:flex md:flex-col bg-gradient-to-b from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800 shrink-0 border-r border-neutral-200 dark:border-neutral-700",
          className
        )}
        initial={false}
        animate={{
          width: animate ? (open ? "300px" : "80px") : "300px",
          paddingLeft: animate ? (open ? "16px" : "8px") : "16px",
          paddingRight: animate ? (open ? "16px" : "8px") : "16px",
        }}
        transition={{
          duration: 0.25,
          ease: [0.4, 0, 0.2, 1],
        }}
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
        {...props}
      >
        {children}
      </motion.div>
    </>
  );
};

export const MobileSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  const { open, setOpen } = useSidebar();
  return (
    <>
      <div
        className={cn(
          "h-auto px-4 py-2 flex flex-row md:hidden items-center justify-between bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800 w-full border-b border-neutral-200 dark:border-neutral-700"
        )}
        {...props}
      >
        <div className="relative flex justify-center items-center w-full z-20 h-16">
          <PawBookingLogo className="size-16" />
          <IconMenu2
            className="absolute right-4 text-neutral-800 dark:text-neutral-200"
            onClick={() => setOpen(!open)}
          />
        </div>

        <AnimatePresence>
          {open && (
            <motion.div
              initial={{ x: "-100%", opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: "-100%", opacity: 0 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className={cn(
                "fixed h-full w-full inset-0 bg-white dark:bg-neutral-900 p-10 z-[100] flex flex-col justify-between",
                className
              )}
            >
              <div
                className="absolute right-10 top-10 z-50 text-neutral-800 dark:text-neutral-200"
                onClick={() => setOpen(!open)}
              >
                <IconX />
              </div>
              {children}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export const SidebarLink = ({
  link,
  className,
  expandedItems,
  setExpandedItems,
  isActive,
  pathname,
  ...props
}: {
  link: Links;
  className?: string;
  expandedItems?: string[];
  setExpandedItems?: React.Dispatch<React.SetStateAction<string[]>>;
  isActive?: boolean;
  pathname?: string;
}) => {
  const { open, animate } = useSidebar();
  const hasSubItems = link.subItems && link.subItems.length > 0;
  const isExpanded = expandedItems?.includes(link.label) || false;
  const active = isActive || link.isActive;

  const handleClick = (e: React.MouseEvent) => {
    if (hasSubItems && setExpandedItems && open) {
      // Only allow expanding if sidebar is open
      e.preventDefault();
      if (isExpanded) {
        setExpandedItems((prev) => prev.filter((item) => item !== link.label));
      } else {
        setExpandedItems((prev) => [...prev, link.label]);
      }
    }
  };

  return (
    <div className="w-full">
      <a
        href={hasSubItems ? "#" : link.href}
        onClick={handleClick}
        className={cn(
          "flex items-center py-3 rounded-lg transition-all duration-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 relative min-h-[44px]",
          (isExpanded && hasSubItems) || active
            ? "bg-neutral-200 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-50 font-medium"
            : "text-neutral-700 dark:text-neutral-300",
          open ? "justify-between px-3 gap-3" : "justify-center px-2", // Adjusted padding and gap based on open state
          className
        )}
        {...props}
      >
        {/* Icon */}
        <div
          className={cn(
            "flex items-center justify-center flex-shrink-0 w-5 h-5",
            active && "text-neutral-900 dark:text-neutral-50"
          )}
        >
          {React.cloneElement(link.icon as React.ReactElement, {
            className: cn(
              "w-100 h-5 transition-colors duration-200",
              active
                ? "text-neutral-900 dark:text-neutral-50"
                : "text-neutral-700 dark:text-neutral-300"
            ),
          })}
        </div>

        {/* Text and Arrow Container */}
        <div
          className={cn(
            "flex items-center justify-between min-w-0 overflow-hidden",
            open ? "flex-1" : "w-0 flex-shrink-0" // Only flex-1 when open, collapse to 0 width when closed
          )}
        >
          <motion.span
            initial={false}
            animate={{
              opacity: open ? 1 : 0,
              x: open ? 0 : -20, // Slide text out/in
            }}
            transition={{
              duration: 0.25, // Match sidebar width duration
              ease: [0.4, 0, 0.2, 1],
            }}
            className={cn(
              "text-sm font-medium whitespace-nowrap", // Removed overflow-hidden here, parent handles it
              active
                ? "text-neutral-900 dark:text-neutral-50"
                : "text-neutral-700 dark:text-neutral-300"
            )}
          >
            {link.label}
          </motion.span>

          {hasSubItems && (
            <motion.div
              initial={false}
              animate={{
                opacity: open ? 1 : 0, // Fade arrow out/in
                rotate: isExpanded ? 180 : 0,
                x: open ? 0 : 20, // Slide arrow out/in
              }}
              transition={{
                duration: 0.25, // Match sidebar width duration
                ease: [0.4, 0, 0.2, 1],
              }}
              className={cn(
                "flex-shrink-0", // Removed overflow-hidden and w-0 here
                active
                  ? "text-neutral-900 dark:text-neutral-50"
                  : "text-neutral-500 dark:text-neutral-400"
              )}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6 9L12 15L18 9"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </motion.div>
          )}
        </div>
      </a>

      {/* Sub Items */}
      <AnimatePresence initial={false}>
        {hasSubItems && isExpanded && open && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: "auto",
              opacity: 1,
            }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.25,
              ease: [0.4, 0, 0.2, 1],
              opacity: { duration: 0.15, delay: 0.05 },
            }}
            className="overflow-hidden"
          >
            <div className="ml-8 mt-1 space-y-1">
              {link.subItems?.map((subItem, subIdx) => {
                const subItemActive = pathname === subItem.href;
                return (
                  <motion.a
                    key={subIdx}
                    href={subItem.href}
                    initial={false}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{
                      duration: 0.2,
                      delay: subIdx * 0.05,
                      ease: [0.4, 0, 0.2, 1],
                    }}
                    className={cn(
                      "flex items-center py-2 px-3 text-sm rounded-md transition-all duration-150 group/subitem",
                      subItemActive
                        ? "bg-neutral-200 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-50 font-medium"
                        : "text-neutral-600 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-neutral-100 hover:bg-neutral-100 dark:hover:bg-neutral-800"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      {subItem.icon && (
                        <div className="text-muted-foreground">
                          {React.cloneElement(
                            subItem.icon as React.ReactElement,
                            {
                              className: "w-4 h-4 text-muted-foreground",
                            }
                          )}
                        </div>
                      )}
                      <span className="group-hover/subitem:translate-x-1 transition-transform duration-150 whitespace-nowrap">
                        {subItem.label}
                      </span>
                    </div>
                  </motion.a>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
