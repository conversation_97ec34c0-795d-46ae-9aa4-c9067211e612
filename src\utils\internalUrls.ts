type Paths = Record<string, string>;

const generateLink = (baseUrl: string, paths: Paths): Paths => {
  const keys = Object.keys(paths);
  const newPaths: Paths = {};

  keys.forEach((key) => {
    newPaths[key] = `${baseUrl}${paths[key]}`;
  });

  return newPaths;
};

export const ADMIN_PATHS = generateLink("/admin", {
  dashboard: "",
});

export const HOTEL_PATHS = generateLink("/hotel", {
  dashboard: "",
});

export const USER_PATHS = generateLink("/user", {
  dashboard: "",
});

export const LANDING_PATHS = generateLink("", {
  home: "/",
});
