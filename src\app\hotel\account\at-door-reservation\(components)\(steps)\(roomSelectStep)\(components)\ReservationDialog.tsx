"use client";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ReservationForm from "./ReservationForm";
import ServiceForm from "./ServiceForm";
import SubscriptionForm from "./SubscriptionForm";
import CustomOverlay from "@/components/CustomOverlay";

interface ReservationDialogProps {
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  serviceData: any;
}

const ReservationDialog: React.FC<ReservationDialogProps> = ({
  startDate,
  endDate,
  hotelToken,
  hotelData,
  subscriptionData,
  serviceData,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<
    "reservation" | "service" | "subscription" | null
  >(null);

  const handleDialogClose = () => {
    setDialogOpen(false);
    setActiveSection(null);
  };

  const handleButtonClick = (
    section: "reservation" | "service" | "subscription"
  ) => {
    setActiveSection(section);
    setDialogOpen(true);
  };

  const renderDialogContent = () => {
    switch (activeSection) {
      case "reservation":
        return (
          <ReservationForm
            startDate={startDate}
            endDate={endDate}
            hotelToken={hotelToken}
            hotelData={hotelData}
            onClose={handleDialogClose}
          />
        );
      case "service":
        return (
          <ServiceForm
            hotelToken={hotelToken}
            hotelData={hotelData}
            serviceData={serviceData}
            onClose={handleDialogClose}
            startDate={startDate}
          />
        );
      case "subscription":
        return (
          <SubscriptionForm
            hotelToken={hotelToken}
            hotelData={hotelData}
            subscriptionData={subscriptionData}
            onClose={handleDialogClose}
          />
        );
      default:
        return (
          <div className="text-neutral-500">Lütfen bir seçenek belirleyin.</div>
        );
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row gap-5 w-full max-md:w-[95vw] max-md:max-w-md pt-2 pb-4 px-10 justify-center">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("reservation")}
          disabled={!startDate || !endDate}
        >
          Yeni Konaklama Ekle
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("service")}
          disabled={!startDate || !endDate}
        >
          Yeni Hizmet Ekle
        </Button>
        {/* <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
          onClick={() => handleButtonClick("subscription")}
          disabled={!startDate || !endDate}
        >
          Yeni Üyelik Kartı Ekle
        </Button> */}
      </div>
      <Dialog
        modal={activeSection !== "service"}
        open={dialogOpen}
        onOpenChange={(isOpen) => {
          if (activeSection !== "service") {
            setDialogOpen(isOpen);
          }
        }}
      >
        {activeSection === "service" && (
          <CustomOverlay isModalOpen={dialogOpen} />
        )}
        <DialogContent className="max-h-[calc(100vh-100px)] max-w-2xl overflow-y-auto md:max-h-[calc(100vh-50px)]">
          <DialogHeader>
            <DialogTitle>
              {activeSection === "reservation" && "Konaklama"}
              {activeSection === "service" && "Hizmet"}
              {activeSection === "subscription" && "Üyelik Kartı"}
            </DialogTitle>
            <DialogDescription>
              Lütfen aşağıdaki alanlardan seçim yapınız.
            </DialogDescription>
          </DialogHeader>
          {renderDialogContent()}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ReservationDialog;
