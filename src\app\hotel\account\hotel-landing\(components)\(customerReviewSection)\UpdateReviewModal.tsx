"use client";
import React, { useState } from "react";
import type { FC, ChangeEvent } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import turkeyCityDistrict from "@/data/jsons/mernis_city_district.json";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Button } from "@/components/ui/button";
import IconEdit from "@/shared/icons/Edit";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

interface updatedReviewModalProps {
  reviews: any;
  customerReviewsSectionData: any;
  hotelToken: string | undefined;
}

const UpdateReviewModal: FC<updatedReviewModalProps> = ({
  reviews,
  customerReviewsSectionData,
  hotelToken,
}) => {
  const { updateCustomerReviewSectionHandler } = useHotelLandingActions();
  const [updatedReview, setUpdatedReview] = useState(reviews);
  const [updatedReviewIsOpen, setUpdatedReviewIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const cancelUpdate = () => {
    setUpdatedReview(reviews);
    setUpdatedReviewIsOpen(false);
  };

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setUpdatedReview({ ...updatedReview, [name]: value });
  };

  return (
    <>
      <IconEdit
        onClick={() => setUpdatedReviewIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />
      <Dialog open={updatedReviewIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)]"
          onInteractOutside={cancelUpdate}
        >
          <DialogHeader>
            <DialogTitle>Yorum Düzenleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updateCustomerReviewSectionHandler(
                event,
                updatedReview,
                hotelToken,
                setLoading,
                setUpdatedReviewIsOpen
              )
            }
          >
            <div className="mb-4 mt-2"></div>
            <div className="space-y-5">
              <div>
                <FormItem label="Müşteri Adı">
                  <Input
                    type="text"
                    name="customerName"
                    className="mt-1.5"
                    value={updatedReview.customerName}
                    onChange={handleChange}
                  />
                </FormItem>
                <FormItem label="Müşteri Yorumu" className="mt-3">
                  <Textarea
                    name="review"
                    className="mt-1.5"
                    value={updatedReview.review}
                    onChange={handleChange}
                  />
                </FormItem>
                <FormItem label="Şehir" className="mt-3">
                  <Select
                    value={updatedReview.city}
                    onValueChange={(selected) =>
                      setUpdatedReview({ ...updatedReview, city: selected })
                    }
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Şehir Seç" />
                    </SelectTrigger>
                    <SelectContent>
                      {turkeyCityDistrict.map((city) => {
                        return (
                          <SelectItem key={city.ilkodu} value={city.iladi}>
                            {city.iladi}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </FormItem>
              </div>
              <div className="flex justify-end items-center mt-3 gap-5">
                <Button type="button" onClick={cancelUpdate} variant="outline">
                  Vazgeç
                </Button>
                <Button
                  type="submit"
                  className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                  //   disabled={isAddButtonDisabled}
                >
                  {loading ? <LoadingSpinner /> : "Kaydet"}
                </Button>
              </div>
            </div>
          </form>
          <DialogClose
            onClick={cancelUpdate}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateReviewModal;
