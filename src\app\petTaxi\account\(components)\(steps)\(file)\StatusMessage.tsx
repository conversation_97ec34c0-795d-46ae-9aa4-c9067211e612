"use client";
import React from "react";
import { Check } from "lucide-react";

interface StatusMessageProps {
  currentStep: number;
  completedSteps: Set<number>;
  isTransitioning: boolean;
}

const StatusMessage: React.FC<StatusMessageProps> = ({
  currentStep,
  completedSteps,
  isTransitioning,
}) => {
  return (
    <div className="flex justify-center mt-8">
      {!completedSteps.has(currentStep) && !isTransitioning && (
        <span className="text-sm text-amber-600 bg-amber-50 px-4 py-3 rounded-lg animate-pulse">
          Bu dosyayı yükleyin
        </span>
      )}
      {completedSteps.has(currentStep) && !isTransitioning && (
        <span className="text-sm text-green-600 bg-green-50 px-4 py-3 rounded-lg flex items-center gap-2">
          <Check className="w-4 h-4" />
          Bu dosya başarıyla yüklendi
        </span>
      )}
      {isTransitioning && (
        <span className="text-sm text-blue-600 bg-blue-50 px-4 py-3 rounded-lg flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          Geçiş yapılıyor...
        </span>
      )}
    </div>
  );
};

export default StatusMessage;
