export enum ImageResizeFitType {
  /**
   * An image resize processing parameter.
   * For more information 'sharp' documents
   * https://sharp.pixelplumbing.com/api-resize
   */
  cover = "cover",
  contain = "contain",
  fill = "fill",
  inside = "inside",
  outside = "outside",
}

export const uploadMultiToS3 = async (
  files: FileList,
  folder: string,
  token: string | undefined,
  fit = ImageResizeFitType.fill,
  doctype?: string
) =>
  new Promise<any>(async (resolve, reject) => {
    if (!files || files.length == 0) {
      return reject("Please select a file to upload.");
    }

    let i = 0;
    const formData = new FormData();

    while (i < files.length) {
      formData.append("file", files.item(i) as File);
      i++;
    }

    formData.append("folder", folder);
    let url = "/s3/imageUpload";

    if (doctype) {
      url = "/s3/fileUpload";
      formData.append("doctype", doctype);
    } else {
      formData.append("fit", fit.toString());
    }

    postFilesToBackend(formData, url, token).then(resolve).catch(reject);
  });

export const uploadSingleToS3 = async (
  file: File,
  folder: string,
  fit: ImageResizeFitType = ImageResizeFitType.fill,
  token: string
) =>
  new Promise<any>(async (resolve, reject) => {
    if (!file) {
      return reject("Please select a file to upload.");
    }

    const formData = new FormData();
    formData.append("file", file);
    formData.append("folder", folder);
    formData.append("fit", fit.toString());

    postFilesToBackend(formData, "/s3/imageUpload", token)
      .then(resolve)
      .catch(reject);
  });

function postFilesToBackend(
  formData: FormData,
  apiPath: string,
  token: string | undefined
) {
  return new Promise<any>((resolve, reject) => {
    const url =
      process.env.NEXT_PUBLIC_API_URI + (apiPath || "/s3/imageUpload");
    const headers = new Headers();
    if (token) {
      headers.append("hotelToken", token);
    }

    fetch(url, {
      method: "POST",
      headers: headers,
      body: formData,
    })
      .then((uploadResponse) => {
        if (uploadResponse.ok) {
          uploadResponse
            .json()
            .then((result) => {
              if (result.success) {
                resolve(result);
              } else {
                reject(result.error);
              }
            })
            .catch((err) => reject(err.message || err));
        } else {
          reject(uploadResponse.statusText);
        }
      })
      .catch((err) => reject(err.message || err));
  });
}
