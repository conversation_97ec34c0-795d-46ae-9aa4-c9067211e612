//   Converts a given date string in the format "yyyy-MM-dd" or a Date object
//   into a local Date object (in the local timezone).

//   - If the input is a string in the "yyyy-MM-dd" format, it splits the string
//     into year, month, and day components, and then creates a new Date object
//     considering the local timezone (months are zero-indexed in JavaScript).
//   - If the input is already a Date object, it returns the same Date object.
//   - If no valid date is provided (undefined or null), it returns undefined.

//   @param dateString - A date string in "yyyy-MM-dd" format or a Date object.
//   @returns A Date object corresponding to the provided date string or Date,
//            or undefined if no valid date is provided.

export const createLocalDate = (dateString: string | Date) => {
  if (!dateString) return undefined;

  if (typeof dateString === "string") {
    const [year, month, day] = dateString.split("-").map(Number);
    return new Date(year, month - 1, day);
  }

  return dateString;
};
