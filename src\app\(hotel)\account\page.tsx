import React from "react";
import TodayContainer from "./(components)/(today)/TodayContainer";
import { cookies } from "next/headers";
import getRoomGroupsWithAllocation from "@/actions/(protected)/hotel/getRoomGroupsWithAllocation";
import getTodayReservations from "@/actions/(protected)/hotel/todayReservations";
import getTodayRevenue from "@/actions/(protected)/hotel/todayRevenue";
import getTodayGuest from "@/actions/(protected)/hotel/todayTotalGuest";
import getTodayCancelled from "@/actions/(protected)/hotel/todayCancelled";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import StepContainer from "./(components)/(steps)/StepContainer";
import StepNav from "./(components)/StepNav";
import getCheckHotelSubMerchant from "@/actions/(protected)/hotel/getCheckHotelSubMerchant";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";

const TodayPage = async () => {
  const roomGroupData = await getRoomGroupsWithAllocation();
  const todayReservations = await getTodayReservations();
  const todayRevenue = await getTodayRevenue();
  const totalGuest = await getTodayGuest();
  const todayCancelled = await getTodayCancelled();
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const checkSubMerchant = await getCheckHotelSubMerchant();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div className="container">
      {hotelData?.data?.status === "approved" ? (
        <TodayContainer
          roomGroupData={roomGroupData?.data}
          hotelToken={hotelToken ?? ""}
          todayData={{
            todayReservations: todayReservations?.data,
            todayRevenue: todayRevenue?.data,
            totalGuest: totalGuest?.data,
            todayCancelled: todayCancelled?.data,
          }}
          membershipData={membershipData?.data}
        />
      ) : (
        <>
          {hotelData?.data?.status !== "inReview" && (
            <StepNav
              hotelData={hotelData?.data}
              checkSubMerchant={checkSubMerchant?.data?.exists}
            />
          )}
          <StepContainer
            hotelData={hotelData?.data}
            hotelToken={hotelToken}
            checkSubMerchant={checkSubMerchant?.data?.exists}
          />
        </>
      )}
    </div>
  );
};

export default TodayPage;
