"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import IconCheck from "@/shared/icons/Check";
import IconXMark from "@/shared/icons/Xmark";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import { useUpdateReservationStatus } from "@/hooks/hotel/useUpdateReservationStatus";

interface ReservationActionButtonsProps {
  rowOriginal: any;
  hotelToken: string | undefined;
}

export interface ParameterTypes {
  status: string;
  text: {
    heading: string;
    description: string;
    button: string;
  };
}
const AcceptDeclineModal = dynamic(() => import("./AcceptDeclineModal"));

const ReservationActionButtons: FC<ReservationActionButtonsProps> = ({
  rowOriginal,
  hotelToken,
}) => {
  const { updateReservationStatusHandler } = useUpdateReservationStatus();
  const translate = useTranslations("ReservationActionButtons");
  const { status } = rowOriginal;
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);
  const [parameter, setParameter] = useState<ParameterTypes>({
    status: "1",
    text: {
      heading: translate("reservationConfirmation"),
      description: translate("reservationConfirmationDesc"),
      button: translate("confirm"),
    },
  });

  const handleCheckIn = () => {
    if (rowOriginal?._id) {
      updateReservationStatusHandler(hotelToken, "checkedIn", rowOriginal?._id);
    }
  };

  const handleCheckOut = () => {
    if (rowOriginal?._id) {
      updateReservationStatusHandler(
        hotelToken,
        "checkedOut",
        rowOriginal?._id
      );
    }
  };

  return (
    <div className="flex justify-center gap-4">
      {status === "waitingForCheckIn" && (
        <Button
          className="!bg-[#2f855a] text-white w-[120px]"
          onClick={handleCheckIn}
        >
          Check-in Yap
        </Button>
      )}
      {status === "waitingForCheckOut" && (
        <Button
          className="!bg-[#c53030] text-white hover:!bg-[#a12222] w-[120px]"
          onClick={handleCheckOut}
        >
          Check-out Yap
        </Button>
      )}
      {status === "booked" || status === "waitingForApproval" ? (
        <>
          <Button
            className="!bg-[#2f855a] text-white"
            onClick={() => {
              setModalIsOpen(true);
              setParameter({
                status: "1",
                text: {
                  heading: translate("reservationConfirmation"),
                  description: translate("reservationConfirmationDesc"),
                  button: translate("confirm"),
                },
              });
            }}
          >
            <IconCheck strokeWidth={2.5} />
          </Button>
          <Button
            className="!bg-[#c53030] text-white hover:!bg-[#a12222]"
            onClick={() => {
              setModalIsOpen(true);
              setParameter({
                status: "0",
                text: {
                  heading: translate("reservationCancellation"),
                  description: translate("reservationCancellationDesc"),
                  button: translate("confirm"),
                },
              });
            }}
          >
            <IconXMark strokeWidth={2.5} />
          </Button>
        </>
      ) : (
        status !== "waitingForCheckIn" && status !== "waitingForCheckOut" && "-"
      )}
      <AcceptDeclineModal
        modalIsOpen={modalIsOpen}
        setModalIsOpen={setModalIsOpen}
        parameter={parameter}
        rowOriginal={rowOriginal}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default ReservationActionButtons;
