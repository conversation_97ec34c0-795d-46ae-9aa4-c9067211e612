import type { FormEvent } from "react";
import { HOTEL_API_PATHS, S3_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import type { Vaccine, PetInformationTypes } from "@/types/petOwner/petTypes";
import slug from "slug";
import { useRouter } from "next/navigation";
import {
  ImageResizeFitType,
  uploadMultiToS3,
  uploadSingleToS3,
} from "@/lib/s3BucketHelper";
import { useSearchParams } from "next/navigation";

export const useHotelPet = () => {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  const orderIdParam = searchParams.get("orderId");
  const addPetParam = searchParams.get("addPet");
  // ADDS NEW PET
  const addNewPetHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelId: string,
    hotelCustomerId: string,
    petInformations: PetInformationTypes,
    vaccineFile: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeAddNewPetModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      try {
        const response = await fetch(HOTEL_API_PATHS.addHotelPet, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: petInformations.name,
            age: petInformations.age,
            kind: petInformations.kind,
            breed: petInformations.breed,
            gender: petInformations.gender,
            color: petInformations.color,
            specified: petInformations.specified,
            infertile: petInformations.infertile,
            microChipNumber: petInformations.microChipNumber,
            vaccines: petInformations.vaccines,
            internalParasiteTreatment:
              petInformations.internalParasiteTreatment,
            externalParasiteTreatment:
              petInformations.externalParasiteTreatment,
            treatmentDate: petInformations.internalTreatmentDate,
            externalTreatmentDate: petInformations.externalTreatmentDate,
            internalTreatmentDate: petInformations.internalTreatmentDate,
            allergicTo: petInformations.allergicTo,
            hereditaryDiseases: petInformations.hereditaryDiseases,
            operationHistory: petInformations.operationHistory,
            medicine: petInformations.medicine,
            feedingHabits: petInformations.feedingHabits,
            description: petInformations.description,
            documentPhotos: vaccineFile,
            hotel: hotelId,
            owner: hotelCustomerId,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Evcil Hayvan Ekleme",
          description: "Evcil hayvan başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeAddNewPetModal();
        setDisabled(false);
        if (orderIdParam && addPetParam) {
          router.push(
            `/hotel/account/at-door-reservation?orderId=${orderIdParam}&petStep=true`
          );
          return;
        } else {
          router.push("/hotel/account/hotel-customer");
        }
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
    }
  };

  // DELETES SELECTED PET
  const deletePetHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    petId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeDeletePetModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.removeHotelPet}/${petId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Evcil Hayvan Silme",
          description: "Evcil hayvan başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeDeletePetModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES SELECTED PET
  const updatePetHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    updatedPetInfo: {
      name: string;
      age: string;
      kind: string;
      breed: string;
      gender: string;
      color: string;
      specified: string;
      infertile: boolean | null | string;
      microChipNumber: string;
      vaccines: Vaccine[];
      internalParasiteTreatment: boolean;
      externalParasiteTreatment: boolean;
      internalTreatmentDate: string;
      externalTreatmentDate: string;
      allergicTo: string[];
      hereditaryDiseases: string[];
      operationHistory: string[];
      medicine: string;
      feedingHabits: string;
      description: string;
    },
    documents: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    hotelId: string,
    hotelCustomerId: string,
    hotelPetId: string,
    closeUpdatePetModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateHotelPet}/${hotelPetId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              name: updatedPetInfo.name,
              age: updatedPetInfo.age,
              kind: updatedPetInfo.kind,
              breed: updatedPetInfo.breed,
              gender: updatedPetInfo.gender,
              color: updatedPetInfo.color,
              specified: updatedPetInfo.specified,
              infertile: updatedPetInfo.infertile,
              microChipNumber: updatedPetInfo.microChipNumber,
              vaccines: updatedPetInfo.vaccines,
              internalParasiteTreatment:
                updatedPetInfo.internalParasiteTreatment,
              externalParasiteTreatment:
                updatedPetInfo.externalParasiteTreatment,
              internalTreatmentDate: updatedPetInfo.internalTreatmentDate,
              externalTreatmentDate: updatedPetInfo.externalTreatmentDate,
              allergicTo: updatedPetInfo.allergicTo,
              hereditaryDiseases: updatedPetInfo.hereditaryDiseases,
              operationHistory: updatedPetInfo.operationHistory,
              medicine: updatedPetInfo.medicine,
              feedingHabits: updatedPetInfo.feedingHabits,
              description: updatedPetInfo.description,
              documentPhotos: documents,
              hotel: hotelId,
              owner: hotelCustomerId,
            }),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Evcil Hayvan Güncelleme",
          description: "Evcil hayvan başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeUpdatePetModal();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
        router.push("/hotel/account/hotel-customer");
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
    }
  };

  const documentPhotosHandler = async (
    event: FormEvent,
    file: FileList | undefined,
    hotelToken: string | undefined,
    hotelId: string,
    hotelCustomerId: string,
    petInformations: PetInformationTypes,
    closeAddNewPetModal: () => void,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      // if vaccine report does not exist skip upload step
      if (!file) {
        return addNewPetHandler(
          event,
          hotelToken,
          hotelId,
          hotelCustomerId,
          petInformations,
          [],
          setLoading,
          closeAddNewPetModal,
          setDisabled
        );
      }
      try {
        const response = await uploadMultiToS3(
          file,
          `hotel/${hotelId}/hotelCustomers/${hotelCustomerId}/petVaccine/${slug(petInformations.name)}`,
          hotelToken,
          ImageResizeFitType.contain
        );
        if (response.success) {
          const documentPhotoId = response.data.map((item: any) => item._id);
          addNewPetHandler(
            event,
            hotelToken,
            hotelId,
            hotelCustomerId,
            petInformations,
            documentPhotoId,
            setLoading,
            closeAddNewPetModal,
            setDisabled
          );
          revalidatePathHandler("/hotel/account/hotel-customer");
        } else {
          setLoading(false);
          setDisabled(false);
          throw new Error("Aşı karnesi başarısız oldu.");
        }
      } catch (error) {
        console.error("Aşı karnesi yükleme hatası:", error);
      }
    }
  };

  const updateDocumentPhotosHandler = async (
    event: FormEvent,
    file: FileList | undefined,
    hotelToken: string | undefined,
    petInformations: PetInformationTypes,
    closeUpdatePetModal: () => void,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    hotelId: string,
    hotelCustomerId: string,
    hotelPetId: string,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      // if vaccine report does not exist skip upload step
      if (!file) {
        const currentDocuments = petInformations.documentPhotos.map(
          (document) => document._id
        );
        return updatePetHandler(
          event,
          hotelToken,
          petInformations,
          currentDocuments,
          setLoading,
          hotelId,
          hotelCustomerId,
          hotelPetId,
          closeUpdatePetModal,
          setDisabled
        );
      }
      try {
        const response = await uploadMultiToS3(
          file,
          `hotel/${hotelId}/hotelCustomers/${hotelCustomerId}/petVaccine/${slug(petInformations.name)}`,
          hotelToken,
          ImageResizeFitType.contain
        );
        if (response.success) {
          const documentPhotoId = response.data.map((item: any) => item._id);
          const currentDocuments = petInformations.documentPhotos.map(
            (document) => document._id
          );
          const mergedDocumentIds = [...currentDocuments, ...documentPhotoId];
          updatePetHandler(
            event,
            hotelToken,
            petInformations,
            mergedDocumentIds,
            setLoading,
            hotelId,
            hotelCustomerId,
            hotelPetId,
            closeUpdatePetModal,
            setDisabled
          );
          revalidatePathHandler("/hotel/account/hotel-customer");
        } else {
          setLoading(false);
          setDisabled(false);
          throw new Error("Aşı karnesi başarısız oldu.");
        }
      } catch (error) {
        console.error("Aşı karnesi yükleme hatası:", error);
      }
    }
  };

  const deleteVaccinationReportPhoto = async (
    hotelToken: string | undefined,
    imageId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.imageUpload}/deleteImage?imageId=${imageId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setTimeout(() => {
            setDisabled(false);
          }, 2000);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Fotoğraf Silme",
          description: "Aşı karnesi fotoğrafı başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/update-pet");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    deletePetHandler,
    updatePetHandler,
    documentPhotosHandler,
    deleteVaccinationReportPhoto,
    updateDocumentPhotosHandler,
  };
};
