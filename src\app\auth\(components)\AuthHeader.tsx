"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { IoIosArrowBack } from "react-icons/io";
import { useTranslations } from "next-intl";

const AuthHeader = () => {
  const translate = useTranslations("AuthHeader");
  const router = useRouter();
  const pathname = usePathname();
  const pathnameCheck =
    pathname.includes("/auth/login") || pathname.includes("/auth/hotel-login");

  return (
    <>
      {!pathnameCheck && (
        <div className="mx-auto flex max-w-[880px] justify-between px-3 pt-10 md:px-0">
          <div
            onClick={() => {
              const confirmResult = window.confirm(translate("approve"));
              if (confirmResult) {
                router.push("/");
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              }
            }}
            className="flex cursor-pointer items-center justify-between text-lg text-orange-600 hover:underline"
          >
            <IoIosArrowBack className="mt-0.5" />
            <span className="block">{translate("back")}</span>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthHeader;
