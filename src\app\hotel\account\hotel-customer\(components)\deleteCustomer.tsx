"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { useHotelCustomers } from "@/hooks/hotel/useHotelCustomers";
import type { HotelCustomerApiTypes } from "@/types/hotel/hotelCustomerType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconDelete from "@/shared/icons/Delete";
import LoadingSpinner from "@/shared/icons/Spinner";
export interface DeleteCustomerProps {
  rowOriginal: HotelCustomerApiTypes;
  hotelToken: string | undefined;
}

const DeleteCustomer: FC<DeleteCustomerProps> = ({
  hotelToken,
  rowOriginal,
}) => {
  const { removeHotelCustomerHandler } = useHotelCustomers();
  const [loading, setLoading] = useState<boolean>(false);
  const [deleteHotelCustomerIsOpen, setDeleteHotelCustomerIsOpen] =
    useState(false);

  function closeModal() {
    setDeleteHotelCustomerIsOpen(false);
    setLoading(false);
  }

  return (
    <>
      <IconDelete
        onClick={() => setDeleteHotelCustomerIsOpen(true)}
        className="size-5 duration-200 hover:text-secondary-6000"
      />
      <Dialog open={deleteHotelCustomerIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Müşteri Silme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              removeHotelCustomerHandler(
                event,
                hotelToken,
                rowOriginal._id,
                setLoading,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                <span className="font-semibold">{rowOriginal.fullName}</span>{" "}
                isimli müşteriyi silmek istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteCustomer;
