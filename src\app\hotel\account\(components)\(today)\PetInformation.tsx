import React from "react";
import type { FC } from "react";
import Image from "next/image";
import catAvatar from "@/images/cat-avatar.png";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useTranslations } from "next-intl";
import { CheckIcon, X, MarsIcon, VenusIcon } from "lucide-react";
import { slugifyName } from "@/utils/formatFileName";

interface PetInformationProps {
  selectedRoom: any;
  onImageClick: (url: string | string[], fileName: string) => void;
}

const PetInformation: FC<PetInformationProps> = ({
  selectedRoom,
  onImageClick,
}) => {
  const translate = useTranslations("PetInformation");

  const photoUrl =
    selectedRoom?.reservation?.pet?.images?.[0]?.src ||
    selectedRoom?.reservation?.hotelPet?.images?.[0]?.src ||
    "";

  const documentPhotos = Object.values(
    selectedRoom?.reservation?.pet?.documentPhotos ||
      selectedRoom?.reservation?.hotelPet?.documentPhotos ||
      {}
  ).map((photo: any) => photo?.src || photo?.img800?.src);

  return (
    <div className="my-4">
      <div className="flex flex-col justify-center items-center gap-1">
        <div
          className={`relative rounded-full border overflow-hidden w-[80px] h-[80px] ${
            photoUrl ? "border-2 border-secondary-6000" : "bg-amber-400"
          }`}
        >
          <Image
            onClick={() => {
              if (photoUrl) {
                const rawName =
                  selectedRoom?.reservation?.pet?.name ||
                  selectedRoom?.reservation?.hotelPet?.name ||
                  "pet";
                const fileName = slugifyName(rawName);
                onImageClick(photoUrl, fileName);
              }
            }}
            src={photoUrl || catAvatar}
            fill
            alt="catPhoto"
            className={`rounded-full object-fill ${
              photoUrl ? "cursor-pointer" : "p-2"
            }`}
          />
        </div>
        <div className="flex flex-col items-center justify-center">
          <div className="text-xl font-medium">
            {selectedRoom?.reservation?.pet?.name ||
              selectedRoom?.reservation?.hotelPet?.name}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 my-5">
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Yaş</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.age ||
                selectedRoom?.reservation?.hotelPet?.age}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Tür</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {translate(selectedRoom?.reservation?.pet?.kind) ||
                translate(selectedRoom?.reservation?.hotelPet?.kind)}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Cins</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.breed ||
                selectedRoom?.reservation?.hotelPet?.breed}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Renk</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.color ||
                selectedRoom?.reservation?.hotelPet?.color}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4 gap-0.5">
          <div>
            <div className="text-sm font-semibold">Cinsiyet</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {(selectedRoom?.reservation?.pet?.gender ||
                selectedRoom?.reservation?.hotelPet?.gender) === "male" ? (
                <MarsIcon className="w-4 h-4 text-blue-600" />
              ) : (
                <VenusIcon className="w-4 h-4 text-pink-500" />
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Çip Numarası</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm">
              {selectedRoom?.reservation?.pet?.microChipNumber ||
                selectedRoom?.reservation?.hotelPet?.microChipNumber}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">İç Parazit</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="whitespace-nowrap text-sm flex items-center gap-1">
              {(selectedRoom?.reservation?.pet?.internalParasiteTreatment ??
              selectedRoom?.reservation?.hotelPet
                ?.internalParasiteTreatment) ? (
                <>
                  <CheckIcon className="w-4 h-4 text-green-600" />(
                  <span>
                    {new Date(
                      selectedRoom?.reservation?.pet?.internalTreatmentDate ??
                        selectedRoom?.reservation?.hotelPet
                          ?.internalTreatmentDate
                    ).toLocaleDateString()}
                  </span>
                  )
                </>
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Dış Parazit</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="whitespace-nowrap text-sm flex items-center gap-1">
              {(selectedRoom?.reservation?.pet?.externalParasiteTreatment ??
              selectedRoom?.reservation?.hotelPet
                ?.externalParasiteTreatment) ? (
                <>
                  <CheckIcon className="w-4 h-4 text-green-600" />(
                  <span>
                    {new Date(
                      selectedRoom?.reservation?.pet?.externalTreatmentDate ??
                        selectedRoom?.reservation?.hotelPet
                          ?.externalTreatmentDate
                    ).toLocaleDateString()}
                  </span>
                  )
                </>
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Kısırlaştırılmış</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm flex items-center gap-1">
              {(selectedRoom?.reservation?.pet?.infertile ??
              selectedRoom?.reservation?.hotelPet?.infertile) ? (
                <CheckIcon className="w-4 h-4 text-green-600" />
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
            </div>
          </div>
        </div>
      </div>
      {documentPhotos?.length > 0 && (
        <div
          className="text-sm font-medium text-secondary-6000 hover:underline cursor-pointer"
          onClick={() => {
            const rawName =
              selectedRoom?.reservation?.pet?.name ||
              selectedRoom?.reservation?.hotelPet?.name ||
              "pet";
            const fileName = slugifyName(rawName);
            onImageClick(documentPhotos, fileName + "-asi-karnesi");
          }}
        >
          Aşı karnesini görüntüle
        </div>
      )}
      <Accordion type="multiple">
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-sm">Alerjiler</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.allergicTo?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.allergicTo?.length > 0 ? (
                <div className="space-y-2">
                  {(
                    selectedRoom.reservation?.pet?.allergicTo ||
                    selectedRoom.reservation?.hotelPet?.allergicTo
                  )?.map((allergy: string, index: number) => (
                    <div className="capitalize" key={index}>
                      {allergy}
                    </div>
                  ))}
                </div>
              ) : (
                <div>Alerji bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger className="text-sm">Hastalıklar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.hereditaryDiseases?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.hereditaryDiseases?.length >
                0 ? (
                <div className="space-y-2">
                  {(
                    selectedRoom.reservation?.pet?.hereditaryDiseases ||
                    selectedRoom.reservation?.hotelPet?.hereditaryDiseases
                  )?.map((disease: string, index: number) => (
                    <div className="capitalize" key={index}>
                      {disease}
                    </div>
                  ))}
                </div>
              ) : (
                <div>Hastalık bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger className="text-sm">Operasyonlar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.operationHistory?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.operationHistory?.length >
                0 ? (
                <div className="space-y-2">
                  {(
                    selectedRoom.reservation?.pet?.operationHistory ||
                    selectedRoom.reservation?.hotelPet?.operationHistory
                  )?.map((operation: string, index: number) => (
                    <div className="capitalize" key={index}>
                      {operation}
                    </div>
                  ))}
                </div>
              ) : (
                <div>Operasyon bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger className="text-sm">İlaçlar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.medicine?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.medicine?.length > 0 ? (
                <div className="capitalize">
                  {selectedRoom.reservation?.pet?.medicine ||
                    selectedRoom.reservation?.hotelPet?.medicine}
                </div>
              ) : (
                <div>İlaç bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-5">
          <AccordionTrigger className="text-sm">
            Beslenme Alışkanlıkları
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.feedingHabits?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.feedingHabits?.length > 0 ? (
                <div>
                  {selectedRoom.reservation?.pet?.feedingHabits ||
                    selectedRoom.reservation?.hotelPet?.feedingHabits}
                </div>
              ) : (
                <div>Beslenme Alışkanlığı bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-6">
          <AccordionTrigger className="text-sm">Alışkanlıklar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom?.reservation?.pet?.specified?.length > 0 ||
              selectedRoom?.reservation?.hotelPet?.specified?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom?.reservation?.pet?.specified ||
                    selectedRoom?.reservation?.hotelPet?.specified}
                </div>
              ) : (
                <div>Alışkanlık bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-7">
          <AccordionTrigger className="text-sm">Açıklama</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.description?.length > 0 ||
              selectedRoom.reservation?.hotelPet?.description?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom.reservation?.pet?.description ||
                    selectedRoom.reservation?.hotelPet?.description}
                </div>
              ) : (
                <div>Açıklama bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default PetInformation;
