"use client";
import React, { useState, useEffect } from "react";
import HotelEmailValidation from "./HotelEmailValidation";
import HotelPhoneValidation from "./HotelPhoneValidation";
import HotelFirstStep from "./HotelFirstStep";
import HotelSecondStep from "./HotelSecondStep";
// import HotelThirdStep from "./HotelThirdStep";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { setStepState } from "@/store/features/hotelAuth/hotel-auth-slice";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelSignupActions } from "@/hooks/createHotelSteps/useHotelSignupActions";
import { useTranslations } from "next-intl";
import {
  onlyLetterRegex,
  usernameRegex,
  passwordRegex,
} from "../create-hotel-regex";

interface StateType {
  thirdStep: boolean;
  fourthStep: boolean;
}

const CreateHotelContainer = () => {
  const translate = useTranslations("CreateHotelContainer");
  const { handleHotelInfo } = useHotelSignupActions();
  const dispatch = useDispatch();
  const owner = useSelector((state: RootState) => state.hotelAuth.owner);
  const hotel = useSelector((state: RootState) => state.hotelAuth.hotel);
  const hotelFeatures = useSelector(
    (state: RootState) => state.hotelAuth.hotelFeatures
  );
  const stepState = useSelector(
    (state: RootState) => state.hotelAuth.stepState
  );
  const [buttonStates, setButtonStates] = useState<StateType>({
    thirdStep: false,
    fourthStep: false,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [agreementCheck, setAgreementCheck] = useState<boolean>(false);
  const [rePassword, setRepassword] = useState<string | number | null>(null);

  const firstNameCheck =
    owner.firstName && onlyLetterRegex.test(owner.firstName);
  const lastNameCheck = owner.lastName && onlyLetterRegex.test(owner.lastName);
  const userNameCheck = owner.userName && usernameRegex.test(owner.userName);
  const passwordCheck = owner.password && passwordRegex.test(owner.password);
  const rePasswordCheck = owner.password && owner.password === rePassword;
  const allOwnerInputsTrue =
    firstNameCheck &&
    lastNameCheck &&
    userNameCheck &&
    passwordCheck &&
    rePasswordCheck;

  // checks if required fields are true
  const isOwnerAllTrue = () => {
    const ownerInputsNotEmpty = Object.values(owner).every(
      (value) => value !== null && value.trim() !== ""
    );

    return ownerInputsNotEmpty;
  };

  // checks if required fields are true
  const isHotelAllTrue = () => {
    const hotelInputsNotEmpty = Object.entries(hotel).every(([key, value]) => {
      if (key === "hotelPhoneNumber") return true;
      return value !== null && value.trim() !== "";
    });
    return hotelInputsNotEmpty;
  };

  // checks if required fields are true
  useEffect(() => {
    const OwnerInputCheck = isOwnerAllTrue();
    const hotelInputCheck = isHotelAllTrue();

    setButtonStates({
      thirdStep: OwnerInputCheck && Boolean(allOwnerInputsTrue),
      fourthStep: hotelInputCheck && agreementCheck,
    });
  }, [owner, hotel, hotelFeatures, agreementCheck, allOwnerInputsTrue]);

  const nextBtnText = stepState === 4 ? translate("send") : translate("next");

  return (
    <div>
      <div className="mb-7">
        <span className="text-4xl font-semibold">{stepState}</span>{" "}
        <span className="text-lg text-neutral-500 dark:text-neutral-400">
          / 4
        </span>
      </div>
      <div className="listingSection__wrap">
        {stepState === 1 && <HotelEmailValidation />}
        {stepState === 2 && <HotelPhoneValidation />}
        {stepState === 3 && (
          <HotelFirstStep
            rePassword={rePassword}
            setRepassword={setRepassword}
          />
        )}
        {stepState === 4 && (
          <HotelSecondStep
            setAgreementCheck={setAgreementCheck}
            agreementCheck={agreementCheck}
          />
        )}
        {/* {stepState === 5 && <HotelThirdStep />} */}
      </div>
      {stepState >= 3 && stepState <= 5 && (
        <div className="mt-10 flex justify-end space-x-5">
          {stepState > 3 && (
            <Button
              variant="outline"
              onClick={() => {
                dispatch(setStepState(stepState - 1));
                window.scrollTo(0, 0);
              }}
              disabled={stepState === 1 && true}
              type="button"
            >
              {translate("previous")}
            </Button>
          )}

          {stepState === 4 ? (
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={
                stepState === 4 && !buttonStates.fourthStep ? true : false
              }
              onClick={() => {
                handleHotelInfo(setLoading);
              }}
              type="button"
            >
              {loading ? <LoadingSpinner /> : nextBtnText}
            </Button>
          ) : (
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={() => {
                dispatch(setStepState(stepState + 1));
                window.scrollTo(0, 0);
              }}
              disabled={
                stepState === 3 && !buttonStates.thirdStep
                  ? true
                  : stepState === 4 && !buttonStates.fourthStep
                    ? true
                    : false
              }
              type="button"
            >
              {nextBtnText}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default CreateHotelContainer;
