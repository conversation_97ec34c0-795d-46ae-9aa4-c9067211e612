import React from "react";
import type { FC } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import DeleteReviewModal from "./DeleteReviewModal";
import UpdateReviewModal from "./UpdateReviewModal";
import type { CustomerReviewsSectionType } from "@/types/hotel/hotelLandingType";

interface CustomerReviewsProps {
  reviews: any;
  customerReviewsSectionData: any;
  hotelToken: string | undefined;
}

const CustomerReviews: FC<CustomerReviewsProps> = ({
  reviews,
  customerReviewsSectionData,
  hotelToken,
}) => {
  const { customerName, review, city } = reviews;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg capitalize">{customerName}</CardTitle>
        <CardDescription className="flex items-center gap-0-5">
          {city}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p>{review}</p>
        <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-3">
          <UpdateReviewModal
            reviews={reviews}
            customerReviewsSectionData={customerReviewsSectionData}
            hotelToken={hotelToken}
          />
          <DeleteReviewModal
            reviews={reviews}
            customerReviewsSectionData={customerReviewsSectionData}
            hotelToken={hotelToken}
          />
        </div>
      </CardContent>
      {/* <CardFooter>
        <p>Card Footer</p>
      </CardFooter> */}
    </Card>
  );
};

export default CustomerReviews;
