export interface OwnerTypes {
  firstName: string | null;
  lastName: string | null;
  userName: string | null;
  email: string | null;
  password: string | null;
  phone: string | null;
  gender: string | null;
  dateOfBirth: string | null;
}

export interface HotelTypes {
  hotelName: string | null;
  propertyType: string | null;
  // taxOffice: string | null;
  // taxNumber: string | null;
  streetName: string | null;
  district: string | null;
  cityName: string | null;
  citySubdivisionName: string | null;
  hotelPhoneNumber: string | null;
  postbox: string | null;
  buildingName: string | null;
  buildingNumber: string | null;
  hotelDescription: string | null;
  // legalName: string | null;
}

export interface FeatureAndPetTypes {
  hotelFeaturesArray: string[];
  acceptedPetTypes: string[];
  hotelCareServices: string[];
}
export type StepState = number;
export type UserPassword = string;
export type FirstStep = boolean;
export type SecondStep = boolean;
export type ThirdStep = boolean;
export type RegisterId = string | null;
export type MailAuthStep = boolean;
export type PhoneAuthStep = boolean;

export interface HotelStates {
  owner: OwnerTypes;
  hotel: HotelTypes;
  hotelFeatures: FeatureAndPetTypes;
  stepState: StepState;
  firstStep: FirstStep;
  secondStep: SecondStep;
  thirdStep: ThirdStep;
  registerId: RegisterId;
  mailAuthStep: MailAuthStep;
  phoneAuthStep: PhoneAuthStep;
}
