"use client";
import Input from "@/shared/Input";
import Label from "@/components/Label";

interface HotelCustomerByPhoneStepProps {
  phoneNumber: string;
  setPhoneNumber: (value: string) => void;
}

const HotelCustomerByPhoneStep: React.FC<HotelCustomerByPhoneStepProps> = ({
  phoneNumber,
  setPhoneNumber,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPhoneNumber(event.target.value);
  };

  return (
    <div>
      <div className="text-xl font-bold">Müşteri Sorgula</div>
      <div className="space-y-2 min-w-[300px] p-4">
        <Label>Telefon Numarası</Label>
        <div className="flex rounded-lg shadow-sm shadow-black/5 w-64">
          <span className="inline-flex items-center rounded-s-lg border border-input bg-background px-3 text-sm text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              version="1.0"
              width="12.000000pt"
              height="12.000000pt"
              viewBox="0 0 512.000000 512.000000"
              preserveAspectRatio="xMidYMid meet"
            >
              <g
                transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
                fill="#E9141B"
                stroke="none"
              >
                <path d="M2332 5109 c-201 -18 -463 -76 -648 -144 -151 -55 -406 -184 -534 -270 -164 -110 -276 -204 -425 -356 -204 -210 -340 -404 -465 -663 -152 -314 -227 -597 -252 -954 -24 -335 30 -719 147 -1038 55 -151 184 -406 270 -534 203 -304 476 -570 781 -761 116 -73 342 -184 478 -234 317 -117 703 -171 1038 -147 357 25 640 100 954 252 259 125 453 261 663 465 229 223 378 427 512 700 160 328 235 606 261 972 13 180 2 409 -28 583 -70 410 -235 793 -486 1128 -89 119 -302 339 -423 437 -203 165 -493 330 -739 420 -337 124 -741 176 -1104 144z m98 -1293 c305 -65 583 -237 761 -471 69 -91 69 -95 -1 -32 -208 188 -456 277 -735 264 -148 -7 -260 -34 -381 -91 -124 -59 -204 -115 -295 -205 -399 -399 -399 -1043 0 -1442 386 -385 1004 -399 1411 -32 69 63 70 59 3 -29 -179 -235 -457 -408 -763 -474 -114 -25 -391 -25 -499 0 -266 60 -467 169 -652 355 -154 153 -247 303 -314 504 -124 372 -65 783 158 1117 64 96 241 273 337 337 173 116 373 191 580 217 70 9 317 -3 390 -18z m1344 -836 c72 -99 135 -180 141 -180 6 0 102 30 214 66 112 36 205 63 208 61 2 -3 -55 -87 -127 -186 l-132 -181 132 -181 c73 -99 130 -183 127 -185 -3 -3 -95 24 -205 60 -110 36 -206 66 -214 66 -8 0 -73 -81 -144 -180 -71 -99 -132 -180 -136 -180 -5 0 -8 103 -8 228 l0 228 -203 67 c-112 37 -209 67 -215 67 -42 0 15 24 203 84 l215 70 0 228 c0 125 3 228 8 228 4 0 65 -81 136 -180z" />
              </g>
            </svg>{" "}
            <span className="ml-1">+90</span>
          </span>
          <Input
            name="phoneNumber"
            type="text"
            placeholder="Telefon Numarası"
            onChange={handleChange}
            value={phoneNumber}
            className="flex-1 block w-full min-w-0 rounded-none rounded-r-lg transition duration-150 ease-in-out sm:text-sm sm:leading-5"
          />
        </div>
      </div>
    </div>
  );
};

export default HotelCustomerByPhoneStep;
