"use client";
import type { FC, KeyboardEvent, ChangeEvent } from "react";
import React, { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import IconPlus from "@/shared/icons/Plus";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useCreateRoom } from "@/hooks/hotel/rooms/useCreateRoom";
import { useTranslations } from "next-intl";
import CreateNewRoomPhotos from "./CreateRoomPhotos";
import CreateRoomInputs from "./CreateRoomInputs";
import type {
  CreateNewRoomModalProps,
  roomInputsTypes,
} from "@/types/hotel/rooms/createRoomTypes";
import { PET_TYPES } from "@/app/(enums)/enums";

const CreateRoomModal: FC<CreateNewRoomModalProps> = ({
  hotel,
  hotelToken,
}) => {
  const translate = useTranslations("UpdateAndCreateNewRoomModal");
  const { photoInputHandler } = useCreateRoom();
  const newRoomInputRef = useRef<HTMLInputElement>(null);
  const [roomInputs, setRoomInputs] = useState<roomInputsTypes>({
    roomName: "",
    roomCapacity: "1",
    petType: [],
    roomCount: 1,
    roomGroupName: "",
    roomNameStartingNumber: 1,
    roomDescription: "",
    roomFeatures: [],
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [createRoomsIsOpen, setCreateRoomsIsOpen] = useState(false);
  const [roomFeatureNames, setRoomFeatureNames] = useState<string>("");
  const [photoFileObject, setPhotoFileObject] = useState<
    FileList | undefined
  >();
  const [modalImage, setModalImage] = useState<string[] | []>([]);

  const resetModalStates = () => {
    setRoomInputs({
      roomName: "",
      roomCapacity: "1",
      petType: [],
      roomCount: 1,
      roomGroupName: "",
      roomNameStartingNumber: 1,
      roomDescription: "",
      roomFeatures: [],
    });
    setPhotoFileObject(undefined);
  };

  // room inputs handle
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setRoomInputs((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  // room feature handles
  const roomFeaturesNameHandler = (event: ChangeEvent<HTMLInputElement>) => {
    setRoomFeatureNames(event.target.value);
  };

  const hotelFeaturesRemoveHandler = (index: number) => {
    setRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: prevState.roomFeatures.filter(
        (_, itemIndex) => itemIndex !== index
      ),
    }));
  };

  const roomFeaturesHandler = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key !== "Enter") {
      return;
    }
    if (roomFeatureNames.trim() === "") {
      setRoomFeatureNames("");
      return;
    }
    setRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: [...prevState.roomFeatures, roomFeatureNames.trim()],
    }));
    setRoomFeatureNames("");
  };

  const handleRoomFeatures = () => {
    if (roomFeatureNames.trim() === "") {
      setRoomFeatureNames("");
      return;
    }
    setRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: [...prevState.roomFeatures, roomFeatureNames.trim()],
    }));
    setRoomFeatureNames("");
  };

  // textarea handle
  const textAreaHandleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setRoomInputs((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handlePetTypeCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { id, checked } = e.target;
    const petTypeId = id as keyof typeof PET_TYPES;

    setRoomInputs((prevState) => {
      if (checked) {
        return {
          ...prevState,
          petType: [...prevState.petType, petTypeId],
        };
      } else {
        return {
          ...prevState,
          petType: prevState.petType.filter((petType) => petType !== petTypeId),
        };
      }
    });
  };

  const handleCancel = () => {
    setCreateRoomsIsOpen(false);
    setLoading(false);
    resetForm();
  };

  const resetForm = () => {
    if (newRoomInputRef.current) {
      newRoomInputRef.current.value = "";
    }
    setModalImage([]);
    setPhotoFileObject(undefined);
    resetModalStates();
  };

  const isButtonDisabled = !(
    roomInputs.roomCapacity !== "0" &&
    roomInputs.petType.length > 0 &&
    roomInputs.roomCount > 0 &&
    roomInputs.roomGroupName.trim() &&
    roomInputs.roomDescription.trim() &&
    roomInputs.roomFeatures.length > 0
  );
  // photoFileObject &&
  // photoFileObject.length > 0;

  return (
    <>
      <Button
        type="button"
        onClick={() => setCreateRoomsIsOpen(true)}
        className="bg-secondary-6000 hover:bg-secondary-700 text-white"
      >
        <IconPlus className="!size-6 text-white" />
        {translate("hotelAddNewRoomGroup")}
      </Button>
      <Dialog open={createRoomsIsOpen}>
        <DialogContent
          onInteractOutside={handleCancel}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>{translate("hotelAddNewRoomGroup")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable">
            <CreateRoomInputs
              roomInputs={roomInputs}
              handleChange={handleChange}
              hotelFeaturesRemoveHandler={hotelFeaturesRemoveHandler}
              roomFeaturesHandler={roomFeaturesHandler}
              acceptedPetTypes={hotel.acceptedPetTypes}
              handlePetTypeCheckboxChange={handlePetTypeCheckboxChange}
              roomFeaturesNameHandler={roomFeaturesNameHandler}
              roomFeatureNames={roomFeatureNames}
              handleRoomFeatures={handleRoomFeatures}
              textAreaHandleChange={textAreaHandleChange}
            />
            <span className="text-lg font-semibold">
              {translate("roomPhotos")}
            </span>
            <CreateNewRoomPhotos
              loading={loading}
              newRoomInputRef={newRoomInputRef}
              setPhotoFileObject={setPhotoFileObject}
              setModalImage={setModalImage}
              modalImage={modalImage}
            />
          </div>
          <div className="mt-7 flex justify-end gap-5">
            <Button variant="outline" type="button" onClick={handleCancel}>
              {translate("cancel")}
            </Button>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={disabled || isButtonDisabled}
              onClick={() =>
                photoInputHandler(
                  hotel._id,
                  hotelToken,
                  roomInputs,
                  photoFileObject,
                  setModalImage,
                  setLoading,
                  setCreateRoomsIsOpen,
                  resetForm,
                  setDisabled
                )
              }
              type="button"
            >
              {loading ? <LoadingSpinner /> : translate("save")}
            </Button>
          </div>
          <DialogClose
            onClick={() => setCreateRoomsIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CreateRoomModal;
