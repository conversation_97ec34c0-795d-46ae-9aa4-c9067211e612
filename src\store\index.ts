import { configureStore } from "@reduxjs/toolkit";
import partnerAuthReducer from "./features/partnerAuth/partner-auth-slice";
import hotelCalendarReducer from "./features/hotelCalendar/calendar-slice";
import calculatedRoomDataReducer from "./features/calculatedRoomData/calculated-room-data-slice";
import checkoutDataReducer from "./features/checkout/checkout-data-slice";
import liveChatReducer from "./features/liveChat/live-chat-slice";

const store = configureStore({
  reducer: {
    partnerAuth: partnerAuthReducer,
    hotelCalendar: hotelCalendarReducer,
    calculatedRoomData: calculatedRoomDataReducer,
    checkoutData: checkoutDataReducer,
    liveChatText: liveChatReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
