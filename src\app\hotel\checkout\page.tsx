import React from "react";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import CheckoutSection from "./(components)/CheckoutSection";
import { redirect } from "next/navigation";

const CheckoutPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  if (membershipData?.data?.membershipType === "plus") {
    redirect("/hotel/account");
  }

  return (
    <div className="container">
      <CheckoutSection
        hotelToken={hotelToken}
        membershipData={membershipData?.data}
      />
    </div>
  );
};

export default CheckoutPage;
