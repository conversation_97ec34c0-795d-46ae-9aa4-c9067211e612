"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelSubscription } from "@/hooks/hotel/useHotelSubscription";
import type { RoomGroupListType } from "@/types/hotel/roomGroupType";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";

interface AddSubscriptionProps {
  hotelToken: string | undefined;
  roomGroupData: RoomGroupListType;
  closeModal: () => void;
}

const AddSubscription: FC<AddSubscriptionProps> = ({
  hotelToken,
  roomGroupData,
  closeModal,
}) => {
  const { addSubscription } = useHotelSubscription();
  const [subscription, setSubscription] = useState({
    isActive: true,
    subscriptionName: "",
    subscriptionDuration: null,
    nightsEarned: null,
    total: null,
    roomGroups: [],
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = event.target;

    if (name === "isActive") {
      setSubscription((prevSubscription) => ({
        ...prevSubscription,
        isActive: checked,
      }));
    } else {
      setSubscription((prevSubscription) => ({
        ...prevSubscription,
        [name]: type === "number" ? Number(value) : value,
      }));
    }
  };

  const handleSelectChange = (name: string, value: string | string[]) => {
    setSubscription((prevSubscription) => ({
      ...prevSubscription,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const roomGroupList = roomGroupData.map((roomGroup) => ({
    value: roomGroup._id,
    label: roomGroup.roomGroupName,
  }));

  const isSubscriptionValid = (subscription: any) => {
    const checkValues = (obj: any) =>
      Object.entries(obj).every(([key, value]) => {
        if (typeof value === "string") {
          return value.trim() !== "";
        }
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return value !== null && value !== undefined;
      });

    return checkValues(subscription);
  };

  const buttonDisabled = isSubscriptionValid(subscription);

  return (
    <form
      onSubmit={(event) =>
        addSubscription(
          event,
          hotelToken,
          subscription,
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <div className="space-y-2 w-72">
        <FormItem label="Üyelik Kartı Adı">
          <Input name="subscriptionName" onChange={handleChange} required />
        </FormItem>
        <FormItem label="Üyelik Kartı Süresi">
          <Select
            onValueChange={(value) =>
              handleSelectChange("subscriptionDuration", value)
            }
          >
            <SelectTrigger className="w-72 rounded-2xl">
              <SelectValue placeholder="Süre Seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="90">3 Ay</SelectItem>
              <SelectItem value="180">6 Ay</SelectItem>
              <SelectItem value="270">9 Ay</SelectItem>
              <SelectItem value="360">12 Ay</SelectItem>
            </SelectContent>
          </Select>
        </FormItem>
        <FormItem label="Gece Sayısı">
          <Input
            name="nightsEarned"
            type="number"
            onChange={handleChange}
            required
          />
        </FormItem>
        <FormItem label="Üyelik Kartı Ücreti">
          <Input name="total" type="number" onChange={handleChange} required />
        </FormItem>
        <FormItem label="Oda Grubu">
          <MultiSelect
            options={roomGroupList}
            onValueChange={(value) => handleSelectChange("roomGroups", value)}
            maxCount={15}
            // defaultValue={campaign.campaignDetails.roomGroups}
            placeholder="Oda Grubu Seçin"
            className="rounded-2xl"
          />
        </FormItem>
        <FormItem label="Aktiflik Durumu">
          <div className="flex gap-2">
            <Switch
              name="isActive"
              checked={subscription.isActive}
              onCheckedChange={(checked) =>
                setSubscription((prev) => ({ ...prev, isActive: checked }))
              }
              className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
            />
            <p
              className={
                subscription.isActive ? "text-green-500" : "text-red-500"
              }
            >
              {subscription.isActive ? "Aktif" : "Pasif"}
            </p>
          </div>
        </FormItem>
      </div>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default AddSubscription;
