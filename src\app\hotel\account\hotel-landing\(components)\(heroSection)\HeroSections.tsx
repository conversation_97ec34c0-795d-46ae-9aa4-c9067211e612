"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import LoadingSpinner from "@/shared/icons/Spinner";
import FormItem from "@/shared/FormItem";
import Image from "next/image";
import type { HeroSectionType } from "@/types/hotel/hotelLandingType";
import HeroSectionPhotos from "./HeroSectionPhotos";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

export interface HeroSectionProps {
  hotelToken: string | undefined;
  heroSectionData: HeroSectionType;
}

const HeroSections: FC<HeroSectionProps> = ({
  hotelToken,
  heroSectionData,
}) => {
  const initialData = {
    title: heroSectionData?.title || "",
    description: heroSectionData?.description || "",
    images: heroSectionData?.images || [],
  };
  const { heroSectionPhotoInputHandler } = useHotelLandingActions();
  const [loading, setLoading] = useState<boolean>(false);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | null>(null);
  const [image, setImage] = useState<string[] | []>([]);
  const [sectionData, setSectionData] = useState<HeroSectionType>(initialData);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setSectionData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <>
      <div className="listingSection__wrap space-y-6 sm:space-y-8">
        <h2 className="text-2xl font-semibold">Otel Hero Alanı</h2>
        <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>

        <div>
          <div className="mb-10 mt-5 space-y-6">
            <FormItem label="Otel Adı">
              <Input
                name="title"
                type="text"
                className="mt-1.5"
                value={sectionData.title || ""}
                onChange={handleChange}
              />
            </FormItem>
            <FormItem label="Otel Açıklama">
              <Textarea
                name="description"
                className="mt-1.5"
                value={sectionData.description || ""}
                onChange={handleChange}
              />
            </FormItem>
            <div className="mt-5">
              <span className="text-lg font-semibold">Otel fotoğrafları</span>
              <div className="mt-5">
                <div className="mb-5">
                  <HeroSectionPhotos
                    heroSectionData={heroSectionData}
                    hotelToken={hotelToken}
                  />
                </div>
                <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto size-12 text-neutral-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                    </svg>
                    <div className="text-sm text-neutral-6000 dark:text-neutral-300">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
                      >
                        <span>Fotoğraf yükle</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          disabled={loading}
                          accept="image/*"
                          multiple={true}
                          onChange={(e) => {
                            const fileObject = e.target.files;
                            setPhotoFileObject(fileObject);
                            if (fileObject) {
                              const imageUrls = Array.from(fileObject).map(
                                (file) => URL.createObjectURL(file)
                              );
                              setImage(imageUrls);
                            }
                          }}
                        />
                      </label>
                      {/* <p className="pl-1">or drag and drop</p> */}
                    </div>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-2 flex justify-center gap-3">
                {image &&
                  image.map((image: any, index: number) => {
                    return (
                      <div key={index}>
                        <Image src={image} width={150} height={150} alt="" />
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end pt-2">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700"
            onClick={() => {
              heroSectionPhotoInputHandler(
                sectionData,
                photoFileObject,
                hotelToken,
                setLoading,
                setPhotoFileObject,
                setImage
              );
            }}
          >
            {loading ? <LoadingSpinner /> : "Kaydet"}
          </Button>
        </div>
      </div>
    </>
  );
};

export default HeroSections;
