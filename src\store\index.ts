import { configureStore } from "@reduxjs/toolkit";
import hotelAuthReducer from "./features/hotelAuth/hotel-auth-slice";
import hotelCalendarReducer from "./features/hotelCalendar/calendar-slice";
import calculatedRoomDataReducer from "./features/calculatedRoomData/calculated-room-data-slice";

const store = configureStore({
  reducer: {
    hotelAuth: hotelAuthReducer,
    hotelCalendar: hotelCalendarReducer,
    calculatedRoomData: calculatedRoomDataReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
