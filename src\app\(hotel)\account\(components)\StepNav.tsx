import React from "react";
import type { FC } from "react";
import Icon<PERSON>aperClip from "@/shared/icons/PaperClip";
import IconCreditCard from "@/shared/icons/CreditCard";
import IconNewspaper from "@/shared/icons/Newspaper";
import IconPawGuest from "@/shared/icons/PawGuest";
import IconFeatures from "@/shared/icons/Features";
import IconMap from "@/shared/icons/Map";
import IconCheckCircle from "@/shared/icons/CheckCircle";
import IconImage from "@/shared/icons/Image";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface StepNavprops {
  hotelData: HotelDataApiTypes;
  checkSubMerchant: boolean;
}

interface HotelInfoItem {
  id: number;
  icon: JSX.Element;
  label: string;
  style: string;
}

const StepNav: FC<StepNavprops> = ({ hotelData, checkSubMerchant }) => {
  // Renders a specific icon based on the provided condition
  const iconHandler = (value: boolean, IconComponent: React.ElementType) => {
    if (value) {
      return <IconCheckCircle className="size-5 text-[#3f894e]" />;
    } else {
      return <IconComponent className="size-5 text-gray-800 dark:text-white" />;
    }
  };

  // Renders a specific color based on the provided condition
  const lineHandler = (firstCondition: boolean, secondCondition: boolean) => {
    const checkedCircle = "bg-[#bff2d4]";

    return firstCondition
      ? checkedCircle
      : secondCondition
        ? "bg-[#d5e4f2] dark:bg-[#7ba9cf]"
        : "bg-gray-200 dark:bg-gray-700";
  };

  // Step Icons
  const fileIconComponent = iconHandler(
    hotelData?.files?.length >= 5,
    IconPaperClip
  );
  const billingIconComponent = iconHandler(checkSubMerchant, IconCreditCard);
  const petTypeIconComponent = iconHandler(
    hotelData?.acceptedPetTypes?.length > 0,
    IconPawGuest
  );
  const hotelFeaturesIconComponent = iconHandler(
    hotelData?.hotelFeatures?.length > 0,
    IconFeatures
  );
  const hotelPhotosIconComponent = iconHandler(
    hotelData?.images?.length > 0,
    IconImage
  );
  const hotelMapIconComponent = iconHandler(!!hotelData?.googleMapUrl, IconMap);
  const policyIconComponent = iconHandler(
    !!hotelData?.policy?.checkOutTime,
    IconNewspaper
  );

  // Step circle and line styles
  const filelineStyle = lineHandler(hotelData?.files?.length >= 5, true);
  const billingLineStyle = lineHandler(
    checkSubMerchant,
    hotelData?.files?.length >= 5
  );
  const petTypeLineStyle = lineHandler(
    hotelData?.acceptedPetTypes?.length > 0,
    checkSubMerchant
  );
  const hotelFeaturesLineStyle = lineHandler(
    hotelData?.hotelFeatures?.length > 0,
    hotelData?.acceptedPetTypes?.length > 0
  );
  const hotelPhotosLineStyle = lineHandler(
    hotelData?.images?.length > 0,
    hotelData?.hotelFeatures?.length > 0
  );
  const hotelMapLineStyle = lineHandler(
    !!hotelData?.googleMapUrl,
    hotelData?.images?.length > 0
  );
  const hotelPolicyLineStype = lineHandler(
    !!hotelData?.policy?.checkOutTime,
    !!hotelData?.googleMapUrl
  );

  const hotelInfoItems: HotelInfoItem[] = [
    {
      id: 1,
      icon: fileIconComponent,
      label: "Belgeler",
      style: filelineStyle,
    },
    {
      id: 2,
      icon: billingIconComponent,
      label: "Ödeme",
      style: billingLineStyle,
    },
    {
      id: 3,
      icon: petTypeIconComponent,
      label: "Pet Türü",
      style: petTypeLineStyle,
    },
    {
      id: 4,
      icon: hotelFeaturesIconComponent,
      label: "Özellikler",
      style: hotelFeaturesLineStyle,
    },
    {
      id: 5,
      icon: hotelPhotosIconComponent,
      label: "Fotoğraflar",
      style: hotelPhotosLineStyle,
    },
    // {
    //   id: 6,
    //   icon: hotelMapIconComponent,
    //   label: "Harita",
    //   style: hotelMapLineStyle,
    // },
    {
      id: 7,
      icon: policyIconComponent,
      label: "İptal Politikası",
      style: hotelPolicyLineStype,
    },
  ];

  return (
    <>
      <div className="max-lg:hidden grid grid-cols-6 mb-5 gap-5">
        <div className={`w-full h-1 ${filelineStyle}`}></div>
        <div className={`w-full h-1 ${billingLineStyle}`}></div>
        <div className={`w-full h-1 ${petTypeLineStyle}`}></div>
        <div className={`w-full h-1 ${hotelFeaturesLineStyle}`}></div>
        <div className={`w-full h-1 ${hotelPhotosLineStyle}`}></div>
        {/* <div className={`w-full h-1 ${hotelMapLineStyle}`}></div> */}
        <div className={`w-full h-1 ${hotelPolicyLineStype}`}></div>
      </div>
      <ol className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-2 items-center w-full">
        {hotelInfoItems.map((item) => (
          <li key={item.id} className="flex w-full gap-2 items-center">
            <span
              className={`flex items-center justify-center w-8 h-8 rounded-full lg:h-10 lg:w-10 shrink-0 ${item.style}`}
            >
              {item.icon}
            </span>
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              {item.label}
            </p>
          </li>
        ))}
      </ol>
    </>
  );
};

export default StepNav;
