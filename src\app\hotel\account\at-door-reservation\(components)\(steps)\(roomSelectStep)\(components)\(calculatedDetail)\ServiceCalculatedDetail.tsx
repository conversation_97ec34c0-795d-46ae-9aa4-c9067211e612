import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import DeleteCalculatedServiceDetail from "../(deleteCalculated)/DeleteCalculatedServiceDetail";
import convertOneDateToString from "@/utils/convertOneDateToString";
import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";

const ServiceCalculatedDetail = ({
  hotelToken,
}: {
  hotelToken: string | undefined;
}) => {
  const calendarLanguage = datePickerLanguageHandler("tr");
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const services = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "service"
  );

  return (
    <>
      {services?.map((service: any) => {
        return (
          <div key={service.id} className="space-y-4">
            <div className="flex justify-between items-end space-y-4">
              {service?.itemData?.serviceType === "veterinaryServices" && (
                <div className="space-y-4">
                  <p className="font-semibold capitalize">
                    {service?.itemData?.serviceName} (Veteriner Hizmeti)
                  </p>
                  <div className="flex items-center gap-1">
                    <p className="text-sm font-semibold">Tarih:</p>
                    <p className="font-medium">
                      {convertOneDateToString(
                        service?.itemData?.serviceDate,
                        true,
                        calendarLanguage.firstValue
                      )}
                    </p>
                  </div>
                  <div>
                    {/* <div className="flex gap-2">
                      <span className="font-semibold">Veteriner Adı:</span>
                      <span>
                        {service?.itemData?.serviceDetails?.veterinarianName}
                      </span>
                    </div> */}
                    {/* <div className="flex gap-2">
                      <span className="font-semibold">Uygun Saat Aralığı:</span>
                      <span>
                        {service?.itemData?.serviceDetails?.availabilityStart} -{" "}
                        {service?.itemData?.serviceDetails?.availabilityEnd}
                      </span>
                    </div> */}
                  </div>
                  <div className="flex justify-start font-semibold">
                    <span>
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(service?.itemData?.total)) + "₺"}
                    </span>
                  </div>
                </div>
              )}

              {service?.itemData?.serviceType === "transportationServices" && (
                <div className="space-y-4 max-w-xs">
                  <div className="font-semibold capitalize">
                    {service?.itemData?.serviceName} (Pet Ulaşım Hizmeti)
                  </div>
                  <div className="mt-8 flex">
                    <div className="flex-shrink-0 flex flex-col items-center py-2">
                      <span className="block w-6 h-6 rounded-full border border-neutral-400"></span>
                      <span className="block flex-grow border-l border-neutral-400 border-dashed my-1"></span>
                      <span className="block w-6 h-6 rounded-full border border-neutral-400"></span>
                    </div>
                    <div className="ml-4 space-y-14 text-sm">
                      <div className="flex flex-col space-y-2">
                        <span className=" text-neutral-500 dark:text-neutral-400">
                          {/* Monday, August 12 · 10:00 */}
                        </span>
                        <span className="font-semibold">
                          {service?.itemData?.serviceDetails?.startPoint}
                        </span>
                      </div>
                      <div className="flex flex-col space-y-2">
                        <span className=" text-neutral-500 dark:text-neutral-400">
                          {/* Monday, August 16 · 10:00 */}
                        </span>
                        <span className="font-semibold">
                          {service?.itemData?.serviceDetails?.endPoint}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <span className="font-semibold">Mesafe:</span>
                    <span>
                      {service?.itemData?.serviceDetails?.distance}
                    </span>{" "}
                    km
                  </div>
                  <div className="flex items-center gap-1">
                    <p className="text-sm font-semibold">Tarih:</p>
                    <p className="font-medium">
                      {convertOneDateToString(
                        service?.itemData?.serviceDate,
                        true,
                        calendarLanguage.firstValue
                      )}
                    </p>
                  </div>
                  <div className="flex justify-start font-semibold">
                    <span>
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(service?.itemData?.total)) + "₺"}
                    </span>
                  </div>
                </div>
              )}

              {service?.itemData?.serviceType === "groomingServices" && (
                <div className="space-y-4">
                  <div className="font-semibold capitalize">
                    {service?.itemData?.serviceName} (Kuaför Hizmeti)
                  </div>
                  <div className="flex gap-2">
                    <span className="font-semibold">Hizmet Süresi:</span>
                    <span>
                      {service?.itemData?.serviceDetails?.duration} dakika
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <p className="text-sm font-semibold">Tarih:</p>
                    <p className="font-medium">
                      {convertOneDateToString(
                        service?.itemData?.serviceDate,
                        true,
                        calendarLanguage.firstValue
                      )}
                    </p>
                  </div>
                  <div className="flex justify-start font-semibold">
                    <span>
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(service?.itemData?.total)) + "₺"}
                    </span>
                  </div>
                </div>
              )}
              <DeleteCalculatedServiceDetail
                hotelToken={hotelToken}
                removedItemId={service?.id}
                serviceId={service?.itemData?.serviceHotelId}
              />
            </div>
            <div className="border-b border-neutral-200 dark:border-neutral-700" />
          </div>
        );
      })}
    </>
  );
};

export default ServiceCalculatedDetail;
