import Label from "@/components/Label";
import React from "react";
import type { FC } from "react";

export interface FormItemProps {
  className?: string;
  label?: string;
  desc?: string;
  children?: React.ReactNode;
  htmlFor?: string;
}

const FormItem: FC<FormItemProps> = ({
  children,
  className,
  label,
  desc,
  htmlFor,
}) => {
  return (
    <div className={className}>
      {label && (
        <Label htmlFor={htmlFor} className={className}>
          {label}
        </Label>
      )}
      <div className="mt-1">{children}</div>
      {desc && (
        <span className="mt-3 block text-xs text-neutral-500 dark:text-neutral-400 ">
          {desc}
        </span>
      )}
    </div>
  );
};

export default FormItem;
