import React from "react";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import SinglePetTaxiFile from "../(components)/(petTaxiFiles)/SinglePetTaxiFile";
import MultiplePetTaxiFiles from "../(components)/(petTaxiFiles)/MultiplePetTaxiFiles";

const PetTaxiFilesPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();

  return (
    <>
      <div className="mt-4 md:my-8">
        <h2 className="text-xl md:text-2xl font-semibold">
          Belgeler ve Evraklar
        </h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Bu alanda Belgelerinizi yükleyerek ruhsat, veteriner ve şirket
          bilgilerinizi belgeleyebilirsiniz.
        </span>
      </div>
      {taxiData && (
        <div className="mt-5 space-y-5">
          <SinglePetTaxiFile
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="license"
            name="İşletme Ruhsatı"
            taxiData={taxiData?.data}
          />
          <SinglePetTaxiFile
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="taxCertificate"
            name="Vergi Levhası"
            taxiData={taxiData?.data}
          />
          <SinglePetTaxiFile
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="identificationDocumentFront"
            name="Kimlik Ön Yüz"
            taxiData={taxiData?.data}
          />
          <SinglePetTaxiFile
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="identificationDocumentBack"
            name="Kimlik Arka Yüz"
            taxiData={taxiData?.data}
          />
          <SinglePetTaxiFile
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="veterinaryContract"
            name="Veteriner Sözleşmesi"
            taxiData={taxiData?.data}
          />
          <MultiplePetTaxiFiles
            petTaxiToken={petTaxiToken}
            filePath={`petTaxi/${taxiData.data._id}/files/`}
            docType="other"
            name="Diğer Belgeler"
            taxiData={taxiData?.data}
          />
        </div>
      )}
    </>
  );
};

export default PetTaxiFilesPage;
