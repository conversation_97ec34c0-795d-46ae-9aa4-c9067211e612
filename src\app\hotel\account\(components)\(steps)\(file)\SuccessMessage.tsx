"use client";
import React from "react";
import { Check } from "lucide-react";

interface SuccessMessageProps {
  completedStepsSize: number;
  filteredStepsLength: number;
}

const SuccessMessage: React.FC<SuccessMessageProps> = ({
  completedStepsSize,
  filteredStepsLength,
}) => {
  if (completedStepsSize !== filteredStepsLength) {
    return null;
  }

  return (
    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg animate-in fade-in duration-1000">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
          <Check className="w-5 h-5 text-white" />
        </div>
        <div>
          <h4 className="font-medium text-green-800">Tebrikler!</h4>
          <p className="text-sm text-green-600">
            Tüm gerekli dosyalar başarıyla yüklendi. Dosya yükleme işlemi
            ta<PERSON>ı.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SuccessMessage;
