"use client";
import React, { useState } from "react";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import type { RootState } from "@/store";
import { useSelector } from "react-redux";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import LoadingSpinner from "@/shared/icons/Spinner";
import RoomCalculatedDetail from "../(calculatedDetail)/RoomCalculatedDetail";
import ServiceCalculatedDetail from "../(calculatedDetail)/ServiceCalculatedDetail";
import SubscriptionCalculatedDetail from "../(calculatedDetail)/SubscriptionCalculatedDetail";

const MobileCalculatedDetail = ({
  hotelToken,
}: {
  hotelToken: string | undefined;
}) => {
  const { createOrder } = useHotelAtDoorReservation();
  const [loading, setLoading] = useState<boolean>(false);

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const [reservationDetailOpen, setReservationDetailOpen] = useState(false);

  return (
    <>
      <div
        className="text-sm font-medium underline"
        onClick={() => setReservationDetailOpen(true)}
      >
        Detayı Gör
      </div>
      <Drawer
        open={reservationDetailOpen}
        onOpenChange={() => setReservationDetailOpen(false)}
      >
        <DrawerContent className="container pb-2 max-h-[calc(100vh-100px)] md:max-h-[calc(100vh-50px)]">
          <DrawerHeader>
            <DrawerTitle className="mb-5">Rezervasyon Bilgisi</DrawerTitle>
            <DrawerDescription className="sr-only"></DrawerDescription>
          </DrawerHeader>
          <div className="overflow-y-auto space-y-4">
            <RoomCalculatedDetail hotelToken={hotelToken} />
            <ServiceCalculatedDetail hotelToken={hotelToken} />
            <SubscriptionCalculatedDetail hotelToken={hotelToken} />
          </div>
          <div className="border-b border-neutral-200 dark:border-neutral-700"></div>
          <div className="mb-5 flex justify-between font-semibold">
            <span>Toplam</span>
            <span>
              {new Intl.NumberFormat("tr-TR", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(Number(calculatedRoomData?.totalOrderPrice)) + "₺"}
            </span>
          </div>
          <Button
            className="w-full bg-secondary-6000 hover:bg-secondary-700 text-white px-5 sm:px-7 py-3"
            onClick={() =>
              createOrder(
                hotelToken,
                calculatedRoomData?.selectedItems,
                calculatedRoomData?.totalOrderPrice,
                setLoading
              )
            }
            disabled={loading}
          >
            {loading ? <LoadingSpinner /> : "Rezervasyon Oluştur"}
          </Button>
          <X
            onClick={() => setReservationDetailOpen(false)}
            className="absolute right-4 top-4 z-20 size-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          />
          <span className="sr-only">"Kapat"</span>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default MobileCalculatedDetail;
