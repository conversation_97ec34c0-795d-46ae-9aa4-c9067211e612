"use client";
import React, { useState } from "react";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import AcceptPetAgreement from "./AcceptPetAgreement";
import Muvafakatname from "./Muvafakatname";
import Taahhütname from "./Taahhütname";
import Checkbox from "@/shared/Checkbox";

interface AgreementContainerProps {
  orderData: any;
  agreementsChecked: boolean;
  setAgreementsChecked: React.Dispatch<React.SetStateAction<boolean>>;
}

const AgreementContainer: FC<AgreementContainerProps> = ({
  orderData,
  agreementsChecked,
  setAgreementsChecked,
}) => {
  const [agreement, setAgreement] = useState<number>(1);
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <div className="mt-4 flex items-center gap-2">
        <Checkbox
          onChange={() => setAgreementsChecked((prev) => !prev)}
          name="agreement"
          id="agreement-id"
          checked={agreementsChecked}
        />
        <div className="text-sm">
          <span
            onClick={() => {
              setAgreement(1);
              setOpenModal(true);
            }}
            className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
          >
            Muvafakatname
          </span>
          ,{" "}
          <span
            onClick={() => {
              setAgreement(2);
              setOpenModal(true);
            }}
            className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
          >
            Taahhütname
          </span>{" "}
          ve
          <span
            onClick={() => {
              setAgreement(3);
              setOpenModal(true);
            }}
            className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
          >
            {" "}
            Evcil Hayvan Kabul Kabul Sözleşmesini
          </span>{" "}
          okudum kabul ediyorum
        </div>
      </div>
      <Dialog open={openModal}>
        <DialogContent
          onInteractOutside={() => setOpenModal(false)}
          className="max-h-[calc(100vh-100px)] max-w-3xl overflow-y-scroll md:max-h-[calc(100vh-50px)]"
        >
          <DialogHeader>
            <DialogTitle className="text-2xl">
              {agreement === 1
                ? "Muvafakatname"
                : agreement === 2
                  ? "Taahhütname"
                  : "Pet Kabul Sözleşmesi"}
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          {agreement === 1 && <Muvafakatname orderData={orderData} />}
          {agreement === 2 && <Taahhütname orderData={orderData} />}
          {agreement === 3 && <AcceptPetAgreement orderData={orderData} />}
          <DialogClose
            onClick={() => setOpenModal(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgreementContainer;
