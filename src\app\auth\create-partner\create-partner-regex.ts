export const onlyLetterRegex =
  /^(?=(.*[a-zA-ZğüşıöçĞÜŞİÖÇåÅäÄöÖãÃáÁàÀçÇéÉêÊíÍóÓôÔúÚ]){2})[a-zA-ZğüşıöçĞÜŞİÖÇåÅäÄöÖãÃáÁàÀçÇéÉêÊíÍóÓôÔúÚ\s]+$/;
export const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
export const usernameRegex = /^[a-z0-9]{4,}$/;
export const phoneRegex = /^0(\d{10})$/;
export const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d!@#$%^&*()_+\.\-]{8,}$/;
