"use client";
import type React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export const useHotelSignupActions = () => {
  const router = useRouter();
  const { toast } = useToast();
  const owner = useSelector((state: RootState) => state.hotelAuth.owner);
  const hotel = useSelector((state: RootState) => state.hotelAuth.hotel);
  const hotelFeatures = useSelector(
    (state: RootState) => state.hotelAuth.hotelFeatures
  );
  const registerId = useSelector(
    (state: RootState) => state.hotelAuth.registerId
  );

  const handleLastStep = async () => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URI}/hotel/auth/register/finish`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          registerId: registerId,
        }),
      }
    );
    const data = await response.json();

    if (!response.ok || !data.success) {
      const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
      toast({
        variant: "error",
        duration: 5000,
        title: "Hata",
        description: `${errorMessage}`,
      });
      throw new Error("Network response was not ok");
    }
    router.push("/success");
  };

  const handleHotelInfo = async (
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading(true);
    const requestBody = {
      registerId: registerId,
      username: owner.userName,
      password: owner.password,
      firstName: owner.firstName?.trim(),
      lastName: owner.lastName?.trim(),
      dateOfBirth: owner.dateOfBirth,
      hotelName: hotel.hotelName,
      hotelPhoneNumber: hotel.hotelPhoneNumber,
      hotelDescription: hotel.hotelDescription,
      propertyType: hotel.propertyType,
      // legalName: hotel.legalName,
      gender: owner.gender,
      address: {
        streetName: hotel.streetName,
        buildingName: hotel.buildingName,
        buildingNumber: hotel.buildingNumber,
        district: hotel.district,
        cityName: hotel.cityName,
        region: hotel.citySubdivisionName,
        postalZone: hotel.postbox,
        country: {
          name: "Türkiye",
          identificationCode: "TR",
        },
      },
      // taxOffice: hotel.taxOffice,
      // taxNumber: hotel.taxNumber,
      hotelFeatures: hotelFeatures.hotelFeaturesArray,
      acceptedPetTypes: hotelFeatures.acceptedPetTypes,
      careServices: hotelFeatures.hotelCareServices,
    };

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/hotel/auth/register/step5`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }
      handleLastStep();
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  return { handleHotelInfo };
};
