"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { usePetTaxiInformationsActions } from "@/hooks/taxi/useTaxiInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import PetTaxiLogo from "./PetTaxiLogo";
import type { PetTaxiInformationApiTypes } from "@/types/taxi/taxiInformationType";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";

interface PetTaxiInformationsProps {
  taxiData: PetTaxiDataApiTypes;
  petTaxiToken: string | undefined;
}

const PetTaxiInformations: FC<PetTaxiInformationsProps> = ({
  taxiData,
  petTaxiToken,
}) => {
  const translate = useTranslations("HotelInformations");
  const initialData = {
    petTaxiName: taxiData?.petTaxiName,
    petTaxiDescription: taxiData?.petTaxiDescription,
  };
  const [PetTaxiInformations, setPetTaxiInformations] =
    useState<PetTaxiInformationApiTypes>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handlePetTaxiInformations } = usePetTaxiInformationsActions();

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;

    const capitalizeEachWord = (str: string) =>
      str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());

    setPetTaxiInformations((prevState) => {
      return {
        ...prevState,
        [name]: name === "petTaxiName" ? capitalizeEachWord(value) : value,
      };
    });
  };

  return (
    <>
      <form
        className="mb-5"
        onSubmit={(event) => {
          handlePetTaxiInformations(
            event,
            petTaxiToken,
            PetTaxiInformations,
            setLoading,
            setDisabled
          );
        }}
      >
        <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 md:gap-7">
          <FormItem label="Taksi Adı">
            <Input
              name="petTaxiName"
              onChange={handleChange}
              value={PetTaxiInformations.petTaxiName}
            />
          </FormItem>
          <FormItem label="Taksi Açıklaması">
            <Textarea
              name="petTaxiDescription"
              onChange={handleChange}
              value={PetTaxiInformations.petTaxiDescription}
            />
          </FormItem>
        </div>
        <div className="mt-10 flex justify-end">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
            disabled={
              disabled ||
              JSON.stringify(initialData) ===
                JSON.stringify(PetTaxiInformations)
            }
            type="submit"
          >
            {loading ? <LoadingSpinner /> : translate("save")}
          </Button>
        </div>
      </form>
      <PetTaxiLogo taxiData={taxiData} petTaxiToken={petTaxiToken} />
    </>
  );
};

export default PetTaxiInformations;
