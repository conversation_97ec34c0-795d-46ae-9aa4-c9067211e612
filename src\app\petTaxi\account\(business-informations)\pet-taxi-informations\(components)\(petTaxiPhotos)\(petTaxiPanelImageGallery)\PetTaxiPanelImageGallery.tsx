"use client";
import "./styles/index.css";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import type { FC } from "react";
import { Fragment, useEffect, useRef } from "react";
import Modal from "./components/Modal";
import type { ListingGalleryImage } from "./utils/types";
import { useLastViewedPhoto } from "./utils/useLastViewedPhoto";
import { Transition } from "@headlessui/react";
import type { Route } from "next";
import type { ImageType } from "@/types/taxi/taxiDataType";

export const getNewParam = ({
  paramName = "photoId",
  value,
}: {
  paramName?: string;
  value: string | number;
}) => {
  const params = new URLSearchParams(document.location.search);
  params.set(paramName, String(value));
  return params.toString();
};

interface Props {
  images?: ListingGalleryImage[];
  isShowModal: boolean;
  taxiPhotos: ImageType[];
}

const PetTaxiPanelImageGallery: FC<Props> = ({ isShowModal, taxiPhotos }) => {
  const taxiImages = taxiPhotos.map((item, index): ListingGalleryImage => {
    return {
      id: index,
      url: item.src,
    };
  });
  const searchParams = useSearchParams();
  const photoId = searchParams?.get("photoId");
  const router = useRouter();
  const [lastViewedPhoto, setLastViewedPhoto] = useLastViewedPhoto();

  const lastViewedPhotoRef = useRef<HTMLDivElement>(null);
  const thisPathname = usePathname();
  useEffect(() => {
    // This effect keeps track of the last viewed photo in the modal to keep the index page in sync when the user navigates back
    if (lastViewedPhoto && !photoId) {
      lastViewedPhotoRef.current?.scrollIntoView({ block: "center" });
      setLastViewedPhoto(null);
    }
  }, [photoId, lastViewedPhoto, setLastViewedPhoto]);

  const renderContent = () => {
    return (
      <div className=" ">
        {photoId && (
          <Modal
            images={taxiImages}
            onClose={() => {
              // @ts-expect-error: photoId type is not compatible with setLastViewedPhoto parameter
              setLastViewedPhoto(photoId);
              const params = new URLSearchParams(document.location.search);
              params.delete("photoId");
              params.delete("modal");
              router.push(`${thisPathname}/?${params.toString()}` as Route);
            }}
          />
        )}
      </div>
    );
  };

  return (
    <>
      <Transition appear show={isShowModal} as={Fragment}>
        {renderContent()}
      </Transition>
    </>
  );
};

export default PetTaxiPanelImageGallery;
