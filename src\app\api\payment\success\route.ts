import { redirect } from "next/navigation";

export async function POST(request: Request) {
  const data = await request.formData();

  const params = new URLSearchParams({
    ucdMd: (data.get("md") as string) || "",
    transactionGuid: (data.get("islemGUID") as string) || "",
    reservationId: (data.get("orderId") as string) || "",
    hash: (data.get("islemHash") as string) || "",
    mdStatus: (data.get("mdStatus") as string) || "",
    status: "success",
  });

  redirect(`${process.env.NEXT_PUBLIC_BASE_URL}checkout?${params.toString()}`);
}
