import type { <PERSON> } from "react";
import React from "react";
import HotelInformationsNav from "./(components)/HotelInformationsNav";

export interface HotelInformationsLayoutProps {
  children?: React.ReactNode;
}

const HotelInformationsLayout: FC<HotelInformationsLayoutProps> = ({
  children,
}) => {
  return (
    <div className="container pt-8">
      <HotelInformationsNav />
      {children}
    </div>
  );
};

export default HotelInformationsLayout;
