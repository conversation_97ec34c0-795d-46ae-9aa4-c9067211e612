"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import { useHotelInstallment } from "@/hooks/hotel/discounts/useHotelInstallment";
import { Switch } from "@/components/ui/switch";
import Checkbox from "@/shared/Checkbox";

interface UpdateInstallmentProps {
  installmentData: any;
  hotelToken: string | undefined;
}

const UpdateInstallment: FC<UpdateInstallmentProps> = ({
  installmentData,
  hotelToken,
}) => {
  const { updateInstallment } = useHotelInstallment();
  const initialData = {
    isActive: installmentData.isActive,
    cardBrands: installmentData.cardBrands,
    maxInstallmentCount: installmentData.maxInstallmentCount,
    startDate: installmentData.startDate
      ? new Date(installmentData.startDate).toISOString().split("T")[0]
      : "",
    endDate: installmentData.endDate
      ? new Date(installmentData.endDate).toISOString().split("T")[0]
      : "",
  };
  const [installment, setInstallment] = useState<any>(initialData);
  const [editInstallmentIsOpen, setEditInstallmentIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setInstallment((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTodayDateCheckbox = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;

    if (checked) {
      const today = new Date().toISOString().split("T")[0];
      setInstallment((prev: any) => ({
        ...prev,
        startDate: today,
      }));
    } else {
      setInstallment((prev: any) => ({
        ...prev,
        startDate: "",
      }));
    }
  };

  const handleSelectChange = (name: string, value: string | string[]) => {
    setInstallment((prev: any) => ({
      ...prev,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const isValidInstallment = (data: any) => {
    if (!data) return false;

    const { cardBrands, maxInstallmentCount, startDate, endDate } = data;

    if (
      !Array.isArray(cardBrands) ||
      cardBrands.length === 0 ||
      !maxInstallmentCount ||
      maxInstallmentCount <= 0 ||
      !startDate ||
      !endDate
    ) {
      return false;
    }

    return true;
  };

  const buttonDisabled = isValidInstallment(installment);

  const cardBrands = [
    { value: "Axess", label: "Axess" },
    { value: "World", label: "World" },
    { value: "CardFinans", label: "CardFinans" },
    { value: "Bonus", label: "Bonus" },
    { value: "Maximum", label: "Maximum" },
    { value: "SaglamKart", label: "SaglamKart" },
    { value: "Combo", label: "Combo" },
    { value: "Shop&Fly", label: "Shop&Fly" },
    { value: "Paraf", label: "Paraf" },
    { value: "Param", label: "Param" },
  ];

  const resetInputs = () => {
    setInstallment(initialData);
    setEditInstallmentIsOpen(false);
  };

  const closeModal = () => {
    setLoading(false);
    setEditInstallmentIsOpen(false);
  };

  return (
    <div>
      <IconEdit
        onClick={() => setEditInstallmentIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={editInstallmentIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
          onInteractOutside={resetInputs}
        >
          <DialogHeader>
            <DialogTitle>Kampanya Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updateInstallment(
                event,
                hotelToken,
                installment,
                installmentData._id,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <h2 className="font-medium text-lg mb-3">Taksit</h2>
            <div className="space-y-4">
              <FormItem label="Kart Tipi">
                <MultiSelect
                  options={cardBrands}
                  onValueChange={(value) =>
                    handleSelectChange("cardBrands", value)
                  }
                  defaultValue={
                    installmentData.cardBrands?.map((brand: any) => brand) || ""
                  }
                  maxCount={10}
                  placeholder="Kart Tipi Seçin"
                  className="rounded-2xl"
                />
              </FormItem>
              <FormItem label="Maks. Taksit Sayısı" className="w-full">
                <Input
                  min={0}
                  value={installment.maxInstallmentCount}
                  name="maxInstallmentCount"
                  type="number"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="Başlangıç Tarihi" className="w-full">
                <Input
                  name="startDate"
                  type="date"
                  onChange={handleChange}
                  value={installment.startDate}
                />
                <Checkbox
                  id="todayDate"
                  label="Bugünün Tarihini Seç"
                  name="todayDate"
                  checked={
                    installment.startDate ===
                    new Date().toISOString().split("T")[0]
                  }
                  onChange={handleTodayDateCheckbox}
                  className="mt-2 !text-sm"
                  inputClass="!size-5 !rounded-lg !-mr-2"
                />
              </FormItem>
              <FormItem label="Bitiş Tarihi" className="w-full">
                <Input
                  value={installment.endDate}
                  name="endDate"
                  type="date"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="Aktiflik Durumu">
                <div className="flex gap-2">
                  <Switch
                    name="isActive"
                    checked={installment.isActive}
                    onCheckedChange={(checked) =>
                      setInstallment((prev: any) => ({
                        ...prev,
                        isActive: checked,
                      }))
                    }
                    className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                  />
                  <p
                    className={
                      installment.isActive ? "text-green-500" : "text-red-500"
                    }
                  >
                    {installment.isActive ? "Aktif" : "Pasif"}
                  </p>
                </div>
              </FormItem>
            </div>
            {!buttonDisabled && (
              <span className="text-red-500 text-sm text-right block mt-2">
                Lütfen tüm alanları doldurunuz!
              </span>
            )}
            <div className="mt-7 flex justify-end gap-5">
              <Button onClick={resetInputs} variant="outline" type="button">
                İptal
              </Button>
              <Button
                disabled={
                  JSON.stringify(installment) === JSON.stringify(initialData) ||
                  !buttonDisabled ||
                  disabled
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdateInstallment;
