"use client";
import React, { useState, useEffect, useRef } from "react";
import type { FC, ChangeEvent } from "react";
import { Send, Check, X } from "lucide-react";
import Textarea from "@/shared/Textarea";
import { useSocket } from "@/app/Socket";
import { useSelector, useDispatch } from "react-redux";
import type { RootState } from "@/store";
import { setText, setIsEdit } from "@/store/features/liveChat/live-chat-slice";
import UploadChatFile from "./UploadChatFile";
import { useLiveChat } from "@/hooks/liveChat/useLiveChat";
import LoadingSpinner from "@/shared/icons/Spinner";
import EmojiContainer from "./EmojiContainer";
import PreviewUploadImageContainer from "./PreviewUploadImageContainer";
import PreviewUploadFileContainer from "./PreviewUploadFileContainer";
import { Button } from "@/components/ui/button";

interface LiveChatInputProps {
  selectedId: string | string[] | undefined;
  hotelId: string;
  petOwnerToken: string | undefined;
}

const LiveChatInput: FC<LiveChatInputProps> = ({
  selectedId,
  hotelId,
  petOwnerToken,
}) => {
  const { sendChat, sendEdited, sendTyping } = useSocket();
  const dispatch = useDispatch();
  const { uploadChatFileHandler, uploadChatImageHandler } = useLiveChat();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const liveChatInputRef = useRef<HTMLInputElement>(null);
  const [emojiOpen, setEmojiOpen] = useState<boolean>(false);
  const [localTyping, setLocalTyping] = useState<boolean>(false);
  const [image, setImage] = useState<string[] | []>([]);
  const [pdfFileNames, setPdfFileNames] = useState<string[]>([]);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | undefined>(
    undefined
  );
  const [loading, setLoading] = useState<boolean>(false);

  const textMessage = useSelector(
    (state: RootState) => state.liveChatText.text
  );
  const oldTextMessage = useSelector(
    (state: RootState) => state.liveChatText.oldText
  );
  const selectedMessageId = useSelector(
    (state: RootState) => state.liveChatText.messageId
  );
  const isEdit = useSelector((state: RootState) => state.liveChatText.isEdit);

  const buttonStateHandler = () => {
    if (
      textMessage?.trim() === "" &&
      (photoFileObject?.length === 0 || !photoFileObject)
    ) {
      return false;
    }

    return true;
  };

  const isButtonDisabled = buttonStateHandler();

  // To focus edit input
  useEffect(() => {
    if (!isEdit) return;

    const timeout = setTimeout(() => {
      textareaRef.current?.focus();
    }, 200);

    return () => clearTimeout(timeout);
  }, [isEdit]);

  const applySendTypingHandler = () => {
    sendTyping({
      from: hotelId,
      to: selectedId,
      isTyping: true,
    });
    setLocalTyping(true);
  };

  const cancelSendTypingHandler = () => {
    sendTyping({
      from: hotelId,
      to: selectedId,
      isTyping: false,
    });
    setLocalTyping(false);
  };

  const textMessageHandler = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value.trim();
    dispatch(setText(event.target.value));

    if (value.length > 0) {
      if (!localTyping) {
        applySendTypingHandler();
      }
    } else {
      if (localTyping) {
        cancelSendTypingHandler();
      }
    }
  };

  const handleBlur = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const sendMessageHandler = async () => {
    if (loading) return;

    if (!isButtonDisabled) {
      dispatch(setText(""));
      return;
    }

    if (photoFileObject && photoFileObject?.length > 0) {
      let uploadedResponse: any[] = [];

      if (photoFileObject[0].type === "application/pdf") {
        uploadedResponse = await uploadChatFileHandler(
          photoFileObject,
          petOwnerToken,
          hotelId,
          setLoading
        );
      } else {
        uploadedResponse = await uploadChatImageHandler(
          photoFileObject,
          petOwnerToken,
          hotelId,
          setLoading
        );
      }

      if (uploadedResponse && uploadedResponse.length > 0) {
        await Promise.all(
          uploadedResponse.map((uploaded, index) =>
            sendChat({
              from: hotelId,
              to: selectedId,
              text: index === 0 ? textMessage.trim() : "",
              fileUrl: uploaded.fileUrl,
              fileType: uploaded.fileType,
              fileName: photoFileObject[index]?.name,
            })
          )
        );
      }

      setTimeout(() => {
        setLoading(false);
      }, 1000);
      setImage([]);
      setPdfFileNames([]);
      setPhotoFileObject(undefined);
    } else {
      sendChat({
        from: hotelId,
        to: selectedId,
        text: textMessage.trim(),
      });
    }

    cancelSendTypingHandler();
    dispatch(setText(""));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (isEdit) {
        return editMessageHandler();
      } else {
        sendMessageHandler();
      }
    }
  };

  const editMessageHandler = () => {
    if (!isButtonDisabled) return;

    if (textMessage.trim() === oldTextMessage.trim()) {
      dispatch(setText(""));
      dispatch(setIsEdit(false));
      return;
    }

    sendEdited({
      from: hotelId,
      to: selectedId,
      messageId: selectedMessageId,
      newText: textMessage,
    });

    cancelSendTypingHandler();
    dispatch(setText(""));
    dispatch(setIsEdit(false));
  };

  const cancelEditMessageHandler = () => {
    cancelSendTypingHandler();
    dispatch(setText(""));
    dispatch(setIsEdit(false));
  };

  return (
    <>
      <div className="overflow-auto border-t md:border-b lg:border-b-0 py-4 max-md:px-2 bg-background md:mb-2 lg:mb-0">
        <div className="max-w-6xl flex items-center gap-5 mx-auto">
          <div className="w-full relative bg-background sm:px-2">
            <div className="flex items-center gap-3 sm:mx-3">
              <div className="flex items-center justify-between">
                {isEdit ? (
                  <div className="cursor-pointer absolute left-1 top-2 dark:text-neutral-300 bg-red-500 hover:bg-red-700 duration-200 text-white w-7 h-7 flex justify-center items-center rounded-full">
                    <X onClick={cancelEditMessageHandler} className="size-4" />
                  </div>
                ) : (
                  <UploadChatFile
                    setPhotoFileObject={setPhotoFileObject}
                    setImage={setImage}
                    liveChatInputRef={liveChatInputRef}
                    setPdfFileNames={setPdfFileNames}
                  />
                )}
                {isEdit && (
                  <div className="cursor-pointer absolute left-1 top-2 dark:text-neutral-300 bg-red-500 hover:bg-red-700 duration-200 text-white w-7 h-7 flex justify-center items-center rounded-full">
                    <X onClick={cancelEditMessageHandler} className="size-4" />
                  </div>
                )}
              </div>
              <div className="relative w-full">
                <Textarea
                  ref={textareaRef}
                  onBlur={handleBlur}
                  onChange={textMessageHandler}
                  onKeyDown={handleKeyDown}
                  value={textMessage}
                  placeholder="mesajınızı yazın..."
                  className="flex-1 pr-8 resize-none focus:outline-none focus:ring-0 focus:!border-neutral-400 dark:focus:!border-neutral-400 dark:!bg-transparent !bg-transparent h-20 sm:h-16 hiddenScrollbar"
                />
                <EmojiContainer
                  emojiOpen={emojiOpen}
                  setEmojiOpen={setEmojiOpen}
                />
              </div>
              {isEdit ? (
                <div
                  onClick={editMessageHandler}
                  className={`${!isButtonDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}  dark:text-neutral-300 bg-blue-600 hover:bg-blue-700 duration-200 text-white w-7 h-7 sm:w-8 sm:h-8 flex justify-center items-center rounded-full`}
                >
                  <Check className="size-4 sm:size-5" />
                </div>
              ) : (
                <Button
                  onClick={sendMessageHandler}
                  className={`${!isButtonDisabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}  dark:text-neutral-300 bg-blue-600 hover:bg-blue-700 duration-200 text-white w-7 h-7 sm:w-8 sm:h-8 flex justify-center items-center`}
                >
                  {loading ? (
                    <LoadingSpinner />
                  ) : (
                    <Send className="size-4 sm:size-5" />
                  )}
                </Button>
              )}
            </div>
            <PreviewUploadImageContainer
              image={image}
              setImage={setImage}
              setPhotoFileObject={setPhotoFileObject}
              liveChatInputRef={liveChatInputRef}
            />
            <PreviewUploadFileContainer
              pdfFileNames={pdfFileNames}
              setPdfFileNames={setPdfFileNames}
              setPhotoFileObject={setPhotoFileObject}
              liveChatInputRef={liveChatInputRef}
            />
          </div>
        </div>
      </div>
      <p className="text-xs text-center pb-2 font-semibold text-neutral-500 lg:hidden">
        Paw Chat by PawBooking
      </p>
    </>
  );
};

export default LiveChatInput;
