"use client";

import type { FC } from "react";
import React, { useEffect, useState } from "react";
import type { CustomerReviewType } from "@/types/hotel/hotelLandingType";
import { MapPinIcon } from "@heroicons/react/24/outline";
import IconEdit from "@/shared/icons/Edit";
import IconDelete from "@/shared/icons/Delete";
import ButtonPrimary from "@/shared/ButtonPrimary";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { X } from "lucide-react";

export interface CustomerReviewListingProps {
  className?: string;
  reviews: CustomerReviewType[];
  onUpdateReview: (updatedReview: CustomerReviewType, index: number) => void;
  onDeleteReview: (index: number) => void;
}

const CommentListing: FC<CustomerReviewListingProps> = ({
  className = "",
  reviews,
  onUpdateReview,
  onDeleteReview,
}) => {
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [currentReview, setCurrentReview] = useState<CustomerReviewType | null>(
    null
  );
  const [initialReview, setInitialReview] = useState<CustomerReviewType | null>(
    null
  );
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [isSaveButtonDisabled, setIsSaveButtonDisabled] = useState(true);

  const openEditModal = (review: CustomerReviewType, index: number) => {
    setCurrentReview(review);
    setInitialReview(review);
    setEditIndex(index);
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setCurrentReview(null);
    setEditIndex(null);
  };

  const handleEditChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (currentReview) {
      setCurrentReview({ ...currentReview, [name]: value });
    }
  };

  const handleSaveEdit = () => {
    if (currentReview && editIndex !== null) {
      onUpdateReview(currentReview, editIndex);
    }
    closeEditModal();
  };

  useEffect(() => {
    if (currentReview && initialReview) {
      setIsSaveButtonDisabled(
        currentReview.customerName === initialReview.customerName &&
          currentReview.review === initialReview.review &&
          currentReview.city === initialReview.city
      );
    }
  }, [currentReview, initialReview]);
  return (
    <div
      className={`nc-CommentListing ${className}`}
      data-nc-id="CommentListing"
    >
      {reviews.length === 0 ? (
        <div className="text-center text-neutral-500 dark:text-neutral-400">
          Henüz Yorum Yok
        </div>
      ) : (
        reviews.map((review, index) => (
          <div key={index} className="mb-4 flex space-x-4">
            <div className="pt-0.5">{/* Avatar */}</div>
            <div className="grow">
              <div className="flex justify-between space-x-3">
                <div className="flex flex-col">
                  <div className="text-md font-semibold">
                    <span>{review.customerName}</span>
                  </div>
                  <div className="mt-2 flex items-center space-x-2 text-sm text-neutral-400">
                    <MapPinIcon className="size-5" />
                    <span>{review.city}</span>
                  </div>
                </div>
              </div>
              <span className="mt-3 block text-neutral-600 dark:text-neutral-300">
                {review.review}
              </span>
            </div>
            <div className="flex items-end">
              <div className="mr-3">
                <IconEdit
                  className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
                  onClick={() => openEditModal(review, index)}
                ></IconEdit>
              </div>
              <div className="mr-3">
                <IconDelete
                  className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
                  onClick={() => onDeleteReview(index)}
                ></IconDelete>
              </div>
            </div>
          </div>
        ))
      )}

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen}>
        <DialogContent
          onInteractOutside={closeEditModal}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>Yorumu Düzenle</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable">
            <div className="space-y-8">
              <div>
                <FormItem label="Müşteri Adı">
                  <Input
                    type="text"
                    name="customerName"
                    className="mt-1.5"
                    value={currentReview?.customerName || ""}
                    onChange={handleEditChange}
                  />
                </FormItem>
                <FormItem label="Müşteri Yorumu" className="mt-3">
                  <Textarea
                    name="review"
                    className="mt-1.5"
                    value={currentReview?.review || ""}
                    onChange={handleEditChange}
                  />
                </FormItem>
                <FormItem label="Şehir" className="mt-3">
                  <Input
                    type="text"
                    name="city"
                    className="mt-1.5"
                    value={currentReview?.city || ""}
                    onChange={handleEditChange}
                  />
                </FormItem>
              </div>
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <ButtonPrimary onClick={closeEditModal}>Vazgeç</ButtonPrimary>
            <ButtonPrimary
              onClick={handleSaveEdit}
              disabled={isSaveButtonDisabled}
            >
              Kaydet
            </ButtonPrimary>
          </div>
          <DialogClose
            onClick={() => setEditModalOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CommentListing;
