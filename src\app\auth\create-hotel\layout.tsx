import React from "react";
import type { FC } from "react";

export interface CreateHotelLayoutProps {
  children: React.ReactNode;
  params: {
    stepIndex: string;
  };
}

const CreateHotelLayout: FC<CreateHotelLayoutProps> = ({ children }) => {
  return (
    <div
      className={`nc-PageAddListing1 mx-auto max-w-3xl px-4 pb-24 pt-14 sm:py-24 lg:pb-32`}
    >
      <div className="space-y-11">
        <div className="">{children}</div>
      </div>
    </div>
  );
};

export default CreateHotelLayout;
