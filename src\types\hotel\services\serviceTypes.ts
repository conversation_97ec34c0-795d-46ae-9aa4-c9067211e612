import { PetOwnerApiTypes } from "@/types/petOwner/petOwnerTypes";
import { PetInformationApiTypes } from "@/types/petOwner/petTypes";
export interface serviceSoldListApiTypes {
  _id: string;
  serviceType: string;
  hotel: string;
  serviceHotelId: string;
  petOwner: PetOwnerApiTypes;
  pet: PetInformationApiTypes;
  serviceName: string;
  total: number;
  totalHotel: number;
  description: string;
  note: string;
  issueDate: string;
  serviceDate: string;
  paymentStatus: string;
  status: string;
  serviceDetails:
    | serviceSoldVeterinaryTypes
    | serviceSoldTransportationTypes
    | serviceSoldGroomingTypes;
  createdAt: string;
  hotelCustomer: HotelCustomerTypes;
  hotelPet: HotelPetTypes;
}

interface serviceSoldVeterinaryTypes {
  veterinarianName: string;
  requiredDocuments: string;
  availabilityStart: string;
  availabilityEnd: string;
}

interface serviceSoldTransportationTypes {
  initialPrice: string;
  maxDistance: string;
  distancePrice: string;
}

interface serviceSoldGroomingTypes {
  duration: string;
}
export interface veterinaryServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    total: string;
    serviceDetails: {
      veterinarianName: string;
      requiredDocuments: string;
      availabilityStart: string;
      availabilityEnd: string;
    };
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface transportationServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    serviceDetails: {
      initialPrice: string;
      maxDistance: string;
      distancePrice: string;
    };
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface groomingServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    serviceDetails: {
      duration: string;
    };
    total: string;
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface transportationServiceTypes {
  isActive: boolean;
  serviceName: string;
  description: string;
  serviceDetails: {
    initialPrice: string;
    maxDistance: string;
    distancePrice: string;
  };
  petType: string[];
}

export interface veterinaryServiceTypes {
  isActive: boolean;
  serviceName: string;
  total: string;
  description: string;
  serviceDetails: {
    veterinarianName: string;
    requiredDocuments: string;
    availabilityStart: string;
    availabilityEnd: string;
  };
  petType: string[];
}

export interface groomingServiceTypes {
  isActive: boolean;
  serviceName: string;
  description: string;
  serviceDetails: {
    duration: string;
  };
  total: string;
  petType: string[];
}

export interface HotelCustomerTypes {
  _id: string;
  hotel: string;
  fullName: string;
  email: string;
  phone: string;
  createdAt: string;
  updatedAt: string;
}

export interface HotelPetTypes {
  age: string;
  allergicTo: string[];
  breed: string;
  color: string;
  createdAt: string;
  description: string;
  documentPhotos: ImageType[];
  externalParasiteTreatment: boolean;
  externalTreatmentDate: string | null;
  feedingHabits: string;
  gender: string;
  hereditaryDiseases: string[];
  images: ImageType[];
  infertile: boolean;
  internalParasiteTreatment: boolean;
  internalTreatmentDate: string | null;
  isDeleted: boolean;
  kind: string;
  medicine: string;
  microChipNumber: string;
  name: string;
  operationHistory: string[];
  passive: boolean;
  owner: string;
  specified: string;
  updatedAt: string;
  vaccines: string[];
  _id: string;
}

interface ImageType {
  img800: Img800;
  img400: Img400;
  img200: Img200;
  img100: Img100;
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  tags: string;
  createdDate: string;
}

interface Img800 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img400 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img200 {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Img100 {
  src: string;
  width: number;
  height: number;
  size: number;
}
