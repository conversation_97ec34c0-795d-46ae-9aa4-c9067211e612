"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import Input from "@/shared/Input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useHotelCustomers } from "@/hooks/hotel/useHotelCustomers";
import type { HotelCustomerTypes } from "@/types/hotel/hotelCustomerType";
import LoadingSpinner from "@/shared/icons/Spinner";
import { onlyLetterRegex, emailRegex } from "@/utils/regex/petOwnerRegex";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface AddNewHotelCustomerProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

const addCustomerModal: FC<AddNewHotelCustomerProps> = ({
  hotelToken,
  hotelData,
}) => {
  const { addHotelCustomerHandler } = useHotelCustomers();
  const initialData = {
    fullName: "",
    email: "",
    phone: "",
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [hotelCustomer, setHotelCustomer] =
    useState<HotelCustomerTypes>(initialData);
  const [isCustomerOpen, setIsCustomerOpen] = useState(false);

  function closeCustomerModal() {
    setIsCustomerOpen(false);
    setLoading(false);
    setHotelCustomer(initialData);
  }

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setHotelCustomer((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const phoneRegex = /^\d{10}$/;
  const nameCheck = onlyLetterRegex.test(hotelCustomer.fullName);
  const mailCheck = emailRegex.test(hotelCustomer.email.toLowerCase().trim());
  const phoneCheck = phoneRegex.test(hotelCustomer.phone);

  const isAllValid = nameCheck && mailCheck && phoneCheck;

  return (
    <div>
      <div className="flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center"
          onClick={() => setIsCustomerOpen(true)}
        >
          Müşteri Ekle
        </Button>
      </div>
      <Dialog open={isCustomerOpen} onOpenChange={setIsCustomerOpen}>
        <DialogContent
          onInteractOutside={closeCustomerModal}
          className="w-auto"
        >
          <DialogHeader>
            <DialogTitle>Müşteri Ekle</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) => {
              addHotelCustomerHandler(
                event,
                hotelToken,
                hotelCustomer,
                hotelData._id,
                setLoading,
                closeCustomerModal
              );
            }}
          >
            <div className="py-4 px-4 space-y-4">
              <label className="flex flex-col justify-start w-80">
                <span className="text-neutral-800 dark:text-neutral-200">
                  Ad-Soyad
                </span>
                <Input
                  type="text"
                  className="mt-1 rounded-lg"
                  name="fullName"
                  onChange={handleChange}
                />
              </label>
              <label className="flex flex-col justify-start w-80">
                <span className="text-neutral-800 dark:text-neutral-200">
                  E-Posta
                </span>
                <Input
                  type="text"
                  className="mt-1 rounded-lg"
                  name="email"
                  onChange={handleChange}
                />
              </label>
              <label className="flex flex-col justify-start">
                <span className="text-neutral-800 dark:text-neutral-200">
                  Telefon
                </span>
                <div className="flex rounded-lg shadow-sm shadow-black/5 w-80">
                  <span className="inline-flex items-center rounded-s-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="30"
                      viewBox="0 -30000 90000 60000"
                    >
                      <path fill="#e30a17" d="m0-30000h90000v60000H0z" />
                      <path
                        fill="#fff"
                        d="m41750 0 13568-4408-8386 11541V-7133l8386 11541zm925 8021a15000 15000 0 1 1 0-16042 12000 12000 0 1 0 0 16042z"
                      />
                    </svg>{" "}
                    <span className="ml-1">+90</span>
                  </span>
                  <Input
                    name="phone"
                    type="text"
                    placeholder="Telefon Numarası"
                    className="flex-1 block w-full min-w-0 rounded-none rounded-r-lg transition duration-150 ease-in-out sm:text-sm sm:leading-5"
                    onChange={handleChange}
                    maxLength={10}
                  />
                </div>
              </label>
              <div className="flex justify-end gap-5">
                <Button
                  variant="outline"
                  onClick={closeCustomerModal}
                  type="button"
                >
                  Vazgeç
                </Button>
                <Button
                  className="bg-secondary-6000 hover:bg-secondary-700"
                  disabled={!isAllValid}
                  type="submit"
                >
                  {loading ? <LoadingSpinner /> : "Kaydet"}
                </Button>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default addCustomerModal;
