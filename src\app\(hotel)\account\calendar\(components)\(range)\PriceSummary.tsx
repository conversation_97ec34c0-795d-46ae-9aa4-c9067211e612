import type { FC } from "react";
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

interface PriceSummaryProps {
  displayCustomerTotalPrice: () => number | undefined;
  isSummaryOpen: boolean;
  setIsSummaryOpen: React.Dispatch<React.SetStateAction<boolean>>;
  nightCount: number;
}

const PriceSummary: FC<PriceSummaryProps> = ({
  displayCustomerTotalPrice,
  isSummaryOpen,
  setIsSummaryOpen,
  nightCount,
}) => {
  const translate = useTranslations("PriceSummary");
  const totalPrice = displayCustomerTotalPrice();

  return (
    <Dialog open={isSummaryOpen}>
      <DialogContent
        onInteractOutside={() => {
          setIsSummaryOpen(false);
        }}
      >
        <DialogHeader>
          <DialogTitle>{translate("priceBreakdown")}</DialogTitle>
          <DialogDescription className="sr-only"></DialogDescription>
        </DialogHeader>
        <div className="flex justify-between">
          <div>
            <div>
              {new Intl.NumberFormat("tr-TR", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(Number(Number(totalPrice) / nightCount)) +
                "₺" +
                " " +
                "x" +
                nightCount +
                " " +
                translate("night")}
            </div>
            <div className="text-sm font-light text-neutral-500 dark:text-neutral-400">
              {translate("nightlyRate")}
            </div>
          </div>
          <div>
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(Number(totalPrice))}
            ₺
          </div>
        </div>
        <div className="flex justify-between">
          <div>{translate("serviceFee")}</div>
          <div>
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(Number(totalPrice) * 0.18)}
            ₺
          </div>
        </div>
        <Separator className="my-4" />
        <div className="flex justify-between">
          <div className="font-medium">{translate("totalPrice")}</div>
          <div className="font-medium">
            {displayCustomerTotalPrice() &&
              new Intl.NumberFormat("tr-TR", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(Number(displayCustomerTotalPrice()) * 1.18) + "₺"}
          </div>
        </div>
        <div className="mt-5 text-center font-medium">
          {translate("yourEarnings")}{" "}
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(totalPrice))}
          ₺
        </div>
        <DialogClose
          onClick={() => {
            setIsSummaryOpen(false);
          }}
          className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="size-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default PriceSummary;
