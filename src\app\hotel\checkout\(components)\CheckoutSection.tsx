"use client";
import React, { useState } from "react";
import type { FC } from "react";
import CheckoutStepNav from "./CheckoutStepNav";
import { PricingSection } from "@/app/(components)/(hotelHomePage)/pricing";
import CheckoutContainer from "./subscriptionCheckout/(components)/CheckoutContainer";
import MobileFooterCheckout from "./subscriptionCheckout/(components)/(mobile)/MobileFooterCheckout";

interface CheckoutSectionProps {
  hotelToken: string | undefined;
  membershipData: any;
}

const CheckoutSection: FC<CheckoutSectionProps> = ({
  hotelToken,
  membershipData,
}) => {
  const [step, setStep] = useState<number>(1);
  const [selectedTier, setSelectedTier] = useState<{
    tierId: string;
    price: number | string;
    frequency: string;
  } | null>(null);

  const handleTierSelect = (
    tierId: string,
    price: number | string,
    frequency: string
  ) => {
    setSelectedTier({ tierId, price, frequency });
    setStep(2);
  };
  return (
    <div>
      <CheckoutStepNav step={step} />
      {step === 1 && (
        <div className="mt-5"> 
        <PricingSection
        /* membershipData={membershipData}
          onSelectTier={handleTierSelect}*/
        />
        </div>
      )}
      {step === 2 && (
        <>
          <CheckoutContainer
            selectedTier={selectedTier}
            hotelToken={hotelToken}
            setStep={setStep}
          />
          <MobileFooterCheckout selectedTier={selectedTier} />
        </>
      )}
    </div>
  );
};

export default CheckoutSection;
