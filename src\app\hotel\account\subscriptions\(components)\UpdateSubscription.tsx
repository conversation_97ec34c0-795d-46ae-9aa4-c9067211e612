"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import type { subscriptionTypes } from "@/types/hotel/subscriptionTypes";
import { useHotelSubscription } from "@/hooks/hotel/useHotelSubscription";
import type { RoomGroupListType } from "@/types/hotel/roomGroupType";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";

interface UpdateSubscriptionProps {
  subscription: any;
  roomGroupData: RoomGroupListType;
  hotelToken: string | undefined;
}

const UpdateSubscription: FC<UpdateSubscriptionProps> = ({
  subscription,
  roomGroupData,
  hotelToken,
}) => {
  const { updateSubscription } = useHotelSubscription();
  const initialData = {
    _id: subscription._id,
    isActive: subscription.isActive,
    subscriptionName: subscription.subscriptionName,
    subscriptionDuration: subscription.subscriptionDuration,
    nightsEarned: subscription.nightsEarned,
    total: subscription.total,
    roomGroups: subscription.roomGroups.map((group: any) => ({
      _id: group._id,
      name: group.name,
    })),
  };
  const [subscriptions, setSubscriptions] =
    useState<subscriptionTypes>(initialData);
  const [editSubscriptionIsOpen, setEditSubscriptionIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = event.target;

    if (name === "isActive") {
      setSubscriptions((prevSubscription) => ({
        ...prevSubscription,
        isActive: checked,
      }));
    } else {
      setSubscriptions((prevSubscription) => ({
        ...prevSubscription,
        [name]: type === "number" ? Number(value) : value,
      }));
    }
  };

  const handleSelectChange = (name: string, value: number | string[]) => {
    setSubscriptions((prevSubscription) => ({
      ...prevSubscription,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const roomGroupList = roomGroupData.map((roomGroup) => ({
    value: roomGroup._id,
    label: roomGroup.roomGroupName,
  }));

  const isSubscriptionValid = (subscription: any) => {
    const checkValues = (obj: any) =>
      Object.entries(obj).every(([key, value]) => {
        if (typeof value === "string") {
          return value.trim() !== "";
        }
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return value !== null && value !== undefined;
      });

    return checkValues(subscription);
  };

  const buttonDisabled = isSubscriptionValid(subscriptions);

  const resetInputs = () => {
    setSubscriptions(initialData);
    setEditSubscriptionIsOpen(false);
  };

  const closeModal = () => {
    setLoading(false);
    setEditSubscriptionIsOpen(false);
  };

  return (
    <div>
      <IconEdit
        onClick={() => setEditSubscriptionIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={editSubscriptionIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
          onInteractOutside={resetInputs}
        >
          <DialogHeader>
            <DialogTitle>Üyelik Kartı Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updateSubscription(
                event,
                hotelToken,
                subscriptions,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <div className="space-y-2 w-72">
              <FormItem label="Üyelik Kartı Adı">
                <Input
                  name="subscriptionName"
                  onChange={handleChange}
                  value={subscriptions?.subscriptionName || ""}
                  required
                />
              </FormItem>
              <FormItem label="Üyelik Kartı Süresi">
                <Select
                  onValueChange={(value) =>
                    handleSelectChange("subscriptionDuration", Number(value))
                  }
                  value={subscriptions?.subscriptionDuration?.toString() || ""}
                  required
                >
                  <SelectTrigger className="w-72 rounded-2xl">
                    <SelectValue placeholder="Süre Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="90">3 Ay</SelectItem>
                    <SelectItem value="180">6 Ay</SelectItem>
                    <SelectItem value="270">9 Ay</SelectItem>
                    <SelectItem value="360">12 Ay</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
              <FormItem label="Gece Sayısı">
                <Input
                  name="nightsEarned"
                  type="number"
                  onChange={handleChange}
                  value={+subscriptions?.nightsEarned || ""}
                  required
                />
              </FormItem>
              <FormItem label="Üyelik Kartı Ücreti">
                <Input
                  name="total"
                  type="number"
                  onChange={handleChange}
                  value={+subscriptions?.total || ""}
                  required
                />
              </FormItem>
              <FormItem label="Oda Grubu">
                <MultiSelect
                  options={roomGroupList}
                  onValueChange={(value) =>
                    handleSelectChange("roomGroups", value)
                  }
                  defaultValue={
                    subscriptions?.roomGroups?.map((group) => group._id) || ""
                  }
                  placeholder="Oda Grubu Seçin"
                  className="rounded-2xl"
                  maxCount={15}
                />
              </FormItem>
              <FormItem label="Aktiflik Durumu">
                <div className="flex gap-2">
                  <Switch
                    name="isActive"
                    checked={!!subscriptions.isActive}
                    onCheckedChange={(checked) =>
                      setSubscriptions((prev) => ({
                        ...prev,
                        isActive: checked,
                      }))
                    }
                    className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                  />
                  <p
                    className={
                      subscriptions.isActive ? "text-green-500" : "text-red-500"
                    }
                  >
                    {subscriptions.isActive ? "Aktif" : "Pasif"}
                  </p>
                </div>
              </FormItem>
            </div>
            {!buttonDisabled && (
              <span className="text-red-500 text-sm text-right block mt-2">
                Lütfen tüm alanları doldurunuz!
              </span>
            )}
            <div className="mt-7 flex justify-end gap-5">
              <Button onClick={resetInputs} variant="outline" type="button">
                İptal
              </Button>
              <Button
                disabled={
                  JSON.stringify(subscriptions) ===
                    JSON.stringify(initialData) ||
                  !buttonDisabled ||
                  disabled
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdateSubscription;
