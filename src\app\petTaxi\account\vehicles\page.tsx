import React from "react";
import VehiclesContainer from "./(components)/VehiclesContainer";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import getVehicles from "@/actions/(protected)/taxi/getVehicles";

const VehiclesPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();
  const vehiclesData = await getVehicles();

  return (
    <div className="container">
      <VehiclesContainer
        taxiData={taxiData?.data}
        petTaxiToken={petTaxiToken}
        vehiclesData={vehiclesData?.data}
      />
    </div>
  );
};

export default VehiclesPage;
