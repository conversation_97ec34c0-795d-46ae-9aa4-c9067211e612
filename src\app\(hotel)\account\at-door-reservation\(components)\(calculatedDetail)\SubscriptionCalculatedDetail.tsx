import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
// import DeleteCalculatedSubscriptionDetail from "../DeleteCalculatedSubscriptionDetail";
import { createLocalDate } from "@/utils/createLocalDate";
import convertOneDateToString from "@/utils/convertOneDateToString";

const SubscriptionCalculatedDetail = () => {
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const subscriptions = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "subscription"
  );

  return (
    <>
      {subscriptions?.map((subscription: any) => {
        const date = createLocalDate(
          subscription?.itemData?.startDate.split("T")[0]
        );

        let formattedDate = "";

        if (date) {
          date.setDate(
            date.getDate() + subscription?.itemData?.subscriptionDuration
          );

          formattedDate = convertOneDateToString(date, true, "tr");
        }

        return (
          <div key={subscription.id} className="space-y-4">
            <div className="flex justify-between items-end space-y-4">
              <div className="space-y-4">
                <div className="font-semibold capitalize">
                  {subscription?.itemData?.subscriptionName} (Üyelik Kartı)
                </div>
                <div className="flex flex-col justify-between">
                  {/* <span>{formattedDate} tarihine kadar geçerli</span> */}
                  <span>{subscription?.itemData?.availableNights} gece</span>
                </div>
                <div className="flex justify-start font-semibold">
                  <span>
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(subscription?.itemData?.total)) + "₺"}
                  </span>
                </div>
              </div>
              {/* <DeleteCalculatedSubscriptionDetail
                removedItemId={subscription?.id}
                subscriptionId={subscription?.itemData?.subscriptionHotelId}
              /> */}
            </div>
            <div className="border-b border-neutral-200 dark:border-neutral-700"></div>
          </div>
        );
      })}
    </>
  );
};

export default SubscriptionCalculatedDetail;
