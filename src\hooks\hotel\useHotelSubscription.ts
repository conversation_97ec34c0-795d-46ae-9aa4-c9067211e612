import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelSubscription = () => {
  const { toast } = useToast();

  const addSubscription = async (
    event: FormEvent,
    hotelToken: string | undefined,
    subscription: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.addSubscription, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(subscription),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Üye Kartı Ekleme",
          description: "Üye Kartı başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/subscriptions");
        setLoading(false);
        closeModal();
        setDisabled(false);
        window.location.reload();
      } catch (error) {
        console.log(error);
      }
    }
  };

  const updateSubscription = async (
    event: FormEvent,
    hotelToken: string | undefined,
    subscription: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.updateSubscription, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(subscription),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setTimeout(() => {
            setDisabled(false);
          }, 1000);

          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Üye Kartı Güncelleme",
          description: "Üye Kartı başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/subscriptions");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
        window.location.reload();
      } catch (error) {
        console.log(error);
      }
    }
  };

  const deleteSubscription = async (
    event: FormEvent,
    hotelToken: string | undefined,
    subscriptionId: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.deleteSubscription}/${subscriptionId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Üye Kartı Kaldırma",
          description: "Üye Kartı başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/subscriptions");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };
  return { addSubscription, updateSubscription, deleteSubscription };
};
