import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { ImageResizeFitType, uploadMultiToS3 } from "@/lib/s3BucketHelper";
import { HOTEL_API_PATHS, S3_API_PATHS } from "@/utils/apiUrls";
import type {
  CustomerReviewsSectionType,
  HeroSectionType,
  TeamMemberType,
  TeamSectionType,
} from "@/types/hotel/hotelLandingType";

export const useHotelLandingActions = () => {
  const { toast } = useToast();

  const createHotelLanding = async (
    event: FormEvent,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(`${HOTEL_API_PATHS.hotelLanding}/create`, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
        });

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        revalidatePathHandler("/hotel/account/hotel-landing");
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const uploadHeroSectionHandler = async (
    heroSectionData: HeroSectionType,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<React.SetStateAction<FileList | null>>,
    setImage: React.Dispatch<React.SetStateAction<string[] | []>>
  ) => {
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/heroSection`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              updates: {
                title: heroSectionData.title,
                description: heroSectionData.description,
                images: heroSectionData.images,
              },
            }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          setPhotoFileObject(null);
          setImage([]);
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Hero Alanı Güncelleme",
          description: "Hero alanı başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing");
        setLoading(false);
        setPhotoFileObject(null);
        setImage([]);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const uploadTeamSectionHandler = async (
    event: FormEvent,
    teamSectionData: TeamSectionType,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/teamSection`,
          {
            method: "POST",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              title: teamSectionData.title?.trim(),
              description: teamSectionData.description?.trim(),
            }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Takım Bilgileri Ekleme",
          description: "Takım Bilgileri başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing");
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const addCustomerReviewSectionHandler = async (
    event: FormEvent,
    customerReviewSectionData: any,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    newReview: any,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/customer-review`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              customerReviewsSection: {
                description: customerReviewSectionData.description,
                title: customerReviewSectionData.title,
                reviews: [...customerReviewSectionData.reviews, newReview],
              },
            }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Yorum Ekleme",
          description: "Yorum Bilgileri başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing/customer-review-section");
        setLoading(false);
        closeModal();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const updateCustomerReviewSectionHandler = async (
    event: FormEvent,
    updatedReview: any,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setUpdatedReviewIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/reviews/${updatedReview._id}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ updatedReview }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          setUpdatedReviewIsOpen(false);
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Yorum Silme",
          description: "Seçili yorum başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing/customer-review-section");
        setLoading(false);
        setUpdatedReviewIsOpen(false);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const deleteCustomerReviewSectionHandler = async (
    event: FormEvent,
    customerReviewSectionData: any,
    filteredReviews: any,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/customer-review`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              customerReviewsSection: {
                description: customerReviewSectionData.description,
                title: customerReviewSectionData.title,
                reviews: filteredReviews,
              },
            }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3000,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          setLoading(false);
          closeModal();
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Yorum Silme",
          description: "Seçili yorum başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing/customer-review-section");
        setLoading(false);
        closeModal();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const deleteTeamMemberHandler = async (
    id: any,
    hotelToken: string | undefined
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.hotelLanding}/team-section/${id}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Çalışan Silme",
          description: "Çalışan başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing");
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const heroSectionPhotoInputHandler = async (
    heroSectionData: HeroSectionType,
    photoFileObject: FileList | null,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<React.SetStateAction<FileList | null>>,
    setImage: React.Dispatch<React.SetStateAction<string[] | []>>
  ) => {
    if (hotelToken) {
      setLoading(true);
      if (!photoFileObject) {
        const updatedHeroSectionData = {
          ...heroSectionData,
          images: heroSectionData.images.map((image) => image._id),
        };
        return uploadHeroSectionHandler(
          updatedHeroSectionData,
          hotelToken,
          setLoading,
          setPhotoFileObject,
          setImage
        );
      }

      try {
        // TODO
        const response = await uploadMultiToS3(
          photoFileObject!,
          "testmulti/",
          hotelToken,
          ImageResizeFitType.fill
        );

        if (response.success) {
          setLoading(false);
          const idsArray = response.data.map((item: any) => item._id);
          const currentImageIds = heroSectionData.images.map(
            (image) => image._id
          );
          const updatedHeroSectionData = {
            ...heroSectionData,
            images: [...currentImageIds, ...idsArray],
          };
          uploadHeroSectionHandler(
            updatedHeroSectionData,
            hotelToken,
            setLoading,
            setPhotoFileObject,
            setImage
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const teamMemberPhotoInputHandler = async (
    event: FormEvent,
    teamMemberData: TeamMemberType,
    teamSectionData: TeamSectionType,
    photoFileObject: FileList | null,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setAddTeamMemberModalIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    resetForm: any
  ) => {
    event.preventDefault();
    if (photoFileObject && hotelToken) {
      setLoading(true);
      try {
        const response = await uploadMultiToS3(
          photoFileObject,
          "testmulti/",
          hotelToken,
          ImageResizeFitType.fill
        );

        if (response.success) {
          const idsArray = response.data.map((item: any) => item._id);
          teamMemberData.memberImage = idsArray;

          const updatedSectionData = {
            ...teamSectionData,
            teamMembers: [
              ...(teamSectionData.teamMembers || []),
              {
                ...teamMemberData,
                memberName: teamMemberData.memberName?.trim(),
                memberTitle: teamMemberData.memberTitle?.trim(),
              },
            ],
          };

          uploadTeamSectionHandler(
            event,
            updatedSectionData,
            hotelToken,
            setLoading
          );
          setLoading(false);
          setAddTeamMemberModalIsOpen(false);
          resetForm();
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const deleteHeroSectionPhoto = async (
    event: FormEvent,
    hotelToken: string | undefined,
    imageId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeletePhotoIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.imageUpload}/deleteImage?imageId=${imageId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeletePhotoIsOpen(false);
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Fotoğraf Silme",
          description: "Fotoğraf başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing");
        setDeletePhotoIsOpen(false);
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const deleteTeamMemberPhoto = async (
    event: FormEvent,
    hotelToken: string | undefined,
    item: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeletePhotoIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();

    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.imageUpload}/deleteImage?imageId=${item.memberImage._id}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeletePhotoIsOpen(false);
          setLoading(false);
          throw new Error("Network response was not ok");
        }

        await deleteTeamMemberHandler(item._id, hotelToken);
        toast({
          variant: "success",
          duration: 6000,
          title: "Üye Kaldırma",
          description: "Üye başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/hotel-landing");
        setDeletePhotoIsOpen(false);
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    createHotelLanding,
    heroSectionPhotoInputHandler,
    uploadTeamSectionHandler,
    deleteHeroSectionPhoto,
    deleteTeamMemberPhoto,
    teamMemberPhotoInputHandler,
    addCustomerReviewSectionHandler,
    deleteCustomerReviewSectionHandler,
    updateCustomerReviewSectionHandler,
  };
};
