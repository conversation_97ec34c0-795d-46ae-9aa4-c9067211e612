import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useBalance = () => {
  const { toast } = useToast();

  const addTransaction = async (
    event: FormEvent,
    hotelToken: string | undefined,
    transactionData: any,
    balanceId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    resetValues: () => void
  ) => {
    event.preventDefault();

    const balanceBody =
      transactionData.transactionType === 1
        ? {
            transactionType: transactionData.transactionType,
            amount: Number(transactionData.amount),
            description: transactionData.description,
            paymentType: transactionData.paymentType,
            isManuel: true,
          }
        : {
            transactionType: transactionData.transactionType,
            transaction: transactionData.transaction,
            amount: Number(transactionData.amount),
            description: transactionData.description,
            isManuel: true,
          };

    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.addTransaction, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            balanceId: balanceId,
            transaction: balanceBody,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 4000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          resetValues();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 4000,
          title: "Transaction Ekleme",
          description: "Transaction başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account");
        resetValues();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const removeTransaction = async (
    event: FormEvent,
    balanceId: string,
    transactionId: string,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.removeTransaction}/${balanceId}/${transactionId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 4000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 4000,
          title: "Transaction Silme",
          description: "Transaction başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  return { addTransaction, removeTransaction };
};
