"use client";
import type { FC } from "react";
import React, { useState } from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import IconCancel from "@/shared/icons/Cancel";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import type { Route } from "@/routers/types";
import { useTranslations } from "next-intl";
import type { ImageType } from "@/types/hotel/hotelDataType";
export interface StaffCardProps {
  className?: string;
  images: ImageType;
  hotelToken: string | undefined;
  index: number;
}

const HotelPhotoCard: FC<StaffCardProps> = ({
  className = "",
  images,
  hotelToken,
  index,
}) => {
  const translate = useTranslations("HotelPhotoCard");
  const router = useRouter();
  const thisPathname = usePathname();
  const { deleteHotelPhoto } = useHotelInformationsActions();
  const [deletePhotoIsOpen, setDeletePhotoIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleOpenModalImageGallery = (imageIndex: number) => {
    router.push(
      `${thisPathname}?modal=HOTEL_PHOTOS_SCROLLABLE&photoId=${imageIndex}` as Route
    );
  };

  return (
    <>
      <div
        className={`nc-CardCategory5 group flex flex-col ${className}`}
        data-nc-id="CardCategory5"
      >
        <div
          className={`group aspect-h-3 aspect-w-4 relative h-0 w-full shrink-0 cursor-pointer overflow-hidden rounded-2xl`}
          onClick={() => handleOpenModalImageGallery(index)}
        >
          <Image
            fill
            alt=""
            src={images.src || ""}
            className="size-full rounded-2xl object-cover"
            sizes="(max-width: 400px) 100vw, 400px"
          />
          <span className="absolute inset-0 bg-black bg-opacity-10 opacity-0 transition-opacity group-hover:opacity-100"></span>
        </div>
        <IconCancel
          onClick={() => {
            setDeletePhotoIsOpen(true);
          }}
          className="absolute right-6 top-1 size-6 cursor-pointer rounded-full bg-white text-secondary-6000 duration-200 group-hover:visible group-hover:opacity-100 lg:invisible lg:opacity-0"
        />
      </div>
      <Dialog open={deletePhotoIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            setDeletePhotoIsOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>{translate("hotelRemovePhoto")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteHotelPhoto(
                event,
                hotelToken,
                images._id,
                setLoading,
                setDeletePhotoIsOpen,
                "/hotel/account/hotel-informations/hotel-photos",
                setDisabled
              )
            }
          >
            <div className="mb-4">
              <p className="text-gray-500 dark:text-neutral-200 max-sm:text-center">
                {translate("hotelDeletePhoto")}
              </p>
            </div>
            <div className="flex justify-center gap-5">
              <Button
                className="w-full md:w-1/3"
                variant="outline"
                type="button"
                onClick={() => {
                  setDeletePhotoIsOpen(false);
                }}
              >
                {translate("hotelCancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-full md:w-1/3"
                type="submit"
                disabled={disabled}
              >
                {loading ? <LoadingSpinner /> : translate("hotelDelete")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={() => {
              setDeletePhotoIsOpen(false);
            }}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HotelPhotoCard;
