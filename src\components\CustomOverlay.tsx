"use client";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";

//dialog modal = {false} den<PERSON><PERSON><PERSON> overlay iptal oluyor. bunu bu gibi senaryolarda kullanıyoruz
//{isTransportationModal && <CustomOverlay />} bu şekilde <DialogContent'in hemen üstüne

export default function CustomOverlay({
  isModalOpen,
}: {
  isModalOpen: boolean;
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isModalOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isModalOpen]);

  if (!mounted) return null;

  return createPortal(
    <div className="fixed inset-0 z-[50] bg-black/60 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />,
    document.body
  );
}
