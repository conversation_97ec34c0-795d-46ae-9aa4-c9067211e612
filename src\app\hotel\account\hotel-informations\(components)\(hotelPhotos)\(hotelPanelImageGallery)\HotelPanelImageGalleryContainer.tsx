"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import HotelPanelImageGallery from "./HotelPanelImageGallery";
import type { ImageType } from "@/types/hotel/hotelDataType";

const HotelPanelImageGalleryContainer = ({
  hotelPhotos,
}: {
  hotelPhotos: ImageType[];
}) => {
  const searchParams = useSearchParams();
  const modal = searchParams?.get("modal");

  return (
    <>
      <HotelPanelImageGallery
        isShowModal={modal === "HOTEL_PHOTOS_SCROLLABLE"}
        hotelPhotos={hotelPhotos}
      />
    </>
  );
};

export default HotelPanelImageGalleryContainer;
