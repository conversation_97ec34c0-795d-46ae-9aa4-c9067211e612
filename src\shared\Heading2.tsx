import React from "react";
import type { ReactNode } from "react";

export interface Heading2Props {
  heading?: ReactNode;
  subHeading?: ReactNode;
  className?: string;
  hotelParams?: Record<string, string | string[] | undefined>;
}

const Heading2: React.FC<Heading2Props> = ({
  className = "",
  heading = "Evcil Hayvan Otelleri",
  subHeading,
  hotelParams,
}) => {
  // TODO: dummy data olduğu için pasife alındı şimdilik
  return (
    <div className={`mb-8 lg:mb-16 ${className}`}>
      <h2 className="text-xl font-semibold md:text-4xl">
        {hotelParams?.city + " " + heading}
      </h2>
      {/* {subHeading ? (
        subHeading
      ) : (
        <span className="block text-neutral-500 dark:text-neutral-400 mt-3">
          233 stays
          <span className="mx-2">·</span>
          Aug 12 - 18
          <span className="mx-2">·</span>2 Guests
        </span>
      )} */}
    </div>
  );
};

export default Heading2;
