// to do petTaxi
import React from "react";
import PartnerHeader from "../(components)/(Header)/PartnerHeader";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import ScrollToTop from "@/components/ScrollToTop";
// import HotelFooterSettings from "./account/(components)/HotelFooterSettings";
import PetTaxiSideBar from "@/app/(components)/(Sidebar)/petTaxiSideBar";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";

export default async function HotelLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value || undefined;
  const propertyTypesCookie =
    cookieStore.get("propertyTypes")?.value || undefined;
  const propertyTypes = propertyTypesCookie
    ? JSON.parse(propertyTypesCookie)
    : [];
  const taxiData = await getMyTaxi();

  if (!token) {
    redirect("/");
  }

  return (
    <div>
      <PetTaxiSideBar taxiData={taxiData?.data} propertyTypes={propertyTypes}>
        {/* <PartnerHeader /> */}
        {children}
      </PetTaxiSideBar>
      {/* <HotelFooterSettings /> */}
      <ScrollToTop />
    </div>
  );
}
