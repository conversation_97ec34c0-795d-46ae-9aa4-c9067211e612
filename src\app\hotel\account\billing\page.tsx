import React from "react";
import BillingContainer from "./BillingContainer";
import { cookies } from "next/headers";
import getHotelSubMerchant from "@/actions/(protected)/hotel/getHotelSubMerchant";

const AccountBilling = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelSubMerchantData = await getHotelSubMerchant();

  return (
    <BillingContainer
      hotelToken={hotelToken}
      hotelSubMerchantData={hotelSubMerchantData?.data}
    />
  );
};

export default AccountBilling;
