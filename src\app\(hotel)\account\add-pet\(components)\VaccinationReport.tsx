"use client";
import React, { useRef } from "react";
import type { FC } from "react";
import Image from "next/image";
import Input from "@/shared/Input";
import IconCancelSolid from "@/shared/icons/CancelSolid";
import type { ImageType } from "@/types/petOwner/petTypes";
import VaccinationPhotoCard from "./VaccinationPhotoCard";

interface VaccinationReportProps {
  image: string[] | [];
  setImage: React.Dispatch<React.SetStateAction<string[] | []>>;
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  initialDocuments?: ImageType[];
  hotelToken?: string | undefined;
}

const VaccinationReport: FC<VaccinationReportProps> = ({
  image,
  setImage,
  setPhotoFileObject,
  initialDocuments,
  hotelToken,
}) => {
  const petPhotoInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="mt-7">
      <h2 className="font-semibold text-lg mb-2"><PERSON><PERSON><PERSON><PERSON> (opsiyonel)</h2>
      <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
        <div className="space-y-1 text-center">
          <svg
            className="mx-auto size-12 text-neutral-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
          </svg>
          <div className="text-sm text-neutral-6000 dark:text-neutral-300">
            <label
              htmlFor="file-upload"
              className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
            >
              <span>Aşı Karnesi Görseli Yükle</span>
              <Input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                accept="image/*"
                multiple={true}
                ref={petPhotoInputRef}
                onChange={(e) => {
                  const newFiles = e.target.files; // FileList
                  if (newFiles) {
                    setPhotoFileObject((prevFileObject) => {
                      const dataTransfer = new DataTransfer();
                      // Add previous files
                      if (prevFileObject) {
                        Array.from(prevFileObject).forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                      }
                      // Add new files
                      Array.from(newFiles).forEach((file) =>
                        dataTransfer.items.add(file)
                      );
                      return dataTransfer.files; // Updated FileList
                    });
                    const newImageUrls = Array.from(newFiles).map((file) =>
                      URL.createObjectURL(file)
                    );
                    setImage((prevImages: any) => [
                      ...prevImages,
                      ...newImageUrls,
                    ]);
                  }
                }}
              />
            </label>
          </div>
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            PNG, JPG, GIF Türü Resim
          </p>
        </div>
      </div>
      <div className="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-5">
        {image &&
          image.map((image: any, index: number) => {
            return (
              <div key={index} className="relative">
                <Image
                  src={image}
                  width={350}
                  height={350}
                  alt="Oda fotoğrafı"
                />
                <button
                  className="absolute top-1 right-1"
                  type="button"
                  onClick={() => {
                    setImage((prevImages: any) =>
                      prevImages.filter((_: any, i: any) => i !== index)
                    );
                    setPhotoFileObject((prevFileObject) => {
                      if (prevFileObject) {
                        // Convert FileList to Array
                        const prevFilesArray = Array.from(prevFileObject);
                        // Perform the delete operation
                        const updatedFiles = prevFilesArray.filter(
                          (_, i) => i !== index
                        );
                        // Convert File[] back to FileList
                        const dataTransfer = new DataTransfer();
                        updatedFiles.forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                        return dataTransfer.files; // Updated FileList
                      }
                      return undefined; // Return undefined instead of null
                    });
                    if (petPhotoInputRef.current) {
                      petPhotoInputRef.current.value = ""; // Clear the input
                    }
                  }}
                >
                  <IconCancelSolid className="size-6 rounded-full bg-white text-secondary-6000" />
                </button>
              </div>
            );
          })}
      </div>
      {initialDocuments && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
          {initialDocuments.map((document) => {
            return (
              <VaccinationPhotoCard
                key={document._id}
                document={document}
                hotelToken={hotelToken}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default VaccinationReport;
