import {
  Calendar,
  CreditCard,
  Gift,
  Home,
  MessageSquare,
  Shield,
  Store,
  Wallet,
  Star,
  Users,
  TrendingUp,
} from "lucide-react";
import { LoginScreen } from "./LoginScreen";
import { HeroSection } from "./HeroSection";
import { StatsSection } from "./StatsSection";
import { FeaturesSection } from "./FeaturesSection";
import { MarketplaceSection } from "./MarketPlaceSection";
import { CTASection } from "./CtaAction";

interface PawBookingPartnerHomepageProps {
  mobile?: boolean;
}

export default function PawBookingPartnerHomepage({
  mobile = false,
}: PawBookingPartnerHomepageProps) {
  // Mobile login ekranı
  if (mobile) {
    return <LoginScreen />;
  }

  const features = [
    {
      icon: Calendar,
      title: "Takvim Yönetimi",
      description:
        "Rezervasyonlarınızı ve fiyatlandırmalarınızı kolayca takvim üzerinden yönetin",
      color: "bg-blue-50 text-blue-600",
    },
    {
      icon: Gift,
      title: "Kampanya & Kuponlar",
      description:
        "<PERSON>zel kampanyalar ve indirim kuponları oluşturarak satışlarınızı artırın",
      color: "bg-purple-50 text-purple-600",
    },
    {
      icon: Home,
      title: "Oda Yönetimi",
      description:
        "Tüm odalarınızı kolayca satışa açın ve müsaitlik durumunu güncelleyin",
      color: "bg-green-50 text-green-600",
    },
    {
      icon: CreditCard,
      title: "Güvenli Ödeme",
      description:
        "SSL sertifikalı güvenli online ödeme sistemi ile güvenle tahsilat yapın",
      color: "bg-emerald-50 text-emerald-600",
    },
    {
      icon: MessageSquare,
      title: "Müşteri Yorumları",
      description:
        "Müşteri yorumlarını yönetin ve remarketing kampanyaları düzenleyin",
      color: "bg-orange-50 text-orange-600",
    },
    {
      icon: Store,
      title: "Kapıda Rezervasyon",
      description: "Walk-in müşteriler için anında rezervasyon alma imkanı",
      color: "bg-pink-50 text-pink-600",
    },
    {
      icon: Wallet,
      title: "Ödeme Planları",
      description:
        "Esnek ödeme planları oluşturun ve müşterilerinize kolaylık sağlayın",
      color: "bg-indigo-50 text-indigo-600",
    },
    {
      icon: Shield,
      title: "Güvenlik & Destek",
      description:
        "7/24 teknik destek ve güvenli altyapı ile hizmetinizi kesintisiz sürdürün",
      color: "bg-red-50 text-red-600",
    },
  ];

  const stats = [
    { icon: Users, value: "10,000+", label: "Aktif Partner" },
    { icon: Star, value: "4.9", label: "Ortalama Puan" },
    { icon: TrendingUp, value: "%85", label: "Gelir Artışı" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50">
      <HeroSection />
      <StatsSection />
      <MarketplaceSection />
      <FeaturesSection />
      <CTASection />
    </div>
  );
}
