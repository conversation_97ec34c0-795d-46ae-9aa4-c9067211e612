"use client";
import React from "react";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
  DialogFooter,
} from "@/components/ui/dialog";
import { MessageSquarePlus } from "lucide-react";
import HotelAvatar from "@/shared/HotelAvatar";
import defaultHotelImage from "@/images/default-hotel-photo.webp";
import { useRouter } from "next/navigation";
import { useSidebar } from "@/components/ui/sidebar";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import {
  setIsLoading,
  setText,
  setIsEdit,
  setIsNewChatOpen,
} from "@/store/features/liveChat/live-chat-slice";
import { Button } from "@/components/ui/button";

interface NewChatContainerProps {
  hotelList: any;
  isCenter?: boolean;
  selectedId: string | string[] | undefined;
}

const NewChatContainer: FC<NewChatContainerProps> = ({
  hotelList,
  isCenter = false,
  selectedId,
}) => {
  // dialog içinde Link componenti developmentta consoleda error verdiği için router kullanıldı
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const dispatch = useDispatch();
  const isNewChatOpen = useSelector(
    (state: RootState) => state.liveChatText.isNewChatOpen
  );

  const openChatHandler = (hotelId: string) => {
    setOpenMobile(false);
    if (selectedId === hotelId) return;

    router.push(`/hotel/account/messages?id=${hotelId}`);
    dispatch(setIsLoading(true));
    dispatch(setIsEdit(false));
    dispatch(setText(""));
    dispatch(setIsNewChatOpen(false));
  };

  return (
    <Dialog
      open={isNewChatOpen}
      onOpenChange={(open) => dispatch(setIsNewChatOpen(open))}
    >
      <DialogTrigger onClick={() => dispatch(setIsNewChatOpen(true))}>
        {isCenter ? (
          <div
            className={`flex items-center rounded-sm text-sm font-medium cursor-pointer hover:bg-[#f4f4f5] dark:hover:bg-[#27272a] absolute right-3 bottom-36 z-20`}
          >
            <div className="bg-sky-600 p-2 rounded-full w-14 h-14 flex justify-center items-center">
              <MessageSquarePlus className="size-8 text-white" />
            </div>
          </div>
        ) : (
          <>
            <MessageSquarePlus className="size-7 hover:scale-105 hover:text-sky-700" />
          </>
        )}
      </DialogTrigger>
      <DialogContent className="flex flex-col gap-0 p-0 max-h-[calc(100vh-50px)] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="border-b px-6 py-4 text-base">
            Yeni Sohbet
          </DialogTitle>
          <DialogDescription className="sr-only"></DialogDescription>
        </DialogHeader>
        <div className="overflow-y-auto grow space-y-3 px-6 py-4">
          {hotelList?.map((hotel: any) => (
            <DialogClose key={hotel?._id} asChild>
              <div
                onClick={() => {
                  openChatHandler(hotel?._id);
                }}
                className="text-sm hover:bg-sky-700 hover:text-white p-2 rounded cursor-pointer capitalize flex items-center gap-2"
              >
                <HotelAvatar
                  sizeClass="w-8 h-8"
                  imgUrl={hotel?.logo?.img200?.src || defaultHotelImage}
                  logo={hotel?.logo?.img200?.src || defaultHotelImage}
                />
                <span className="font-medium">{hotel?.hotelName}</span>
              </div>
            </DialogClose>
          ))}
        </div>
        <DialogFooter className="border-t px-6 py-4 md:hidden">
          <DialogClose asChild>
            <Button type="button" variant="outline" className="w-full">
              Kapat
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NewChatContainer;
