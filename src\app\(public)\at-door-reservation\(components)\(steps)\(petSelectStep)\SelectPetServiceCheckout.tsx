"use client";
import React from "react";
import type { FC } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Label from "@/components/Label";
import Image from "next/image";
import catAvatar from "@/images/cat-avatar.png";
import dogAvatar from "@/images/dog-avatar.png";
import petAvatar from "@/images/avatars/paw.png";
import IconPlus from "@/shared/icons/Plus";
import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useTranslations } from "next-intl";
import type { Route } from "@/routers/types";
import { Separator } from "@/components/ui/separator";

interface SelectPetServiceCheckoutProps {
  hotelCustomerPetData: any;
  orderId: string;
  hotelCustomerId: string | null;
  petTypes: string[];
  multiple?: boolean;
  index?: number;
  serviceSelectedPetArray: string[] | [];
  serviceName?: string;
}

const SelectPetServiceCheckout: FC<SelectPetServiceCheckoutProps> = ({
  hotelCustomerPetData,
  orderId,
  hotelCustomerId,
  petTypes,
  multiple,
  index,
  serviceSelectedPetArray,
  serviceName,
}) => {
  const translate = useTranslations("AddPet");
  const dispatch = useDispatch();
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const selectPetHandler = (selectedPet: string) => {
    const updatedPetArray = [...checkoutData.servicePet];
    const petIndex = multiple && index ? index : 0;

    updatedPetArray[petIndex] = selectedPet;

    dispatch(
      setCheckoutData({
        ...checkoutData,
        hotelCustomer: hotelCustomerId,
        servicePet: updatedPetArray,
      })
    );
  };

  const valueHandler = () => {
    if (multiple && index) return checkoutData.servicePet[index] || "";
    return checkoutData.servicePet[0] || "";
  };

  const selectedValue = valueHandler();

  const addPetPathHandler = () => {
    return `/at-door-reservation?orderId=${orderId}&addPet=true&petTypes=${petTypes}`;
  };
  const addPetPath = addPetPathHandler();

  return (
    <div>
      {serviceName && (
        <>
          <div className="flex items-center gap-1 font-semibold">
            <p className="font-semibold">Hizmet adı:</p>
            <p className="text-lg font-semibold capitalize">{serviceName}</p>
          </div>
          <Separator className="mt-1 mb-2 w-20 dark:bg-white" />
        </>
      )}
      <h2 className="font-semibold text-lg">Evcil Hayvan Seçimi - Hizmet</h2>
      <p className="text-sm mb-2">
        Lütfen hizmet almak istediğiniz evcil hayvanınızı seçiniz.{" "}
        <span className="font-semibold">
          Seçtiğiniz hizmete uygun evcil hayvanlarınız listelenmektedir.
        </span>
        <span className="flex max-md:flex-col gap-1 font-semibold mt-2">
          <span>Hizmet türü:</span>
          <span className="font-medium">
            {petTypes?.map((petType) => translate(petType)).join(", ")}
          </span>
        </span>
      </p>
      <RadioGroup
        id={`select-pet-radio-service-${index}`}
        onValueChange={(selected: string) => selectPetHandler(selected)}
        value={selectedValue || serviceSelectedPetArray[index || 0] || ""}
        className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-6 gap-4 mt-10"
      >
        {hotelCustomerPetData
          ?.filter((pet: any) => petTypes?.includes(pet?.kind))
          ?.map((pet: any) => {
            const petPhoto =
              pet.kind === "cat"
                ? catAvatar
                : pet.kind === "smallDogBreed" ||
                    pet.kind === "largeDogBreed" ||
                    pet.kind === "mediumDogBreed"
                  ? dogAvatar
                  : petAvatar;
            const petPhotos =
              pet?.images?.length > 0 && pet?.images[0]?.src
                ? pet.images[0].src
                : petPhoto;

            return (
              <div key={pet._id} className="hover:scale-105 duration-200">
                <RadioGroupItem
                  value={pet._id}
                  id={`${pet._id}-${index}-service`}
                  className="peer sr-only"
                />
                <Label
                  htmlFor={`${pet._id}-${index}-service`}
                  className={`cursor-pointer capitalize flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary`}
                >
                  <Image src={petPhotos} width={100} height={100} alt="" />
                  <span className="mt-2">{pet.name}</span>
                </Label>
              </div>
            );
          })}
        <div className="hover:scale-105 duration-200">
          <RadioGroupItem
            value={`add-new-pet-${index}`}
            id={`add-new-pet-${index}`}
            className={`peer sr-only disabled:opacity-10`}
          />
          <Link href={addPetPath as Route}>
            <Label
              htmlFor={`add-new-pet-${index}`}
              className="h-full capitalize flex flex-col items-center justify-center gap-1 rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary !cursor-pointer"
            >
              <span>Yeni ekle</span>
              <IconPlus />
            </Label>
          </Link>
        </div>
      </RadioGroup>
      {!multiple && <Separator className="mt-5" />}
    </div>
  );
};

export default SelectPetServiceCheckout;
