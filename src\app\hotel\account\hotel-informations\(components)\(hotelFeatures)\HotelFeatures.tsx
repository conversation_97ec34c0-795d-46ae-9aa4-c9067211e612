"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import Checkbox from "@/shared/Checkbox";
import { Button } from "@/components/ui/button";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelFeaturesProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

interface Feature {
  label: string;
  name: string;
  id: string;
}

const HotelFeatures: FC<HotelFeaturesProps> = ({ hotelData, hotelToken }) => {
  const translate = useTranslations("HotelFeatures");
  const [hotelFeatures, setHotelFeatures] = useState<string[]>(
    hotelData.hotelFeatures || []
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handleHotelFeatures } = useHotelInformationsActions();

  const features: {
    category: string;
    description: string;
    features: Feature[];
  }[] = [
    {
      category: translate("FeaturesCategories.healthAndSafety"),
      description: translate("FeaturesCategories.healthAndSafetyDescription"),
      features: [
        {
          label: translate("FeaturesLabels.veterinaryServices"),
          name: "veterinaryServices",
          id: "veterinary-services",
        },
        {
          label: translate("FeaturesLabels.cameraSurveillance"),
          name: "cameraSurveillance",
          id: "camera-surveillance",
        },
        {
          label: translate("FeaturesLabels.securityCameras"),
          name: "securityCameras",
          id: "security-cameras",
        },
        {
          label: translate("FeaturesLabels.emergencyEquipment"),
          name: "emergencyEquipment",
          id: "emergency-equipment",
        },
        {
          label: translate("FeaturesLabels.hygienicToiletAreas"),
          name: "hygienicToiletAreas",
          id: "hygienic-toilet-areas",
        },
      ],
    },
    {
      category: translate("FeaturesCategories.comfortAndAccommodation"),
      description: translate(
        "FeaturesCategories.comfortAndAccommodationDescription"
      ),
      features: [
        {
          label: translate("FeaturesLabels.climateControl"),
          name: "climateControl",
          id: "climate-control",
        },
        {
          label: translate("FeaturesLabels.individualRooms"),
          name: "individualRooms",
          id: "individual-rooms",
        },
        {
          label: translate("FeaturesLabels.comfortableBeds"),
          name: "comfortableBeds",
          id: "comfortable-beds",
        },
        {
          label: translate("FeaturesLabels.indoorShelters"),
          name: "indoorShelters",
          id: "indoor-shelters",
        },
        {
          label: translate("FeaturesLabels.outdoorShelters"),
          name: "outdoorShelters",
          id: "outdoor-shelters",
        },
      ],
    },
    {
      category: translate("FeaturesCategories.activitiesAndEntertainment"),
      description: translate(
        "FeaturesCategories.activitiesAndEntertainmentDescription"
      ),
      features: [
        {
          label: translate("FeaturesLabels.playAreas"),
          name: "playAreas",
          id: "play-areas",
        },
        {
          label: translate("FeaturesLabels.indoorPlayAreas"),
          name: "indoorPlayAreas",
          id: "indoor-play-areas",
        },
        {
          label: translate("FeaturesLabels.gardenArea"),
          name: "gardenArea",
          id: "garden-area",
        },
        {
          label: translate("FeaturesLabels.trainingField"),
          name: "trainingField",
          id: "training-field",
        },
        {
          label: translate("FeaturesLabels.playPool"),
          name: "playPool",
          id: "play-pool",
        },
        {
          label: translate("FeaturesLabels.socializationActivities"),
          name: "socializationActivities",
          id: "socialization-activities",
        },
      ],
    },
    {
      category: translate("FeaturesCategories.extraServices"),
      description: translate("FeaturesCategories.extraServicesDescription"),
      features: [
        {
          label: translate("FeaturesLabels.liveCameraAccess"),
          name: "liveCameraAccess",
          id: "live-camera-access",
        },
        {
          label: translate("FeaturesLabels.photoUpdates"),
          name: "photoUpdates",
          id: "photo-updates",
        },
        {
          label: translate("FeaturesLabels.specialDietMeals"),
          name: "specialDietMeals",
          id: "special-diet-meals",
        },
        {
          label: translate("FeaturesLabels.petSpa"),
          name: "petSpa",
          id: "pet-spa",
        },
        {
          label: translate("FeaturesLabels.pickupDropoff"),
          name: "pickupDropoff",
          id: "pickup-dropoff",
        },
      ],
    },
  ];

  // adds hotel features to an array and removes
  const hotelFeaturesHandler = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      setHotelFeatures([...hotelFeatures, name]);
    } else {
      setHotelFeatures(hotelFeatures.filter((feature) => feature !== name));
    }
  };

  const sortedInitial = [...hotelData.hotelFeatures].sort();
  const sortedCurrent = [...hotelFeatures].sort();

  return (
    <form
      className="mt-4 md:mt-8"
      onSubmit={(event) => {
        handleHotelFeatures(
          event,
          hotelToken,
          hotelFeatures,
          setLoading,
          setDisabled
        );
      }}
    >
      <div className="space-y-6">
        <div>
          <label className="block mb-8">
            <div className="">
              <h2 className="text-2xl font-semibold leading-8">
                {translate("hotelFeatures")}
              </h2>
              <span className="text-sm text-neutral-500 dark:text-neutral-300">
                Bu alanda seçilen otel özellikleri otel detay sayfanızda
                barındırdığınız özellikler olarak gösterilecektir.
              </span>
            </div>
          </label>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
            {features?.map((feature) => (
              <div
                key={feature.category}
                className="space-y-4 bg-white dark:bg-neutral-800 shadow-md rounded-lg px-8 py-6"
              >
                <div className="">
                  <h2 className="text-[18px] font-semibold leading-8">
                    {feature?.category}
                  </h2>
                  <span className="text-sm text-neutral-500 dark:text-neutral-300">
                    {feature?.description}
                  </span>
                </div>

                <div className="space-y-3">
                  {feature.features.map((item) => (
                    <Checkbox
                      key={item.id}
                      id={item.id}
                      label={item.label}
                      name={item.name}
                      checked={hotelFeatures.includes(item.name)}
                      onChange={hotelFeaturesHandler}
                    />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            disabled ||
            JSON.stringify(sortedInitial) === JSON.stringify(sortedCurrent)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </form>
  );
};

export default HotelFeatures;
