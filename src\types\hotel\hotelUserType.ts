export interface HotelUserInformationApiTypes {
  success: boolean;
  data: {
    docs: UserInformationApiTypes[];
    page: number;
    pageCount: number;
    pageSize: number;
    totalDocs: number;
  };
}

export interface UserInformationApiTypes {
  _id: string;
  hotel: string;
  bio: string;
  dateOfBirth: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  fullName: string;
  gender: string;
  phone: string;
  role: string;
  image: string | null;
  permissions: {
    allocations: Record<string, any>;
    rooms: Record<string, any>;
    petOwnerHistory: Record<string, any>;
    petHistory: Record<string, any>;
    additionalServices: Record<string, any>;
    [key: string]: any;
  };
  passive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface HotelUserTypes {
  firstName: string;
  lastName: string;
  username: string;
  gender: string;
  password: string;
  email: string;
  dateOfBirth: string;
  phone: string;
  bio: string;
}
