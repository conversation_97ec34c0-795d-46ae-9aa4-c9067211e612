"use client";
import type { FC } from "react";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import IconEdit from "@/shared/icons/Edit";
import IconInfo from "@/shared/icons/Info";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import { Switch } from "@/components/ui/switch";
import UpdateRoomPrice from "./UpdateRoomPrice";
import PriceSummary from "./PriceSummary";
import RoomSelect from "./RoomSelect";
import { useTranslations } from "next-intl";
import UpdateMultipleRoomPrice from "./UpdateMultipleRoomPrice";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { PET_TYPES } from "@/app/(enums)/enums";

interface HotelCalendarSideBarRangeProps {
  open: boolean;
  startDate: Date | undefined;
  endDate: Date | undefined;
  hotelToken: string | undefined;
  setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>;
  minPrice: number | undefined;
  maxPrice: number | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  dateStatues: boolean[];
  hasUndefinedPrice: boolean;
  inRangePrices: number[] | [];
  roomGroupData: any;
}

const HotelCalendarSideBarRange: FC<HotelCalendarSideBarRangeProps> = ({
  open,
  startDate,
  endDate,
  hotelToken,
  setFetchAgain,
  minPrice,
  maxPrice,
  setEndDate,
  setStartDate,
  dateStatues,
  hasUndefinedPrice,
  inRangePrices,
  roomGroupData,
}) => {
  const translate = useTranslations("HotelCalendarSideBarRange");

  const falseClass = "translate-x-[700px] invisible";
  const trueClass = "translate-x-0 visible";
  const selectedRoomId = useSelector(
    (state: RootState) => state.hotelCalendar.selectedRoomId
  );
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRoomData, setSelectedRoomData] = useState<any>(null);
  const [checked, setChecked] = useState(false);
  const [isSummaryOpen, setIsSummaryOpen] = useState<boolean>(false);
  const { fetchSelectedRoomData, calendarDateStatusHandler } =
    useHotelCalendarActions();

  function openEditPrice() {
    setIsOpen(true);
  }

  function closeEditPrice() {
    setIsOpen(false);
  }

  // Adjusted start and end date
  const adjustedStartDate = adjustDateToTimezone(startDate);
  const adjustedEndDate = adjustDateToTimezone(endDate);
  const startDateToString = adjustedStartDate
    ? adjustedStartDate.toISOString()
    : undefined;
  const endDateToString = adjustedEndDate
    ? adjustedEndDate.toISOString()
    : undefined;

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchSelectedRoomData(hotelToken, selectedRoomId);
      setSelectedRoomData(data.data);
    };
    fetchData();
  }, [selectedRoomId, hotelToken, fetchSelectedRoomData]);

  //if one of the dates is passive sets switch off
  useEffect(() => {
    if (dateStatues.includes(true)) {
      setChecked(false);
    } else {
      setChecked(true);
    }
  }, [dateStatues]);

  const displayCustomerTotalPrice = () => {
    if (minPrice && maxPrice) {
      const sumArray = inRangePrices.reduce(
        (accumulator, currentValue) => accumulator + currentValue,
        0
      );
      return sumArray;
    }
  };

  const petTypeNames = selectedRoomData?.petType
    .map((petType: any) => PET_TYPES[petType as keyof typeof PET_TYPES])
    .filter(Boolean); // eskiden kalan sadece dog tipi undefined dönüyor onu filterlamak için

  return (
    <>
      {selectedRoomData && (
        <div className="relative max-w-md overflow-hidden px-3 pt-5 text-sm">
          <div
            className={`h-full space-y-5 overflow-auto overflow-x-hidden px-3 pb-10 pt-2 duration-500  ${
              open ? falseClass : trueClass
            }`}
          >
            <RoomSelect roomGroupData={roomGroupData} />
            <UpdateMultipleRoomPrice
              roomGroupData={roomGroupData}
              hotelToken={hotelToken}
              setFetchAgain={setFetchAgain}
            />
            <h2 className="w-96 text-2xl font-semibold capitalize underline">
              {selectedRoomData?.roomName}
            </h2>
            {/* <div className="border rounded-lg border-gray-300 p-3">
              <div className="font-semibold mb-2">
                Ortalama günlük Oda fiyatı
              </div>
              <div className="text-xl font-semibold">{500}₺</div>
            </div> */}
            <div className="rounded-lg border border-gray-300  p-3">
              <div className="mb-2 font-semibold">
                {translate("roomCapacity")}
              </div>
              <div className="text-xl font-semibold">
                {selectedRoomData?.roomCapacity}
              </div>
            </div>
            <div className="rounded-lg border border-gray-300  p-3">
              <div className="mb-2 font-semibold">{translate("petType")}</div>
              <div className="font-semibold capitalize">
                {petTypeNames.map((pet: any, index: number) => {
                  return (
                    <p key={index}>
                      {pet}
                      {index < petTypeNames.length - 1 && <span>,&nbsp;</span>}
                    </p>
                  );
                })}
              </div>
            </div>
            {/* <div className="rounded-lg border border-gray-300  p-3">
              <div className="mb-2 font-semibold">{translate("roomType")}</div>
              <div className="text-xl font-semibold capitalize">
                {selectedRoomData?.roomType}
              </div>
            </div> */}
          </div>
          <div
            className={`absolute top-0 size-full space-y-5 overflow-auto bg-neutral-50 pl-3 pr-10 pt-5 duration-500 dark:bg-neutral-900 ${
              open ? trueClass : falseClass
            }`}
          >
            <div>
              <div className="text-end">
                <button
                  onClick={() => {
                    setStartDate(undefined);
                    setEndDate(undefined);
                  }}
                  type="button"
                  className="text-right underline duration-300 hover:text-secondary-6000"
                >
                  {translate("clearSelection")}
                </button>
              </div>
              {!hasUndefinedPrice && (
                <div className="my-5 flex items-center gap-5">
                  <Switch
                    className="data-[state=checked]:bg-[#58d68d] data-[state=unchecked]:bg-[#6c757d]"
                    checked={checked}
                    onClick={() => {
                      setChecked(!checked);
                      calendarDateStatusHandler(
                        hotelToken,
                        startDateToString,
                        endDateToString,
                        selectedRoomId,
                        setFetchAgain,
                        checked
                      );
                    }}
                  />
                  <div>
                    {checked
                      ? translate("closetoReservations")
                      : translate("opentoReservations")}
                  </div>
                </div>
              )}

              <div className="mb-5 flex items-center text-lg font-medium underline">
                <div>
                  {adjustedStartDate &&
                    adjustedStartDate.toLocaleDateString(translate("locale"), {
                      month: "short",
                      day: "2-digit",
                    })}
                </div>
                <div>-</div>
                <div>
                  {adjustedEndDate &&
                    adjustedEndDate.toLocaleDateString(translate("locale"), {
                      month: "short",
                      day: "2-digit",
                    })}
                </div>
              </div>

              <div
                onClick={openEditPrice}
                className="cursor-pointer rounded-lg border border-gray-300 p-3"
              >
                {minPrice ? (
                  <div className="text-2xl font-semibold">
                    {minPrice === maxPrice
                      ? new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(maxPrice))
                      : new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(minPrice)) +
                        "-" +
                        new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(maxPrice))}
                    ₺
                  </div>
                ) : (
                  <div className="h-8" />
                )}
              </div>
              <div
                onClick={openEditPrice}
                className="mt-2 flex cursor-pointer items-center justify-center gap-0.5 text-center text-xs underline duration-300 hover:text-secondary-6000"
              >
                {translate("changePrice")}
                <IconEdit className="size-4" />
              </div>
              <div
                onClick={() => setIsSummaryOpen(true)}
                className="mt-5 cursor-pointer rounded-lg border border-gray-300 p-3 text-sm"
              >
                <div className="text-center font-medium text-neutral-700 dark:text-neutral-400">
                  {translate("totalPrice")}
                </div>
                <div className="mt-2 text-center text-2xl font-semibold">
                  {displayCustomerTotalPrice() &&
                    new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(
                      Number(
                        (Number(displayCustomerTotalPrice()) * 1.18).toFixed(2)
                      )
                    ) + "₺"}
                </div>
              </div>
              <div
                onClick={() => setIsSummaryOpen(true)}
                className="mt-2 flex cursor-pointer items-center justify-center gap-0.5 text-center text-xs underline duration-300 hover:text-secondary-6000"
              >
                {translate("clickForDetails")}
                <IconInfo className="size-4" />
              </div>
            </div>
          </div>
        </div>
      )}
      <PriceSummary
        isSummaryOpen={isSummaryOpen}
        setIsSummaryOpen={setIsSummaryOpen}
        displayCustomerTotalPrice={displayCustomerTotalPrice}
        nightCount={inRangePrices.length}
      />
      <UpdateRoomPrice
        isOpen={isOpen}
        closeEditPrice={closeEditPrice}
        hotelToken={hotelToken}
        startDateToString={startDateToString}
        endDateToString={endDateToString}
        selectedRoomId={selectedRoomId}
        setFetchAgain={setFetchAgain}
      />
    </>
  );
};

export default HotelCalendarSideBarRange;
