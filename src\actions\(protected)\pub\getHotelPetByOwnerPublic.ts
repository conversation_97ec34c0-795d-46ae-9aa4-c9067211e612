"use server";
import { redirect } from "next/navigation";
import { PUBLIC_API_PATHS } from "@/utils/apiUrls";

export async function getHotelPetByOwnerPublic(owner: string | null) {
  try {
    const url = `${PUBLIC_API_PATHS.getHotelPetByOwner}/${owner}`;
    const response = await fetch(url, {
      cache: "no-cache",
      headers: {
        "Content-Type": "application/json",
      },
    });
    const result = await response.json();

    if (result.status === 401) {
      redirect("/");
    } else if (result.status === 404) {
      redirect("/404");
    }

    if (!response.ok || !result.success) {
      console.error("Network response was not ok");
      return undefined;
    }
    return result;
  } catch (err: unknown) {
    console.error("Error fetching data:", err);
    return undefined;
  }
}
