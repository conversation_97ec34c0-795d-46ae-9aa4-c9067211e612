"use client";
import React from "react";
import type { FC, ChangeEvent } from "react";
import FormItem from "@/shared/FormItem";
import Textarea from "@/shared/Textarea";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";

interface PetHabitsProps {
  petInformations: PetInformationTypes;
  setPetInformations: React.Dispatch<React.SetStateAction<PetInformationTypes>>;
}

const PetHabits: FC<PetHabitsProps> = ({
  petInformations,
  setPetInformations,
}) => {
  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setPetInformations((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  return (
    <div className="mt-7">
      <h2 className="font-semibold text-lg mb-2">
        <PERSON>v<PERSON><PERSON>lıkları (opsiyonel)
      </h2>
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 mb-10 gap-3">
        <FormItem className="max-w-96" label="Alışkanlıklar (opsiyonel)">
          <Textarea
            name="specified"
            onChange={handleChange}
            value={petInformations.specified}
          />
        </FormItem>
        <FormItem
          className="max-w-96"
          label="Beslenme Alışkanlıkları (opsiyonel)"
        >
          <Textarea
            name="feedingHabits"
            onChange={handleChange}
            value={petInformations.feedingHabits}
          />
        </FormItem>
        <FormItem className="max-w-96" label="Açıklama (opsiyonel)">
          <Textarea
            name="description"
            onChange={handleChange}
            value={petInformations.description}
          />
        </FormItem>
      </div>
    </div>
  );
};

export default PetHabits;
