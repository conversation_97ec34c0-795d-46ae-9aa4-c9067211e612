import React from "react";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface TransportationDetailProps {
  servicesSoldData: any;
}

const TransportationDetail: FC<TransportationDetailProps> = ({
  servicesSoldData,
}) => {
  return (
    <>
      <section>
        <h3 className="text-lg font-medium mb-2">🚕 Hizmet Bilgileri</h3>
        <ul className="space-y-1">
          <li>
            Hizmet Adı:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceName || "-"}
            </span>
          </li>
          <li>
            Hizmet Tarihi:{" "}
            <span className="font-semibold">
              {formatDateToDayMonthYear(servicesSoldData?.serviceDate) || "-"}
            </span>
          </li>
          <li>
            Başlangıç Noktası:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.startPoint || "-"}
            </span>
          </li>
          <li>
            Varış Noktası:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.endPoint || "-"}
            </span>
          </li>
          <li>
            Mesafe:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.distance
                ? `${servicesSoldData.serviceDetails.distance} km`
                : "-"}
            </span>
          </li>
          <li>
            Açık Adres:{" "}
            <span className="font-semibold">
              {servicesSoldData?.note || "-"}
            </span>
          </li>
        </ul>
      </section>
      <Separator />
    </>
  );
};

export default TransportationDetail;
