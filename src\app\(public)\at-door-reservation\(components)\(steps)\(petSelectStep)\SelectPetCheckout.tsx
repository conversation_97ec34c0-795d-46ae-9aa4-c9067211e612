"use client";
import React from "react";
import type { FC } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Label from "@/components/Label";
import Image from "next/image";
import catAvatar from "@/images/cat-avatar.png";
import dogAvatar from "@/images/dog-avatar.png";
import petAvatar from "@/images/avatars/paw.png";
import IconPlus from "@/shared/icons/Plus";
import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useTranslations } from "next-intl";
import type { Route } from "@/routers/types";
import { Separator } from "@/components/ui/separator";

interface SelectPetCheckoutProps {
  hotelCustomerPetData: any;
  orderId: string;
  hotelCustomerId: string | null;
  petTypes: string[];
  multiple?: boolean;
  index?: number;
  reservationSelectedPetArray: string[] | [];
}

const SelectPetCheckout: FC<SelectPetCheckoutProps> = ({
  hotelCustomerPetData,
  orderId,
  hotelCustomerId,
  petTypes,
  multiple,
  index,
  reservationSelectedPetArray,
}) => {
  const translate = useTranslations("AddPet");
  const dispatch = useDispatch();
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const selectPetHandler = (selectedPet: string) => {
    const updatedPetArray = [...checkoutData.pet];
    const petIndex = multiple && index ? index : 0;

    updatedPetArray[petIndex] = selectedPet;

    dispatch(
      setCheckoutData({
        ...checkoutData,
        hotelCustomer: hotelCustomerId,
        pet: updatedPetArray,
      })
    );
  };

  const valueHandler = () => {
    if (multiple && index) return checkoutData.pet[index] || "";
    return checkoutData.pet[0] || "";
  };

  const selectedValue = valueHandler();

  const addPetPathHandler = () => {
    return `/at-door-reservation?orderId=${orderId}&addPet=true&petTypes=${petTypes}`;
  };
  const addPetPath = addPetPathHandler();

  return (
    <div>
      <h2 className="font-semibold text-lg">Evcil Hayvan Seçimi - Oda</h2>
      <p className="text-sm mb-2">
        Lütfen rezervasyon yapmak istediğiniz evcil hayvanınızı seçiniz.{" "}
        <span className="font-semibold">
          Sadece seçili oda türüne uygun evcil hayvanlarınızı seçebilirsiniz.
        </span>
        <span className="flex max-md:flex-col gap-1 font-semibold mt-2">
          <span>Oda türü:</span>
          <span className="font-medium">
            {petTypes?.map((petType) => translate(petType)).join(", ")}
          </span>
        </span>
      </p>

      <RadioGroup
        id={`select-pet-radio-reservation-${index}`}
        onValueChange={(selected: string) => selectPetHandler(selected)}
        value={selectedValue || reservationSelectedPetArray[index || 0] || ""}
        className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-6 gap-4 mt-10"
      >
        {hotelCustomerPetData?.map((pet: any) => {
          const petPhoto =
            pet.kind === "cat"
              ? catAvatar
              : pet.kind === "smallDogBreed" ||
                  pet.kind === "largeDogBreed" ||
                  pet.kind === "mediumDogBreed"
                ? dogAvatar
                : petAvatar;
          const petPhotos =
            pet?.images?.length > 0 && pet?.images[0]?.src
              ? pet.images[0].src
              : petPhoto;

          const isSelectedElsewhere =
            checkoutData.pet.includes(pet._id) &&
            checkoutData.pet[index || 0] !== pet._id;

          return (
            <div key={pet._id} className="hover:scale-105 duration-200">
              <RadioGroupItem
                value={pet._id}
                id={`${pet._id}-${index}-reservation`}
                className="peer sr-only"
                disabled={isSelectedElsewhere || !petTypes?.includes(pet?.kind)}
              />
              <Label
                htmlFor={`${pet._id}-${index}-reservation`}
                className={`cursor-pointer ${isSelectedElsewhere || !petTypes?.includes(pet?.kind) ? "opacity-50 !cursor-not-allowed" : ""} capitalize flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary`}
              >
                <span className="size-28 relative">
                  <Image src={petPhotos} className="object-cover" fill alt="" />
                </span>
                <span className="mt-2">{pet.name}</span>
              </Label>
            </div>
          );
        })}
        <div className="hover:scale-105 duration-200">
          <RadioGroupItem
            value={`add-new-pet-${index}`}
            id={`add-new-pet-${index}`}
            className={`peer sr-only disabled:opacity-10`}
          />
          <Link href={addPetPath as Route}>
            <Label
              htmlFor={`add-new-pet-${index}`}
              className="h-full capitalize flex flex-col items-center justify-center gap-1 rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary !cursor-pointer"
            >
              <span>Yeni ekle</span>
              <IconPlus />
            </Label>
          </Link>
        </div>
      </RadioGroup>
      {!multiple && <Separator className="mt-5" />}
    </div>
  );
};

export default SelectPetCheckout;
