"use client";
import React from "react";
import type { FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import type { Route } from "@/routers/types";

interface ReservationFilterNavProps {
  tab: string | string[];
  reservationPage: number;
}
interface NavItem {
  name: string;
  path: string;
  id: number;
  tabName: string;
}

const ResevationFilterNavMobile: FC<ReservationFilterNavProps> = ({
  tab,
  reservationPage,
}) => {
  const router = useRouter();
  const navList: NavItem[] = [
    {
      name: "Tümü",
      path: "/hotel/account/reservations",
      id: 1,
      tabName: "all",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      path: `/hotel/account/reservations?tab=waitingForAction&page=${reservationPage}`,
      id: 2,
      tabName: "waitingForAction",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      path: `/hotel/account/reservations?tab=currentInHotel&page=${reservationPage}`,
      id: 3,
      tabName: "currentInHotel",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      path: `/hotel/account/reservations?tab=tomorrowsCheckins&page=${reservationPage}`,
      id: 4,
      tabName: "tomorrowsCheckins",
    },
  ];

  return (
    <div>
      <Select
        onValueChange={(value) => {
          const selectedItem = navList.find((item) => item.tabName === value);
          if (selectedItem) {
            router.push(selectedItem.path as Route);
          }
        }}
        value={Array.isArray(tab) ? tab[0] : tab || "all"}
      >
        <SelectTrigger className="w-[180px] mb-5">
          <SelectValue placeholder="Filtre" />
        </SelectTrigger>
        <SelectContent>
          {navList.map((navItem) => {
            return (
              <SelectItem key={navItem.id} value={navItem.tabName}>
                {navItem.name}
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ResevationFilterNavMobile;
