import type { S3Image } from "../../dbModels/s3Image";

export interface HeroSectionType {
  title?: string;
  description?: string;
  images: S3Image[] | any[];
}
export interface CustomerReviewType {
  customerName: string;
  review: string;
  city: string;
  _id?: string;
}

export interface CustomerReviewsSectionType {
  title?: string;
  description?: string;
  reviews?: CustomerReviewType[];
}

export interface TeamMemberType {
  memberName?: string;
  memberTitle?: string;
  memberImage: S3Image | null;
}

export interface TeamSectionType {
  title?: string;
  description?: string;
  teamMembers?: TeamMemberType[];
}

export interface HotelLandingType {
  _id?: string;
  hotel?: any | string;
  heroSection?: HeroSectionType;
  customerReviewsSection?: CustomerReviewsSectionType;
  teamSection?: TeamSectionType;
}
