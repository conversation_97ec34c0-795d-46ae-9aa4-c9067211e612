"use client";
import React, { useEffect } from "react";
import TodayRoomCard from "./TodayRoomCard";
import { useTranslations } from "next-intl";
import StatsCard from "@/shared/StatsCard";
import { useSocket } from "@/app/Socket";
import { Button } from "@/components/ui/button";

const TodayContainer = ({
  roomGroupData,
  hotelToken,
  todayData,
  membershipData,
}: {
  roomGroupData: any;
  todayData: any;
  hotelToken: string;
  membershipData: any;
}) => {
  const translate = useTranslations("TodayContainer");
  const { todayReservations, todayRevenue, totalGuest, todayCancelled } =
    todayData;
  const { sendPush } = useSocket();

  // const { connected, send, sendChat} = useSocket();
  //
  // useEffect(() => {
  //   send({to: "684aab2aa63e46e7e9557056", type:"push", title: "Test", body: "This is a test message from the TodayContainer component."});
  // }, []);

  return (
    <div className="w-full">
      {/* <div className="flex flex-wrap gap-2 my-5">
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "Rezervasyon ulaştı",
              data: {
                pushType: "reservation",
              },
              body: "yeni misafirin rezervasyon onayı için bekliyor",
            });
          }}
        >
          Rezervasyon
        </Button>
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "Hizmet ulaştı",
              data: {
                pushType: "service",
              },
              body: "yeni misafirin hizmet onayı için bekliyor",
            });
          }}
        >
          Hizmet
        </Button>
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "Üyelik Kartı ulaştı",
              data: {
                pushType: "subscription",
              },
              body: "yeni misafirin üyelik kartı onayı için bekliyor",
            });
          }}
        >
          Üyelik Kartı
        </Button>
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "Mesaj ulaştı",
              data: {
                pushType: "newMessage",
              },
              body: "yeni mesajınız var",
            });
          }}
        >
          Mesaj
        </Button>
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "İstatistik",
              data: {
                pushType: "statistic",
              },
              body: "varsayılan", 
            });
          }}
        >
          İstatistik
        </Button>
        <Button
          onClick={() => {
            sendPush({
              to: "6762c2a14bb8c2affd222a42",
              title: "Varsayılan",
              data: "",
              body: "varsayılan",
            });
          }}
        >
          Varsayılan
        </Button>
      </div> */}
      <div className="mx-auto mb-10 grid grid-cols-2 lg:grid-cols-4 gap-4 max-lg:overflow-auto max-lg:py-5">
        <StatsCard
          title={translate("reservation")}
          todayValue={todayReservations?.todayReservations}
          yesterdayValue={todayReservations?.yesterdayReservations}
          percentageChange={todayReservations?.percentageChange}
          icon="IconCalendar"
          cardColor="blue"
        />
        <StatsCard
          title={translate("revenue")}
          todayValue={`${parseFloat(todayRevenue?.todayRevenue)}₺`}
          yesterdayValue={`${parseFloat(todayRevenue?.yesterdayRevenue)}₺`}
          percentageChange={`${todayRevenue?.percentageChange}`}
          icon="IconChart"
          cardColor="green"
        />
        <StatsCard
          title={translate("guest")}
          todayValue={totalGuest?.totalGuests}
          totalRoom={totalGuest?.totalRooms}
          icon="IconPawGuest"
          cardColor="gray"
        />
        <StatsCard
          title={translate("cancellation")}
          todayValue={todayCancelled?.cancelledCount}
          icon="IconCancel"
          cardColor="red"
        />
      </div>
      {roomGroupData?.data?.length > 0 && (
        <TodayRoomCard
          roomGroupData={roomGroupData.data}
          hotelToken={hotelToken}
          membershipData={membershipData}
        />
      )}
    </div>
  );
};

export default TodayContainer;
