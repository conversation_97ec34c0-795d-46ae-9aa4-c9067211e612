import React from "react";
import { cookies } from "next/headers";
import getHotelLanding from "@/actions/(protected)/hotel/hotelLanding/getHotelLanding";
import CustomerReviewsSections from "../(components)/(customerReviewSection)/CustomerReviewSections";
import { redirect } from "next/navigation";

const CustomerReviewSectionPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelLandingData = await getHotelLanding();

  if (!hotelLandingData) {
    redirect("/account/hotel-landing");
  }

  return (
    <CustomerReviewsSections
      hotelToken={hotelToken}
      customerReviewsSectionData={
        hotelLandingData?.data?.customerReviewsSection
      }
    />
  );
};

export default CustomerReviewSectionPage;
