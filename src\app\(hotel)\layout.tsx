import React from "react";
import HotelHeader from "../(components)/(Header)/HotelHeader";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import ScrollToTop from "@/components/ScrollToTop";
import HotelFooterSettings from "./account/(components)/HotelFooterSettings";

export default function HotelLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value || undefined;

  if (!token) {
    redirect("/");
  }

  return (
    <div>
      <HotelHeader />
      <div className="relative flex justify-center pt-5 md:hidden">
        <PawBookingLogo className="size-16 self-center" />
      </div>
      {children}
      <HotelFooterSettings />
      <ScrollToTop />
    </div>
  );
}
