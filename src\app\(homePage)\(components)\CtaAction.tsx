import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  ArrowRight,
  Users,
  Clock,
  Shield,
  Star,
  Sparkles,
} from "lucide-react";
import { useRouter } from "next/navigation";

export function CTASection() {
  const router = useRouter();
  const ctaFeatures = [
    { icon: Clock, text: "bir kaç tıkta kurulum" },
    { icon: Shield, text: "Güvenli ödeme sistemi" },
    { icon: Users, text: "7/24 müşteri desteği" },
    { icon: Star, text: "Ücretsiz 30 gün deneme" },
  ];
  /*
  const testimonials = [
    {
      name: "<PERSON><PERSON> Yılmaz",
      business: "Happy Paws Hotel",
      text: "3 ayda rezervasyonlarımız %200 arttı!",
      rating: 5,
    },
    {
      name: "<PERSON><PERSON>",
      business: "Pet Paradise",
      text: "<PERSON>üşteri memnuniyeti çok yükseldi.",
      rating: 5,
    },
  ]*/

  return (
    <section className="relative py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100"></div>

      {/* Subtle floating elements */}
      <div className="absolute top-10 right-10 w-24 h-24 bg-orange-200/30 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 left-10 w-32 h-32 bg-orange-200/20 rounded-full blur-xl"></div>

      <div className="relative max-w-7xl mx-auto">
        <div className="text-center mb-12 lg:mb-16">
          <Badge
            variant="secondary"
            className="mb-4 bg-orange-200 text-orange-700"
          >
            <Sparkles className="w-4 h-4 mr-1" />
            Özel Fırsat
          </Badge>

          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6">
            Hemen Başlayın,
            <span className="block text-orange-600">İlk 30 Gün Ücretsiz!</span>
          </h2>

          <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            Kurulum ücreti yok, taahhüt yok. İstediğiniz zaman iptal
            edebilirsiniz. Binlerce pet sahibine ulaşmaya bugün başlayın.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button
              onClick={() => router.push("/auth/create-partner")}
              size="lg"
              className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <CheckCircle className="w-5 h-5 mr-2" />
              Ücretsiz Hesap Oluştur
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button
              onClick={() =>
                window.open(
                  "https://calendly.com/pawbooking-info/paw-tutorial",
                  "_blank"
                )
              }
              size="lg"
              variant="outline"
              className="border-orange-300 text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg font-semibold bg-white"
            >
              Satış Temsilcisi ile Görüş
            </Button>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
            {ctaFeatures.map((feature, index) => (
              <div
                key={index}
                className="flex flex-col items-center text-center"
              >
                <div className="w-12 h-12 bg-orange-200 rounded-full flex items-center justify-center mb-2">
                  <feature.icon className="w-6 h-6 text-orange-600" />
                </div>
                <span className="text-gray-700 text-sm font-medium">
                  {feature.text}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/*   Testimonials
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {testimonials?.map((testimonial, index) => (
            <Card key={index} className="bg-white border-orange-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-orange-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.text}"</p>
                <div className="text-gray-600">
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm opacity-80">{testimonial.business}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>*/}

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-gray-600">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm">SSL Güvenlik Sertifikası</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm">KVKK Uyumlu</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm">7/24 Teknik Destek</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm">10,000+ Mutlu Partner</span>
          </div>
        </div>

        {/* Bottom Note */}
        <div className="text-center mt-8">
          <p className="text-gray-500 text-sm">
            💳 Kredi kartı bilgisi gerekmez • 📞 Satış araması yok • ⚡ Anında
            aktif
          </p>
        </div>
      </div>
    </section>
  );
}
