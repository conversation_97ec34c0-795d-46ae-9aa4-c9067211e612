"use client";
import type { <PERSON>, Dispatch, SetStateAction } from "react";
import React from "react";
import CheckoutStep from "./CheckoutStep";
import CheckoutSideBar from "./CheckoutSideBar";

export interface CheckoutContainerProps {
  selectedTier: any;
  hotelToken: string | undefined;
  setStep: Dispatch<SetStateAction<number>>;
}

const CheckoutContainer: FC<CheckoutContainerProps> = ({
  selectedTier,
  hotelToken,
  setStep,
}) => {
  const renderMain = () => {
    return (
      <div className="flex w-full flex-col space-y-8">
        <div className="mx-auto w-full rounded-2xl bg-white pr-2 dark:bg-neutral-900">
          <CheckoutStep
            selectedTier={selectedTier}
            hotelToken={hotelToken}
            setStep={setStep}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="relative mb-16 md:mt-11 flex flex-col-reverse gap-5 lg:flex-row lg:gap-0">
      <div className="w-full lg:w-3/5 lg:pr-10 xl:w-2/3 ">{renderMain()}</div>
      <div className="grow max-lg:hidden">
        <CheckoutSideBar selectedTier={selectedTier} />
      </div>
    </div>
  );
};

export default CheckoutContainer;
