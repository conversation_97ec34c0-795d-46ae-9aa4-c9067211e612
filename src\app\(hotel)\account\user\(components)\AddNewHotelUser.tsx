"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import Label from "@/components/Label";
import Input from "@/shared/Input";
import Select from "@/shared/Select";
import Textarea from "@/shared/Textarea";
import type { HotelUserTypes } from "@/types/hotel/hotelUserType";
import { useHotelUserInformations } from "@/hooks/hotel/useHotelUserInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import {
  onlyLetterRegex,
  emailRegex,
  usernameRegex,
  passwordRegex,
} from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";
import PawPlus from "@/shared/PawPlus";
import PlusFeatureModal from "@/components/PlusFeatureModal";
interface AddNewHotelUserProps {
  hotelToken: string | undefined;
  membershipData: any;
}

const AddNewHotelUser: FC<AddNewHotelUserProps> = ({
  hotelToken,
  membershipData,
}) => {
  const translate = useTranslations("AddNewAndUpdateHotelUser");
  const { addHotelUserInformationHandler } = useHotelUserInformations();
  const initialData = {
    firstName: "",
    lastName: "",
    gender: "Male",
    username: "",
    email: "",
    dateOfBirth: "",
    phone: "",
    bio: "",
    password: "",
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [hotelUserInformations, setHotelUserInformations] =
    useState<HotelUserTypes>(initialData);
  const [userAddModalIsOpen, setuserAddModalIsOpen] = useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);

  function closeUserAddModal() {
    setuserAddModalIsOpen(false);
    setLoading(false);
    setHotelUserInformations(initialData);
  }

  const handleChange = (
    event: ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = event.target;
    setHotelUserInformations((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const phoneRegex = /^(0|90)(\d{10})$/;
  const nameCheck = onlyLetterRegex.test(hotelUserInformations.firstName);
  const lastNameCheck = onlyLetterRegex.test(hotelUserInformations.lastName);
  const mailCheck = emailRegex.test(
    hotelUserInformations.email.toLowerCase().trim()
  );
  const usernameCheck = usernameRegex.test(hotelUserInformations.username);
  const phoneCheck = phoneRegex.test(hotelUserInformations.phone);
  const dateOfBirthCheck = hotelUserInformations.dateOfBirth.length > 0;
  const passwordCheck = passwordRegex.test(hotelUserInformations.password);

  const isAllValid =
    nameCheck &&
    lastNameCheck &&
    mailCheck &&
    usernameCheck &&
    phoneCheck &&
    dateOfBirthCheck;

  return (
    <div className="mb-5 flex justify-end">
      {membershipData?.membershipType === "free" ? (
        <Button
          type="button"
          className="bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
          onClick={() => setModalIsVisible(true)}
        >
          <PawPlus width="20" height="20" />
          Kullanıcı Ekle
        </Button>
      ) : membershipData?.membershipType === "plus" ? (
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700"
          onClick={() => setuserAddModalIsOpen(true)}
        >
          {translate("addUser")}
        </Button>
      ) : (
        <Button
          type="button"
          className="bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
          onClick={() => setModalIsVisible(true)}
        >
          <PawPlus width="20" height="20" />
          Kullanıcı Ekle
        </Button>
      )}
      {modalIsVisible && (
        <PlusFeatureModal
          isOpen={modalIsVisible}
          setIsOpen={setModalIsVisible}
          message="Daha fazla oda grubu oluşturmak için Plus üye olmalısınız."
        />
      )}
      <Dialog open={userAddModalIsOpen}>
        <DialogContent
          onInteractOutside={closeUserAddModal}
          className="overflow-y-auto max-h-[calc(100vh-50px)]"
        >
          <DialogHeader>
            <DialogTitle>{translate("addNewUser")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            className="mt-10 max-w-3xl grow space-y-6 md:mt-0"
            onSubmit={(event) => {
              addHotelUserInformationHandler(
                event,
                hotelToken,
                hotelUserInformations,
                setLoading,
                closeUserAddModal
              );
            }}
          >
            <div>
              <Label>{translate("firstName")}</Label>
              <Input
                className="mt-1.5"
                name="firstName"
                onChange={handleChange}
              />
              {!nameCheck && hotelUserInformations.firstName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validFirstName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("lastName")}</Label>
              <Input
                className="mt-1.5"
                name="lastName"
                onChange={handleChange}
              />
              {!lastNameCheck && hotelUserInformations.lastName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validLastName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("gender")}</Label>
              <Select className="mt-1.5" name="gender" onChange={handleChange}>
                <option value="Male">{translate("male")}</option>
                <option value="Female">{translate("female")}</option>
                <option value="Other">{translate("other")}</option>
              </Select>
            </div>
            <div>
              <Label>{translate("username")}</Label>
              <Input
                className="mt-1.5"
                name="username"
                onChange={handleChange}
              />
              {!usernameCheck && hotelUserInformations.username.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validUsername")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("password")}</Label>
              <Input
                className="mt-1.5"
                name="password"
                type="password"
                onChange={handleChange}
              />
              {!passwordCheck && hotelUserInformations.password.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validPassword")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("email")}</Label>
              <Input className="mt-1.5" name="email" onChange={handleChange} />
              {!mailCheck && hotelUserInformations.email.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validEmail")}
                </span>
              )}
            </div>
            <div className="max-w-lg">
              <Label>{translate("dateOfBirth")}</Label>
              <Input
                className="mt-1.5"
                type="date"
                name="dateOfBirth"
                onChange={handleChange}
              />
            </div>
            <div>
              <Label>{translate("phone")}</Label>
              <Input className="mt-1.5" name="phone" onChange={handleChange} />
              {!phoneCheck && hotelUserInformations.phone.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validPhone")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("bio")}</Label>
              <Textarea className="mt-1.5" name="bio" onChange={handleChange} />
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                onClick={closeUserAddModal}
                type="button"
              >
                {translate("cancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                disabled={!isAllValid}
                type="submit"
              >
                {loading ? <LoadingSpinner /> : translate("confirm")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeUserAddModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AddNewHotelUser;
