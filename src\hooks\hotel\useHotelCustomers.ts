import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

export const useHotelCustomers = () => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  const router = useRouter();

  // ADDS NEW HOTEL USER
  const addHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelCustomer: {
      fullName: string;
      email: string;
      phone: string;
    },
    hotelId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeCustomerModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.addHotelCustomer, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fullName: hotelCustomer.fullName.trim(),
            email: hotelCustomer.email.toLowerCase(),
            phone: hotelCustomer.phone,
            hotel: hotelId,
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeCustomerModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Ekleme",
          description: "Müşteri başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeCustomerModal();
        dispatch(
          setCheckoutData({
            hotelCustomer: data?.data?.hotelCustomer._id,
            pet: [],
            servicePet: [],
            transportationServiceChoice: null,
            channel: "",
            paymentType: "",
          })
        );
      } catch (error) {
        console.log(error);
      }
    }
  };

  // DELETES SELECTED HOTEL USER
  const removeHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    id: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.removeHotelCustomer}/${id}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Silme",
          description: "Müşteri başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES SELECTED HOTEL USER
  const updateHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    updatedHotelCustomer: {
      fullName: string;
      email: string;
      phone: string;
    },
    hotelCustomerId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeCustomerUpdateModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateHotelCustomer}/${hotelCustomerId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              fullName: updatedHotelCustomer.fullName.trim(),
              email: updatedHotelCustomer.email.toLowerCase(),
              phone: updatedHotelCustomer.phone,
            }),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeCustomerUpdateModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Güncelleme",
          description: "Müşteri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-customer");
        closeCustomerUpdateModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  const sendSmsToHotelCustomer = async (
    hotelToken: string | undefined,
    hotelCustomerId: string | null,
    orderId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.sendSmsToHotelCustomer, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            customerId: hotelCustomerId,
            orderId: orderId,
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 12000,
          title: "Sms Gönderme",
          description:
            "Sms başarıyla gönderildi. Müşteri rezervasyonu tamamladığında bildirim alacaksınız.",
        });
        setLoading(false);
        closeModal();
        router.push("/hotel/account");
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    addHotelCustomerHandler,
    removeHotelCustomerHandler,
    updateHotelCustomerHandler,
    sendSmsToHotelCustomer,
  };
};
