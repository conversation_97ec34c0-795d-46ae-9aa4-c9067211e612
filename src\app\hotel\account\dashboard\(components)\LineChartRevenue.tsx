"use client";
import React from "react";
import type { FC } from "react";
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { ChartConfig } from "@/components/ui/chart";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartConfig = {
  views: {
    label: "Gelir",
  },
  stayedPetCount: {
    label: "Desktop",
    color: "hsl(var(--chart-1))",
  },
  mobile: {
    label: "Mobile",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig;

interface LineChartOccupancyProps {
  data: any;
  startDate: string | string[];
  endDate: string | string[];
}

const LineChartRevenue: FC<LineChartOccupancyProps> = ({
  data,
  startDate,
  endDate,
}) => {
  const formatIsoDate = (isoDate: string | string[]): string => {
    const dateStr = Array.isArray(isoDate) ? isoDate[0] : isoDate;

    const [year, month, day] = dateStr.split("-");
    return `${day}.${month}.${year}`;
  };
  return (
    <Card>
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
          <CardTitle className="text-center">Gelir Dağılımı</CardTitle>
          <CardDescription className="text-center">
            {formatIsoDate(startDate) + " - " + formatIsoDate(endDate)}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="px-2 sm:p-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <LineChart
            accessibilityLayer
            data={data}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("tr-TR", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <YAxis
              tickFormatter={(value) => {
                if (value >= 1000) {
                  return (value / 1000).toFixed(0) + "K";
                }
                return value.toFixed(0);
              }}
              tickLine={false}
              axisLine={false}
              tickMargin={8}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  className="w-[150px]"
                  nameKey="views"
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("tr-TR", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                />
              }
            />
            <Line
              dataKey={"total"}
              type="monotone"
              stroke={`var(--color-stayedPetCount)`}
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default LineChartRevenue;
