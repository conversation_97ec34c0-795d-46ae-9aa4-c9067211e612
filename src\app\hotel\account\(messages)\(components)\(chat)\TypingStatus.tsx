"use client";
import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

const TypingStatus = () => {
  const isTyping = useSelector(
    (state: RootState) => state.liveChatText.isTyping
  );

  return (
    <span
      className={`absolute -bottom-3 left-0 text-xs text-neutral-500 dark:text-neutral-400 font-normal ${isTyping ? "visible opacity-100" : "invisible opacity-0"} duration-200`}
    >
      yazıyor...
    </span>
  );
};

export default TypingStatus;
