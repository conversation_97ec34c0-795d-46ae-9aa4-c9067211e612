"use client";
import type { FC } from "react";
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  setSelectedRoomId,
  setselectedRoomGroupId,
} from "@/store/features/hotelCalendar/calendar-slice";
import type { RootState } from "@/store";
import HotelCalendarRange from "./(range)/HotelCalendarRange";
import HotelCalendarSideBarRange from "./(range)/HotelCalendarSideBarRange";
import HotelCalendarRangeMobile from "./(range)/(mobile)/HotelCalendarRangeMobile";
import HotelCalendarDrawerRangeMobile from "./(range)/(mobile)/HotelCalendarDrawerRangeMobile";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import type { AllocationType } from "@/store/features/hotelCalendar/calendar-types";
import { useWindowSize } from "react-use";
import { useTranslations } from "next-intl";
import { RoomGroupDataApiTypes } from "@/types/hotel/roomGroupType"; // TODO: sonra bunu güncelle ve ekle geri
import { Button } from "@/components/ui/button";
import Link from "next/link";
import CalendarRoomCard from "./CalendarRoomCard";
import { createLocalDate } from "@/utils/createLocalDate";
interface HotelCalendarContainerProps {
  hotelToken: string | undefined;
  roomGroupData: any;
  roomParams: Record<string, string | string[] | undefined>;
}

const HotelCalendarContainer: FC<HotelCalendarContainerProps> = ({
  hotelToken,
  roomGroupData,
  roomParams,
}) => {
  const translate = useTranslations("HotelCalendarContainer");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [fetchAgain, setFetchAgain] = useState<boolean>(false);
  const [open, setOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [minPrice, setMinPrice] = useState<number | undefined>(undefined);
  const [maxPrice, setMaxPrice] = useState<number | undefined>(undefined);
  const [dateStatuses, setDateStatuses] = useState<boolean[]>([]);
  const [hasUndefinedPrice, setHasUndefinedPrice] = useState(false);
  const [inRangePrices, setInRangePrices] = useState<number[] | []>([]);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const dispatch = useDispatch();
  const selectedRoomId = useSelector(
    (state: RootState) => state.hotelCalendar.selectedRoomId
  );
  const selectedRoomCalendarData = useSelector(
    (state: RootState) => state.hotelCalendar.allocations
  );
  const { getRoomAllocation } = useHotelCalendarActions();

  useEffect(() => {
    getRoomAllocation(hotelToken, selectedRoomId);
  }, [hotelToken, fetchAgain, selectedRoomId, getRoomAllocation]);

  const windowWidth = useWindowSize().width;

  useEffect(() => {
    if (windowWidth > 767) {
      setIsMobile(false);
    } else {
      setIsMobile(true);
    }
  }, [windowWidth]);

  useEffect(() => {
    if (roomParams) {
      dispatch(setSelectedRoomId(roomParams.selectedRoom));
      dispatch(setselectedRoomGroupId(roomParams.selectedRoomGroup));
    }
  }, [roomParams, dispatch]);

  // Gets min and max price between start and end date
  useEffect(() => {
    if (startDate && endDate && selectedRoomCalendarData) {
      const dates = getDatesInRange(startDate, endDate);

      const prices = dates.map((date) => {
        const matchingItem = selectedRoomCalendarData?.find(
          (item: AllocationType) => {
            const itemDate = createLocalDate(item.allocationDate);
            if (itemDate) {
              return (
                itemDate.getFullYear() === date.getFullYear() &&
                itemDate.getMonth() === date.getMonth() &&
                itemDate.getDate() === date.getDate()
              );
            }
          }
        );
        return matchingItem
          ? (matchingItem?.roomPrice ?? matchingItem?.price)
          : undefined;
      });

      // Check if any price is undefined
      const hasUndefined = prices.some((price) => price === undefined);
      setHasUndefinedPrice(hasUndefined);

      const filteredPrices = prices.filter(
        (price) => price !== undefined
      ) as number[];

      setInRangePrices(filteredPrices);

      if (filteredPrices.length > 0) {
        setMinPrice(Math.min(...filteredPrices));
        setMaxPrice(Math.max(...filteredPrices));
      } else {
        setMinPrice(undefined);
        setMaxPrice(undefined);
      }
    }
  }, [startDate, endDate, selectedRoomCalendarData]);

  //gets the passive status between start and end date
  useEffect(() => {
    if (startDate && endDate && selectedRoomCalendarData) {
      const dates = getDatesInRange(startDate, endDate);

      const passiveStatuses = dates.map((date) => {
        const matchingItem = selectedRoomCalendarData?.find(
          (item: AllocationType) => {
            const itemDate = createLocalDate(item.allocationDate);
            if (itemDate) {
              return (
                itemDate.getFullYear() === date.getFullYear() &&
                itemDate.getMonth() === date.getMonth() &&
                itemDate.getDate() === date.getDate()
              );
            }
          }
        );
        return matchingItem ? matchingItem.passive : true;
      });
      setDateStatuses(passiveStatuses);
    }
  }, [startDate, endDate, selectedRoomCalendarData]);

  // Gets dates between start and end date
  const getDatesInRange = (startDate: Date, endDate: Date): Date[] => {
    const dates = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  return (
    <>
      {!selectedRoomId ? (
        <div className="container">
          <div className="mt-4 md:my-8 max-w-[480px]">
            <h2 className="text-xl md:text-2xl font-semibold">
              Takvim ve Fiyatlandırma
            </h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300">
              Otelinize ait odaların fiyatlarını dönemsel olarak fiyatlandırıp
              dinamik bir takvim ayarlayabilirsiniz. Oda seçerek hemen başlayın.
            </span>
          </div>
          {roomGroupData && roomGroupData?.length > 0 ? (
            <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
              {roomGroupData.map((room: any) => {
                return <CalendarRoomCard key={room._id} room={room} />;
              })}
            </div>
          ) : (
            <div className="container flex flex-col items-center">
              <p className=" mb-5 text-center text-lg font-semibold">
                Önceden oluşturduğunuz oda grubu yok. Lütfen oda grubu
                oluşturunuz.
              </p>
              <Link href="/hotel/account/rooms">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center">
                  Oda sayfasına git
                </Button>
              </Link>
            </div>
          )}
        </div>
      ) : (
        <>
          {!isMobile && (
            <div className="flex">
              <HotelCalendarRange
                setOpen={setOpen}
                startDate={startDate}
                endDate={endDate}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedRoomCalendarData={selectedRoomCalendarData}
              />
              <HotelCalendarSideBarRange
                startDate={startDate}
                endDate={endDate}
                open={open}
                hotelToken={hotelToken}
                setFetchAgain={setFetchAgain}
                minPrice={minPrice}
                maxPrice={maxPrice}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                dateStatues={dateStatuses}
                hasUndefinedPrice={hasUndefinedPrice}
                inRangePrices={inRangePrices}
                roomGroupData={roomGroupData}
              />
            </div>
          )}
          {isMobile && (
            <div>
              <HotelCalendarRangeMobile
                setOpen={setOpen}
                setDrawerOpen={setDrawerOpen}
                startDate={startDate}
                endDate={endDate}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedRoomCalendarData={selectedRoomCalendarData}
              />
              <HotelCalendarDrawerRangeMobile
                startDate={startDate}
                endDate={endDate}
                drawerOpen={drawerOpen}
                setDrawerOpen={setDrawerOpen}
                hotelToken={hotelToken}
                setFetchAgain={setFetchAgain}
                minPrice={minPrice}
                maxPrice={maxPrice}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                dateStatues={dateStatuses}
                hasUndefinedPrice={hasUndefinedPrice}
                inRangePrices={inRangePrices}
              />
            </div>
          )}
        </>
      )}
    </>
  );
};

export default HotelCalendarContainer;
