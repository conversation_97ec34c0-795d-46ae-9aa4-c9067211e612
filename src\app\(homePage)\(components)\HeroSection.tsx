"use client";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Play } from "lucide-react";
import { useRouter } from "next/navigation";

export function HeroSection() {
  const router = useRouter();

  return (
    <section className="relative py-12 sm:py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50 via-white to-blue-50"></div>
      <div className="absolute top-0 right-0 w-48 h-48 sm:w-96 sm:h-96 bg-orange-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 sm:w-96 sm:h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000"></div>

      <div className="relative max-w-7xl mx-auto">
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left order-2 lg:order-1">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              <span className="text-orange-500">Evcil Hayvan Oteli</span>
              <span className="block">Rezervasyon Sistemi</span>
            </h1>

            <p className="text-base sm:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 leading-relaxed max-w-lg mx-auto lg:mx-0">
              Pet hotel işletmenizi modern rezervasyon yönetim sistemi ile
              büyütün. Online rezervasyon, ödeme sistemi ve müşteri yönetimi tek
              platformda.
            </p>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6 sm:mb-8 justify-center lg:justify-start">
              <Button
                onClick={() => router.push("/auth/create-partner")}
                size="lg"
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg w-full sm:w-auto"
              >
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Ücretsiz Deneme Başlat
              </Button>
              <Button
                onClick={() =>
                  window.open(
                    "https://calendly.com/pawbooking-info/paw-tutorial",
                    "_blank"
                  )
                }
                size="lg"
                variant="outline"
                className="border-orange-300 text-orange-600 hover:bg-orange-50 px-8 py-4 text-lg font-semibold bg-white"
              >
                Satış Temsilcisi ile Görüş
              </Button>
              {/*<Button
                size="lg"
                variant="outline"
                className="px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg bg-transparent w-full sm:w-auto"
              >
                <Play className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Demo İzle
              </Button>*/}
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start space-y-2 sm:space-y-0 sm:space-x-6 text-xs sm:text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>Ücretsiz kurulum</span>
              </div>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>30 gün deneme</span>
              </div>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>Taahhüt yok</span>
              </div>
            </div>
          </div>

          {/* Right Content - Hero Image/Illustration */}
          <div className="relative order-1 lg:order-2 w-full max-w-sm sm:max-w-md lg:max-w-none mx-auto">
            <div className="relative bg-white rounded-xl sm:rounded-2xl shadow-xl sm:shadow-2xl p-4 sm:p-6 lg:p-8 transform hover:rotate-0 lg:rotate-3 transition-transform duration-300">
              {/* Mock Dashboard */}
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900 text-sm sm:text-base">
                    Partner Dashboard
                  </h3>
                  <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full"></div>
                </div>

                {/* Mock Calendar */}
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <div className="grid grid-cols-7 gap-1 sm:gap-2 mb-2">
                    {["P", "S", "Ç", "P", "C", "C", "P"].map((day, i) => (
                      <div
                        key={i}
                        className="text-xs text-gray-500 text-center font-medium"
                      >
                        {day}
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-7 gap-1 sm:gap-2">
                    {Array.from({ length: 21 }).map((_, i) => (
                      <div
                        key={i}
                        className={`w-6 h-6 sm:w-8 sm:h-8 rounded text-xs flex items-center justify-center ${
                          i === 10
                            ? "bg-orange-500 text-white"
                            : i === 15
                              ? "bg-blue-500 text-white"
                              : "bg-white text-gray-700"
                        }`}
                      >
                        {i + 1}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Mock Stats */}
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  <div className="bg-orange-50 rounded-lg p-2 sm:p-3">
                    <div className="text-lg sm:text-2xl font-bold text-orange-600">
                      24
                    </div>
                    <div className="text-xs text-gray-600">
                      Aktif Rezervasyon
                    </div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-2 sm:p-3">
                    <div className="text-lg sm:text-2xl font-bold text-blue-600">
                      ₺12,450
                    </div>
                    <div className="text-xs text-gray-600">Aylık Gelir</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Elements - Hidden on mobile for cleaner look */}
            <div className="hidden sm:flex absolute -top-4 -right-4 w-16 h-16 lg:w-20 lg:h-20 bg-yellow-200 rounded-full items-center justify-center text-xl lg:text-2xl animate-bounce">
              🏆
            </div>
            <div className="hidden sm:flex absolute -bottom-4 -left-4 w-12 h-12 lg:w-16 lg:h-16 bg-green-200 rounded-full items-center justify-center text-base lg:text-xl animate-pulse">
              💰
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
