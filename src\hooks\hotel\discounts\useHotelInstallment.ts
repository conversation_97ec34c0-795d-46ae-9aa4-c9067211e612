import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelInstallment = () => {
  const { toast } = useToast();

  const defineInstallment = async (
    event: FormEvent,
    hotelToken: string | undefined,
    installment: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.defineInstallment, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(installment),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Taksit Ekleme",
          description: "Taksit başarıyla eklendi.",
        });
        revalidatePathHandler("/account/discounts");
        setLoading(false);
        closeModal();
        setDisabled(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const updateInstallment = async (
    event: FormEvent,
    hotelToken: string | undefined,
    installmentData: any,
    installmentId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateInstallment}/${installmentId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(installmentData),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setTimeout(() => {
            setDisabled(false);
          }, 1000);

          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Taksit Güncelleme",
          description: "Taksit başarıyla güncellendi.",
        });
        revalidatePathHandler("/account/discounts");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const deleteInstallment = async (
    event: FormEvent,
    hotelToken: string | undefined,
    installmentId: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.deleteInstallment}/${installmentId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Taksit Kaldırma",
          description: "Taksit başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/account/discounts");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  return { defineInstallment, updateInstallment, deleteInstallment };
};
