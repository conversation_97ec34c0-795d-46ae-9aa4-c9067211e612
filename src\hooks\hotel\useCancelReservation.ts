import { useToast } from "@/components/ui/use-toast";

export const useCancelReservation = () => {
  const { toast } = useToast();

  const cancelReservation = async (
    reservationData: any,
    hotelToken: string | undefined
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/payment/cancelOrder`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              hotelToken: hotelToken,
            },
            body: JSON.stringify({
              pyOrderGuid: reservationData.pyOrderGuid,
              subMerchantGuid: reservationData.subMerchantGuid,
              orderId: reservationData.orderId,
              refundAmount: reservationData.refundAmount,
              finalAmountToPay: reservationData.finalAmountToPay,
            }),
          }
        );

        const data = await response.json();
        if (!response.ok || !data.success) {
          toast({
            variant: "error",
            title: "<PERSON>a",
            description: data.error || "Beklenmedik bir hata oluştu.",
          });
          throw new Error("Error creating sub-merchant");
        }

        toast({
          variant: "success",
          title: "Başarılı",
          description: "Rezervasyon başarılı bir şekilde iptal edildi.",
        });
      } catch (error: any) {
        console.error("Error:", error);
        toast({
          variant: "error",
          title: "Hata",
          description: error.message,
        });
      }
    }
  };

  return {
    cancelReservation,
  };
};
