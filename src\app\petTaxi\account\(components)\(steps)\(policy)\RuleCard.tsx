"use client";
import React from "react";
import type { FC } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import IconDelete from "@/shared/icons/Delete";
import IconEdit from "@/shared/icons/Edit";

interface RuleCardProps {
  rules: { title: string; rule: string };
  index: number;
  deleteRuleHandler: (index: number) => void;
  editRuleHandler: (index: number) => void;
}

const RuleCard: FC<RuleCardProps> = ({
  rules,
  index,
  deleteRuleHandler,
  editRuleHandler,
}) => {
  const { title, rule } = rules;
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg capitalize">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm line-clamp-2">{rule}</p>
        <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-3">
          <IconEdit
            onClick={() => editRuleHandler(index)}
            className="cursor-pointer size-5 hover:text-secondary-6000 duration-200"
          />
          <IconDelete
            className="cursor-pointer size-5 hover:text-secondary-6000 duration-200"
            onClick={() => deleteRuleHandler(index)}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default RuleCard;
