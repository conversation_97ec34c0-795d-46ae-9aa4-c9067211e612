import React from "react";
import dynamic from "next/dynamic";
import TotalRevenueCard from "./(components)/(cards)/TotalRevenueCard";
import TotalReservationCard from "./(components)/(cards)/TotalReservationCard";
import TotalGuestCard from "./(components)/(cards)/TotalGuestCard";
import TotalCancellationCard from "./(components)/(cards)/TotalCancellationCard";
import getHotelAnalytics from "@/actions/(protected)/hotel/dashboard/getHotelAnalytics";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import getDepositAmounts from "@/actions/(protected)/hotel/dashboard/getDepositAmounts";
import { getOrdersBetweenDates } from "@/actions/(protected)/hotel/dashboard/getOrdersBetweenDates";
import { getRevenueAnalytic } from "@/actions/(protected)/hotel/dashboard/getRevenueAnalytic";
import { getCancelledReservationStats } from "@/actions/(protected)/hotel/dashboard/getCancelledReservationStats";
import { getPaymentTypes } from "@/actions/(protected)/hotel/dashboard/getPaymentTypes";

import DashboardContainter from "./(components)/DashboardContainter";
const ChartDateFilter = dynamic(
  () => import("./(components)/ChartDateFilter"),
  {
    ssr: false,
    loading: () => (
      <div className="mb-5 h-10 w-full animate-pulse rounded-md bg-gray-200"></div>
    ),
  }
);

const DashboardPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const startDate =
    searchParams.startDate ||
    new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0];
  const endDate =
    searchParams.endDate || new Date().toISOString().split("T")[0];
  const displaySetting = searchParams.display || "week";
  const hotelAnalytics = await getHotelAnalytics(startDate, endDate);
  const totalGuest =
    hotelAnalytics?.data?.data?.petTypeRates.length > 0
      ? hotelAnalytics?.data?.data?.petTypeRates[0].count
      : 0;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);
  const depositAmounts = await getDepositAmounts();
  const ordersBetweenDates = await getOrdersBetweenDates(startDate, endDate);
  const revenueAnalytics = await getRevenueAnalytic(startDate, endDate);
  const cancelledReservationStats = await getCancelledReservationStats(
    startDate,
    endDate
  );
  const paymentTypes = await getPaymentTypes(startDate, endDate);

  return (
    <div className="relative mx-auto">
      <div className="my-5">
        <div>
          <ChartDateFilter
            startDate={startDate}
            endDate={endDate}
            display={displaySetting}
          />
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 max-lg:overflow-auto lg:justify-center lg:gap-5 mt-5">
          <TotalReservationCard
            title="Toplam Rezervasyon"
            data={hotelAnalytics?.data?.data?.cancelledReservations}
          />
          <TotalRevenueCard
            title="Toplam Gelir"
            data={hotelAnalytics?.data?.data?.totalRevenue}
          />
          <TotalGuestCard title="Toplam Misafir" data={totalGuest} />
          <TotalCancellationCard
            title="Toplam İptal"
            data={hotelAnalytics?.data?.data?.cancelledReservations}
          />
        </div>
      </div>
      <DashboardContainter
        hotelAnalytics={hotelAnalytics?.data?.data}
        startDate={startDate}
        endDate={endDate}
        membershipData={membershipData?.data}
        depositAmounts={depositAmounts?.data}
        ordersBetweenDates={ordersBetweenDates?.data?.orders}
        revenueAnalytics={revenueAnalytics?.data?.data}
        cancelledReservationStats={cancelledReservationStats?.data}
        paymentTypes={paymentTypes?.data?.paymentTypes}
      />
    </div>
  );
};

export default DashboardPage;
