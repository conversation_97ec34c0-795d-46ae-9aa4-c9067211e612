"use client";

import * as React from "react";
import { PricingCard, type PricingTier } from "@/components/ui/pricing-card";
import { Tab } from "@/components/ui/pricing-tab";
import { useTranslations } from "next-intl";

interface PricingSectionProps {
  title: string;
  subtitle: string;
  tiers: PricingTier[];
  frequencies: string[];
  membershipData: any;
  onSelectTier?: (
    tierId: string,
    price: number | string,
    frequency: string
  ) => void;
}

export function PricingSection({
  title,
  subtitle,
  tiers,
  frequencies,
  membershipData,
  onSelectTier,
}: PricingSectionProps) {
  const translate = useTranslations("LandingPricing");
  const [selectedFrequency, setSelectedFrequency] = React.useState(
    `${translate("monthly")}`
  );
  return (
    <section className="flex flex-col items-center gap-10 py-10">
      <div className="space-y-7 text-center">
        <div className="space-y-4">
          <h1 className="text-4xl font-medium md:text-5xl">{title}</h1>
          <p className="text-muted-foreground">{subtitle}</p>
        </div>
        <div className="mx-auto flex w-fit rounded-full bg-muted p-1">
          {frequencies.map((freq) => (
            <Tab
              key={freq}
              text={freq}
              selected={selectedFrequency === freq}
              setSelected={setSelectedFrequency}
            />
          ))}
        </div>
      </div>

      <div className="grid w-full max-w-6xl gap-6 sm:grid-cols-2 xl:grid-cols-2">
        {tiers.map((tier) => (
          <PricingCard
            key={tier.name}
            tier={tier}
            paymentFrequency={selectedFrequency}
            membershipData={membershipData}
            onSelectTier={onSelectTier}
          />
        ))}
      </div>
    </section>
  );
}
