import React from "react";
import type { FC } from "react";
import DeleteSubscription from "./DeleteSubscription";
import UpdateSubscription from "./UpdateSubscription";
import type { RoomGroupListType } from "@/types/hotel/roomGroupType";

export interface SubscriptionCardProps {
  subscriptions: any;
  hotelToken: string | undefined;
  roomGroupData: RoomGroupListType;
}

const SubscriptionCard: FC<SubscriptionCardProps> = ({
  subscriptions,
  hotelToken,
  roomGroupData,
}) => {
  const {
    subscriptionName,
    subscriptionDuration,
    nightsEarned,
    total,
    isActive,
    _id,
  } = subscriptions;

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex">
        <span className="relative flex h-2.5 w-2.5 mt-[7px] mr-2">
          <span
            className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
              isActive ? "bg-green-700" : "bg-red-700"
            } opacity-75`}
          ></span>
          <span
            className={`relative inline-flex rounded-full h-2.5 w-2.5 ${
              isActive ? "bg-green-600" : "bg-red-600"
            }`}
          ></span>
        </span>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <h2 className="font-semibold capitalize text-neutral-900 dark:text-white">
              <span className="line-clamp-1">{subscriptionName}</span>
            </h2>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>Abonelik Süresi:</div>
            <div>{subscriptionDuration} gün</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>Gece Sayısı:</div>
            <div> {nightsEarned}</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>Abonelik Ücreti:</div>
            <div>
              {new Intl.NumberFormat("tr-TR", {
                style: "decimal",
              }).format(total) + "₺"}
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdateSubscription
          subscription={subscriptions}
          roomGroupData={roomGroupData}
          hotelToken={hotelToken}
        />
        <DeleteSubscription hotelToken={hotelToken} subscriptionId={_id} />
      </div>
    </div>
  );
};

export default SubscriptionCard;
