import React from "react";
import HotelInformations from "./(components)/(hotelInfo)/HotelInformations";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";

const HotelInformationsPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();

  return (
    <>
      <div className="mt-4 md:mt-8">
        <h2 className="text-xl md:text-2xl font-semibold">Otel Bilgileri</h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Bu alanda otelinize ait logo, isim ve hikaye kısımlarını düzenleyerek
          pet sahiplerinin dikkatini çekebilirsiniz.
        </span>
      </div>
      {hotelData && (
        <HotelInformations
          hotelData={hotelData?.data}
          hotelToken={hotelToken}
        />
      )}
    </>
  );
};

export default HotelInformationsPage;
