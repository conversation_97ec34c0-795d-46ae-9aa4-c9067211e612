"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconCancel from "@/shared/icons/Cancel";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import { useTranslations } from "next-intl";

interface DeleteFileModalProps {
  hotelToken: string | undefined;
  fileId: string;
}

const DeleteFileModal: FC<DeleteFileModalProps> = ({ hotelToken, fileId }) => {
  const translate = useTranslations("DeleteFileModal");
  const { deleteHotelFileHandler } = useHotelInformationsActions();
  const [deleteFileIsOpen, setDeleteFileIsOpen] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <>
      <IconCancel
        onClick={() => setDeleteFileIsOpen(true)}
        className="absolute -right-8 top-0 size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />
      <Dialog open={deleteFileIsOpen}>
        <DialogContent onInteractOutside={() => setDeleteFileIsOpen(false)}>
          <DialogHeader>
            <DialogTitle>{translate("title")}</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteHotelFileHandler(
                event,
                hotelToken,
                fileId,
                setLoading,
                setDeleteFileIsOpen,
                setDisabled
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                {translate("confirmMessage")}
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                type="button"
                onClick={() => setDeleteFileIsOpen(false)}
              >
                {translate("cancelButton")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center"
                type="submit"
                disabled={disabled}
              >
                {loading ? <LoadingSpinner /> : translate("confirmButton")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={() => setDeleteFileIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteFileModal;
