import React from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetD<PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import VeterinaryDetail from "./(servicesSoldDetail)/VeterinaryDetail";
import TransportationDetail from "./(servicesSoldDetail)/TransportationDetail";
import GroomingDetail from "./(servicesSoldDetail)/GroomingDetail";

const ServicesSoldDetail = ({
  servicesSoldData,
}: {
  servicesSoldData: any;
}) => {
  const translate = useTranslations("ReservationDetail");
  return (
    <Sheet>
      <SheetTrigger className="font-medium">
        {translate("details")}
      </SheetTrigger>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="mb-5"><PERSON><PERSON><PERSON> Detayı</SheetTitle>
          <SheetDescription className="sr-only"></SheetDescription>
        </SheetHeader>
        <div className="space-y-3">
          {servicesSoldData?.serviceType === "veterinaryServices" && (
            <VeterinaryDetail servicesSoldData={servicesSoldData} />
          )}
          {servicesSoldData?.serviceType === "transportationServices" && (
            <TransportationDetail servicesSoldData={servicesSoldData} />
          )}
          {servicesSoldData?.serviceType === "groomingServices" && (
            <GroomingDetail servicesSoldData={servicesSoldData} />
          )}
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("petInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petName")}:</p>
              <p>{servicesSoldData?.pet?.name}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petAge")}:</p>
              <p>{servicesSoldData?.pet?.age}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petKind")}:</p>
              <p>{servicesSoldData?.pet?.kind}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petBreed")}:</p>
              <p>{servicesSoldData?.pet?.breed}</p>
            </div>
            {servicesSoldData?.pet?.images?.length > 0 && (
              <div className="grid grid-cols-2 gap-3">
                {servicesSoldData?.pet?.images?.map((image: any) => {
                  return (
                    <div key={image._id}>
                      <Image
                        src={image?.img800 || image?.src}
                        width={150}
                        height={150}
                        alt="pet-image"
                        className="my-2"
                      />
                      <a
                        href={image?.src}
                        target="_blank"
                        className="text-sm hover:text-secondary-6000 cursor-pointer"
                      >
                        <Button className="text-xs w-full" variant="outline">
                          {translate("showPhoto")}
                        </Button>
                      </a>
                    </div>
                  );
                })}
              </div>
            )}
            {servicesSoldData?.pet?.documentPhotos?.length > 0 && (
              <div>
                <Separator className="my-2" />
                <h4 className="my-2 font-semibold">
                  {translate("vaccinationReport")}
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  {servicesSoldData?.pet?.documentPhotos?.map((image: any) => {
                    return (
                      <div key={image._id}>
                        <Image
                          src={image?.img800 || image?.src}
                          width={150}
                          height={150}
                          alt="vaccination-report-image"
                          className="my-2"
                        />
                        <a
                          href={image?.src}
                          target="_blank"
                          className="text-sm hover:text-secondary-6000 cursor-pointer"
                        >
                          <Button className="text-xs w-full" variant="outline">
                            {translate("showPhoto")}
                          </Button>
                        </a>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
          <Separator />
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("petOwnerInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petOwnerName")}:</p>
              <p>{servicesSoldData?.petOwner?.fullName}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("phone")}:</p>
              <a href={`tel:${servicesSoldData?.petOwner?.phone}`}>
                {servicesSoldData?.petOwner?.phone}
              </a>
            </div>
            <div className="flex gap-1 text-sm">
              <p className="font-semibold">{translate("email")}:</p>
              <a href={`mailto:${servicesSoldData?.petOwner?.email}`}>
                {servicesSoldData?.petOwner?.email}
              </a>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ServicesSoldDetail;
