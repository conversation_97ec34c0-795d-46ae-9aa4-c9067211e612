"use client";
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import VeterinaryService from "./(Services)/VeterinaryService";
import TransportationService from "./(Services)/TransportationService";
import GroomingService from "./(Services)/GroomingService";
import { Button } from "@/components/ui/button";

interface ServiceFormProps {
  hotelToken: string | undefined;
  hotelData: any;
  serviceData: any;
  onClose?: () => void;
  startDate: Date | string | undefined;
}

const ServiceForm: React.FC<ServiceFormProps> = ({
  hotelToken,
  hotelData,
  serviceData,
  onClose,
  startDate,
}) => {
  const [selectedService, setSelectedService] = useState<any>(null);
  const [selectedServiceType, setSelectedServiceType] = useState<string>("");

  const handleServiceSelection = (serviceId: string) => {
    const service = serviceData.find((s: any) => s._id === serviceId);
    if (service) {
      setSelectedServiceType(service.serviceType);
      setSelectedService(service);
    }
  };

  return (
    <div className="listingSection__wrap_disable space-y-3">
      <Select
        value={selectedService?._id || ""}
        onValueChange={(selected) => handleServiceSelection(selected)}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Hizmet Seç" />
        </SelectTrigger>
        <SelectContent>
          {serviceData && serviceData.length > 0 ? (
            serviceData.map((service: any) => (
              <SelectItem key={service._id} value={service._id}>
                <span className="font-semibold capitalize">
                  {service?.serviceData?.serviceName}
                </span>{" "}
                -{" "}
                <span className="font-medium text-neutral-700 dark:text-neutral-300">
                  {service?.serviceType === "veterinaryServices"
                    ? "Veteriner"
                    : service?.serviceType === "transportationServices"
                      ? "Pet Ulaşım"
                      : service?.serviceType === "groomingServices"
                        ? "Pet Kuaför"
                        : "Bilinmeyen Hizmet Türü"}
                </span>
              </SelectItem>
            ))
          ) : (
            <SelectItem value="no-service" disabled>
              Hizmet Bulunamadı
            </SelectItem>
          )}
        </SelectContent>
      </Select>
      {selectedServiceType === "veterinaryServices" && (
        <VeterinaryService
          hotelToken={hotelToken}
          hotelId={hotelData?._id}
          service={selectedService}
          onClose={onClose}
        />
      )}
      {selectedServiceType === "transportationServices" && (
        <TransportationService
          hotelToken={hotelToken}
          hotelId={hotelData?._id}
          service={selectedService}
          onClose={onClose}
          hotelLocation={hotelData?.googleMapUrl}
          startDate={startDate}
        />
      )}
      {selectedServiceType === "groomingServices" && (
        <GroomingService
          hotelToken={hotelToken}
          hotelId={hotelData?._id}
          service={selectedService}
          onClose={onClose}
        />
      )}
      {!selectedServiceType && (
        <div className="flex justify-end gap-5 pb-1">
          <Button variant="ghost" onClick={onClose}>
            İptal
          </Button>
        </div>
      )}
    </div>
  );
};

export default ServiceForm;
