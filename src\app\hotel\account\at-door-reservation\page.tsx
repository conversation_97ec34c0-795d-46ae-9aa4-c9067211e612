import React from "react";
import { cookies } from "next/headers";
import AtDoorReservationsContainer from "./(components)/atDoorReservationsContainer";
import FooterReservation from "./(components)/(reservationFooter)/FooterReservation";
import MobileFooterSticky from "./(components)/(steps)/(roomSelectStep)/(components)/(mobile)/MobileFooterSticky";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import getAvailableSubscriptions from "@/actions/(protected)/pub/getAvailableSubscriptionsFromHotel";
import getAvailableServices from "@/actions/(protected)/pub/getAvailableServicesFromHotel";
import getOrderById from "@/actions/(protected)/hotel/getOrderById";
import getAllHotelCustomers from "@/actions/(protected)/hotel/getAllHotelCustomers";
import { getHotelPetByOwner } from "@/actions/(protected)/hotel/getHotelPetByOwner";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import { redirect } from "next/navigation";

const AtDoorReservationPage = async ({
  searchParams,
}: {
  searchParams: Promise<Record<string, string>>;
}) => {
  const searchP = await searchParams;
  const orderId = searchP.orderId;
  const addPetParam = searchP.addPet;
  const checkoutCustomerStepParam = searchP.customerStep;
  const checkoutPetStepParam = searchP.petStep;
  const checkoutSummaryStepParam = searchP.summaryStep;
  const checkoutPaymentStepParam = searchP.paymentStep;
  const selectedPetTypes = searchP.petTypes?.split(",") || [];
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const subscriptionData = await getAvailableSubscriptions(
    hotelData?.data?._id
  );
  const serviceData = await getAvailableServices(hotelData?.data?._id);
  const orderData = await getOrderById(orderId);
  const hotelCustomerId = orderData?.data?.order?.hotelCustomer;
  const hotelCustomerPetData = await getHotelPetByOwner(hotelCustomerId);
  const allHotelCustomers = await getAllHotelCustomers();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  if (membershipData?.data?.membershipType !== "plus") {
    redirect("/hotel/account");
  }

  if (orderData?.data?.order?.status === "confirmed") {
    redirect("/hotel/account");
  }

  const hasStepParams =
    checkoutCustomerStepParam ||
    checkoutPetStepParam ||
    checkoutSummaryStepParam ||
    checkoutPaymentStepParam;

  return (
    <div className="container">
      <AtDoorReservationsContainer
        hotelToken={hotelToken}
        hotelData={hotelData?.data}
        subscriptionData={subscriptionData?.data}
        serviceData={serviceData?.data}
        allHotelCustomers={allHotelCustomers?.data?.hotelCustomers}
        orderData={orderData?.data?.order}
        hotelCustomerId={hotelCustomerId}
        hotelCustomerPetData={hotelCustomerPetData?.data?.hotelPets}
        addPetParam={addPetParam}
        selectedPetTypes={selectedPetTypes}
        stepParams={{
          customer: checkoutCustomerStepParam,
          pet: checkoutPetStepParam,
          summary: checkoutSummaryStepParam,
          payment: checkoutPaymentStepParam,
        }}
      />
      {hasStepParams && (
        <FooterReservation
          orderData={orderData?.data?.order}
          addPetParam={addPetParam}
          hotelToken={hotelToken}
          stepParams={{
            customer: checkoutCustomerStepParam,
            pet: checkoutPetStepParam,
            summary: checkoutSummaryStepParam,
            payment: checkoutPaymentStepParam,
          }}
          hotelCustomerId={orderData?.data?.hotelCustomer}
        />
      )}
      {!hasStepParams && <MobileFooterSticky hotelToken={hotelToken} />}
    </div>
  );
};

export default AtDoorReservationPage;
