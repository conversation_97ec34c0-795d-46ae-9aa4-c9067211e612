"use client";
import type { ChangeEvent, FormEvent } from "react";
import React, { useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Input from "@/shared/Input";
import FormItem from "@/shared/FormItem";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import IconPlus from "@/shared/icons/Plus";
import { useBalance } from "@/hooks/hotel/useBalance";
import LoadingSpinner from "@/shared/icons/Spinner";

interface balanceDataTypes {
  transactionType: number | null;
  transaction: string | null;
  amount: number;
  description: string | null;
  paymentType: string | null;
}

const AddBalance = ({
  balanceId,
  hotelToken,
  onAddSuccess,
}: {
  balanceId: string;
  hotelToken: any;
  onAddSuccess: () => void;
}) => {
  const [balanceData, setBalanceData] = useState<balanceDataTypes>({
    transactionType: null,
    transaction: null,
    amount: 0,
    description: null,
    paymentType: null,
  });

  const [showPopover, setShowPopover] = useState<boolean>(false);
  const [transactionType, setTransactionType] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const { addTransaction } = useBalance();

  const handleAddTransaction = async (event: FormEvent) => {
    await addTransaction(
      event,
      hotelToken,
      balanceData,
      balanceId,
      setLoading,
      setDisabled,
      resetValues
    );
    onAddSuccess();
    setShowPopover(false);
  };

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;

    setBalanceData((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const resetValues = () => {
    setShowPopover(false);
    setLoading(false);
    setTransactionType("");
    setBalanceData({
      transactionType: null,
      transaction: null,
      amount: 0,
      description: null,
      paymentType: null,
    });
  };

  const isBalanceDataValid = (data: balanceDataTypes): boolean => {
    if (data.transactionType === null) return false;

    const isValid = (value: any) => value !== null && value !== "";

    return (
      isValid(data.transactionType) &&
      isValid(data.description) &&
      data.amount !== null &&
      data.amount > 0 &&
      (data.transactionType === 1 ? true : isValid(data.transaction)) &&
      (data.transactionType !== 1 ? true : isValid(data.paymentType))
    );
  };

  const balanceButtonDisabled = isBalanceDataValid(balanceData);

  return (
    <div className="flex justify-center basis-1/3 relative">
      <Popover open={showPopover}>
        <PopoverTrigger
          onClick={() => setShowPopover(true)}
          className={`relative z-20 -mt-12 bg-secondary-6000 rounded-full hover:bg-secondary-700 w-12 h-12 flex items-center justify-center ${showPopover && "pointer-events-none"}`}
        >
          <IconPlus className="!size-6 text-white" />
        </PopoverTrigger>
        <PopoverContent
          className={`w-[350px] shadow-xl overflow-y-auto max-md:max-h-[475px] ${balanceData.transactionType === 1 && "bg-[#eefbef]"} ${balanceData.transactionType === 0 && "bg-[#fdf5f5]"}`}
        >
          <form onSubmit={handleAddTransaction}>
            <div className="space-y-3">
              <div>
                <p className="mb-1 text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  İşlem Türü
                </p>
                <Select
                  onValueChange={(selected) => {
                    setTransactionType(selected);
                    const transactionTypeValue = selected === "payment" ? 1 : 0;
                    setBalanceData({
                      transactionType: transactionTypeValue,
                      transaction: null,
                      amount: 0,
                      description: null,
                      paymentType: null,
                    });
                  }}
                  value={transactionType}
                >
                  <SelectTrigger className="w-[180px] focus:ring-0">
                    <SelectValue placeholder="İşlem türü seç" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="payment">Ödeme</SelectItem>
                    <SelectItem value="service">Servis</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {transactionType === "payment" && (
                <>
                  <div>
                    <p className="mb-1 text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      Ödeme Türü
                    </p>
                    <Select
                      onValueChange={(selected) =>
                        setBalanceData({
                          ...balanceData,
                          paymentType: selected,
                        })
                      }
                      value={balanceData.paymentType || ""}
                    >
                      <SelectTrigger className="w-[180px] focus:ring-0">
                        <SelectValue placeholder="Ödeme türü seç" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Nakit</SelectItem>
                        <SelectItem value="creditCard">Kredi Kartı</SelectItem>
                        <SelectItem value="transfer">EFT/Havale</SelectItem>
                        <SelectItem value="mailOrder">Mail Order</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <FormItem className="w-[180px]" label="Ödeme Tutarı">
                    <Input
                      className="rounded-md"
                      onChange={handleChange}
                      name="amount"
                      min={0}
                      type="number"
                      required
                      value={balanceData.amount}
                    />
                  </FormItem>
                </>
              )}
              {transactionType === "service" && (
                <>
                  <div>
                    <p className="mb-1 text-sm font-medium text-neutral-700 dark:text-neutral-300">
                      Servis Türü
                    </p>
                    <Select
                      onValueChange={(selected) => {
                        setBalanceData({
                          ...balanceData,
                          transaction: selected,
                        });
                      }}
                    >
                      <SelectTrigger className="w-[180px] focus:ring-0">
                        <SelectValue placeholder="Servis türü seç" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="petShower">Pet Shower</SelectItem>
                        <SelectItem value="petTaxi">Pet Taksi</SelectItem>
                        <SelectItem value="training">Eğitim</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <FormItem className="w-[180px]" label="Ödeme Tutarı">
                    <Input
                      className="rounded-md"
                      onChange={handleChange}
                      name="amount"
                      min={0}
                      type="number"
                      required
                      value={balanceData.amount}
                    />
                  </FormItem>
                </>
              )}
            </div>
            {transactionType && (
              <FormItem className="mt-3" label="Açıklama">
                <Textarea
                  name="description"
                  onChange={handleChange}
                  value={balanceData.description || ""}
                />
              </FormItem>
            )}
            <div className="flex justify-end gap-5 mt-5">
              <Button onClick={resetValues} type="button" variant="outline">
                Vazgeç
              </Button>
              <Button
                disabled={disabled || !balanceButtonDisabled}
                type="submit"
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
        </PopoverContent>
      </Popover>
      <span className="absolute w-[52px] z-10 h-12 bg-transparency bottom-[28px]"></span>
      <span className="absolute -bottom-1 border h-[55px] w-[55px] border-t border-gray-300 rounded-full"></span>
    </div>
  );
};

export default AddBalance;
