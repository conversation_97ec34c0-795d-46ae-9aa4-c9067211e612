"use client";
import React from "react";
import type { FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { PolicyDataTypes } from "./PolicyStep";
import { useTranslations } from "next-intl";

interface CancellationOptionProps {
  policyData: PolicyDataTypes;
  setPolicyData: React.Dispatch<React.SetStateAction<PolicyDataTypes>>;
}

const CancellationOption: FC<CancellationOptionProps> = ({
  policyData,
  setPolicyData,
}) => {
  const translate = useTranslations("CancellationOption");
  return (
    <>
      <div className="space-y-2">
        <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm">
          {translate("cancellationOption")}
        </p>
        <Select
          value={policyData.cancellationPolicyType}
          onValueChange={(selected) =>
            setPolicyData({
              ...policyData,
              cancellationPolicyType: selected,
              dateRange: "",
            })
          }
        >
          <SelectTrigger className="w-[210px]">
            <SelectValue placeholder={translate("select")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="NO_CANCELLATION">
              {translate("noCancellation")}
            </SelectItem>
            <SelectItem value="CANCEL_UNTIL_DATE_RANGE">
              {translate("cancelUntilDateRange")}
            </SelectItem>
            <SelectItem value="Other" disabled>
              {translate("other1")}
            </SelectItem>
            <SelectItem value="Other2" disabled>
              {translate("other2")}
            </SelectItem>
            <SelectItem value="Other3" disabled>
              {translate("other3")}
            </SelectItem>
            <SelectItem value="Other4" disabled>
              {translate("other4")}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      {policyData.cancellationPolicyType === "CANCEL_UNTIL_DATE_RANGE" && (
        <div className="space-y-2">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold">
            {translate("dateRange")}
          </p>
          <Select
            value={policyData.dateRange}
            onValueChange={(selected) =>
              setPolicyData({ ...policyData, dateRange: selected })
            }
          >
            <SelectTrigger className="w-[210px]">
              <SelectValue placeholder={translate("select")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_24_hours">
                {translate("last24Hours")}
              </SelectItem>
              <SelectItem value="last_48_hours">
                {translate("last48Hours")}
              </SelectItem>
              <SelectItem value="last_week">{translate("lastWeek")}</SelectItem>
              <SelectItem value="last_10_days">
                {translate("last10Days")}
              </SelectItem>
              <SelectItem value="last_month">
                {translate("lastMonth")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
    </>
  );
};

export default CancellationOption;
