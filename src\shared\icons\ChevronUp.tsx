import type { FC } from "react";
import React from "react";

interface IconChevronUpProps {
  className?: string;
  strokeWidth?: number;
}

const IconChevronUp: FC<IconChevronUpProps> = ({
  className = "size-6",
  strokeWidth = 1.5,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m4.5 15.75 7.5-7.5 7.5 7.5"
      />
    </svg>
  );
};

export default IconChevronUp;
