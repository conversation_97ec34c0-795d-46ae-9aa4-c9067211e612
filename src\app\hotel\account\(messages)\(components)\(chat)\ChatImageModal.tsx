"use client";
import React from "react";
import Lightbox from "yet-another-react-lightbox";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import Download from "yet-another-react-lightbox/plugins/download";
import "yet-another-react-lightbox/styles.css";

interface ChatImageLightboxProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  imageUrl: string;
}

const ChatImageLightbox: React.FC<ChatImageLightboxProps> = ({
  isOpen,
  setIsOpen,
  imageUrl,
}) => {
  return (
    <Lightbox
      open={isOpen}
      close={() => setIsOpen(false)}
      slides={[
        {
          src: imageUrl,
          downloadUrl: `/api/download?url=${encodeURIComponent(imageUrl)}&filename=chat-image.jpg`,
        },
      ]}
      plugins={[Zoom, Download]}
      carousel={{ finite: true }}
      render={{ buttonPrev: () => null, buttonNext: () => null }}
    />
  );
};

export default ChatImageLightbox;
