import React from "react";
import type { FC } from "react";
import BarChartActiveSelect from "../BarChartActiveSelect";
import LineChartOccupancy from "../LineChartOccupancy";
import LineChartRevenue from "../LineChartRevenue";
import BarChartReservationAndCancellation from "../BarChartReservationAndCancellation";
import PetTypeRatePieChart from "../PetTypeRatePieChart";
import PawPlus from "@/shared/PawPlus";

interface ReportSummaryProps {
  hotelAnalytics: any;
  revenueAnalytics: any;
  startDate: string | string[];
  endDate: string | string[];
  membershipData: any;
}

const ReportSummary: FC<ReportSummaryProps> = ({
  hotelAnalytics,
  revenueAnalytics,
  startDate,
  endDate,
  membershipData,
}) => {
  let isFreeUser;
  if (membershipData?.membershipType === "free") {
    isFreeUser = true;
  } else if (membershipData?.membershipType === "plus") {
    isFreeUser = false;
  } else {
    isFreeUser = true;
  }

  const OverlayMessage = () => (
    <div className="absolute inset-0 flex flex-col items-center justify-center text-black text-center font-bold text-lg">
      <PawPlus width="200" height="200" />
      <span>Bu istatistikleri görüntülemek için Plus üye olmalısınız</span>
    </div>
  );

  const ChartContainer = ({ children }: { children: React.ReactNode }) => (
    <div className="relative">
      {children}
      {isFreeUser && <OverlayMessage />}
    </div>
  );

  return (
    <>
      <ChartContainer>
        <div className="flex max-md:flex-col gap-3 mb-3">
          <div
            className={`basis-1/2 ${isFreeUser ? "blur-sm pointer-events-none" : ""}`}
          >
            <LineChartOccupancy
              occupancyData={hotelAnalytics?.occupancyAnalytics}
              startDate={startDate}
              endDate={endDate}
            />
          </div>
          <div
            className={`basis-1/2 ${isFreeUser ? "blur-sm pointer-events-none" : ""}`}
          >
            <BarChartReservationAndCancellation
              data={hotelAnalytics?.cancelledReservations}
              startDate={startDate}
              endDate={endDate}
            />
          </div>
        </div>
        <div className="flex max-lg:flex-col gap-3">
          <div
            className={`basis-2/4 ${isFreeUser ? "blur-sm pointer-events-none" : ""}`}
          >
            <LineChartRevenue
              data={revenueAnalytics}
              startDate={startDate}
              endDate={endDate}
            />
          </div>
          <div className="basis-2/4 flex max-md:flex-col gap-3">
            <div
              className={`basis-1/2 ${isFreeUser ? "blur-sm pointer-events-none" : ""}`}
            >
              <PetTypeRatePieChart
                petTypeData={hotelAnalytics?.petTypeRates}
                startDate={startDate}
                endDate={endDate}
              />
            </div>
            <div
              className={`basis-1/2 ${isFreeUser ? "blur-sm pointer-events-none" : ""}`}
            >
              <BarChartActiveSelect
                title="Tercih Edilen Odalar"
                preferredRoomData={hotelAnalytics?.preferredRooms}
                startDate={startDate}
                endDate={endDate}
              />
            </div>
          </div>
        </div>
      </ChartContainer>
    </>
  );
};

export default ReportSummary;
