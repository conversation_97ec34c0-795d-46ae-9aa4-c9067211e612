import React from "react";
import { cookies } from "next/headers";
import getHotelCards from "@/actions/(protected)/hotel/getHotelCards";
import ManagePayment from "./(components)/ManagePayment";

const ManagePaymentPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelCards = await getHotelCards();
  return (
    <div className="min-h-screen container">
      <ManagePayment
        hotelCards={hotelCards?.data?.data}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default ManagePaymentPage;
