"use client";
import React from "react";
import type { FC, ChangeEvent } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";

interface descriptionTypes {
  allergic: string;
  diseases: string;
  operation: string;
  internalParasite: string;
  externalParasite: string;
  medicines: string;
}

interface PetHealthInformationsProps {
  petInformations: PetInformationTypes;
  setPetInformations: React.Dispatch<React.SetStateAction<PetInformationTypes>>;
  descriptions: descriptionTypes;
  setDescriptions: React.Dispatch<React.SetStateAction<descriptionTypes>>;
}

const PetHealthInformations: FC<PetHealthInformationsProps> = ({
  petInformations,
  setPetInformations,
  descriptions,
  setDescriptions,
}) => {
  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setPetInformations((prevState) => {
      return {
        ...prevState,
        [name]: value.trim() ? [value] : [],
      };
    });
  };
  const handleChangeDate = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPetInformations((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const today = adjustDateToTimezone(new Date());
  const adjustedToday = today && today.toISOString().split("T")[0];

  return (
    <div className="mt-7">
      <h2 className="font-semibold text-lg mb-2">
        Evcil Hayvan Sağlık Bilgileri (opsiyonel)
      </h2>
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 mb-10 gap-3">
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Alerji
          </p>
          <Select
            onValueChange={(selected) => {
              setDescriptions({ ...descriptions, allergic: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                allergicTo: [],
              }));
            }}
            value={descriptions.allergic}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Alerji" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Var</SelectItem>
              <SelectItem value="false">Yok</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.allergic === "true" && (
            <FormItem className="max-w-96" label="Alerjileri belirtiniz*">
              <Textarea
                name="allergicTo"
                onChange={handleChange}
                value={petInformations.allergicTo[0] || ""}
              />
            </FormItem>
          )}
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Hastalık
          </p>
          <Select
            onValueChange={(selected) => {
              setDescriptions({ ...descriptions, diseases: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                hereditaryDiseases: [],
              }));
            }}
            value={descriptions.diseases}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Hastalık" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Var</SelectItem>
              <SelectItem value="false">Yok</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.diseases === "true" && (
            <FormItem className="max-w-96" label="Hastalıkları belirtiniz*">
              <Textarea
                name="hereditaryDiseases"
                onChange={handleChange}
                value={petInformations.hereditaryDiseases[0] || ""}
              />
            </FormItem>
          )}
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Operasyon
          </p>
          <Select
            onValueChange={(selected) => {
              setDescriptions({ ...descriptions, operation: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                operationHistory: [],
              }));
            }}
            value={descriptions.operation}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Operasyon" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Var</SelectItem>
              <SelectItem value="false">Yok</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.operation === "true" && (
            <FormItem
              className="max-w-96"
              label="Geçirdiği operasyonları belirtiniz*"
            >
              <Textarea
                name="operationHistory"
                onChange={handleChange}
                value={petInformations.operationHistory[0] || ""}
              />
            </FormItem>
          )}
        </div>
      </div>
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 mb-10 gap-3">
        <div>
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
            İç Parazit
          </p>
          <Select
            onValueChange={(selected) => {
              const selectedValue = selected === "true" ? true : false;
              setDescriptions({ ...descriptions, internalParasite: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                internalParasiteTreatment: selectedValue,
                internalTreatmentDate: "",
              }));
            }}
            value={descriptions.internalParasite}
          >
            <SelectTrigger className="w-[180px] mb-2">
              <SelectValue placeholder="İç Parazit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yapıldı</SelectItem>
              <SelectItem value="false">Yapılmadı</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.internalParasite === "true" && (
            <FormItem label="İç parazitin yapıldığı tarihi seçin*">
              <Input
                onChange={handleChangeDate}
                name="internalTreatmentDate"
                type="date"
                max={adjustedToday}
                className="max-w-96"
                value={petInformations.internalTreatmentDate}
              />
            </FormItem>
          )}
        </div>
        <div>
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
            Dış Parazit
          </p>
          <Select
            onValueChange={(selected) => {
              const selectedValue = selected === "true" ? true : false;
              setDescriptions({ ...descriptions, externalParasite: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                externalParasiteTreatment: selectedValue,
                externalTreatmentDate: "",
              }));
            }}
            value={descriptions.externalParasite}
          >
            <SelectTrigger className="w-[180px] mb-2">
              <SelectValue placeholder="Dış Parazit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yapıldı</SelectItem>
              <SelectItem value="false">Yapılmadı</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.externalParasite === "true" && (
            <FormItem label="Dış parazitin yapıldığı tarihi seçin*">
              <Input
                onChange={handleChangeDate}
                name="externalTreatmentDate"
                type="date"
                max={adjustedToday}
                className="max-w-96"
                value={petInformations.externalTreatmentDate}
              />
            </FormItem>
          )}
        </div>
        <div>
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
            İlaçlar
          </p>
          <Select
            onValueChange={(selected) => {
              setDescriptions({ ...descriptions, medicines: selected });
              setPetInformations((prevState) => ({
                ...prevState,
                medicine: "",
              }));
            }}
            value={descriptions.medicines}
          >
            <SelectTrigger className="w-[180px] mb-2">
              <SelectValue placeholder="İlaçlar" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Var</SelectItem>
              <SelectItem value="false">Yok</SelectItem>
            </SelectContent>
          </Select>
          {descriptions.medicines === "true" && (
            <FormItem label="Kullandığı ilaçları belirtiniz*">
              <Textarea
                onChange={(event) =>
                  setPetInformations((prevState) => ({
                    ...prevState,
                    medicine: event.target.value,
                  }))
                }
                name="medicine"
                className="max-w-96"
                value={petInformations.medicine}
              />
            </FormItem>
          )}
        </div>
      </div>
    </div>
  );
};

export default PetHealthInformations;
