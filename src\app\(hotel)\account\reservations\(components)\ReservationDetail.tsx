import React from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import Image from "next/image";
import { Button } from "@/components/ui/button";

const ReservationDetail = ({ reservationData }: { reservationData: any }) => {
  const translate = useTranslations("ReservationDetail");
  return (
    <Sheet>
      <SheetTrigger className="font-medium">
        {translate("details")}
      </SheetTrigger>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="mb-5">
            {translate("reservationDetails")}
          </SheetTitle>
          <SheetDescription className="sr-only"></SheetDescription>
        </She<PERSON><PERSON>eader>
        <div className="space-y-3">
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("roomInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("roomGroupName")}:</p>
              <p>{reservationData?.room?.roomGroup?.roomGroupName}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("roomName")}:</p>
              <p>{reservationData?.room?.roomName}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("roomCapacity")}:</p>
              <p>{reservationData?.room?.roomCapacity}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petType")}:</p>
              <p>{reservationData?.room?.petType}</p>
            </div>
          </div>
          <Separator />
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("accommodationInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("checkInDate")}:</p>
              <p>{formatDateToDayMonthYear(reservationData?.startDate)}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("checkOutDate")}:</p>
              <p>{formatDateToDayMonthYear(reservationData?.endDate)}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("stayDuration")}:</p>
              <p>
                {reservationData?.nights} {translate("night")}
              </p>
            </div>
          </div>
          <Separator />
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("petInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petName")}:</p>
              <p>{reservationData?.pet?.name}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petAge")}:</p>
              <p>{reservationData?.pet?.age}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petKind")}:</p>
              <p>{reservationData?.pet?.kind}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petBreed")}:</p>
              <p>{reservationData?.pet?.breed}</p>
            </div>
            {reservationData?.pet?.images?.length > 0 && (
              <div className="grid grid-cols-2 gap-3">
                {reservationData?.pet?.images?.map((image: any) => {
                  return (
                    <div key={image._id}>
                      <Image
                        src={image?.img800 || image?.src}
                        width={150}
                        height={150}
                        alt="pet-image"
                        className="my-2"
                      />
                      <a
                        href={image?.src}
                        target="_blank"
                        className="text-sm hover:text-secondary-6000 cursor-pointer"
                      >
                        <Button className="text-xs w-full" variant="outline">
                          {translate("showPhoto")}
                        </Button>
                      </a>
                    </div>
                  );
                })}
              </div>
            )}
            {reservationData?.pet?.documentPhotos?.length > 0 && (
              <div>
                <Separator className="my-2" />
                <h4 className="my-2 font-semibold">
                  {translate("vaccinationReport")}
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  {reservationData?.pet?.documentPhotos?.map((image: any) => {
                    return (
                      <div key={image._id}>
                        <Image
                          src={image?.img800 || image?.src}
                          width={150}
                          height={150}
                          alt="vaccination-report-image"
                          className="my-2"
                        />
                        <a
                          href={image?.src}
                          target="_blank"
                          className="text-sm hover:text-secondary-6000 cursor-pointer"
                        >
                          <Button className="text-xs w-full" variant="outline">
                            {translate("showPhoto")}
                          </Button>
                        </a>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
          <Separator />
          <div className="space-y-1">
            <h3 className="mb-2 text-lg font-semibold">
              {translate("petOwnerInformation")}
            </h3>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("petOwnerName")}:</p>
              <p>{reservationData?.petOwner?.fullName}</p>
            </div>
            <div className="flex gap-1 text-sm capitalize">
              <p className="font-semibold">{translate("phone")}:</p>
              <a href={`tel:${reservationData?.petOwner?.phone}`}>
                {reservationData?.petOwner?.phone}
              </a>
            </div>
            <div className="flex gap-1 text-sm">
              <p className="font-semibold">{translate("email")}:</p>
              <a href={`mailto:${reservationData?.petOwner?.email}`}>
                {reservationData?.petOwner?.email}
              </a>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ReservationDetail;
