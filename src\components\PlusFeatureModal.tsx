import { FC } from "react";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import pluspet from "@/images/pluspet.png";
import Image from "next/image";
import { X } from "lucide-react";
import Link from "next/link";

interface PlusFeatureModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  message: string;
}

const PlusFeatureModal: FC<PlusFeatureModalProps> = ({
  isOpen,
  setIsOpen,
  message,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[600px] md:h-[600px] border-none shadow-none overflow-visible bg-transparent">
        <div className="relative md:mx-10 mt-16">
          <div className="bg-[#fafafa] border-4 border-secondary-6000 rounded-[50px] px-8 pt-8">
            <div>
              <div className="font-bold text-xl md:text-2xl mx-auto ">
                Bu özellik Plus üyeler içindir
              </div>
              <DialogClose className="absolute top-8 right-8 hover:text-secondary-6000 focus:outline-none hidden md:block">
                <X className="w-8 h-8" strokeWidth={3} />
              </DialogClose>
            </div>
            <div className="font-semibold text-base md:text-lg mt-5">
              Plus üye olarak, bu özelliğe erişim sağlayabilirsiniz.
            </div>
            <Link href={"/hotel/checkout"}>
              <div className="flex flex-col md:flex-row justify-center items-center md:justify-between mt-8 mb-6 md:mb-0">
                <Image
                  src={pluspet}
                  alt="plus-pet"
                  className="w-12 md:w-24 h-auto"
                />
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white w-28">
                  Plus Üye Ol
                </Button>
              </div>
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PlusFeatureModal;
