"use client";

import { useState, type FC } from "react";
import {
  Popover,
  <PERSON>over<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { BellIcon } from "@heroicons/react/24/outline";
import {
  PawPrint,
  Scissors,
  MessageCircle,
  ChartLine,
  IdCard,
  Info,
} from "lucide-react";
import { formatDistanceToNowStrict } from "date-fns";
import { tr } from "date-fns/locale";
import { useHotelNotification } from "@/hooks/hotel/useHotelNotification";
import { useRouter } from "next/navigation";

interface NotificationDropdownProps {
  hotelToken: string | undefined;
  unseenWebPushes?: {
    _id: string;
    title: string;
    body: string;
    createdAt: string;
    type: string;
  }[];
  className?: string;
  refresh?: () => void;
}

const getIconByType = (type: string) => {
  switch (type) {
    case "reservation":
      return <PawPrint className="text-green-600 size-6" />;
    case "service":
      return <Scissors className="text-orange-600 size-6" />;
    case "subscription":
      return <IdCard className="text-purple-600 size-6" />;
    case "chat":
      return <MessageCircle className="text-blue-600 size-6" />;
    case "statistic":
      return <ChartLine className="text-red-600 size-6" />;
    default:
      return <Info className="text-gray-600 size-6" />;
  }
};

const formatTimeDifference = (createdAt: string) => {
  return formatDistanceToNowStrict(new Date(createdAt), {
    addSuffix: true,
    locale: tr,
  });
};

const NotificationDropdown: FC<NotificationDropdownProps> = ({
  hotelToken,
  unseenWebPushes,
  className,
  refresh,
}) => {
  const { updateWebPush } = useHotelNotification();
  const router = useRouter();
  const hasNotifications = unseenWebPushes && unseenWebPushes.length > 0;
  const [showPopover, setShowPopover] = useState<boolean>(false);

  const handleMarkAsRead = async (webPushId: string, type: string) => {
    try {
      await updateWebPush(hotelToken, { isSeen: true }, webPushId);

      if (refresh) refresh();

      setShowPopover(false);

      switch (type) {
        case "reservation":
          router.push("/hotel/account/reservations");
          break;
        case "service":
          router.push("/hotel/account/services/services-sold");
          break;
        case "subscription":
          router.push("/hotel/account/subscriptions/subscriber-list");
          break;
        case "chat":
          router.push("/hotel/account/messages");
          break;
        case "statistic":
          router.push("/hotel/account/dashboard");
          break;
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className={`${className} relative flex`}>
      <Popover open={showPopover} onOpenChange={setShowPopover}>
        <PopoverTrigger
          onClick={() => setShowPopover(true)}
          className="relative size-10 items-center justify-center self-center rounded-full text-base font-medium"
        >
          {hasNotifications && (
            <span className="absolute right-2 top-1.5 flex h-2.5 w-2.5">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-500 opacity-75"></span>
              <span className="relative inline-flex h-2.5 w-2.5 rounded-full bg-green-500"></span>
            </span>
          )}
          <BellIcon className="size-6 opacity-80 hover:opacity-100" />
        </PopoverTrigger>
        <PopoverContent
          side="bottom"
          align="end"
          className="z-10 w-screen max-w-xs px-4 sm:max-w-sm sm:p-0 rounded-3xl bg-white dark:bg-neutral-800"
        >
          <div className="p-7">
            <h3 className="text-xl font-semibold">Bildirimler</h3>
            <div className="mt-6 space-y-1 max-h-80 overflow-y-auto -mr-4">
              {hasNotifications ? (
                unseenWebPushes.map((item, index) => (
                  <div
                    onClick={() => handleMarkAsRead(item._id, item.type)}
                    key={index}
                    className="relative flex rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 mr-1.5 cursor-pointer"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        {getIconByType(item.type)}
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-200">
                          {item.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {item.body}
                        </p>
                        <p className="text-xs text-gray-400">
                          {formatTimeDifference(item.createdAt)}
                        </p>
                      </div>
                      <span className="absolute right-1 top-1/2 size-2 -translate-y-1/2 rounded-full bg-secondary-6000"></span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-sm text-gray-500 dark:text-gray-400">
                  Yeni bildirim yok
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default NotificationDropdown;
