import React from "react";
import { cookies } from "next/headers";
import getHotelUserInformations from "@/actions/(protected)/hotel/getHotelUserInformation";
import HotelUserTable from "./(components)/HotelUserTable";
import AddNewHotelUser from "./(components)/AddNewHotelUser";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";

const AccountPage = async () => {
  const hotelUserInformations = await getHotelUserInformations();
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div className="container pt-8">
      <AddNewHotelUser
        hotelToken={hotelToken}
        membershipData={membershipData?.data}
      />
      <HotelUserTable
        hotelUserInformations={hotelUserInformations.data.docs}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default AccountPage;
