"use client";
import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "@heroicons/react/24/outline";
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth";
import DatePickerCustomDay from "@/components/DatePickerCustomDay";
import DatePicker from "react-datepicker";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import convertOneDateToString from "@/utils/convertOneDateToString";
import { createLocalDate } from "@/utils/createLocalDate";
import { Button } from "@/components/ui/button";
import { getSelectedHotelAllocation } from "@/actions/(protected)/pub/getSelectedHotelAllocation";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useDispatch } from "react-redux";
import { setCalculatedRoomData } from "@/store/features/calculatedRoomData/calculated-room-data-slice";
import { registerLocale } from "react-datepicker";
import { tr } from "date-fns/locale";

interface HotelCustomerPetSelectProps {
  customerData: any;
  hotelToken: string | undefined;
  onSelectRoom: (
    selectedRoomId: string,
    startDate: string,
    endDate: string,
    selectedPetId: string
  ) => void;
  initialValues?: {
    selectedRoomId: string;
    startDate: string;
    endDate: string;
    selectedPetId: string;
    rooms?: string;
  };
}

const HotelCustomerPetSelectStep: React.FC<HotelCustomerPetSelectProps> = ({
  customerData,
  hotelToken,
  onSelectRoom,
  initialValues,
}) => {
  registerLocale("tr", tr);
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const { calculateReservation } = useHotelAtDoorReservation();
  const [datePickerIsOpen, setDatePickerIsOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date | string | undefined>(
    initialValues?.startDate ||
      new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0]
  );
  const [endDate, setEndDate] = useState<Date | string | undefined>(
    initialValues?.endDate ||
      new Date(new Date().getTime() + 14 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0]
  );
  const [selectedPetId, setSelectedPetId] = useState<string>(
    initialValues?.selectedPetId || ""
  );
  const [rooms, setRooms] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchRooms = async () => {
      if (!startDate || !endDate || !selectedPetId) return;

      setLoading(true);

      const petType =
        customerData?.pets
          .find(
            (pet: { _id: string; kind: string }) => pet._id === selectedPetId
          )
          ?.kind.toLowerCase() || "";
      const adjustedStartDate =
        adjustDateToTimezone(startDate)?.toISOString().split("T")[0] || "";
      const adjustedEndDate =
        adjustDateToTimezone(endDate)?.toISOString().split("T")[0] || "";

      try {
        const result = await getSelectedHotelAllocation(
          customerData?.hotelCustomer?.hotel,
          petType,
          adjustedStartDate,
          adjustedEndDate
        );
        if (result?.data?.allocationData) {
          setRooms(result.data.allocationData);
        }
      } catch (error) {
        console.error("Error fetching rooms:", error);
      }
      setLoading(false);
    };
    fetchRooms();
  }, [startDate, endDate, selectedPetId]);

  useEffect(() => {
    dispatch(setCalculatedRoomData(""));
  }, [dispatch]);

  const handlePetChange = (petId: string) => {
    setSelectedPetId(petId);
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  const formattedStartDate = startDate && convertOneDateToString(startDate);
  const formattedEndDate = endDate && convertOneDateToString(endDate);

  const renderInput = () => {
    return (
      <div className="relative z-10 flex flex-1 cursor-pointer items-center justify-center space-x-3 py-5 px-7 focus:outline-none">
        <div className="text-neutral-300 dark:text-neutral-400">
          <CalendarIcon className="size-5 lg:size-7" />
        </div>
        <div className="grow text-left">
          <span className="block font-semibold xl:text-lg whitespace-nowrap">
            {(formattedStartDate && formattedStartDate) || "Tarih seç"}
            {formattedEndDate ? " - " + formattedEndDate : ""}
          </span>
          <span className="mt-1 block text-sm font-light leading-none text-neutral-400">
            Giriş - Çıkış
          </span>
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className="text-xl font-bold">Tarih ve Oda</div>
      <div className="py-4 px-16 space-y-4">
        <div className="top-full flex w-full max-w-sm grow gap-2 px-1 py-2 sm:min-w-[340px]">
          <div className="flex flex-col rounded-3xl border border-neutral-200 dark:border-neutral-700">
            <Popover open={datePickerIsOpen}>
              <PopoverTrigger asChild>
                <div onClick={() => setDatePickerIsOpen(true)}>
                  {renderInput()}
                </div>
              </PopoverTrigger>
              <PopoverContent className="w-full border-none bg-transparent pointer-events-auto">
                <div className="overflow-hidden rounded-3xl bg-white max-md:w-[95vw] max-md:max-w-md p-6 max-md:p-4 shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-neutral-800">
                  <div className="max-md:max-h-[50vh] max-md:overflow-y-scroll">
                    <DatePicker
                      locale={"tr"}
                      selected={
                        startDate ? createLocalDate(startDate) : undefined
                      }
                      minDate={new Date()}
                      onChange={onChangeDate}
                      startDate={
                        startDate ? createLocalDate(startDate) : undefined
                      }
                      endDate={endDate ? createLocalDate(endDate) : undefined}
                      selectsRange
                      monthsShown={2}
                      showPopperArrow={false}
                      inline
                      renderCustomHeader={(p) => (
                        <DatePickerCustomHeaderTwoMonth {...p} />
                      )}
                      renderDayContents={(day, date) => (
                        <DatePickerCustomDay dayOfMonth={day} date={date} />
                      )}
                    />
                  </div>
                  <div className="mt-3">
                    <Button
                      type="button"
                      onClick={() => {
                        setDatePickerIsOpen(false);
                      }}
                      className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                    >
                      Tamam
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
            <div className="top-full flex w-full max-w-sm grow gap-2 px-1 py-2 sm:min-w-[340px]">
              <Select value={selectedPetId} onValueChange={handlePetChange}>
                <SelectTrigger className="h-[45px] w-full rounded-2xl border-none focus:ring-transparent">
                  <SelectValue placeholder="Lütfen evcil hayvan seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  {customerData?.pets &&
                    customerData?.pets.map((pet: any, index: number) => {
                      return (
                        <SelectItem key={index} value={pet._id}>
                          {pet.name}
                        </SelectItem>
                      );
                    })}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div className="rounded-3xl border border-neutral-200 dark:border-neutral-700 p-1">
          <Select
            disabled={!startDate || !endDate || !selectedPetId || loading}
            onValueChange={(selectedRoomId) => {
              calculateReservation(
                hotelToken,
                selectedRoomId,
                startDate,
                endDate,
                selectedPetId
              );
              onSelectRoom(
                selectedRoomId,
                startDate?.toString() || "",
                endDate?.toString() || "",
                selectedPetId
              );
            }}
          >
            <SelectTrigger className="h-[45px] w-full rounded-2xl border-none focus:ring-transparent">
              <SelectValue placeholder="Lütfen oda seçiniz" />
            </SelectTrigger>
            <SelectContent>
              {rooms.length > 0 ? (
                rooms.map((room: any, index: number) => (
                  <SelectItem key={index} value={room?.firstAvailableRoom?._id}>
                    <span className="font-semibold">
                      {room?.roomGroup?.roomGroupName}
                    </span>{" "}
                    -{" "}
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(room?.avgRoomPrice)) + "₺"}{" "}
                    (
                    <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                      gecelik fiyatı
                    </span>
                    )
                  </SelectItem>
                ))
              ) : (
                <div className="text-center p-2 text-neutral-400 dark:text-neutral-500">
                  Uygun oda bulunamadı
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
        {calculatedRoomData && (
          <div className="mx-2">
            <div className="font-semibold">
              {calculatedRoomData?.allocations[0]?.roomDetails?.roomName}
            </div>
            <div className="mt-0.5">
              <span>
                {new Intl.NumberFormat("tr-TR", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(Number(calculatedRoomData?.roomPrice)) + "₺"}
                {""} x {calculatedRoomData?.nights} gece
              </span>
            </div>
            <div className="w-full border-b border-neutral-200 dark:border-neutral-700 mt-3"></div>
            <div className="font-semibold flex justify-between">
              <span>Toplam</span>
              <span>
                {new Intl.NumberFormat("tr-TR", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }).format(Number(calculatedRoomData?.roomPriceTotal)) + "₺"}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HotelCustomerPetSelectStep;
