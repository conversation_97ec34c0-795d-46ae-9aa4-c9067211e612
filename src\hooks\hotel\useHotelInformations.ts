import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import {
  ImageResizeFitType,
  uploadMultiToS3,
  uploadSingleToS3,
} from "@/lib/s3BucketHelper";
import { HOTEL_API_PATHS, S3_API_PATHS } from "@/utils/apiUrls";
import type { HotelAddressApiTypes } from "@/types/hotel/hotelInformationType";
import { useRouter } from "next/navigation";

export const useHotelInformationsActions = () => {
  const { toast } = useToast();
  const router = useRouter();

  // UPDATES HOTEL INFORMATIONS
  const handleHotelInformations = async (
    event: FormEvent,
    hotelToken: string | undefined,
    HotelInformations: {
      hotelName: string;
      // hotelTaxNumber: string;
      // hotelTaxOffice: string;
      hotelDescription: string;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            hotelName: HotelInformations.hotelName,
            hotelDescription: HotelInformations.hotelDescription,
            // taxNumber: HotelInformations.hotelTaxNumber,
            // taxOffice: HotelInformations.hotelTaxOffice,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Bilgi Güncelleme",
          description: "Otel bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES HOTEL ADDRESS
  const handleHotelAddress = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelAddress: HotelAddressApiTypes,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            address: {
              cityName: hotelAddress.cityName,
              region: hotelAddress.region,
              district: hotelAddress.district,
              streetName: hotelAddress.streetName,
              buildingName: hotelAddress.buildingName,
              buildingNumber: hotelAddress.buildingNumber,
              postalZone: hotelAddress.postalZone,
            },
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Adres Güncelleme",
          description: "Adres bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const addHotelMap = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelMap: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    method: string,
    resetInputs?: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: method,
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            googleMapUrl: hotelMap,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          if (resetInputs) {
            resetInputs();
          }
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Harita Ekleme",
          description: "Harita başarıyla eklendi.",
        });
        if (resetInputs) {
          resetInputs();
        }
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES HOTEL ACCEPTED PET TYPES
  const handleHotelPetTypes = async (
    event: FormEvent,
    hotelToken: string | undefined,
    acceptedPetTypes: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            acceptedPetTypes: acceptedPetTypes,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Evcil Hayvan Tür Güncelleme",
          description: "Tür bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES HOTEL CARE SERVICES
  const handleHotelCareServices = async (
    event: FormEvent,
    hotelToken: string | undefined,
    careServices: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            careServices: careServices,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Bakım Hizmetleri Güncelleme",
          description: "Bakım hizmetleri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES HOTEL FEATURES
  const handleHotelFeatures = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelFeatures: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            hotelFeatures: hotelFeatures,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Otel Özellikleri Güncelleme",
          description: "Otel Özellikleri başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES HOTES PHOTOS
  const uploadPhotoHandler = async (
    hotelImageArray: string[],
    hotelToken: string | undefined
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            images: hotelImageArray,
          }),
        });

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          throw new Error("Network response was not ok");
        }

        if (data.success) {
          toast({
            variant: "success",
            duration: 3500,
            title: "Fotoğraf Değiştirme",
            description: "Fotoğraf güncellendi.",
          });
          revalidatePathHandler("/hotel/account/hotel-informations");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // DELETES SELECTED PHOTO
  const deleteHotelPhoto = async (
    event: FormEvent,
    hotelToken: string | undefined,
    imageId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeletePhotoIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    revalitePath: string,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.imageUpload}/deleteImage?imageId=${imageId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeletePhotoIsOpen(false);
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Fotoğraf Silme",
          description: "Fotoğraf başarıyla silindi.",
        });
        revalidatePathHandler(revalitePath);
        setDeletePhotoIsOpen(false);
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPLOADS IMAGES TO S3
  const photoInputHandler = async (
    photoFileObject: FileList | null,
    hotelToken: string | undefined,
    hotelId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setImage: React.Dispatch<React.SetStateAction<string[] | []>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<React.SetStateAction<FileList | null>>
  ) => {
    if (photoFileObject && hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await uploadMultiToS3(
          photoFileObject,
          `hotel/${hotelId}/photos/`,
          hotelToken,
          ImageResizeFitType.fill
        );

        if (response.success) {
          setLoading(false);
          setImage([]);
          setPhotoFileObject(null);
          setTimeout(() => {
            setDisabled(false);
          }, 1000);
          const idsArray = response.data.map((item: any) => item._id);
          uploadPhotoHandler(idsArray, hotelToken);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const putHotelFiles = async (
    files: string[],
    hotelToken: string | undefined
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            files,
          }),
        });

        const data = await response.json();

        if (data.success) {
          revalidatePathHandler("/hotel/account/hotel-informations");
        }
      } catch (error) {
        console.error("Error updating files:", error);
      }
    }
  };

  const uploadFileHandler = async (
    event: FormEvent,
    files: File[] | FileList | null,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    filePath: string,
    doctype: string,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (files && files?.length > 0 && hotelToken) {
      setLoading(true);
      setDisabled(true);
      let filesArray: FileList | null = null;

      if (files instanceof FileList) {
        filesArray = files;
      } else if (Array.isArray(files)) {
        const dataTransfer = new DataTransfer();
        files.forEach((file) => dataTransfer.items.add(file));
        filesArray = dataTransfer.files;
      }

      if (filesArray && filesArray.length > 0) {
        try {
          const response = await uploadMultiToS3(
            filesArray,
            filePath,
            hotelToken,
            undefined,
            doctype
          );

          if (response.success) {
            toast({
              variant: "success",
              duration: 3500,
              title: "Dosya Yükleme",
              description: "Dosyalar başarıyla yüklendi.",
            });

            const idsArray = response.data.map((item: any) => item._id);
            await putHotelFiles(idsArray, hotelToken);
            setLoading(false);
            setTimeout(() => {
              setDisabled(false);
            }, 3000);
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            toast({
              variant: "error",
              duration: 3500,
              title: "Hata",
              description: "Dosya yükleme sırasında bir hata oluştu.",
            });
            setLoading(false);
            setDisabled(false);
          }
        } catch (error) {
          console.error("Dosya yükleme hatası:", error);
        }
      }
    }
  };

  // DELETES SELECTED HOTEL FILE
  const deleteHotelFileHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    fileId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeleteFileIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.fileUpload}/deleteFile?fileId=${fileId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeleteFileIsOpen(false);
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Dosya Kaldırma",
          description: "Dosya başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/hotel-informations/hotel-files");
        setDeleteFileIsOpen(false);
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // Otel logosu yükleme işleyicisi
  const hotelLogoHandler = async (
    event: FormEvent,
    file: File | null | undefined,
    hotelId: string | undefined,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<
      React.SetStateAction<File | null | undefined>
    >,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (file && hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await uploadSingleToS3(
          file,
          `hotel/${hotelId}/logo/`,
          ImageResizeFitType.fill,
          hotelToken
        );

        if (response.success) {
          const logoId = response.data[0]._id;
          await updateHotelLogo(logoId, hotelToken);
          toast({
            variant: "success",
            duration: 3500,
            title: "Logo Yükleme",
            description: "Otel logosu başarıyla güncellendi.",
          });
          revalidatePathHandler("/hotel/account/hotel-informations");
        } else {
          setLoading(false);
          setDisabled(false);
          setPhotoFileObject(null);
          throw new Error("Logo yükleme başarısız oldu.");
        }
      } catch (error) {
        console.error("Logo yükleme hatası:", error);
        toast({
          variant: "error",
          duration: 3500,
          title: "Hata",
          description: "Logo yükleme sırasında bir hata oluştu.",
        });
      } finally {
        setLoading(false);
        setPhotoFileObject(null);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      }
    }
  };

  // Otel logosunu güncelleme
  const updateHotelLogo = async (logoId: string, hotelToken: string) => {
    try {
      const response = await fetch(HOTEL_API_PATHS.myHotel, {
        method: "PUT",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          logo: logoId,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Logo güncelleme hatası:", error);
      throw error;
    }
  };

  // UPDATES HOTEL STATUS
  const handleHotelStatusUpdate = async (
    event: FormEvent,
    hotelToken: string | undefined,
    newStatus: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.updateHotelStatus, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            newStatus,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        router.push("/hotel/account");
        revalidatePathHandler("/hotel/account");
        setLoading(false);
      } catch (error) {
        console.error("Error updating hotel status:", error);
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: "Otel durumu güncellenirken bir hata oluştu.",
        });
      }
    }
  };

  return {
    handleHotelInformations,
    handleHotelAddress,
    handleHotelPetTypes,
    handleHotelCareServices,
    handleHotelFeatures,
    photoInputHandler,
    uploadFileHandler,
    hotelLogoHandler,
    deleteHotelPhoto,
    deleteHotelFileHandler,
    handleHotelStatusUpdate,
    addHotelMap,
  };
};
