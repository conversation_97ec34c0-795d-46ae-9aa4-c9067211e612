"use client";
import type { FC, FormEvent, ChangeEvent } from "react";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import { v4 as uuid } from "uuid";
import { emailRegex } from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";
import IconEye from "@/shared/icons/Eye";
import IconEyeOff from "@/shared/icons/EyeOff";
import { useMobile } from "@/hooks/useMobile";
import LoadingSpinner from "@/shared/icons/Spinner";

export interface LoginContainerProps {
  role: string;
  apiPath: string;
  queryParams: any;
}

const LoginContainer: FC<LoginContainerProps> = ({
  role,
  apiPath,
  queryParams,
}) => {
  const translate = useTranslations("LoginPage");
  const { expoPushToken } = useMobile(queryParams);

  const { toast } = useToast();
  const router = useRouter();

  const [isEmail, setIsEmail] = useState<boolean>(false);
  const [deviceId, setDeviceId] = useState(uuid());
  const [formData, setFormData] = useState<{
    email: string;
    password: string;
  }>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const inputCheck = formData.email.length > 0 && formData.password.length > 0;

  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setDisabled(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}${apiPath}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            [isEmail ? "email" : "username"]: formData.email,
            password: formData.password,
            deviceId: deviceId,
            expoPushToken: expoPushToken,
          }),
        }
      );

      const result = await response.json();

      if (!response.ok || !result.success) {
        const errorMessage = result.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        setDisabled(false);
        throw new Error("Network response was not ok");
      }

      if (result.success) {
        await fetch("/api/set-cookie", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            tokenName: "token",
            token: result.data.hotelToken,
            deviceIdName: "deviceId",
            deviceId: deviceId,
          }),
        });
        router.push("/account");
        setTimeout(() => {
          setLoading(false);
          setDisabled(false);
        }, 2000);
      } else {
        toast({
          variant: "error",
          duration: 3000,
          title: "Hata",
          description: result.error,
        });
      }
    } catch (error: any) {
      console.log(error);
    }
  };
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    if (name === "email") {
      if (emailRegex.test(value.toLowerCase().trim())) {
        setIsEmail(true);
      } else {
        setIsEmail(false);
      }
    }

    setFormData((prevState) => ({
      ...prevState,
      [name]: name === "email" ? value.toLowerCase().trim() : value,
    }));
  };

  return (
    <div className={`nc-PageLogin`}>
      <div className="">
        <div className="mx-auto max-w-md space-y-6">
          <form className="grid grid-cols-1 gap-6" onSubmit={handleLogin}>
            <label className="block">
              <span className="text-neutral-800 dark:text-neutral-200">
                {translate("loginUsername")}
              </span>
              <Input
                type="text"
                className="mt-1"
                onChange={handleChange}
                name="email"
                autoCapitalize="none"
              />
            </label>
            <label className="relative block">
              <span className="flex items-center justify-between text-neutral-800 dark:text-neutral-200">
                {translate("loginPassword")}
              </span>
              <Input
                type={`${showPassword === true ? "text" : "password"}`}
                className="mt-1"
                name="password"
                onChange={handleChange}
              />
              <IconEye
                className={`absolute right-4 top-10 size-5 cursor-pointer ${
                  showPassword ? "hidden" : "block"
                }`}
                onClick={() => setShowPassword(true)}
              />
              <IconEyeOff
                className={`absolute right-4 top-10 size-5 cursor-pointer ${
                  showPassword ? "block" : "hidden"
                }`}
                onClick={() => setShowPassword(false)}
              />
              <span className="mt-1 block text-right">
                <Link
                  href={`/auth/forgot-password?role=${role}`}
                  className="text-sm font-medium underline"
                >
                  {translate("loginForgotPassword")}
                </Link>
              </span>
            </label>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={disabled || !inputCheck}
              type="submit"
            >
              {loading ? <LoadingSpinner /> : translate("loginLogin")}
            </Button>
          </form>
          <span className="block text-center text-neutral-700 dark:text-neutral-300">
            {translate("loginNewuser")} {` `}
            <Link href="/auth/create-hotel" className="font-semibold underline">
              {translate("loginCreateaccount")}
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
};

export default LoginContainer;
