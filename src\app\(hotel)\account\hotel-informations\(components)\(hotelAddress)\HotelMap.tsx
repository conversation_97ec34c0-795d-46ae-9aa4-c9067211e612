"use client";
import React, { useState } from "react";
import type { FC } from "react";
import Input from "@/shared/Input";
import FormItem from "@/shared/FormItem";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import HowItWorksMap from "./HowItWorksMap";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
interface HotelMapProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
  method?: string;
}

const HotelMap: FC<HotelMapProps> = ({
  hotelData,
  hotelToken,
  method = "POST",
}) => {
  const translate = useTranslations("HotelMap");
  const { toast } = useToast();
  const { addHotelMap } = useHotelInformationsActions();
  const [iframeInput, setIframeInput] = useState("");
  const [mapSrc, setMapSrc] = useState("");
  const [loading, setLoading] = useState<boolean>(false);

  const extractMapUrl = () => {
    const regex = /<iframe[^>]+src="([^"]+)"/;
    const match = iframeInput.match(regex);

    if (match && match[1]) {
      setMapSrc(match[1]);
    } else {
      toast({
        variant: "warning",
        duration: 3000,
        title: "Uyarı",
        description: "Lütfen geçerli bir harita linki ekleyiniz.",
      });
      setIframeInput("");
    }
  };

  const resetInputs = () => {
    setIframeInput("");
    setMapSrc("");
  };

  return (
    <div className="mt-10">
      <form
        onSubmit={(event) =>
          addHotelMap(
            event,
            hotelToken,
            mapSrc,
            setLoading,
            method,
            resetInputs
          )
        }
      >
        <div className="max-w-sm mb-5">
          <FormItem label="Google Harita Yerleştirme HTML Alanı">
            <div className="flex gap-5">
              <div className="flex flex-col items-start">
                <Input
                  onChange={(e) => setIframeInput(e.target.value)}
                  value={iframeInput}
                  className="w-full md:w-[360px]"
                />

                <HowItWorksMap />
              </div>
              <Button
                type="button"
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                disabled={iframeInput ? false : true}
                onClick={extractMapUrl}
              >
                Haritada göster
              </Button>
            </div>
          </FormItem>
        </div>
        {mapSrc ? (
          <iframe
            src={mapSrc}
            width="100%"
            height="500"
            style={{ border: "0" }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          />
        ) : (
          <iframe
            src={hotelData?.googleMapUrl}
            width="100%"
            height="500"
            style={{ border: "0" }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          />
        )}
        <div className="mt-10 flex justify-end">
          <Button
            disabled={!mapSrc}
            type="submit"
            className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          >
            {loading ? <LoadingSpinner /> : "Kaydet"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default HotelMap;
