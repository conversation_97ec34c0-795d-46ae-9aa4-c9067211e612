"use client";
import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { petTypes } from "@/types/petOwner/petTypes";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { getSelectedHotelAllocation } from "@/actions/(protected)/pub/getSelectedHotelAllocation";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import LoadingSpinner from "@/shared/icons/Spinner";

interface ReservationFormProps {
  startDate: Date | string | undefined;
  endDate: Date | string | undefined;
  hotelToken: string | undefined;
  hotelData: any;
  onClose?: () => void;
}

const ReservationForm: React.FC<ReservationFormProps> = ({
  startDate,
  endDate,
  hotelToken,
  hotelData,
  onClose,
}) => {
  const [selectedPetTypes, setSelectedPetTypes] = useState<string | null>(null);
  const [selectedRooms, setSelectedRooms] = useState<string | null>(null);
  const [allocations, setAllocations] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const { addItemToCart } = useHotelAtDoorReservation();
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const petTypeList = petTypes.map((petType) => ({
    value: petType.value,
    label: petType.label,
  }));

  const fetchAllocations = async () => {
    if (!startDate || !endDate || !selectedPetTypes || !hotelToken) return;

    setLoading(true);

    const adjustedStartDate =
      adjustDateToTimezone(startDate)?.toISOString().split("T")[0] || "";
    const adjustedEndDate =
      adjustDateToTimezone(endDate)?.toISOString().split("T")[0] || "";

    try {
      const result = await getSelectedHotelAllocation(
        hotelData?._id,
        selectedPetTypes,
        adjustedStartDate,
        adjustedEndDate
      );
      setAllocations(result?.data?.allocationData || []);
    } catch (error) {
      console.error("Failed to fetch allocations:", error);
    }
    setLoading(false);
  };

  const handleAddToCart = async () => {
    if (!selectedRooms || !startDate || !endDate) return;

    const adjustedStartDate =
      adjustDateToTimezone(startDate)?.toISOString().split("T")[0] || "";
    const adjustedEndDate =
      adjustDateToTimezone(endDate)?.toISOString().split("T")[0] || "";

    const reservationRequestBody = {
      itemType: "reservation",
      itemData: {
        room: selectedRooms,
        startDate: adjustedStartDate,
        endDate: adjustedEndDate,
        pet: "",
      },
      selectedItems: calculatedRoomData?.selectedItems || [],
      totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
    };

    try {
      await addItemToCart(
        hotelToken,
        reservationRequestBody,
        setLoading,
        onClose,
        setDisabled
      );
      onClose?.();
    } catch (error) {
      console.error("Ekleme işlemi başarısız:", error);
    }
  };

  useEffect(() => {
    fetchAllocations();
  }, [startDate, endDate, selectedPetTypes]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        <div>
          <Label>Pet Türü</Label>
          <Select
            onValueChange={setSelectedPetTypes}
            disabled={!startDate || !endDate || loading}
          >
            <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-full mt-1">
              <SelectValue placeholder="Pet Türü Seç" />
            </SelectTrigger>
            <SelectContent>
              {petTypeList.map((petType) => (
                <SelectItem key={petType.value} value={petType.value}>
                  {petType.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Oda Seçimi</Label>
          <Select
            disabled={!startDate || !endDate || !selectedPetTypes || loading}
            onValueChange={setSelectedRooms}
          >
            <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-full mt-1">
              <SelectValue placeholder="Oda Seç" />
            </SelectTrigger>
            <SelectContent>
              {allocations?.length > 0 ? (
                allocations?.map((room: any, idx: number) => (
                  <SelectItem key={idx} value={room?.firstAvailableRoom?._id}>
                    <span className="font-semibold">
                      {room?.roomGroup?.roomGroupName}
                    </span>{" "}
                    -{" "}
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(room?.avgTotalPrice)) + "₺"}{" "}
                    (
                    <span className="text-xs font-medium text-neutral-700 dark:text-neutral-300">
                      gecelik fiyatı
                    </span>
                    )
                  </SelectItem>
                ))
              ) : (
                <div className="text-center p-2 text-neutral-400 dark:text-neutral-500">
                  Uygun oda bulunamadı
                </div>
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end gap-5 pb-1">
          <Button variant="ghost" onClick={onClose}>
            İptal
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleAddToCart}
            disabled={!selectedRooms || loading || disabled}
          >
            {loading ? <LoadingSpinner /> : "Ekle"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReservationForm;
