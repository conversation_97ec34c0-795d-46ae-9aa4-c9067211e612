"use client";
import type { FC } from "react";
import React, { useEffect, useRef } from "react";
import DatePicker from "react-datepicker";
import HotelCalendarDatePickerRange from "./HotelCalendarDatePickerRange";
import {
  setSelectedRoom,
  setSelectedRoomId,
} from "@/store/features/hotelCalendar/calendar-slice";
import { useDispatch } from "react-redux";
import type { AllocationType } from "@/store/features/hotelCalendar/calendar-types";
import { registerLocale } from "react-datepicker";
import { tr } from "date-fns/locale";
import IconChevronLeft from "@/shared/icons/ChevronLeft";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

interface HotelCalendarRangeProps {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedRoomCalendarData: AllocationType[];
  startDate: Date | undefined;
  endDate: Date | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
}

const HotelCalendarRange: FC<HotelCalendarRangeProps> = ({
  setOpen,
  startDate,
  endDate,
  setStartDate,
  setEndDate,
  selectedRoomCalendarData,
}) => {
  const translate = useTranslations("HotelCalendarRange");
  registerLocale("tr", tr);
  const dispatch = useDispatch();
  const router = useRouter();
  const calendarContainerRef = useRef<HTMLDivElement>(null);

  const backRoomChoose = () => {
    dispatch(setSelectedRoomId(""));
    dispatch(setSelectedRoom(""));
    setStartDate(undefined);
    setEndDate(undefined);
    router.push("/account/calendar");
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  useEffect(() => {
    if (endDate !== undefined) {
      setOpen(true);
    } else setOpen(false);
  }, [endDate, setOpen]);

  //scrolls to today
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "instant" });

    setTimeout(() => {
      const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
      const todayElement = document.getElementById(today);

      if (todayElement && calendarContainerRef.current) {
        // calendarContainerRef.current.scrollTop = 0;
        todayElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
      }
    }, 100);
  }, []);

  const renderSectionCheckIndate = () => {
    return (
      <div
        ref={calendarContainerRef}
        className="flex h-[600px] w-full flex-col space-y-6 overflow-auto overscroll-contain border-b border-neutral-200 bg-[#fdfdfd] px-0 pb-10 dark:border-neutral-700 dark:bg-neutral-900 sm:space-y-8 sm:rounded-2xl sm:border-x sm:border-t sm:px-4 xl:px-8 2xl:h-[750px]"
      >
        {/* HEADING */}
        <div className="sticky top-0 z-50 flex items-center justify-between bg-[#fdfdfd] px-2 py-5 dark:bg-neutral-900">
          <div
            className="inline-flex cursor-pointer items-center justify-center gap-1 rounded-xl bg-secondary-6000 px-4 py-2 text-center text-sm font-medium text-white duration-200 hover:bg-secondary-700"
            onClick={backRoomChoose}
          >
            <IconChevronLeft className="size-4" />
            <div>{translate("selectRoom")}</div>
          </div>
          <div className="flex items-center space-x-5">
            <div className="flex items-center gap-2">
              <span className="size-3.5 rounded-full bg-[#58d68d]"></span>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                {translate("activeDays")}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="size-3.5 rounded-full bg-[#FFB3B3]"></span>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                {translate("reservedDays")}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="size-3.5 rounded-full bg-[#6c757d]"></span>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                {translate("passiveDays")}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="size-3.5 rounded-full bg-[#fd7e14]"></span>
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                {translate("noPriceDays")}
              </span>
            </div>
          </div>
        </div>

        <div className="customallocation !mt-0">
          <DatePicker
            minDate={new Date()}
            onChange={onChangeDate}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            monthsShown={3}
            showPopperArrow={false}
            inline
            locale={translate("locale")}
            renderDayContents={(day, date) => (
              <HotelCalendarDatePickerRange
                dayOfMonth={day}
                date={date}
                selectedRoomCalendarData={selectedRoomCalendarData}
              />
            )}
          />
        </div>
      </div>
    );
  };

  return renderSectionCheckIndate();
};

export default HotelCalendarRange;
