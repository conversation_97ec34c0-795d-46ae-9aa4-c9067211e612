"use client";
import React, { useState } from "react";
import type { FC } from "react";
import Image from "next/image";
import IconCancelSolid from "@/shared/icons/CancelSolid";
import type { RoomImage } from "@/types/hotel/roomGroupType";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";

interface HotelRoomPhotoCardProps {
  image: RoomImage;
  hotelToken: string | undefined;
}

const HotelRoomPhotoCard: FC<HotelRoomPhotoCardProps> = ({
  image,
  hotelToken,
}) => {
  const { deleteHotelPhoto } = useHotelInformationsActions();
  const [deleteRoomPhotoIsOpen, setDeleteRoomPhotoIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  function closeModal() {
    setDeleteRoomPhotoIsOpen(false);
  }

  return (
    <div className="relative group">
      <Image src={image} width={150} height={150} alt="" />
      <IconCancelSolid
        onClick={() => {
          setDeleteRoomPhotoIsOpen(true);
        }}
        className="absolute right-1 top-1 size-6 cursor-pointer rounded-full bg-white text-secondary-6000 duration-200 group-hover:visible group-hover:opacity-100 lg:invisible lg:opacity-0"
      />
      <Dialog open={deleteRoomPhotoIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Oda Fotoğrafı Kaldırma</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteHotelPhoto(
                event,
                hotelToken,
                image._id,
                setLoading,
                setDeleteRoomPhotoIsOpen,
                "/account/rooms",
                setDisabled
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="capitalize text-gray-500 dark:text-neutral-200">
                Seçili oda fotoğrafını kaldırmak istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
                disabled={disabled}
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HotelRoomPhotoCard;
