"use client";
import React from "react";
import type { FC } from "react";
import Link from "next/link";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useSearchParams } from "next/navigation";
import type { Route } from "@/routers/types";

interface AddPetFooterButtonsProps {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  isPetInfoValid: boolean | undefined;
  isPetHealthInfoValid: boolean;
  isVaccinationReport: boolean;
  kind: string;
  loading: boolean;
  disabled: boolean;
  isCheckout?: boolean;
  isPublic?: boolean;
}

const AddPetFooterButtons: FC<AddPetFooterButtonsProps> = ({
  step,
  setStep,
  isPetInfoValid,
  isPetHealthInfoValid,
  kind,
  isVaccinationReport,
  loading,
  disabled,
  isCheckout = false,
  isPublic = false,
}) => {
  const searchParams = useSearchParams();
  const orderIdParam = searchParams.get("orderId");

  const cancelAddPetButtonPath = () => {
    if (isPublic) {
      return `/at-door-reservation?orderId=${orderIdParam}`;
    } else if (isCheckout) {
      return `/hotel/account/at-door-reservation?orderId=${orderIdParam}&petStep=true`;
    }

    return "/hotel/account/hotel-customer";
  };

  const linkPath = cancelAddPetButtonPath();
  const buttonsConfig: Record<number, { text: string; disabled: boolean }> = {
    1: { text: "İleri", disabled: !kind },
    2: { text: "İleri", disabled: !isPetInfoValid },
    3: { text: "İleri", disabled: !isPetHealthInfoValid },
    4: { text: "İleri", disabled: false },
  };
  return (
    <div className="fixed bottom-0 bg-white dark:bg-neutral-800 dark:border-gray-700 rounded-tr-3xl rounded-tl-3xl border w-full left-0 right-0 z-50">
      <div className="flex max-md:flex-col-reverse max-md:gap-2 justify-between mt-5 pb-2 md:pb-5 container">
        <Link className="md:basis-2/12" href={linkPath as Route}>
          <ButtonSecondary className="w-full">Vazgeç</ButtonSecondary>
        </Link>
        {step !== 5 && (
          <div className="flex gap-5 justify-end md:basis-4/12">
            {step > 1 && (
              <ButtonSecondary
                type="button"
                className="w-full md:w-1/2"
                onClick={() => setStep((prev) => prev - 1)}
              >
                Geri
              </ButtonSecondary>
            )}
            {buttonsConfig[step] && (
              <ButtonPrimary
                type="button"
                className="w-full md:w-1/2"
                disabled={buttonsConfig[step].disabled}
                onClick={() => setStep((prev) => prev + 1)}
              >
                {buttonsConfig[step].text}
              </ButtonPrimary>
            )}
          </div>
        )}
        {step === 5 && (
          <div className="flex gap-5 justify-end basis-4/12">
            <ButtonSecondary
              type="button"
              className="w-1/2"
              onClick={() => setStep((prev) => prev - 1)}
            >
              Geri
            </ButtonSecondary>
            <ButtonPrimary
              className="w-1/2"
              disabled={disabled || !isVaccinationReport}
              type="submit"
            >
              {loading ? <LoadingSpinner /> : "Kaydet"}
            </ButtonPrimary>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddPetFooterButtons;
