import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelNotification = () => {
  const updateWebPush = async (
    hotelToken: string | undefined,
    webPushData: any,
    webPushId: string
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateWebPush}/${webPushId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(webPushData),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  return { updateWebPush };
};
