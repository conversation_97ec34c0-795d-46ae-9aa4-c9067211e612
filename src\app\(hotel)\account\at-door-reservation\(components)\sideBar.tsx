"use client";
import type { FC } from "react";
import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import CalculatedDetailContainer from "./CalculatedDetailContainer";

interface SideBarProps {}

const SideBar: FC<SideBarProps> = () => {
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const reservations = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "reservation"
  );
  const subscriptions = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "subscription"
  );

  const services = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "service"
  );

  return (
    <div className="listingSectionSidebar__wrap shadow-xl">
      {calculatedRoomData &&
      (subscriptions?.length > 0 ||
        reservations?.length > 0 ||
        services?.length > 0) ? (
        <CalculatedDetailContainer />
      ) : (
        <div className="flex justify-center items-center h-full">Sepet boş</div>
      )}
    </div>
  );
};

export default SideBar;
