import React from "react";
import type { <PERSON> } from "react";

interface ArrowLeftProps {
  className?: string;
  strokeWidth?: number;
  onClick?: () => void;
}

const IconArrowLeft: FC<ArrowLeftProps> = ({
  className = "size-6",
  strokeWidth = 1.5,
  onClick,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
      onClick={onClick}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
      />
    </svg>
  );
};

export default IconArrowLeft;
