import React from "react";
import Layout from "emails/_components/Layout";

export interface Allocation {
  roomAllocation: string;
  room: string;
  allocationDate: string;
  serviceFee: number;
  roomPrice: number;
  totalPrice: number;
  currency: string;
  _id: string;
}

export interface Reservation {
  _id: string;
  hotel: string;
  petOwner: string;
  pet: string;
  room: string;
  startDate: string;
  endDate: string;
  issueDate: string;
  checkedIn: string | null;
  checkedOut: string | null;
  nights: number;
  allocations: Allocation[];
  status: string;
  total: number;
  currency: string;
  paymentStatus: string;
  channel: string;
  note: string;
  createdAt: string;
  updatedAt: string;
}

interface Props {
  reservation: Reservation;
}

const ConfirmReservation: React.FC<Props> = ({ reservation }) => {
  return (
    <Layout>
      {/* 🛑 Uyarı Mesajı */}
      <div
        style={{
          backgroundColor: "rgb(211, 251, 221)",
          borderLeft: "4px solid rgb(63, 255, 111)",
          color: "#fffff",
          padding: "12px",
          marginBottom: "16px",
          marginTop: "16px",
          borderRadius: "4px",
        }}
      >
        <p style={{ fontWeight: "bold", margin: "0" }}>Tebrikler!</p>
        <p style={{ margin: "4px 0 0 0" }}>
          Yeni bir rezervasyon alındı. İşletme sahibi olarak, bu rezervasyonu{" "}
          <strong>3 saat içinde</strong> onaylamanız gerekmektedir. Aksi
          takdirde rezervasyon otomatik olarak iptal edilebilir.
        </p>
      </div>
      <div
        style={{
          maxWidth: "600px",
          margin: "0 auto",
          padding: "24px",
          backgroundColor: "#ffffff",
          borderRadius: "8px",
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Genel Bilgiler */}
        <h2 style={{ fontSize: "20px", fontWeight: "600", color: "#111827" }}>
          Rezervasyon Detayları
        </h2>
        <p style={{ fontSize: "14px", color: "#6B7280" }}>
          Rezervasyon ID: {reservation?._id}
        </p>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: "12px",
          }}
        >
          <div>
            <h3
              style={{ fontSize: "16px", fontWeight: "600", color: "#374151" }}
            >
              Otel Bilgileri
            </h3>
            <p style={{ color: "#6B7280" }}>Otel ID: {reservation?.hotel}</p>
            <p style={{ color: "#6B7280" }}>
              Pet Sahibi ID: {reservation?.petOwner}
            </p>
            <p style={{ color: "#6B7280" }}>
              Evcil Hayvan ID: {reservation?.pet}
            </p>
          </div>
          <div>
            <h3
              style={{ fontSize: "16px", fontWeight: "600", color: "#374151" }}
            >
              Rezervasyon Tarihleri
            </h3>
            <p style={{ color: "#6B7280" }}>
              Başlangıç: {reservation?.startDate}
            </p>
            <p style={{ color: "#6B7280" }}>Bitiş: {reservation?.endDate}</p>
            <p style={{ color: "#6B7280" }}>
              Gece Sayısı: {reservation?.nights}
            </p>
          </div>
        </div>

        {/* Oda ve Tahsis Detayları */}
        <h3
          style={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#374151",
            marginTop: "16px",
          }}
        >
          Oda Tahsisleri
        </h3>
        <table
          style={{
            width: "100%",
            borderCollapse: "collapse",
            marginTop: "8px",
          }}
        >
          <thead>
            <tr style={{ backgroundColor: "#F3F4F6", textAlign: "left" }}>
              <th style={{ padding: "8px", borderBottom: "2px solid #E5E7EB" }}>
                Tarih
              </th>
              <th style={{ padding: "8px", borderBottom: "2px solid #E5E7EB" }}>
                Oda ID
              </th>
              <th style={{ padding: "8px", borderBottom: "2px solid #E5E7EB" }}>
                Hizmet Bedeli
              </th>
              <th style={{ padding: "8px", borderBottom: "2px solid #E5E7EB" }}>
                Oda Ücreti
              </th>
              <th style={{ padding: "8px", borderBottom: "2px solid #E5E7EB" }}>
                Toplam Fiyat
              </th>
            </tr>
          </thead>
          <tbody>
            {reservation?.allocations.map((alloc, index) => (
              <tr key={index}>
                <td
                  style={{ padding: "8px", borderBottom: "1px solid #E5E7EB" }}
                >
                  {alloc?.allocationDate}
                </td>
                <td
                  style={{ padding: "8px", borderBottom: "1px solid #E5E7EB" }}
                >
                  {alloc?.room}
                </td>
                <td
                  style={{ padding: "8px", borderBottom: "1px solid #E5E7EB" }}
                >
                  {alloc?.serviceFee} {alloc.currency}
                </td>
                <td
                  style={{ padding: "8px", borderBottom: "1px solid #E5E7EB" }}
                >
                  {alloc?.roomPrice} {alloc?.currency}
                </td>
                <td
                  style={{
                    padding: "8px",
                    fontWeight: "bold",
                    borderBottom: "1px solid #E5E7EB",
                  }}
                >
                  {alloc?.totalPrice} {alloc?.currency}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* 📌 Rezervasyon Detaylarını Görüntüle Butonu */}
        <div style={{ marginTop: "24px", textAlign: "center" }}>
          <a
            href="https://partner.pawbooking.co/hotel/account/reservations"
            target="_blank"
            style={{
              display: "inline-block",
              backgroundColor: "#C53030",
              color: "#ffffff",
              padding: "12px 20px",
              borderRadius: "6px",
              fontWeight: "600",
              textDecoration: "none",
            }}
          >
            Rezervasyon Detaylarını Görüntüle
          </a>
        </div>
      </div>
    </Layout>
  );
};

export default ConfirmReservation;
