"use client";
import type { FC } from "react";
import React from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import IconCancelSolid from "@/shared/icons/CancelSolid";
import type { CreateRoomPhotosProps } from "@/types/hotel/rooms/createRoomTypes";

const CreateRoomPhotos: FC<CreateRoomPhotosProps> = ({
  loading,
  newRoomInputRef,
  setPhotoFileObject,
  setModalImage,
  modalImage,
}) => {
  const translate = useTranslations("UpdateAndCreateNewRoomModal");
  return (
    <div className="mt-5">
      <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
        <div className="space-y-1 text-center">
          <svg
            className="mx-auto size-12 text-neutral-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
          </svg>
          <div className="text-sm text-neutral-6000 dark:text-neutral-300">
            <label
              htmlFor="file-upload"
              className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
            >
              <span>{translate("hotelUploadPhoto")}</span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                disabled={loading}
                accept="image/*"
                multiple={true}
                ref={newRoomInputRef}
                onChange={(e) => {
                  const newFiles = e.target.files; // FileList
                  if (newFiles) {
                    setPhotoFileObject((prevFileObject) => {
                      const dataTransfer = new DataTransfer();
                      // Add previous files
                      if (prevFileObject) {
                        Array.from(prevFileObject).forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                      }
                      // Add new files
                      Array.from(newFiles).forEach((file) =>
                        dataTransfer.items.add(file)
                      );
                      return dataTransfer.files; // Updated FileList
                    });
                    const newImageUrls = Array.from(newFiles).map((file) =>
                      URL.createObjectURL(file)
                    );
                    setModalImage((prevImages: any) => [
                      ...prevImages,
                      ...newImageUrls,
                    ]);
                  }
                }}
              />
            </label>
            {/* <p className="pl-1">or drag and drop</p> */}
          </div>
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            PNG, JPG
          </p>
        </div>
      </div>
      <div className="mt-2 grid grid-cols-3 gap-3">
        {modalImage &&
          modalImage.map((image: any, index: number) => {
            return (
              <div key={index} className="relative">
                <Image
                  src={image}
                  width={150}
                  height={150}
                  alt="Oda fotoğrafı"
                />
                <button
                  className="absolute top-1"
                  onClick={() => {
                    setModalImage((prevImages: any) =>
                      prevImages.filter((_: any, i: any) => i !== index)
                    );
                    setPhotoFileObject((prevFileObject) => {
                      if (prevFileObject) {
                        // Convert FileList to Array
                        const prevFilesArray = Array.from(prevFileObject);
                        // Perform the delete operation
                        const updatedFiles = prevFilesArray.filter(
                          (_, i) => i !== index
                        );
                        // Convert File[] back to FileList
                        const dataTransfer = new DataTransfer();
                        updatedFiles.forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                        return dataTransfer.files; // Updated FileList
                      }
                      return undefined; // Return undefined instead of null
                    });
                    if (newRoomInputRef.current) {
                      newRoomInputRef.current.value = ""; // Clear the input
                    }
                  }}
                >
                  <IconCancelSolid className="size-6 rounded-full bg-white text-secondary-6000" />
                </button>
              </div>
            );
          })}
      </div>
    </div>
  );
};

export default CreateRoomPhotos;
