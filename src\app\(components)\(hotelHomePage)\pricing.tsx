"use client";
import { PricingSection } from "@/components/ui/pricing-section";
import { useTranslations } from "next-intl";
import type { FC } from "react";

interface PricingSectionsProps {
  membershipData?: any;
  onSelectTier?: (
    tierId: string,
    price: number | string,
    frequency: string
  ) => void;
}

export const PricingSections: FC<PricingSectionsProps> = ({
  membershipData,
  onSelectTier,
}) => {
  const translate = useTranslations("LandingPricing");

  const PAYMENT_FREQUENCIES = [
    `${translate("monthly")}`,
    `${translate("yearly")}`,
  ];

  const TIERS = [
    {
      id: "free",
      name: translate("standard"),
      price: {
        [translate("monthly")]: translate("free"),
        [translate("yearly")]: translate("free"),
      },
      description: translate("features"),
      features: [
        {
          name: translate("pawbookingOnly"),
          included: true,
        },
        {
          name: translate("additionalServices"),
          included: true,
          description: translate("additionalServicesSubtitle"),
        },
        {
          name: translate("commission"),
          included: true,
          description: "%18",
        },
        {
          name: translate("customerSupport"),
          included: true,
          description: translate("customerSupportDescription"),
        },
        {
          name: translate("advancedContractManagement"),
          included: true,
        },
        {
          name: translate("customizablePolicies"),
          included: true,
        },
      ],
      cta: "Ücretsiz Plana Geç",
    },
    {
      id: "plus",
      name: translate("plus"),
      price: {
        [translate("monthly")]: 3500,
        [translate("yearly")]: 35000,
      },
      description: translate("features"),
      features: [
        {
          name: translate("multiChannelBookings"),
          included: true,
          description: translate("multiChannelBookingsDescription"),
        },
        {
          name: translate("sellAdditionalServices"),
          included: true,
          description: translate("additionalServicesExamples"),
        },
        {
          name: translate("commissionAdvantage"),
          included: true,
          description: translate("commissionAdvantageDescription"),
        },
        {
          name: translate("advancedContractManagement"),
          included: true,
        },
        {
          name: translate("customizablePolicies"),
          included: true,
        },
        {
          name: translate("staffAccounts"),
          included: true,
          description: translate("staffAccountsDescription"),
        },
        {
          name: translate("discountCoupons"),
          included: true,
        },
        {
          name: translate("customWebPages"),
          included: true,
        },
        {
          name: translate("multiChannelSupport"),
          included: true,
        },
        {
          name: translate("privateCloudStorage"),
          included: true,
          description: translate("privateCloudStorageDescription"),
        },
        {
          name: translate("customerSupport"),
          included: true,
          description: translate("customerSupportDescription"),
        },
        {
          name: translate("dedicatedSupport"),
          included: true,
          description: translate("dedicatedSupportDescription"),
        },
      ],
      cta: "Yükselt",
      popular: true,
    },
  ];

  return (
    <div className="container relative min-h-screen">
      <div className="absolute inset-0 -z-10"></div>
      <PricingSection
        title={translate("subscription")}
        subtitle={translate("subscriptionSubtitle")}
        frequencies={PAYMENT_FREQUENCIES}
        tiers={TIERS}
        membershipData={membershipData}
        onSelectTier={onSelectTier}
      />
    </div>
  );
};
