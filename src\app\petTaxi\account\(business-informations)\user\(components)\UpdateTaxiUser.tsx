"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import Label from "@/components/Label";
import { Button } from "@/components/ui/button";
import Input from "@/shared/Input";
import Select from "@/shared/Select";
import Textarea from "@/shared/Textarea";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelUserInformations } from "@/hooks/hotel/useHotelUserInformations";
import type { UserInformationApiTypes } from "@/types/hotel/hotelUserType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import {
  onlyLetterRegex,
  emailRegex,
  usernameRegex,
} from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";

export interface UpdateTaxiUserProps {
  rowOriginal: UserInformationApiTypes;
  petTaxiToken: string | undefined;
}

const UpdateTaxiUser: FC<UpdateTaxiUserProps> = ({
  petTaxiToken,
  rowOriginal,
}) => {
  const translate = useTranslations("AddNewAndUpdateHotelUser");
  const { updateHotelUserHandler } = useHotelUserInformations();
  const initialData = {
    firstName: rowOriginal.firstName,
    lastName: rowOriginal.lastName,
    gender: rowOriginal.gender,
    username: rowOriginal.username,
    email: rowOriginal.email,
    dateOfBirth: rowOriginal.dateOfBirth,
    phone: rowOriginal.phone,
    bio: rowOriginal.bio,
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [taxiUserUpdateModalIsOpen, setTaxiUserModalIsOpen] = useState(false);
  const [updatedTaxiUserInfo, setUpdatedTaxiUserInfo] = useState(initialData);

  const taxiUserInfoHandler = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setUpdatedTaxiUserInfo((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  function closeHotelUserUpdateModal() {
    setTaxiUserModalIsOpen(false);
    setLoading(false);
    setUpdatedTaxiUserInfo(initialData);
  }

  const phoneRegex = /^(0|90)(\d{10})$/;
  const nameCheck = onlyLetterRegex.test(updatedTaxiUserInfo.firstName);
  const lastNameCheck = onlyLetterRegex.test(updatedTaxiUserInfo.lastName);
  const mailCheck = emailRegex.test(
    updatedTaxiUserInfo.email.toLowerCase().trim()
  );
  const usernameCheck = usernameRegex.test(updatedTaxiUserInfo.username);
  const phoneCheck = phoneRegex.test(updatedTaxiUserInfo.phone);
  const dateOfBirthCheck = updatedTaxiUserInfo.dateOfBirth.length > 0;

  const isAllValid =
    nameCheck &&
    lastNameCheck &&
    mailCheck &&
    usernameCheck &&
    phoneCheck &&
    dateOfBirthCheck;

  return (
    <>
      <IconEdit
        onClick={() => setTaxiUserModalIsOpen(true)}
        className="size-5 duration-200 hover:text-secondary-6000"
      />
      <Dialog open={taxiUserUpdateModalIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)]"
          onInteractOutside={closeHotelUserUpdateModal}
        >
          <DialogHeader>
            <DialogTitle> {translate("updateUser")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            className="mt-10 max-w-3xl grow space-y-6 md:mt-0"
            onSubmit={(event) => {
              updateHotelUserHandler(
                event,
                petTaxiToken,
                updatedTaxiUserInfo,
                rowOriginal._id,
                setLoading,
                closeHotelUserUpdateModal
              );
            }}
          >
            <div>
              <Label>{translate("firstName")}</Label>
              <Input
                className="mt-1.5"
                name="firstName"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.firstName || ""}
              />
              {!nameCheck && updatedTaxiUserInfo.firstName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validFirstName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("lastName")}</Label>
              <Input
                className="mt-1.5"
                name="lastName"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.lastName || ""}
              />
              {!lastNameCheck && updatedTaxiUserInfo.lastName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validLastName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("gender")}</Label>
              <Select
                className="mt-1.5"
                name="gender"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.gender || ""}
              >
                <option value="Male">{translate("male")}</option>
                <option value="Female">{translate("female")}</option>
                <option value="Other">{translate("other")}</option>
              </Select>
            </div>
            <div>
              <Label>{translate("username")}</Label>
              <Input
                className="mt-1.5"
                name="username"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.username || ""}
              />
              {!usernameCheck && updatedTaxiUserInfo.username.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validUsername")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("email")}</Label>
              <Input
                className="mt-1.5"
                name="email"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.email || ""}
              />
              {!mailCheck && updatedTaxiUserInfo.email.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validEmail")}
                </span>
              )}
            </div>
            <div className="max-w-lg">
              <Label>{translate("dateOfBirth")}</Label>
              <Input
                className="mt-1.5"
                type="date"
                name="dateOfBirth"
                max="2007-01-01"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.dateOfBirth || ""}
              />
              {!dateOfBirthCheck && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validDateOfBirth")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("phone")}</Label>
              <Input
                className="mt-1.5"
                name="phone"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.phone || ""}
              />
              {!phoneCheck && updatedTaxiUserInfo.phone.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validPhone")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("bio")}</Label>
              <Textarea
                className="mt-1.5"
                name="bio"
                onChange={taxiUserInfoHandler}
                value={updatedTaxiUserInfo.bio || ""}
              />
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                onClick={closeHotelUserUpdateModal}
                type="button"
              >
                {translate("cancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                disabled={
                  !(
                    isAllValid &&
                    JSON.stringify(initialData) !==
                      JSON.stringify(updatedTaxiUserInfo)
                  )
                }
                type="submit"
              >
                {loading ? <LoadingSpinner /> : translate("confirm")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeHotelUserUpdateModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateTaxiUser;
