"use client";
import type { FC } from "react";
import React, { useEffect, useState } from "react";
import HotelPhotoCard from "./HotelPhotoCard";
import { AnimatePresence, motion, MotionConfig } from "framer-motion";
import { useSwipeable } from "react-swipeable";
import PrevBtn from "@/components/PrevBtn";
import NextBtn from "@/components/NextBtn";
import { variants } from "@/utils/animationVariants";
import { useWindowSize } from "react-use";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
import type { ImageType } from "@/types/hotel/hotelDataType";

export interface HotelPhotosProps {
  className?: string;
  itemClassName?: string;
  heading?: string;
  subHeading?: string;
  people?: any;
  categoryCardType?: "card3" | "card4" | "card5";
  itemPerRow?: 3 | 4 | 5;
  sliderStyle?: "style1" | "style2";
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const HotelPhotos: FC<HotelPhotosProps> = ({
  className = "",
  itemClassName = "",
  itemPerRow = 4,
  hotelData,
  hotelToken,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [numberOfItems, setNumberOfitem] = useState(0);

  const windowWidth = useWindowSize().width;
  useEffect(() => {
    if (windowWidth < 320) {
      return setNumberOfitem(1);
    }
    if (windowWidth < 500) {
      return setNumberOfitem(itemPerRow - 3);
    }
    if (windowWidth < 1024) {
      return setNumberOfitem(itemPerRow - 2);
    }
    if (windowWidth < 1280) {
      return setNumberOfitem(itemPerRow - 1);
    }

    setNumberOfitem(itemPerRow);
  }, [itemPerRow, windowWidth]);

  function changeItemId(newVal: number) {
    if (newVal > currentIndex) {
      setDirection(1);
    } else {
      setDirection(-1);
    }
    setCurrentIndex(newVal);
  }

  const handlers = useSwipeable({
    onSwipedLeft: () => {
      if (hotelData?.images.length)
        if (currentIndex < hotelData?.images.length - 1) {
          changeItemId(currentIndex + 1);
        }
    },
    onSwipedRight: () => {
      if (currentIndex > 0) {
        changeItemId(currentIndex - 1);
      }
    },
    trackMouse: true,
  });

  if (!numberOfItems) return null;

  return (
    <div className={`nc-SectionSliderNewCategories mt-8 ${className}`}>
      <div className="mt-4 md:my-8">
        <h2 className="text-xl md:text-2xl font-semibold">Otel Fotoğrafları</h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Fotoğrafları yükleyerek kullanıcıların otelinize ait fotoğrafları
          görüntüleyebilmesini sağlayın.
        </span>
      </div>
      <MotionConfig
        transition={{
          x: { type: "spring", stiffness: 300, damping: 30 },
          opacity: { duration: 0.2 },
        }}
      >
        <div className={`relative flow-root`} {...handlers}>
          <div className={`flow-root overflow-hidden rounded-xl`}>
            <motion.ul
              initial={false}
              className="relative -mx-2 whitespace-nowrap xl:-mx-4"
            >
              <AnimatePresence initial={false} custom={direction}>
                {hotelData?.images?.map((item: ImageType, indx: number) => (
                  <motion.li
                    className={`relative inline-block px-2 xl:px-4 ${itemClassName}`}
                    custom={direction}
                    initial={{
                      x: `${(currentIndex - 1) * -100}%`,
                    }}
                    animate={{
                      x: `${currentIndex * -100}%`,
                    }}
                    variants={variants(200, 1)}
                    key={indx}
                    style={{
                      width: `calc(1/${numberOfItems} * 100%)`,
                    }}
                  >
                    <HotelPhotoCard
                      images={item}
                      hotelToken={hotelToken}
                      index={indx}
                    />
                  </motion.li>
                ))}
              </AnimatePresence>
            </motion.ul>
          </div>

          {currentIndex ? (
            <PrevBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex - 1)}
              className="absolute -left-3 top-1/3 z-[1] size-9 -translate-y-1/2 text-lg xl:-left-6 xl:size-12"
            />
          ) : null}

          {hotelData?.images?.length > currentIndex + numberOfItems ? (
            <NextBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex + 1)}
              className="absolute -right-3 top-1/3 z-[1] size-9 -translate-y-1/2 text-lg xl:-right-6 xl:size-12"
            />
          ) : null}
        </div>
      </MotionConfig>
    </div>
  );
};

export default HotelPhotos;
