import React from "react";
import PublicHeader from "@/app/(components)/(Header)/PublicHeader";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import ScrollToTop from "@/components/ScrollToTop";

export default async function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <PublicHeader />
      <div className="relative flex justify-center pt-5 md:hidden">
        <PawBookingLogo className="size-16 self-center" />
      </div>
      {children}
      <ScrollToTop />
    </div>
  );
}
