import React from "react";
import IconCheck from "@/shared/icons/Check";
import ParticlesContainer from "@/components/ParticlesContainer";
import ConfettiFireworks from "./(components)/ConfettiContainer";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";

const ReservationSuccessPage = () => {
  return (
    <div className="container flex min-h-screen flex-col items-center space-y-4 py-16 md:py-32">
      <IconCheck className="size-12 rounded-full bg-green-500 p-2 text-white md:size-16" />
      <h2 className="text-center text-lg font-semibold md:text-2xl">
        PawBooking Plus Üyelik Satın Alma Başarıyla Tamamlandı!
      </h2>
      <p className="text-center md:text-lg">
        PawBooking Plus üyeliğiniz artık aktif! Artık daha düşük komisyon
        avantajından ve özel destek hizmetlerinden yararlanabilirsiniz.
      </p>
      <p className="text-center md:text-lg">
        Plus üyelikle birlikte rezervasyon işlemlerinde %10'a kadar komisyon
        indirimi ve öncelikli müşteri destek hizmetlerinden faydalanabilirsiniz.
        Daha fazla detay için bizimle iletişime geçmekten çekinmeyin. 🐾
      </p>
      <div className="flex gap-3">
        <ButtonPrimary href="/hotel/account">Anasayfa</ButtonPrimary>
        <ButtonSecondary href="/hotel/account/my-user">Hesabım</ButtonSecondary>
      </div>
      <ConfettiFireworks />
      <div className="opacity-50">
        <ParticlesContainer />
      </div>
    </div>
  );
};

export default ReservationSuccessPage;
