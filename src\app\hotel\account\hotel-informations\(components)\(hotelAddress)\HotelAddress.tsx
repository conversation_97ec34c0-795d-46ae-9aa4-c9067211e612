"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import type { HotelAddressApiTypes } from "@/types/hotel/hotelInformationType";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelAddressProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const HotelAddress: FC<HotelAddressProps> = ({ hotelData, hotelToken }) => {
  const translate = useTranslations("HotelAddress");
  const initialData = {
    cityName: hotelData.address.cityName,
    region: hotelData.address.region,
    district: hotelData.address.district,
    streetName: hotelData.address.streetName,
    buildingName: hotelData.address.buildingName,
    buildingNumber: hotelData.address.buildingNumber,
    postalZone: hotelData.address.postalZone,
  };
  const [hotelAddress, setHotelAddress] =
    useState<HotelAddressApiTypes>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handleHotelAddress } = useHotelInformationsActions();

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    setHotelAddress((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  return (
    <form
      onSubmit={(event) => {
        handleHotelAddress(
          event,
          hotelToken,
          hotelAddress,
          setLoading,
          setDisabled
        );
      }}
    >
      <div className="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-5">
        {Object.entries(hotelAddress).map(([key, value]) => {
          return (
            <FormItem key={key} label={translate(key)}>
              <Input name={key} onChange={handleChange} value={value} />
            </FormItem>
          );
        })}
      </div>
      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            disabled ||
            JSON.stringify(initialData) === JSON.stringify(hotelAddress)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </form>
  );
};

export default HotelAddress;
