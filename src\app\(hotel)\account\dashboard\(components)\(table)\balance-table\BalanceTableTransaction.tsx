import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface BalanceTableTransactionProps {
  transactionsData: any;
}

const BalanceTableTransaction = ({
  transactionsData,
}: BalanceTableTransactionProps) => {
  return (
    <TableRow className="hover:bg-transparent">
      <TableCell className="p-1 px-5">
        <div>
          {!transactionsData ? (
            <div className="text-center"></div>
          ) : (
            <Table className="w-full table-fixed">
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="font-bold text-center h-7">
                    İşlem Türü
                  </TableHead>
                  <TableHead className="font-bold text-center h-7">
                    İşlem Adı
                  </TableHead>
                  <TableHead className="font-bold text-center h-7">
                    Tarih
                  </TableHead>
                  <TableHead className="font-bold text-center h-7">
                    Ödeme Tipi
                  </TableHead>
                  <TableHead className="font-bold text-center h-7">
                    Açıklama
                  </TableHead>
                  <TableHead className="font-bold text-center h-7">
                    İşlem Tutarı
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactionsData.map((transaction: any, index: number) => (
                  <TableRow className="hover:bg-transparent" key={index}>
                    <TableCell className="text-center p-0">
                      {transaction?.transactionType === 0
                        ? "Alacak"
                        : "Verecek"}
                    </TableCell>
                    <TableCell className="text-center p-0">
                      {(() => {
                        const name = transaction?.transaction;
                        switch (name) {
                          case "reservations":
                            return "Konaklama";
                          case "services":
                            return "Hizmet";
                          case "subscriptions":
                            return "Üyelik Kartı";
                          default:
                            return name || "-";
                        }
                      })()}
                    </TableCell>
                    <TableCell className="text-center p-0">
                      {formatDateToDayMonthYear(transaction?.createdAt) || "-"}
                    </TableCell>
                    <TableCell className="text-center p-0">
                      {transaction?.paymentType || "-"}
                    </TableCell>
                    <TableCell className="text-center p-0">
                      {transaction?.description || "-"}
                    </TableCell>
                    <TableCell
                      className={`text-center p-0 ${
                        transaction?.transactionType === 0
                          ? "text-red-500"
                          : "text-green-500"
                      }`}
                    >
                      {transaction?.transactionType === 0 ? "-" : "+"}
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(transaction?.amount) || 0)}
                      {" ₺"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
};

export default BalanceTableTransaction;
