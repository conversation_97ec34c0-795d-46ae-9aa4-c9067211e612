import React from "react";
import type { FC } from "react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import { useTranslations } from "next-intl";
import ReservationActionButtons from "../ReservationActionButtons";
import ReservationDetail from "../ReservationDetail";
import { ReservationListApiTypes } from "@/types/hotel/reservation/reservationListType";

interface ReservationCardMobileProps {
  reservationData: ReservationListApiTypes;
  hotelToken: string | undefined;
}

export function statusHandler(status: string | undefined) {
  if (!status) return { statusName: status, color: "" };

  const statusColors: Record<string, string> = {
    confirmed: "text-[#2f855a]",
    booked: "text-[#FF9800]",
    checkedIn: "text-[#2196F3]",
    declined: "text-[#c53030]",
    waitingForCheckIn: "text-[#c53030]",
    waitingForCheckOut: "text-[#c53030]",
    waitingForApproval: "text-[#c53030]",
  };

  return {
    color: statusColors[status],
  };
}

const ReservationCardMobile: FC<ReservationCardMobileProps> = ({
  reservationData,
  hotelToken,
}) => {
  const translateDetail = useTranslations("ReservationDetail");
  const translate = useTranslations("ReservationList");
  const { room, createdAt, startDate, endDate, status } = reservationData;

  const reservationStatus = statusHandler(status);

  const price =
    new Intl.NumberFormat("tr-TR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Number(reservationData.totalHotel)) + "₺";

  return (
    <Card>
      <CardHeader className="px-6 py-3">
        <CardTitle className="text-base">
          {room?.roomName || "No Name"}
        </CardTitle>
        <CardDescription>{formatDateToDayMonthYear(createdAt)}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translateDetail("checkInDate")}:</p>
          <p>{formatDateToDayMonthYear(startDate)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translateDetail("checkOutDate")}:</p>
          <p>{formatDateToDayMonthYear(endDate)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translate("status")}:</p>
          <p className={`${reservationStatus?.color}`}>{translate(status)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translate("total")}:</p>
          <p>{price}</p>
        </div>
      </CardContent>
      <CardFooter className="justify-between">
        <ReservationDetail reservationData={reservationData} />
        <ReservationActionButtons
          rowOriginal={reservationData}
          hotelToken={hotelToken}
        />
      </CardFooter>
    </Card>
  );
};

export default ReservationCardMobile;
