"use client";
import type { ChangeEvent } from "react";
import React from "react";
import Input from "@/shared/Input";
import FormItem from "@/shared/FormItem";
import { setHotel } from "@/store/features/hotelAuth/hotel-auth-slice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import turkeyCityDistrict from "@/data/jsons/mernis_city_district.json";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import CreateHotelAgreement from "./CreateHotelAgreement";
import { Checkbox } from "@/components/ui/checkbox";
import Textarea from "@/shared/Textarea";

const HotelSecondStep = ({
  setAgreementCheck,
  agreementCheck,
}: {
  setAgreementCheck: React.Dispatch<React.SetStateAction<boolean>>;
  agreementCheck: boolean;
}) => {
  const translate = useTranslations("HotelSecondStep");

  const dispatch = useDispatch();
  const hotel = useSelector((state: RootState) => state.hotelAuth.hotel);
  const handleChange = (
    e: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    dispatch(setHotel({ ...hotel, [name]: value }));
  };

  const filteredCityDistrict = turkeyCityDistrict.find(
    (city) => city.iladi === hotel.cityName
  );

  const propertyTypes = [
    { slug: "petHotel", name: translate("petHotel") },
    { slug: "petTaxi", name: translate("petTaxi") },
    { slug: "petGroomer", name: translate("petGroomer") },
    { slug: "veterinary", name: translate("veterinary") },
    { slug: "petFriendlyHotel", name: translate("petFriendlyHotel") },
    { slug: "trainingFarm", name: translate("trainingFarm") },
  ];

  return (
    <>
      <h2 className="pb-5 text-2xl font-semibold">{translate("hotelInfo")}</h2>
      <div className="mb-5 w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      {/* FORM */}
      <div className="flex flex-col h-full items-start gap-2">
        <label htmlFor="">{translate("propertyType")}</label>
        <Select
          onValueChange={(selectedPropertyType) => {
            dispatch(
              setHotel({
                ...hotel,
                propertyType: selectedPropertyType,
              })
            );
          }}
          value={hotel?.propertyType || ""}
        >
          <SelectTrigger className="h-12 rounded-2xl">
            <SelectValue placeholder="İşletme Türü" />
          </SelectTrigger>
          <SelectContent>
            {propertyTypes.map((property, index) => {
              return (
                <SelectItem key={index} value={property.slug}>
                  {property.name}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-8">
        {/* ITEM */}
        <FormItem label={translate("hotelName")}>
          <Input
            name="hotelName"
            onChange={handleChange}
            value={hotel?.hotelName || ""}
          />
        </FormItem>
        <div className="space-y-8">
          <div>
            <div className="mb-3 text-lg font-semibold">
              {translate("hotelAddress")}
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-5">
              <div className="flex h-full items-center">
                <Select
                  onValueChange={(selectedCityName) => {
                    dispatch(
                      setHotel({
                        ...hotel,
                        cityName: selectedCityName,
                        citySubdivisionName: null,
                      })
                    );
                  }}
                  value={hotel?.cityName || ""}
                >
                  <SelectTrigger className="h-12 rounded-2xl">
                    <SelectValue placeholder={translate("hotelCity")} />
                  </SelectTrigger>
                  <SelectContent>
                    {turkeyCityDistrict.map((cities, index) => {
                      return (
                        <SelectItem key={index} value={cities.iladi}>
                          {cities.iladi}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex h-full items-center">
                <Select
                  onValueChange={(selectedcitySubdivisionName) =>
                    dispatch(
                      setHotel({
                        ...hotel,
                        citySubdivisionName: selectedcitySubdivisionName,
                      })
                    )
                  }
                  value={hotel?.citySubdivisionName || ""}
                  disabled={hotel.cityName ? false : true}
                >
                  <SelectTrigger className="h-12 rounded-2xl">
                    <SelectValue placeholder={translate("hotelDistrict")} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredCityDistrict &&
                      filteredCityDistrict.ilceler.map((district) => {
                        return (
                          <SelectItem
                            key={district.ilcekodu}
                            value={district.ilceadi}
                          >
                            {district.ilceadi}
                          </SelectItem>
                        );
                      })}
                  </SelectContent>
                </Select>
              </div>
              <FormItem
                label={translate("hotelNeighbourhood")}
                desc={translate("hotelNeighbourhoodDesc")}
              >
                <Input
                  name="district"
                  onChange={handleChange}
                  value={hotel?.district || ""}
                />
              </FormItem>
              <FormItem
                label={translate("hotelStreet")}
                desc={translate("hotelStreetDesc")}
              >
                <Input
                  name="streetName"
                  onChange={handleChange}
                  value={hotel?.streetName || ""}
                />
              </FormItem>
              <FormItem
                label={translate("hotelBuildingName")}
                desc={translate("hotelBuildingNameDesc")}
              >
                <Input
                  name="buildingName"
                  onChange={handleChange}
                  value={hotel?.buildingName || ""}
                />
              </FormItem>
              <FormItem
                label={translate("hotelBuildingNumber")}
                desc={translate("hotelBuildingNumberDesc")}
              >
                <Input
                  name="buildingNumber"
                  onChange={handleChange}
                  value={hotel?.buildingNumber || ""}
                />
              </FormItem>
              <FormItem
                label={translate("hotelPostalCode")}
                desc={translate("hotelPostalCodeDesc")}
              >
                <Input
                  name="postbox"
                  onChange={handleChange}
                  value={hotel?.postbox || ""}
                />
              </FormItem>
              <FormItem
                label={translate("hotelPhoneNumber")}
                desc={translate("hotelPhoneNumberDesc")}
              >
                <Input
                  type="tel"
                  name="hotelPhoneNumber"
                  onChange={handleChange}
                  value={hotel?.hotelPhoneNumber || ""}
                />
              </FormItem>
            </div>
          </div>
          {/* <div>
            <div className="mb-3 text-lg font-semibold">
              {translate("hotelCompanyInfo")}
            </div>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-5">
              <FormItem
                label={translate("hotelCompanyName")}
                desc={translate("hotelCompanyNameDesc")}
              >
                <Input name="legalName" onChange={handleChange} />
              </FormItem>
              <FormItem
                label={translate("hotelCompanyTaxNumber")}
                desc={translate("hotelCompanyTaxNumberDesc")}
              >
                <Input name="taxNumber" onChange={handleChange} />
              </FormItem>
              <FormItem
                label={translate("hotelCompanyTaxOffice")}
                desc={translate("hotelCompanyTaxOfficeDesc")}
              >
                <Input name="taxOffice" onChange={handleChange} />
              </FormItem>
            </div>
          </div> */}
        </div>
        <div>
          <div className="mb-3 text-lg font-semibold">
            {translate("hotelDescription")}
          </div>
          <FormItem label={`${translate("hotelDescription")} *`}>
            <Textarea
              name="hotelDescription"
              onChange={handleChange}
              value={hotel?.hotelDescription || ""}
            />
          </FormItem>
        </div>
        <div className="flex items-center flex-wrap gap-1">
          <Checkbox
            checked={agreementCheck}
            onClick={() => setAgreementCheck((prev) => !prev)}
          />

          <Dialog>
            <DialogTrigger>
              {" "}
              <p className="text-sm">
                <span className="font-medium underline">
                  Pawbooking partnerlik sözleşmesini
                </span>{" "}
              </p>
            </DialogTrigger>{" "}
            <span className="text-sm">okudum ve kabul ediyorum.</span>
            <DialogContent className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl">
              <DialogHeader>
                <DialogTitle>Pawbooking Partnerlik Sözleşmesi</DialogTitle>
                <DialogDescription className="sr-only"></DialogDescription>
              </DialogHeader>
              <CreateHotelAgreement hotel={hotel} />
              <DialogClose>
                <div className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                  Kapat
                </div>
              </DialogClose>
            </DialogContent>
          </Dialog>
        </div>
        <p className="mt-5 text-sm font-medium">
          * {translate("hotelRequired")}
        </p>
      </div>
    </>
  );
};

export default HotelSecondStep;
