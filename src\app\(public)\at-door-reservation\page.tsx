import React from "react";
import AtDoorReservationContainer from "./(components)/atDoorReservationsContainer";
import FooterReservation from "./(components)/(reservationFooter)/FooterReservation";
import getOrderByIdPublic from "@/actions/(protected)/pub/getOrderByIdPublic";
import { getHotelPetByOwnerPublic } from "@/actions/(protected)/pub/getHotelPetByOwnerPublic";
import { redirect } from "next/navigation";

const AtDoorReservationPage = async ({
  searchParams,
}: {
  searchParams: Promise<Record<string, string>>;
}) => {
  const searchP = await searchParams;
  const summaryStepParam = searchP.summaryStep;
  const paymentStepParam = searchP.paymentStep;
  const orderId = searchP.orderId;
  const addPetParam = searchP.addPet;
  const selectedPetTypes = searchP.petTypes?.split(",") || [];
  const orderData = await getOrderByIdPublic(orderId);
  const hotelCustomerId = orderData?.data?.order?.hotelCustomer?._id;
  const hotelCustomerPetData = await getHotelPetByOwnerPublic(hotelCustomerId);
  const hotelId = orderData?.data?.order?.hotel?._id;

  if (
    !orderId ||
    !hotelId ||
    !hotelCustomerId ||
    orderData?.data?.order?.status === "confirmed"
  ) {
    redirect("/");
  }

  return (
    <div className="container mt-10 mb-36">
      <AtDoorReservationContainer
        hotelId={hotelId}
        orderData={orderData?.data?.order}
        hotelCustomerId={hotelCustomerId}
        hotelCustomerPetData={hotelCustomerPetData?.data?.hotelPets}
        addPetParam={addPetParam}
        selectedPetTypes={selectedPetTypes}
        stepParams={{
          summary: summaryStepParam,
          payment: paymentStepParam,
        }}
      />
      <FooterReservation
        orderData={orderData?.data?.order}
        addPetParam={addPetParam}
        stepParams={{
          summary: summaryStepParam,
          payment: paymentStepParam,
        }}
        hotelCustomerId={hotelCustomerId}
      />
    </div>
  );
};

export default AtDoorReservationPage;
