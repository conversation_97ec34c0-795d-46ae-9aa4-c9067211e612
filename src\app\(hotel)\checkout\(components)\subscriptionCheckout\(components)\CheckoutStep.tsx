"use client";
import type { FC, ChangeEvent, Dispatch, SetStateAction } from "react";
import React, { useState } from "react";
import Input from "@/shared/Input";
import Label from "@/components/Label";
import { Button } from "@/components/ui/button";
import AcceptPetAgreement from "./(agreements)/AcceptPetAgreement";
import Muvafakatname from "./(agreements)/Muvafakatname";
import <PERSON>ahhütname from "./(agreements)/Taahhütname";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import Checkbox from "@/shared/Checkbox";
import VisaImage from "@/components/VisaImage";
import MasterCardImage from "@/components/MasterCardImage";
import LoadingSpinner from "@/shared/icons/Spinner";
import Cards from "react-credit-cards-2";
import "react-credit-cards-2/dist/lib/styles.scss";
import { useReceivePayment } from "@/hooks/hotel/useReceivePayment";
import IconArrowLeft from "@/shared/icons/ArrowLeft";

export interface CheckoutStepProps {
  className?: string;
  selectedTier: any;
  hotelToken: string | undefined;
  setStep: Dispatch<SetStateAction<number>>;
}
export interface CardData {
  cardNumber: string;
  cardHolder: string;
  expiredDate: string;
  cvv: string;
}

const CheckoutStep: FC<CheckoutStepProps> = ({
  className = "",
  selectedTier,
  hotelToken,
  setStep,
}) => {
  const { receivePayment } = useReceivePayment();
  const { cardValidationHandler } = useReceivePayment();
  const [loading, setLoading] = useState<boolean>(false);
  const [agreementsChecked, SetAgreementChecked] = useState<boolean>(false);
  const [data, setData] = useState();
  const [agreement, SetAgreement] = useState<number>(1);
  const [openModal, setOpenModal] = useState(false);
  const [userCardData, setUserCardData] = useState<CardData>({
    cardNumber: "",
    cardHolder: "",
    expiredDate: "",
    cvv: "",
  });
  const [focused, setFocused] = useState<
    "cardNumber" | "expiredDate" | "cvc" | "cardHolder"
  >("cardNumber");
  const [previousLengthValid, setPreviousLengthValid] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setFocused(
      e.target.name as "cardNumber" | "expiredDate" | "cvc" | "cardHolder"
    );
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    setUserCardData((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const handlerCardTypeImage = () => {
    if (validationResult) {
      if (
        validationResult.Kart_Org === "VISA" ||
        validationResult.Kart_Org === "Visa" ||
        validationResult.Kart_Org === "visa"
      ) {
        return <VisaImage className="size-12" />;
      } else if (
        validationResult.Kart_Org === "MASTER" ||
        validationResult.Kart_Org === "MASTER CARD" ||
        validationResult.Kart_Org === "Master Card" ||
        validationResult.Kart_Org === "master card"
      ) {
        return <MasterCardImage className="size-12" />;
      } else {
        return null;
      }
    }
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, "");
    value = value.replace(/(.{4})/g, "$1 ");

    if (value.length > 19) {
      value = value.slice(0, 19);
    }

    if (value.length >= 7 && !previousLengthValid) {
      setPreviousLengthValid(true);
      cardValidationHandler(
        hotelToken,
        value.replace(/\s+/g, "").trim(),
        setValidationResult
      );
    }

    if (value.length < 7) {
      setPreviousLengthValid(false);
    }

    setUserCardData({
      ...userCardData,
      cardNumber: value.trim(),
    });
  };

  const handleExpiryDateChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    let formattedValue = value;

    if (value.length >= 2) {
      formattedValue = value.slice(0, 2) + "/" + value.slice(2);
    }

    const inputEvent = e.nativeEvent as InputEvent;

    if (
      inputEvent.inputType === "deleteContentBackward" &&
      e.target.selectionStart === 3
    ) {
      formattedValue = value.slice(0, 2);
    }

    setUserCardData({
      ...userCardData,
      expiredDate: formattedValue,
    });
  };

  const handleCVCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    value = value.replace(/\D/g, "");

    if (value.length <= 3) {
      setUserCardData({
        ...userCardData,
        cvv: value,
      });
    }
  };

  const buttonDisabled = Object.values(userCardData).every(
    (value) => value !== ""
  );

  const handlePayment = async () => {
    const [expiryMonth, expiryYear] = userCardData.expiredDate.split("/");

    const modifiedCardData = {
      cardNumber: Number(userCardData.cardNumber.replace(/\s/g, "")),
      cardHolder: userCardData.cardHolder,
      cvv: userCardData.cvv,
      expiryMonth: Number(expiryMonth),
      expiryYear: Number(expiryYear),
    };

    const frequency =
      selectedTier.frequency === "Aylık"
        ? "monthly"
        : selectedTier.frequency === "Yıllık"
          ? "yearly"
          : selectedTier.frequency;

    const result = await receivePayment(
      hotelToken,
      modifiedCardData,
      frequency,
      selectedTier.price,
      setLoading
    );

    setData(result);
  };

  const renderMain = () => {
    return (
      <div
        onClick={(e) => {
          const tagName = (e.target as HTMLElement).tagName.toLowerCase();
          if (tagName !== "input") {
            setFocused("cardNumber");
          }
        }}
        className="flex w-full flex-col space-y-4 md:space-y-8 border-neutral-200 px-0 dark:border-neutral-700 sm:rounded-2xl sm:border sm:p-6 xl:p-8 mt-10 md:mt-0"
      >
        <div className="py-1.5 flex cursor-pointer" onClick={() => setStep(1)}>
          <IconArrowLeft className="size-5" strokeWidth={2} />
        </div>
        <h2 className="text-xl md:text-3xl font-semibold lg:text-4xl">
          Onayla ve Ödeme
        </h2>
        <Cards
          number={userCardData.cardNumber}
          expiry={userCardData.expiredDate}
          cvc={userCardData.cvv}
          name={userCardData.cardHolder}
          focused={focused as any}
          placeholders={{ name: "İsminiz" }}
          locale={{ valid: "VALID THRU" }}
        />
        <div>
          <div className="mt-1">
            <div className="space-y-5 bg-gray-100 p-5 dark:bg-neutral-800 rounded-lg">
              <div className="relative space-y-1">
                <Label>Kart Numarası</Label>
                <Input
                  name="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  value={userCardData.cardNumber}
                  onChange={handleCardNumberChange}
                  onFocus={handleFocus}
                />
                <span className="absolute right-2 top-5">
                  {userCardData.cardNumber.length >= 7 &&
                    handlerCardTypeImage()}
                </span>
              </div>
              <div className="space-y-1">
                <Label>Kart Üzerindeki İsim</Label>
                <Input
                  onChange={handleChange}
                  name="cardHolder"
                  placeholder="Kart Üzerindeki İsim"
                  onFocus={handleFocus}
                />
              </div>
              <div className="max-lg:space-y-5 lg:flex lg:space-x-5">
                <div className="flex-1 space-y-1">
                  <Label>Son Kullanma Tarihi</Label>
                  <Input
                    name="expiredDate"
                    placeholder="Ay/Yıl"
                    maxLength={5}
                    pattern="(0[1-9]|1[0-2])\/\d{2}"
                    onChange={handleExpiryDateChange}
                    value={userCardData.expiredDate}
                    onFocus={handleFocus}
                  />
                </div>

                <div className="flex-1 space-y-1">
                  <Label>CVV</Label>
                  <Input
                    onChange={handleCVCChange}
                    name="cvc"
                    type="text"
                    maxLength={3}
                    placeholder="cvv"
                    value={userCardData.cvv}
                    onFocus={handleFocus}
                  />
                </div>
              </div>
            </div>
            <Dialog open={openModal}>
              <DialogContent
                onInteractOutside={() => setOpenModal(false)}
                className="max-h-[calc(100vh-100px)] max-w-3xl overflow-y-scroll md:max-h-[calc(100vh-50px)]"
              >
                <DialogHeader>
                  <DialogTitle className="text-2xl">
                    {agreement === 1
                      ? "Muvafakatname"
                      : agreement === 2
                        ? "Taahhütname"
                        : "Pet Kabul Sözleşmesi"}
                  </DialogTitle>
                  <DialogDescription></DialogDescription>
                </DialogHeader>
                {agreement === 1 && (
                  <Muvafakatname checkoutData={selectedTier} />
                )}
                {agreement === 2 && <Taahhütname checkoutData={selectedTier} />}
                {agreement === 3 && (
                  <AcceptPetAgreement checkoutData={selectedTier} />
                )}
                <DialogClose
                  onClick={() => setOpenModal(false)}
                  className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
                >
                  <X className="size-4" />
                  <span className="sr-only">Close</span>
                </DialogClose>
              </DialogContent>
            </Dialog>
            <div className="mt-4 flex items-center gap-2">
              <Checkbox
                onChange={() => SetAgreementChecked((prev) => !prev)}
                name="agreement"
                id="agreement-id"
                checked={agreementsChecked}
              />
              <div className="text-sm">
                <span
                  onClick={() => {
                    SetAgreement(1);
                    setOpenModal(true);
                  }}
                  className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
                >
                  Muvafakatname
                </span>{" "}
                {/* ,{" "}
                <span
                  onClick={() => {
                    SetAgreement(2);
                    setOpenModal(true);
                  }}
                  className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
                >
                  Taahhütname
                </span>{" "} */}
                ve
                <span
                  onClick={() => {
                    SetAgreement(3);
                    setOpenModal(true);
                  }}
                  className="cursor-pointer font-medium underline duration-200 hover:text-primary-6000"
                >
                  {" "}
                  Evcil Hayvan Kabul Kabul Sözleşmesini
                </span>{" "}
                okudum kabul ediyorum
              </div>
            </div>
            <div className="pt-8 pb-10 md:pb-20">
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                onClick={() => {
                  handlePayment();
                  setLoading(true);
                }}
                disabled={
                  !buttonDisabled || !agreementsChecked || loading
                    ? true
                    : false
                }
              >
                {loading ? <LoadingSpinner /> : "Üyeliği Başlat"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`nc-CheckOutPagePageMain ${className}`}>{renderMain()}</div>
  );
};

export default CheckoutStep;
