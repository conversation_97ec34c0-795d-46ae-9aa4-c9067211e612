import React from "react";
import type { FC } from "react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import HotelDetail from "./HotelDetail";
import PetOwnerDetail from "./PetOwnerDetail";

interface DetailDrawerProps {
  contactDetail: any;
}

const DetailDrawer: FC<DetailDrawerProps> = ({ contactDetail }) => {
  return (
    <Drawer>
      <DrawerTrigger asChild>
        <Button className="text-xs" variant="outline">
          Ayrıntılar
        </Button>
      </DrawerTrigger>
      <DrawerContent className="max-h-[calc(100vh-100px)]">
        <DrawerHeader className="sr-only">
          <DrawerTitle></DrawerTitle>
          <DrawerDescription></DrawerDescription>
        </DrawerHeader>
        <div className="overflow-auto">
          {contactDetail?.type === "petOwner" ? (
            <PetOwnerDetail contactDetail={contactDetail?.data} />
          ) : (
            <HotelDetail contactDetail={contactDetail?.data} />
          )}
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">Kapat</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default DetailDrawer;
