import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";

interface ResetCheckoutModalState {
  isOpen: boolean;
  triggerType: "back" | "logo" | null;
}

const initialState: ResetCheckoutModalState = {
  isOpen: false,
  triggerType: null,
};

const resetCheckoutModalSlice = createSlice({
  name: "resetCheckoutModal",
  initialState,
  reducers: {
    setOpenModal: (
      state,
      action: PayloadAction<{
        isOpen: boolean;
        triggerType: "back" | "logo" | null;
      }>
    ) => {
      state.isOpen = action.payload.isOpen;
      state.triggerType = action.payload.triggerType;
    },
  },
});

export const { setOpenModal } = resetCheckoutModalSlice.actions;
export default resetCheckoutModalSlice.reducer;
