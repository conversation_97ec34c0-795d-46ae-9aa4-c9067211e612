import type { <PERSON> } from "react";
import React from "react";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

interface PriceSummaryMobileProps {
  displayCustomerTotalPrice: () => number | undefined;
  nightCount: number;
}

const PriceSummaryMobile: FC<PriceSummaryMobileProps> = ({
  displayCustomerTotalPrice,
  nightCount,
}) => {
  const translate = useTranslations("PriceSummary");
  const totalPrice = displayCustomerTotalPrice();

  return (
    <div>
      <h3 className="mb-5 text-center text-xl font-semibold">
        {translate("priceBreakdown")}
      </h3>
      <div className="mb-2 flex justify-between text-sm">
        <div>
          <div>
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(Number(Number(totalPrice) / nightCount)) +
              "₺" +
              " " +
              "x" +
              nightCount +
              " " +
              translate("night")}
          </div>
          <div className="font-light text-neutral-500 dark:text-neutral-400">
            {translate("nightlyRate")}
          </div>
        </div>
        <div>
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(totalPrice))}
          ₺
        </div>
      </div>
      <div className="flex justify-between text-sm">
        <div>{translate("serviceFee")}</div>
        <div>
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(totalPrice) * 0.18)}
          ₺
        </div>
      </div>
      <Separator className="my-4" />
      <div className="flex items-center justify-between text-sm font-medium">
        <div>{translate("totalPrice")}</div>
        <div>
          {displayCustomerTotalPrice() &&
            new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(Number(displayCustomerTotalPrice()) * 1.18) + "₺"}
        </div>
      </div>
      <div className="mt-5 text-center font-medium">
        {translate("yourEarnings")}{" "}
        {new Intl.NumberFormat("tr-TR", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(Number(totalPrice))}
        ₺
      </div>
    </div>
  );
};

export default PriceSummaryMobile;
