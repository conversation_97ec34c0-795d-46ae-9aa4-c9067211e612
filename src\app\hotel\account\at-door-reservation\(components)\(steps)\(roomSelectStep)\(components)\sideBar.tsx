"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import CalculatedDetailContainer from "./CalculatedDetailContainer";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";

interface SideBarProps {
  hotelToken: string | undefined;
}

const SideBar: FC<SideBarProps> = ({ hotelToken }) => {
  const { createOrder } = useHotelAtDoorReservation();
  const [loading, setLoading] = useState<boolean>(false);

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const reservations = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "reservation"
  );
  const subscriptions = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "subscription"
  );

  const services = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "service"
  );

  const isDisabledCondition =
    !calculatedRoomData ||
    (subscriptions?.length === 0 &&
      reservations?.length === 0 &&
      services?.length === 0);

  return (
    <div className="listingSectionSidebar__wrap shadow-xl bg-white dark:bg-neutral-800">
      {calculatedRoomData &&
      (subscriptions?.length > 0 ||
        reservations?.length > 0 ||
        services?.length > 0) ? (
        <CalculatedDetailContainer hotelToken={hotelToken} />
      ) : (
        <div className="flex justify-center items-center h-full">Sepet boş</div>
      )}
      <Button
        className="w-full bg-secondary-6000 hover:bg-secondary-700 text-white"
        onClick={() => {
          createOrder(
            hotelToken,
            calculatedRoomData?.selectedItems,
            calculatedRoomData?.totalOrderPrice,
            setLoading
          );
        }}
        disabled={loading || isDisabledCondition}
      >
        {loading ? <LoadingSpinner /> : "Rezervasyon Oluştur"}
      </Button>
    </div>
  );
};

export default SideBar;
