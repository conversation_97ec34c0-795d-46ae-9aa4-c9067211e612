"use client";
import type { FC } from "react";
import React from "react";
import PetTaxiVehicleCard from "./PetTaxiVehicleCard";
import CreateVehicleModal from "./(createVehicles)/CreateVehicleModal";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
import type {
  VehicleDataApiTypes,
  VehicleListType,
} from "@/types/taxi/vehicleType";

interface PostingContainerProps {
  taxiData: PetTaxiDataApiTypes;
  petTaxiToken: string | undefined;
  vehiclesData: VehicleListType;
}

const PostingContainer: FC<PostingContainerProps> = ({
  taxiData,
  petTaxiToken,
  vehiclesData,
}) => {
  return (
    <>
      {vehiclesData?.length === 0 ? (
        <>
          <div className="mb-5 text-center text-lg font-semibold">
            Önceden oluşturduğunuz araç yok. Lütfen araç oluşturunuz.
          </div>
          <div className="flex items-center justify-center w-full">
            <CreateVehicleModal
              taxiData={taxiData}
              petTaxiToken={petTaxiToken}
            />
          </div>
        </>
      ) : (
        <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
          <div className="mt-4 md:my-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Araç Filosu</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Araçlarınızı yönetin ve düzenleyin
            </p>
            <span className="text-sm text-neutral-500 dark:text-neutral-300"></span>
          </div>
          <CreateVehicleModal taxiData={taxiData} petTaxiToken={petTaxiToken} />
        </div>
      )}
      <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
        {vehiclesData &&
          vehiclesData?.length > 0 &&
          vehiclesData?.map((vehicle: VehicleDataApiTypes) => (
            <PetTaxiVehicleCard
              taxiData={taxiData}
              vehicle={vehicle}
              key={vehicle._id}
              petTaxiToken={petTaxiToken}
            />
          ))}
      </div>
    </>
  );
};

export default PostingContainer;
