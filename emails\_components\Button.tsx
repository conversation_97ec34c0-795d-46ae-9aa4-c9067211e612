import * as React from "react";

import { Link, Section } from "@react-email/components";

interface Props {
  href: string;
  text: string;
  primary?: boolean;
}

const Button = ({ href, text, primary = true }: Props) => {
  return (
    <Section style={buttonContainer}>
      <Link style={primary ? primaryStyle : secondaryStyle} href={href}>
        {text}
      </Link>
    </Section>
  );
};

export default Button;

const buttonContainer = {
  margin: "20px 0",
  display: "flex",
  width: "100%",
  justifyContent: "flex-start",
};

const primaryStyle = {
  display: "inline-flex",
  justifyContent: "center",
  alignItems: "center",
  cursor: "pointer",
  fontWeight: 500,
  borderRadius: "6px",
  padding: "12px 32px",
  margin: "16px 0",
  background: "#e04c28",
  color: "white",
  border: "1px solid #dddddd",
};

const secondaryStyle = {
  display: "inline-flex",
  justifyContent: "center",
  alignItems: "center",
  cursor: "pointer",
  fontWeight: 500,
  borderRadius: "6px",
  padding: "12px 32px",
  background: "#FB37FF",
  color: "white",
  border: "1px solid #dddddd",
};
