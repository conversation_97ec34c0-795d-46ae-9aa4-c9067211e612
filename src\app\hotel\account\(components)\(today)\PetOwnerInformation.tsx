import React from "react";
import type { FC } from "react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { formatPhoneNumber } from "./TodayRoomCard";
import ChatButton from "./ChatButton";

interface PetOwnerInformationProps {
  selectedRoom: any;
}

const PetOwnerInformation: FC<PetOwnerInformationProps> = ({
  selectedRoom,
}) => {
  return (
    <>
      <Separator />
      <div className="flex max-md:flex-col md:items-center gap-4 md:gap-6 p-4 md:p-6 mt-6 rounded-xl border border-gray-200 shadow-sm bg-white">
        {/* Avatar */}
        <div className="flex justify-center">
          <div className="w-24 h-24 rounded-full border-2 border-gray-400 overflow-hidden">
            <Image
              src={
                selectedRoom.reservation?.petOwner?.image?.src
                  ? selectedRoom.reservation.petOwner.image.src
                  : "https://api.dicebear.com/9.x/personas/svg?seed=Destiny"
              }
              width={112}
              height={112}
              alt="petOwnerPhoto"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        {/* Bilgiler */}
        <div className="flex flex-col gap-2 text-sm text-gray-800">
          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">Pet Sahibi:</span>
            <span className="font-semibold text-gray-600 capitalize">
              {selectedRoom.reservation?.petOwner?.fullName ||
                selectedRoom.reservation?.hotelCustomer?.fullName ||
                ""}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">E-posta:</span>
            <span className="font-semibold text-gray-600">
              {selectedRoom.reservation?.petOwner?.email ||
                selectedRoom.reservation?.hotelCustomer?.email ||
                ""}
            </span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">Telefon:</span>
            <span className="font-semibold text-gray-600">
              {formatPhoneNumber(
                selectedRoom.reservation?.petOwner?.phone ||
                  selectedRoom.reservation?.hotelCustomer?.phone ||
                  ""
              )}
            </span>
          </div>
        </div>
      </div>
      {selectedRoom?.reservation?.petOwner && (
        <div className="flex items-center justify-center mt-5">
          <ChatButton petOwnerId={selectedRoom.reservation?.petOwner?._id} />
        </div>
      )}
    </>
  );
};

export default PetOwnerInformation;
