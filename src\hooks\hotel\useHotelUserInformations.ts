import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelUserInformations = () => {
  const { toast } = useToast();

  // ADDS NEW HOTEL USER
  const addHotelUserInformationHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelUserInformations: {
      firstName: string;
      lastName: string;
      gender: string;
      username: string;
      password: string;
      email: string;
      dateOfBirth: string;
      phone: string;
      bio: string;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeUserAddModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.users, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            firstName: hotelUserInformations.firstName.trim(),
            lastName: hotelUserInformations.lastName.trim(),
            gender: hotelUserInformations.gender.toLowerCase(),
            username: hotelUserInformations.username.toLowerCase(),
            password: hotelUserInformations.password,
            email: hotelUserInformations.email.toLowerCase(),
            dateOfBirth: hotelUserInformations.dateOfBirth,
            phone: hotelUserInformations.phone,
            bio: hotelUserInformations.bio,
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeUserAddModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Kullanıcı Ekleme",
          description: "Kullanıcı başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/users");
        closeUserAddModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // DELETES SELECTED HOTEL USER
  const deleteHotelUserHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelUserID: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeUserDeleteModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.users}/${hotelUserID}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeUserDeleteModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Kullanıcı Silme",
          description: "Kullanıcı başarıyla silindi.",
        });
        revalidatePathHandler("/hotel/account/users");
        closeUserDeleteModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES SELECTED HOTEL USER
  const updateHotelUserHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    updatedHotelUserInfo: {
      firstName: string;
      lastName: string;
      gender: string;
      username: string;
      email: string;
      dateOfBirth: string;
      phone: string;
      bio: string;
    },
    hotelUserID: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeUserUpdateModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.users}/${hotelUserID}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              firstName: updatedHotelUserInfo.firstName.trim(),
              lastName: updatedHotelUserInfo.lastName.trim(),
              gender: updatedHotelUserInfo.gender,
              username: updatedHotelUserInfo.username.toLowerCase(),
              email: updatedHotelUserInfo.email.toLowerCase(),
              dateOfBirth: updatedHotelUserInfo.dateOfBirth,
              phone: updatedHotelUserInfo.phone,
              bio: updatedHotelUserInfo.bio,
            }),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeUserUpdateModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Kullanıcı Güncelleme",
          description: "Kullanıcı başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/users");
        closeUserUpdateModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    addHotelUserInformationHandler,
    deleteHotelUserHandler,
    updateHotelUserHandler,
  };
};
