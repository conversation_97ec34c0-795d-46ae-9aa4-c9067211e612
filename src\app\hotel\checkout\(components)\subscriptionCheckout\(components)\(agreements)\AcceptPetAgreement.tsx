import React from "react";
import type { FC } from "react";

interface AcceptPetAgreementProps {
  checkoutData: any;
}

const AcceptPetAgreement: FC<AcceptPetAgreementProps> = ({ checkoutData }) => {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          1. Taraflar
        </h3>
        <div>
          <p className="mb-3 capitalize">
            İşletmeci: {checkoutData?.hotel?.legalName}
          </p>
          <p className="mb-3">Müşteri:</p>
          <ul className="list-inside space-y-2 pl-2.5">
            <li className="text-sm">Ad: {checkoutData?.petOwner?.firstName}</li>
            <li className="text-sm">
              Soyad: {checkoutData?.petOwner?.lastName}
            </li>
            <li className="text-sm">TCKN:</li>
            <li className="text-sm">
              Telefon: {checkoutData?.petOwner?.phone}
            </li>
            <li className="text-sm">Adres:</li>
          </ul>
        </div>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          2. Sözleşmenin Konusu
        </h3>
        <p className="text-sm md:text-base">
          Bu sözleşme, müşteri tarafından işletmeciye emanet edilen hayvanın
          (burada “hayvan” olarak anılacaktır.) bakım ve konaklama hizmetlerinin
          sağlanması için yapılmıştır.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          3. Hizmetler
        </h3>
        <p className="text-sm md:text-base">
          İşletmeci; hayvanın konaklama, beslenme ve diğer gerekli bakım ve
          ihtiyaçlarını karşılayacaktır. Bununla birlikte hayvanın sağlık durumu
          ve ihtiyaçlarına bağlı olarak müşterinin onayıyla ek hizmetler de
          sunabilir.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          4. Ücret
        </h3>
        <p className="text-sm md:text-base">
          Müşteri, bu hizmetler için belirlenen ücreti ödemeyi kabul, beyan ve
          taahhüt eder. Ücret hayvanın konaklama süresine, alınan ek hizmetlere
          göre değişebilir.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          5. Sağlık ve Aşılar
        </h3>
        <p className="text-sm md:text-base">
          Müşteri, hayvanın güncel aşıları ve sağlık durumu hakkında işletmeciye
          detaylı bilgi verecektir. Ayrıca hayvanın sağlık sorunları varsa
          ve/veya özel bir tedavi gerekiyorsa, işletmeciye bu konuda bilgi
          vermek müşterinin sorumluluğundadır. Müşteri, işletmeciyi
          bilgilendirmediği durumlardan kaynaklı çıkan olumsuzluklardan
          işletmecinin sorumlu tutulamayacağını kabul, beyan ve taahhüt eder.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          6. Sorumluluk Sınırı
        </h3>
        <p className="text-sm md:text-base">
          İşletmeci, hayvana karşı özen gösterecek ve güvenli bir ortam
          sağlayacaktır. Ancak beklenmedik olaylar (Bilinmeyen/bilgisi
          verilmeyen hastalık, kanser, kalp krizi, mide dönmesi vb.) veya
          kazalar durumunda işletmeci sorumlu tutulamaz.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          7. Teslim ve Teslim Süresi
        </h3>
        <p className="text-sm md:text-base">
          Müşteri, taahhüt ettiği tarihte hayvanı almaya gelmez ve yazılı
          mazeret bildirmezse, hayvanı alması için yazılı olarak bir başkasını
          yetkilendirmesi istenecektir. Müşteriye taahhüt edilen süreye ek 14
          günlük ek süre verilerek hayvanını alması istenecektir. Bu süre
          içerisinde müşteri işletmeciyle iletişime geçmezse, hayvanı teslim
          almazsa, hayvanın tüm hakları işletmeciye geçecektir ve müşteri bu
          hususta hiçbir şekilde hak talep edemeyecektir. Müşteri bu hususu
          kabul, beyan ve taahhüt eder. Bu süreçteki pansiyon ve mama ücretleri
          kanuni yollarla günlük %.... faiz uygulanacaktır.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          8. Diğer Hükümler
        </h3>
        <p className="text-sm md:text-base">
          Bu sözleşme, taraflar arasında yapılan tüm anlaşmaları ve taahhütleri
          kapsar. Tarafların yazılı onayı olmadan feshedilemez, değiştirilemez
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          9. Yetkili mahkeme ve İcra Daireleri
        </h3>
        <p className="text-sm md:text-base">
          Bu sözleşmeden kaynaklı, taraflar arasında çıkabilecek her türlü
          uyuşmazlıklarda Gaziantep Mahkemeleri ve İcra Daireleri yetkilidir.
        </p>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          10. Yürürlük
        </h3>
        <p className="text-sm md:text-base">
          Bu sözleşme, tarafların imzası ile yürürlüğe girer.
        </p>
      </div>
      <div className="my-10 space-y-10">
        <div className="space-y-2">
          <div>Müşteri</div>
          <div>Tarih</div>
          <div>İmza</div>
        </div>
        <div className="space-y-2">
          <div>İşletmeci</div>
          <div>Tarih</div>
          <div>İmza</div>
        </div>
      </div>
    </div>
  );
};

export default AcceptPetAgreement;
