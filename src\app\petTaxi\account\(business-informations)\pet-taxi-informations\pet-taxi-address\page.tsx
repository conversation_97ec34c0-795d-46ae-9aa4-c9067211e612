import React from "react";
import PetTaxiAddress from "../(components)/(petTaxiAddress)/PetTaxiAddress";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import PetTaxiMap from "../(components)/(petTaxiAddress)/PetTaxiMap";

const PetTaxiAddressPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();

  return (
    <>
      {taxiData && (
        <>
          <div className="mt-4 md:mt-8">
            <h2 className="text-xl md:text-2xl font-semibold">
              Adres Bilgileri
            </h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300">
              Bu adım gelecek olan şifreler ile alt üye işyeri hesabınızın
              takibini yapmak için önemlidir
            </span>
          </div>
          <PetTaxiAddress
            taxiData={taxiData?.data}
            petTaxiToken={petTaxiToken}
          />
          {/* <div className="mt-16">
            <h2 className="text-2xl font-semibold">Harita Bilgileri</h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300">
              Bu adım taksi detay sayfasında görünecek olan harita alanı
              içindir. Google Maps üzerindeki paylaş alanından HTML alanı buraya
              yapıştırmanızı bekliyoruz.
            </span>
          </div>
          <PetTaxiMap
            taxiData={taxiData?.data}
            petTaxiToken={petTaxiToken}
            method="POST"
          /> */}
        </>
      )}
    </>
  );
};

export default PetTaxiAddressPage;
