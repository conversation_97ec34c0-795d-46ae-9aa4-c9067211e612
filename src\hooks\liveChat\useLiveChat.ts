import { ImageResizeFitType, uploadMultiToS3 } from "@/lib/s3BucketHelper";
import { useToast } from "@/components/ui/use-toast";

export const useLiveChat = () => {
  const { toast } = useToast();
  const uploadChatFileHandler = async (
    files: File[] | FileList | null,
    hotelToken: string | undefined,
    hotelId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (files && files?.length > 0) {
      setLoading(true);
      let filesArray: FileList | null = null;

      if (files instanceof FileList) {
        filesArray = files;
      } else if (Array.isArray(files)) {
        const dataTransfer = new DataTransfer();
        files.forEach((file) => dataTransfer.items.add(file));
        filesArray = dataTransfer.files;
      }
      if (filesArray && filesArray.length > 0) {
        try {
          const response = await uploadMultiToS3(
            filesArray,
            `hotel/${hotelId}/liveChatFiles`,
            hotelToken,
            undefined,
            "chatFile"
          );
          if (response.success) {
            const uploadedItemList = response?.data?.map((item: any) => {
              return {
                fileUrl: item?.src,
                fileType: item?.mimetype,
              };
            });
            return uploadedItemList;
          } else {
            setLoading(false);
            throw new Error("Fotoğraf yükleme başarısız oldu.");
          }
        } catch (error) {
          setLoading(false);
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `yükleme hatası: ${error}`,
          });
          console.error("yükleme hatası:", error);
        }
      }
    }
  };

  const uploadChatImageHandler = async (
    file: FileList | undefined,
    hotelToken: string | undefined,
    hotelId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (!file || file?.length === 0) return;
    setLoading(true);
    try {
      const response = await uploadMultiToS3(
        file,
        `hotel/${hotelId}/liveChatFiles`,
        hotelToken,
        ImageResizeFitType.inside
      );
      if (response.success) {
        const uploadedItemList = response?.data?.map((item: any) => {
          return {
            fileUrl: item?.src,
            fileType: item?.mimetype,
          };
        });
        return uploadedItemList;
      } else {
        setLoading(false);
        throw new Error("Fotoğraf yükleme başarısız oldu.");
      }
    } catch (error) {
      setLoading(false);
      toast({
        variant: "error",
        duration: 3500,
        title: "Hata",
        description: `yükleme hatası: ${error}`,
      });
      console.error("yükleme hatası:", error);
    }
  };

  return { uploadChatFileHandler, uploadChatImageHandler };
};
