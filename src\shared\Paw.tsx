import React from "react";

interface PawHoofLogoType {
  className: string;
}

const PawHoofLogo: React.FC<PawHoofLogoType> = ({ className }) => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 673 650"
        className={`${className}`}
      >
        <g clipPath="url(#clip0_473_66)">
          <path
            d="M149 239.2C149 239.2 126.81 193.66 88.28 179.64C49.74 165.63 24.05 220.51 15.88 268.39C7.71 316.27 45.07 378.16 87.11 393.34C129.15 408.52 173.52 367.65 174.69 338.46C175.86 309.27 149 239.2 149 239.2Z"
            fill="#E7411B"
          />
          <path
            d="M260.08 11.78C239.86 13.21 221.32 23.48 208.49 39.18C192.14 59.19 165.14 96.65 157.17 134.11C145.49 188.99 163.01 241.54 212.05 257.89C261.1 274.24 299.63 227.53 314.81 184.32C329.99 141.11 321.82 67.54 303.13 32.51C293.79 14.99 276.56 10.61 260.07 11.78H260.08Z"
            fill="#E7411B"
          />
          <path
            d="M384.88 19.6601C384.88 19.6601 366.2 51.1901 361.52 115.42C356.85 179.65 375.53 225.19 403.56 247.38C431.59 269.57 482.97 264.9 510.99 225.19C539.02 185.49 528.51 104.91 507.49 73.3801C486.48 41.8501 445.61 4.48008 423.42 0.980079C401.23 -2.51992 391.89 3.32008 384.88 19.6601Z"
            fill="#E7411B"
          />
          <path
            d="M595.94 185.06C578.57 190.38 563.64 201.62 553.01 216.35C536.97 238.57 511.1 278.96 506.33 313.94C499.32 365.32 541.36 406.19 570.56 406.19C599.76 406.19 642.96 385.17 662.81 320.94C682.66 256.71 637.12 187.82 617.27 183.14C610.89 181.64 603.3 182.79 595.94 185.05V185.06Z"
            fill="#E7411B"
          />
          <path
            d="M147.65 458.62L146.08 458.76L145.83 457.88C146.25 457.2 146.68 456.53 147.14 455.86L147.65 458.62Z"
            fill="#E7411B"
          />
          <path
            d="M527.2 456.33L491.12 458.02L465.26 455.97L463.79 458.25L420.87 455.85L420.51 473.09C417.03 470.22 413.05 467.92 408.57 466.23C406.49 465.46 404.49 464.87 402.53 464.44C398.87 463.61 394.95 463.19 390.87 463.19C386.79 463.19 382.55 463.79 378.71 464.98C374.93 466.15 371.38 467.8 368.13 469.91C364.9 472.01 361.96 474.51 359.41 477.35C358.57 478.27 357.79 479.25 357.03 480.24C355.77 478.51 354.38 476.89 352.86 475.39C348.85 471.43 343.99 468.36 338.39 466.24C333 464.22 327.04 463.19 320.66 463.19C316.42 463.19 312.33 463.79 308.52 464.98C304.75 466.14 301.19 467.79 297.94 469.9C295.3 471.61 292.86 473.62 290.62 475.87C289.51 473.73 288.18 471.75 286.61 470C284.34 467.44 281.67 465.26 278.69 463.54C275.97 461.97 273.03 460.68 269.93 459.71C266.94 458.78 263.83 458.11 260.68 457.73C257.64 457.35 254.63 457.16 251.74 457.16C249.28 457.16 246.67 457.29 243.94 457.56C241.3 457.83 238.65 458.22 236.1 458.71C233.48 459.21 230.83 459.84 228.21 460.61C227.47 460.83 226.75 461.06 226.04 461.29L186.02 457.72L185.94 458.56H157.67L157.57 457.74L147.65 458.62L146.08 458.76L141.79 459.14C142.88 457.43 143.99 455.74 145.14 454.07C154.24 440.85 165.89 429.69 176.82 418.04C202.49 390.69 229.13 364.25 255.89 337.96C259.85 334.06 263.83 330.17 267.8 326.28C323.41 271.83 389.68 301.42 427.56 351.12C442.5 370.75 458.8 389.33 476.04 406.96C486.29 417.44 497.49 426.91 508.37 436.72L527.2 456.33Z"
            fill="#E7411B"
          />
          <path
            d="M179.92 546.05L178.47 546.08L178.39 545.6C178.9 545.75 179.41 545.9 179.92 546.05Z"
            fill="#E7411B"
          />
          <path
            d="M562.38 577.84C547.57 614.03 511.98 641.64 481.98 648.12C438.2 657.59 394.42 624.46 316.32 626.83C238.22 629.2 207.45 657.59 151.85 623.27C151.68 623.17 151.51 623.07 151.34 622.96C127.14 607.83 112.93 581.21 112.46 552.95L125.86 551.22L126.45 554.66L169.51 553.14L170.03 556.26L178.59 556.08H178.74L180.13 556.05L217.48 555.25L218.42 549.7L220.36 550.65C222.94 551.91 225.67 552.97 228.49 553.78C231.13 554.54 233.86 555.16 236.57 555.62C237.05 555.7 237.54 555.77 238.02 555.84L238.21 555.87C240.3 556.18 242.48 556.42 244.7 556.57C247.29 556.74 249.84 556.83 252.28 556.83C254.72 556.83 257.43 556.73 260.25 556.54C263.35 556.32 266.42 555.9 269.38 555.28C272.52 554.62 275.58 553.7 278.49 552.55C281.82 551.22 284.83 549.45 287.42 547.32C287.76 547.04 288.11 546.74 288.49 546.4C289.8 545.2 290.96 543.88 292 542.47C293.06 543.41 294.16 544.3 295.28 545.11C296.32 545.87 297.47 546.62 298.85 547.44C302.14 549.35 305.68 550.85 309.35 551.88C313.13 552.95 317.15 553.5 321.3 553.5C325.45 553.5 329.73 552.92 333.59 551.77C337.44 550.63 341.05 549.02 344.28 547.01C345.4 546.31 346.49 545.55 347.52 544.76C349.54 543.24 351.45 541.5 353.2 539.6C354.28 538.43 355.27 537.2 356.21 535.93C357.39 537.52 358.67 539.02 360.05 540.42C362.71 543.14 365.73 545.49 369.07 547.45C372.36 549.36 375.9 550.85 379.56 551.88C383.34 552.95 387.36 553.5 391.51 553.5C395.66 553.5 399.94 552.92 403.79 551.77C405.31 551.33 406.76 550.82 408.26 550.2C410.45 549.29 412.56 548.21 414.52 546.98C416.09 546 417.59 544.91 419.02 543.74L418.7 559.17L460.95 557.55L466.11 557.4L487.41 553.64V554.78L518.96 553.14L518.92 554.68H560.7L564.18 554.58L565.89 554.42C566.22 562.65 565.32 570.67 562.38 577.84Z"
            fill="#E7411B"
          />
          <path
            d="M178.39 545.6L178.47 546.08L179.92 546.05C179.41 545.9 178.9 545.75 178.39 545.6Z"
            fill="#A62717"
          />
          <path
            d="M112.5 553C108.1 509.4 130.333 472.167 142 459C255.333 436.667 490.9 404.8 526.5 456C562.1 507.2 567.667 543.333 566 555C416.667 572.5 116.9 596.6 112.5 553Z"
            fill="#E7411B"
          />
        </g>
        <defs>
          <clipPath id="clip0_473_66">
            <rect width="672.07" height="649.83" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </>
  );
};

export default PawHoofLogo;
