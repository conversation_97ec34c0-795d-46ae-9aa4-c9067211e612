// "use client";
// import React, { useState } from "react";
// import type { FC, ChangeEvent } from "react";
// import DistanceCalculator from "../DistanceCalculator";
// import { Separator } from "@/components/ui/separator";
// import Textarea from "@/shared/Textarea";
// import ButtonPrimary from "@/shared/ButtonPrimary";
// import ButtonSecondary from "@/shared/ButtonSecondary";
// import { format } from "date-fns";
// import LoadingSpinner from "@/shared/icons/Spinner";
// import { useSelector } from "react-redux";
// import type { RootState } from "@/store";
// import { usePetOwnerOrder } from "@/hooks/petOwner/usePetOwnerOrder";
// import { usePathname } from "next/navigation";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
// import { Calendar } from "@/components/ui/calendar";
// import { createLocalDate } from "@/utils/createLocalDate";
// import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
// import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";
// import { useTranslations } from "next-intl";

// interface TransportationServiceProps {
//   service: any;
//   petOwnerToken: string | undefined;
//   hotelId: string;
//   loading: boolean;
//   setLoading: React.Dispatch<React.SetStateAction<boolean>>;
//   closeModal: () => void;
//   formData: any;
//   setFormData: any;
//   handleDistanceChange: any;
//   handleLocationsChange: any;
//   orderId?: string;
//   petOwnerPetData?: any;
//   hotelLocation: string;
// }

// const TransportationService: FC<TransportationServiceProps> = ({
//   service,
//   petOwnerToken,
//   hotelId,
//   loading,
//   setLoading,
//   closeModal,
//   formData,
//   setFormData,
//   handleDistanceChange,
//   handleLocationsChange,
//   orderId,
//   petOwnerPetData,
//   hotelLocation,
// }) => {
//   const translate = useTranslations("ServicesCart");
//   const pathname = usePathname();
//   const { addItemToCart, addItemToOrder } = usePetOwnerOrder();
//   const [selectedPet, setSelectedPet] = useState<string>("");
//   const [disabled, setDisabled] = useState<boolean>(false);
//   const today = format(new Date(), "yyyy-MM-dd");
//   const isCheckout = pathname === "/checkout";
//   const calendarLanguage = datePickerLanguageHandler(translate("language"));
//   const monthFormatter = new Intl.DateTimeFormat(
//     calendarLanguage.firstValue === "tr" ? "tr" : "en",
//     {
//       month: "long",
//     }
//   );

//   const userReservationData = useSelector(
//     (state: RootState) => state.userReservationData.userReservationData
//   );
//   const calculatedRoomData = useSelector(
//     (state: RootState) => state.calculatedRoomData.calculatedRoomData
//   );

//   const handleChange = (
//     event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
//   ) => {
//     const { name, value } = event.target;
//     setFormData((prevState: any) => {
//       return {
//         ...prevState,
//         [name]: value,
//       };
//     });
//   };

//   const buttonDisabledHandler = () => {
//     if (isCheckout && !selectedPet) {
//       return { errorText: "", disabled: true };
//     }

//     const isFormDataValid = Object.entries(formData).every(([key, value]) => {
//       // if (key === "note") return true;
//       if (typeof value === "string") {
//         return value.trim() !== "";
//       }
//       return Boolean(value);
//     });

//     if (!isFormDataValid) {
//       return { errorText: "", disabled: true };
//     }

//     const isValidDate = new Date(formData.serviceDate) >= new Date(today);
//     if (!isValidDate) {
//       return {
//         errorText: translate("errorText"),
//         disabled: true,
//       };
//     }

//     return { errorText: "", disabled: false };
//   };

//   const isButtonDisabled = buttonDisabledHandler();

//   const addToCartHandler = () => {
//     const requestBody = {
//       itemType: "service",
//       itemData: {
//         ...service?.serviceData,
//         note: formData.note,
//         serviceDate: formData.serviceDate,
//         serviceDetails: {
//           ...service?.serviceData?.serviceDetails,
//           startPoint: formData.startLocation,
//           endPoint: formData.endLocation,
//           distance: formData.distance,
//         },
//         serviceType: service?.serviceType,
//         serviceHotelId: service?._id,
//         hotel: hotelId,
//         pet: userReservationData.selectedPet,
//       },
//       selectedItems: calculatedRoomData?.selectedItems || [],
//       totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
//     };

//     addItemToCart(
//       petOwnerToken,
//       requestBody,
//       setLoading,
//       closeModal,
//       setDisabled
//     );
//   };

//   const addToOrderHandler = () => {
//     const requestBody = {
//       orderId: orderId,
//       itemData: {
//         ...service?.serviceData,
//         note: formData.note,
//         serviceDate: formData.serviceDate,
//         serviceDetails: {
//           ...service?.serviceData?.serviceDetails,
//           startPoint: formData.startLocation,
//           endPoint: formData.endLocation,
//           distance: formData.distance,
//         },
//         serviceType: service?.serviceType,
//         hotel: hotelId,
//         pet: selectedPet,
//       },
//     };

//     addItemToOrder(
//       petOwnerToken,
//       requestBody,
//       "service",
//       setLoading,
//       closeModal,
//       setDisabled
//     );
//   };

//   const submitHandler = () => {
//     if (orderId) {
//       addToOrderHandler();
//     } else {
//       addToCartHandler();
//     }
//   };

//   return (
//     <form
//       className="space-y-3"
//       onSubmit={(event) => {
//         event.preventDefault();
//         submitHandler();
//       }}
//     >
//       <div className="flex flex-col gap-1">
//         <div className="flex items-center gap-1">
//           <p className="text-sm font-semibold">{translate("serviceName")}:</p>
//           <p className="text-sm capitalize font-medium">
//             {service?.serviceData?.serviceName}
//           </p>
//         </div>
//         <div className="flex items-center gap-1">
//           <p className="text-sm font-semibold">{translate("startingPrice")}:</p>
//           <p className="text-sm capitalize font-medium">
//             {new Intl.NumberFormat("tr-TR", {
//               minimumFractionDigits: 2,
//               maximumFractionDigits: 2,
//             }).format(
//               Number(service?.serviceData?.serviceDetails?.initialPrice)
//             ) + "₺"}
//           </p>
//         </div>
//         <div className="flex items-center gap-1">
//           <p className="text-sm font-semibold">{translate("maxDistance")}:</p>
//           <p className="text-sm font-medium">
//             {service?.serviceData?.serviceDetails?.maxDistance} km
//           </p>
//         </div>
//         <div className="flex items-center gap-1">
//           <p className="text-sm font-semibold">{translate("pricePerKm")}:</p>
//           <p className="text-sm capitalize font-medium">
//             {new Intl.NumberFormat("tr-TR", {
//               minimumFractionDigits: 2,
//               maximumFractionDigits: 2,
//             }).format(
//               Number(service?.serviceData?.serviceDetails?.distancePrice)
//             ) + "₺"}
//           </p>
//         </div>
//         <div className="flex items-center gap-1">
//           <p className="text-sm font-semibold">{translate("description")}:</p>
//           <p className="text-sm font-medium">
//             {service?.serviceData?.description}
//           </p>
//         </div>
//         <Separator className="mt-2" />
//         <div className="flex flex-col gap-1 z-[1000] mt-3">
//           <p className="text-sm font-semibold">
//             {translate("addressSelection")}:
//           </p>
//           <DistanceCalculator
//             onDistanceChange={handleDistanceChange}
//             onLocationsChange={handleLocationsChange}
//             hotelLocation={hotelLocation}
//             setFormData={setFormData}
//           />
//         </div>
//         <div className="flex flex-col gap-1 mt-2">
//           <p className="text-sm font-semibold">{translate("serviceDate")}:</p>
//           <Calendar
//             mode="single"
//             locale={calendarLanguage.secondValue}
//             formatters={{
//               formatMonthDropdown: (date) => monthFormatter.format(date),
//             }}
//             disabled={{ before: new Date() }}
//             defaultMonth={createLocalDate(formData.serviceDate)}
//             startMonth={
//               new Date(new Date().getFullYear(), new Date().getMonth())
//             }
//             endMonth={new Date(2050, 11)}
//             required
//             selected={createLocalDate(formData.serviceDate)}
//             onSelect={(selectedDate) => {
//               const adjustedStartDate = adjustDateToTimezone(selectedDate);
//               const dateToString = adjustedStartDate
//                 ?.toISOString()
//                 .split("T")[0];
//               setFormData((prev: any) => ({
//                 ...prev,
//                 serviceDate: dateToString,
//               }));
//             }}
//             className="rounded-md border shadow-sm w-full"
//             classNames={{
//               today: "bg-transparent text-foreground rounded-md",
//             }}
//             {...({ disableAutoUnselect: true } as any)}
//             captionLayout="dropdown"
//           />
//           <p className="text-red-500 text-[12px] mt-1">
//             {isButtonDisabled.errorText}
//           </p>
//         </div>
//         {isCheckout && petOwnerPetData?.length > 0 && (
//           <>
//             <p className="text-sm font-semibold">{translate("petSelection")}</p>
//             <Select onValueChange={(selected) => setSelectedPet(selected)}>
//               <SelectTrigger className="w-[200px]">
//                 <SelectValue placeholder={translate("chooseAPet")} />
//               </SelectTrigger>
//               <SelectContent>
//                 {petOwnerPetData?.map((pet: any) => {
//                   return (
//                     <SelectItem key={pet?._id} value={pet?._id}>
//                       {pet?.name}
//                     </SelectItem>
//                   );
//                 })}
//               </SelectContent>
//             </Select>
//           </>
//         )}
//         <div className="flex flex-col gap-1 mt-3">
//           <p className="text-sm font-semibold">{translate("fullAddress")}:</p>
//           <Textarea
//             name="note"
//             placeholder={translate("pleaseFullAddress")}
//             value={formData.note}
//             onChange={handleChange}
//           />
//         </div>
//       </div>
//       <div className="flex items-center gap-1">
//         <p className="max-sm:text-sm font-medium">{translate("price")}:</p>
//         <p className="font-bold">
//           {new Intl.NumberFormat("tr-TR", {
//             minimumFractionDigits: 2,
//             maximumFractionDigits: 2,
//           }).format(Number(formData.totalPrice)) + "₺"}
//         </p>
//       </div>
//       <div className="flex justify-end gap-5 pb-1">
//         <ButtonSecondary
//           className="w-1/2 sm:w-32"
//           type="button"
//           onClick={closeModal}
//         >
//           {translate("cancel")}
//         </ButtonSecondary>
//         <ButtonPrimary
//           disabled={disabled || isButtonDisabled.disabled}
//           type="submit"
//           className="w-1/2 sm:w-32"
//         >
//           {loading ? <LoadingSpinner /> : translate("add")}
//         </ButtonPrimary>
//       </div>
//     </form>
//   );
// };

// export default TransportationService;
