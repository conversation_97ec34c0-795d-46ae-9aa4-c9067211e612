import { cn } from "@/lib/utils";
import { Marquee } from "@/components/ui/marquee";

const reviews = [
  {
    name: "<PERSON><PERSON>v <PERSON> O<PERSON>",
    img: "/img/partnerHotel/miyahavpetotel.png",
  },
  {
    name: "Gaia Pet Otel",
    img: "/img/partnerHotel/gaiapetotel.png",
  },
  {
    name: "Patidogya",
    img: "/img/partnerHotel/patidogyapetotel.jpg",
  },
  {
    name: "Park PetPoint Tuğba",
    img: "/img/partnerHotel/parkpetpointtugba.jpg",
  },
  {
    name: "Ex Po Pet Otel",
    img: "/img/partnerHotel/expopetotel.jpg",
  },
  {
    name: "<PERSON>talya <PERSON>",
    img: "/img/partnerHotel/antalyapetotel.jpg",
  },
  {
    name: "Antalya Dog Club",
    img: "/img/partnerHotel/antalyadogclub.jpg",
  },
  {
    name: "<PERSON>yland Pet Otel",
    img: "/img/partnerHotel/bobylandkopekoteli.png",
    type: "rectangle" as const,
  },
  {
    name: "Köpeğim Butik Hotel",
    img: "/img/partnerHotel/kopegimbutikpetotel.jpg",
  },
  {
    name: "Petgiller",
    img: "/img/partnerHotel/petgillerpetotel.jpg",
    type: "rectangle" as const,
  },
  {
    name: "Antalya Köpek Oteli",
    img: "/img/partnerHotel/antalyakopekoteli.jpg",
  },
];

const ReviewCard = ({
  img,
  name,
  type,
}: {
  img: string;
  name: string;
  type?: "rectangle";
}) => {
  return (
    <div
      className={cn(
        // light styles
        "border-wihte",
        // dark styles
        "dark:border-gray-50/[.1]",
        "relative" // remove group class
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <div
          className={`h-24 overflow-hidden rounded-xl relative group ${type === "rectangle" ? "w-full" : "w-24"}`}
        >
          <img className="object-cover w-full h-full" alt={name} src={img} />
        </div>
      </div>
    </div>
  );
};

export function MarqueePartnerHotel() {
  return (
    <div>
      <div className="relative flex h-36 w-full flex-col items-center justify-center overflow-hidden bg-transparent">
        <Marquee pauseOnHover className="[--duration:20s]">
          {reviews.map((review) => (
            <ReviewCard key={review.name} {...review} />
          ))}
        </Marquee>
        <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background"></div>
        <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background"></div>
      </div>
    </div>
  );
}
