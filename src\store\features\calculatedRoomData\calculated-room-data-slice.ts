import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { calculatedRoomDataStates } from "./calculated-room-data-types";

const initialState: calculatedRoomDataStates = {
  calculatedRoomData: null,
};

const calculatedRoomDataSlice = createSlice({
  name: "calculatedRoomData",
  initialState,
  reducers: {
    setCalculatedRoomData: (state, action: PayloadAction<any>) => {
      state.calculatedRoomData = action.payload;
    },
  },
});

export const { setCalculatedRoomData } = calculatedRoomDataSlice.actions;
export default calculatedRoomDataSlice.reducer;
