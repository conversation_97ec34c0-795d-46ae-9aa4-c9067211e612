import type { <PERSON> } from "react";
import React from "react";

interface IconMinusProps {
  className?: string;
}

const IconMinus: FC<IconMinusProps> = ({ className = "size-6" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={3.7}
      stroke="currentColor"
      className={className}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M5 12h14" />
    </svg>
  );
};

export default IconMinus;
