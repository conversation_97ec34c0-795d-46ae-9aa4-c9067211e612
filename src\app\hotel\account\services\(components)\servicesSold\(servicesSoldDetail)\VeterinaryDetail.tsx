import React from "react";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface VeterinaryDetailProps {
  servicesSoldData: any;
}

const VeterinaryDetail: FC<VeterinaryDetailProps> = ({ servicesSoldData }) => {
  return (
    <>
      <section>
        <h3 className="text-lg font-medium mb-2">🩺 Hizmet Bilgileri</h3>
        <ul className="space-y-1">
          <li>
            Hizmet Adı:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceName || "-"}
            </span>
          </li>
          <li>
            Hizmet Tarihi:{" "}
            <span className="font-semibold">
              {formatDateToDayMonthYear(servicesSoldData?.serviceDate) || "-"}
            </span>
          </li>
          <li>
            Veteriner Adı:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.veterinarianName || "-"}
            </span>
          </li>
          <li>
            Gerekli <PERSON>alar:{" "}
            <span className="font-semibold">
              {servicesSoldData?.serviceDetails?.requiredDocuments || "-"}
            </span>
          </li>
          <li>
            Not:{" "}
            <span className="font-semibold">
              {servicesSoldData?.note || "-"}
            </span>
          </li>
        </ul>
      </section>
      <Separator />
    </>
  );
};

export default VeterinaryDetail;
