"use client";

import React from "react";
import { BellAlertIcon } from "@heroicons/react/24/solid";
import { BellIcon } from "@heroicons/react/24/outline";
export interface NotificationsProps {
  className?: string;
}
const Notifications: React.FC<NotificationsProps> = ({ className = "" }) => {
  return (
    <button
      className={`flex size-12 items-center justify-center self-center rounded-full text-2xl text-neutral-700 opacity-80 hover:opacity-100 focus:outline-none dark:text-neutral-300 dark:hover:opacity-100 md:text-3xl ${className}`}
    >
      <span className="sr-only">Enable dark mode</span>
      {false ? (
        <BellAlertIcon className="size-6" aria-hidden="true" />
      ) : (
        <BellIcon className="size-6" aria-hidden="true" />
      )}
    </button>
  );
};

export default Notifications;
