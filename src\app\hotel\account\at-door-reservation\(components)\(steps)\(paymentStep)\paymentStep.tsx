"use client";
import { useEffect, useState } from "react";
import Label from "@/components/Label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";

interface PaymentStepProps {}
const PaymentStep: React.FC<PaymentStepProps> = ({}) => {
  const dispatch = useDispatch();
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const [channel, setChannel] = useState("");
  const [paymentType, setPaymentType] = useState("");

  const handleChannelChange = (newChannel: string) => {
    setChannel(newChannel);
  };

  const handlePaymentTypeChange = (newPaymentType: string) => {
    setPaymentType(newPaymentType);
  };

  useEffect(() => {
    dispatch(
      setCheckoutData({
        ...checkoutData,
        channel,
        paymentType,
      })
    );
  }, [channel, paymentType]);

  return (
    <div>
      <div className="text-xl font-bold">Kanal ve Ödeme Tipi</div>
      <div className="flex flex-col mt-5 space-y-5 min-w-[300px] w-64">
        <div>
          <Label>Kanal</Label>
          <Select value={channel} onValueChange={handleChannelChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Kanal Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="atTheDoor">Kapıda</SelectItem>
              <SelectItem value="telephone">Telefon</SelectItem>
              <SelectItem value="whatsapp">Whatsapp</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Ödeme Tipi</Label>
          <Select value={paymentType} onValueChange={handlePaymentTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Ödeme Tipi Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cash">Nakit</SelectItem>
              <SelectItem value="creditCard">Kredi Kartı</SelectItem>
              <SelectItem value="transfer">Havale</SelectItem>
              <SelectItem value="online">Online</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default PaymentStep;
