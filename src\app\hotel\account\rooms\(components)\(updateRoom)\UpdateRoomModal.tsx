"use client";
import type { FC, KeyboardEvent, ChangeEvent } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import IconEdit from "@/shared/icons/Edit";
import { useUpdateRoom } from "@/hooks/hotel/rooms/useUpdateRoom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import UpdateRoomPhotos from "./UpdateRoomPhotos";
import UpdateRoomInputs from "./UpdateRoomInputs";
import type {
  updateRoomInputsTypes,
  UpdateRoomModalProps,
} from "@/types/hotel/rooms/updateRoomTypes";
import { PET_TYPES } from "@/app/(enums)/enums";

const UpdateRoomModal: FC<UpdateRoomModalProps> = ({
  hotel,
  hotelToken,
  roomGroup,
  acceptedPetTypes,
}) => {
  const translate = useTranslations("UpdateAndCreateNewRoomModal");
  const { updatePhotoInputHandler } = useUpdateRoom();
  const initialInputData = {
    roomGroupName: roomGroup.roomGroupName,
    roomCapacity: roomGroup.rooms[0].roomCapacity.toString(),
    petType: roomGroup.petType,
    roomImages: roomGroup.images,
    roomDescription: roomGroup.rooms[0].roomDescription,
    roomFeatures: roomGroup.rooms[0].roomFeatures,
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [updateRoomsIsOpen, setUpdateRoomsIsOpen] = useState(false);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | undefined>(
    undefined
  );
  const [updateRoomInputs, setUpdateRoomInputs] =
    useState<updateRoomInputsTypes>(initialInputData);
  const [roomFeatureNames, setRoomFeatureNames] = useState<string>("");
  const [newImage, setNewImage] = useState<any>([]);

  const resetInputs = () => {
    setUpdateRoomInputs(initialInputData);
    setRoomFeatureNames("");
    setUpdateRoomsIsOpen(false);
    setNewImage([]);
    setPhotoFileObject(undefined);
  };

  // room inputs handle
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setUpdateRoomInputs((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const roomFeaturesNameHandler = (event: ChangeEvent<HTMLInputElement>) => {
    setRoomFeatureNames(event.target.value);
  };

  const hotelFeaturesRemoveHandler = (index: number) => {
    setUpdateRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: prevState.roomFeatures.filter(
        (_, itemIndex) => itemIndex !== index
      ),
    }));
  };

  const roomFeaturesHandler = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key !== "Enter") {
      return;
    }
    if (roomFeatureNames.trim() === "") {
      setRoomFeatureNames("");
      return;
    }
    setUpdateRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: [...prevState.roomFeatures, roomFeatureNames.trim()],
    }));
    setRoomFeatureNames("");
  };

  const handleRoomFeatures = () => {
    if (roomFeatureNames.trim() === "") {
      setRoomFeatureNames("");
      return;
    }
    setUpdateRoomInputs((prevState) => ({
      ...prevState,
      roomFeatures: [...prevState.roomFeatures, roomFeatureNames.trim()],
    }));
    setRoomFeatureNames("");
  };

  // textarea handle
  const textAreaHandleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setUpdateRoomInputs((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handlePetTypeCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { id, checked } = e.target;
    const petTypeId = id as keyof typeof PET_TYPES;

    setUpdateRoomInputs((prevState) => {
      if (checked) {
        return {
          ...prevState,
          petType: [...prevState.petType, petTypeId],
        };
      } else {
        return {
          ...prevState,
          petType: prevState.petType.filter((petType) => petType !== petTypeId),
        };
      }
    });
  };

  const isEqualWithoutImages = (
    obj1: updateRoomInputsTypes,
    obj2: updateRoomInputsTypes
  ) => {
    const { roomImages: _, ...restObj1 } = obj1;
    const { roomImages: __, ...restObj2 } = obj2;

    return JSON.stringify(restObj1) === JSON.stringify(restObj2);
  };
  const isEqual = isEqualWithoutImages(initialInputData, updateRoomInputs);

  return (
    <>
      <IconEdit
        onClick={() => setUpdateRoomsIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />
      <Dialog open={updateRoomsIsOpen}>
        <DialogContent
          onInteractOutside={resetInputs}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>{translate("roomUpdate")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable">
            <UpdateRoomInputs
              updateRoomInputs={updateRoomInputs}
              roomFeatureNames={roomFeatureNames}
              handlePetTypeCheckboxChange={handlePetTypeCheckboxChange}
              handleChange={handleChange}
              hotelFeaturesRemoveHandler={hotelFeaturesRemoveHandler}
              roomFeaturesHandler={roomFeaturesHandler}
              roomFeaturesNameHandler={roomFeaturesNameHandler}
              textAreaHandleChange={textAreaHandleChange}
              handleRoomFeatures={handleRoomFeatures}
              acceptedPetTypes={acceptedPetTypes}
            />
            <UpdateRoomPhotos
              loading={loading}
              setPhotoFileObject={setPhotoFileObject}
              initialInputData={initialInputData}
              newImage={newImage}
              setNewImage={setNewImage}
              hotelToken={hotelToken}
            />
          </div>
          <div className="mt-7 flex justify-end gap-5">
            <Button variant="outline" type="button" onClick={resetInputs}>
              {translate("cancel")}
            </Button>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={() => {
                updatePhotoInputHandler(
                  hotel._id,
                  hotelToken,
                  photoFileObject,
                  updateRoomInputs,
                  roomGroup._id,
                  setUpdateRoomsIsOpen,
                  setLoading,
                  setNewImage,
                  setDisabled,
                  setPhotoFileObject
                );
              }}
              disabled={disabled || (isEqual && newImage.length === 0)}
              type="button"
            >
              {loading ? <LoadingSpinner /> : translate("save")}
            </Button>
          </div>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateRoomModal;
