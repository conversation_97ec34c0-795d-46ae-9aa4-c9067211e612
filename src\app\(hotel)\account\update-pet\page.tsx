import React from "react";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getHotelPetByOwner } from "@/actions/(protected)/hotel/getHotelPetByOwner";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import UpdatePetContainer from "./(components)/UpdatePetContainer";
import type { PetInformationApiTypes } from "@/types/petOwner/petTypes";

const UpdatePetPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelCustomerId = searchParams.hotelCustomerId;
  const hotelPetByOwner = await getHotelPetByOwner(hotelCustomerId as string);
  const hotelData = await getMyHotel();
  const selectedPet = hotelPetByOwner?.data?.hotelPets?.find(
    (pet: PetInformationApiTypes) => pet._id === searchParams.petId
  );

  if (!hotelToken || !selectedPet || !hotelCustomerId) {
    redirect("/");
  }

  return (
    <div className="bg-neutral-50 dark:bg-neutral-900 min-h-screen">
      <div className="container pt-5 pb-8">
        <h1 className="text-lg font-semibold text-center">
          Evcil Hayvan Güncelleme
        </h1>
        <UpdatePetContainer
          hotelToken={hotelToken}
          selectedPet={selectedPet}
          hotelData={hotelData?.data}
          hotelCustomerId={hotelCustomerId}
        />
      </div>
    </div>
  );
};

export default UpdatePetPage;
