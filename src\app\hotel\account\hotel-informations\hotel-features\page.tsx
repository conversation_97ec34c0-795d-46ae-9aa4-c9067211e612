import React from "react";
import HotelFeatures from "../(components)/(hotelFeatures)/HotelFeatures";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";

const HotelFeaturesPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  return (
    <>
      {hotelData && (
        <HotelFeatures hotelData={hotelData?.data} hotelToken={hotelToken} />
      )}
    </>
  );
};

export default HotelFeaturesPage;
