import React from "react";
import { cookies } from "next/headers";
import getCancellationPolicyInformationsTaxi from "@/actions/(protected)/policy/getCancellationPolicyInformationTaxi";
import Policy from "./(components)/Policy";

const CancellationPoliciesPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const cancellationPolicyInformationsTaxiData =
    await getCancellationPolicyInformationsTaxi();

  return (
    <div className="container">
      <Policy
        cancellationPolicyInformationsTaxiData={
          cancellationPolicyInformationsTaxiData?.data
        }
        petTaxiToken={petTaxiToken}
      />
    </div>
  );
};

export default CancellationPoliciesPage;
