"use client";
import { CalendarIcon, FileTextIcon } from "@radix-ui/react-icons";
import { BellIcon, Share2Icon } from "lucide-react";

import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import { useTranslations } from "next-intl";

const files = [
  {
    name: "bitcoin.pdf",
    body: "Bitcoin is a cryptocurrency invented in 2008 by an unknown person or group of people using the name <PERSON><PERSON>.",
  },
  {
    name: "finances.xlsx",
    body: "A spreadsheet or worksheet is a file made of rows and columns that help sort data, arrange data easily, and calculate numerical data.",
  },
  {
    name: "logo.svg",
    body: "Scalable Vector Graphics is an Extensible Markup Language-based vector image format for two-dimensional graphics with support for interactivity and animation.",
  },
  {
    name: "keys.gpg",
    body: "GPG keys are used to encrypt and decrypt email, files, directories, and whole disk partitions and to authenticate messages.",
  },
  {
    name: "seed.txt",
    body: "A seed phrase, seed recovery phrase or backup seed phrase is a list of words which store all the information needed to recover Bitcoin funds on-chain.",
  },
];

export function Grid() {
  const translate = useTranslations("LandingGrid");

  const features = [
    {
      Icon: FileTextIcon,
      name: translate("reservationManagementName"),
      description: translate("reservationManagementDescription"),
      className: "col-span-3 lg:col-span-2",
    },
    {
      Icon: BellIcon,
      name: translate("createListingsName"),
      description: translate("createListingsDescription"),
      className: "col-span-3 lg:col-span-1",
    },
    {
      Icon: Share2Icon,
      name: translate("guestRelationsName"),
      description: translate("guestRelationsDescription"),
      className: "col-span-3 lg:col-span-1",
    },
    {
      Icon: CalendarIcon,
      name: translate("calendarName"),
      description: translate("calendarDescription"),
      className: "col-span-3 lg:col-span-2",
    },
  ];

  return (
    <BentoGrid>
      {features.map((feature, idx) => (
        <BentoCard key={idx} {...feature} />
      ))}
    </BentoGrid>
  );
}
