import type { ButtonHTMLAttributes } from "react";
import React from "react";

export interface ButtonCircleProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  size?: string;
}

const ButtonCircle: React.FC<ButtonCircleProps> = ({
  className = " ",
  size = " w-9 h-9 ",
  ...args
}) => {
  return (
    <button
      className={`ttnc-ButtonCircle flex items-center justify-center rounded-full bg-secondary-6000 !leading-none text-neutral-50 hover:bg-secondary-700 disabled:bg-opacity-70 ${className} ${size} `}
      {...args}
    />
  );
};

export default ButtonCircle;
