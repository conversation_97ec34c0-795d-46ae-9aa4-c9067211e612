import React, { ChangeEvent, FC } from "react";
import IconUpload from "@/shared/icons/Upload";
import type { VehicleDataTypes } from "@/types/taxi/vehicleType";
import DeleteFileModal from "./DeleteFileModal";

interface MultipleUserFilesProps {
  name: string;
  files: File[];
  setFiles: (files: File[]) => void;
  vehicleData?: VehicleDataTypes;
  docType?: string;
  petTaxiToken?: string | undefined;
}

const MultipleUserFiles: FC<MultipleUserFilesProps> = ({
  name,
  files,
  setFiles,
  vehicleData,
  docType,
  petTaxiToken,
}) => {
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files
      ? Array.from(event.target.files)
      : [];
    setFiles(selectedFiles);
    event.target.value = "";
  };

  const filteredFiles = vehicleData?.documents?.filter(
    (file: any) => file?.doctype === docType
  );

  const sortedData = filteredFiles?.sort(
    (a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const renderSortedFileNames = () => {
    return sortedData?.map((file: any, index: number) => (
      <div
        key={index}
        className="relative flex items-center border-b border-neutral-300 pb-1"
      >
        <a
          className="hover:underline max-md:w-60"
          href={file?.src}
          target="_blank"
        >
          <span className="text-sm max-md:text-xs">{file?.originalname}</span>
        </a>
        {file?._id && (
          <DeleteFileModal fileId={file?._id} petTaxiToken={petTaxiToken} />
        )}
      </div>
    ));
  };

  return (
    <div className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-5">
      <div className="w-full">
        <div className="mt-5 mb-2 text-xs font-medium capitalize text-neutral-700 dark:text-neutral-300">
          {name}
        </div>
        <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
          <div className="flex flex-col items-center space-y-1 text-center">
            <div className="text-sm text-neutral-6000 dark:text-neutral-300">
              <label className="group relative cursor-pointer rounded-md font-medium text-secondary-6000">
                <div className="mb-1 flex justify-center">
                  <IconUpload className="size-6 text-secondary-6000 group-hover:text-primary-700" />
                </div>
                <span className="group-hover:text-primary-700 group-hover:underline">
                  {name}
                </span>
                <input
                  type="file"
                  className="sr-only"
                  accept="image/jpeg, image/png, application/pdf"
                  multiple={true}
                  onChange={handleFileChange}
                />
              </label>
            </div>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              PDF or Image
            </p>
            <div className="mt-4 font-semibold">
              {files.length > 0 && (
                <div className="mt-2 flex items-center">
                  {files.map((file, i) => (
                    <div key={i} className="text-sm max-md:w-60 max-md:text-xs">
                      {file.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="mt-4 space-y-3">{renderSortedFileNames()}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultipleUserFiles;
