import type { FormEvent } from "react";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";

export const useSubscriptionPayment = () => {
  const { toast } = useToast();

  const setPrimaryCard = async (
    hotelToken: string | undefined,
    targetGUID: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (!hotelToken) return null;

    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.setPrimaryCard, {
        method: "PUT",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          targetGUID: targetGUID,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata olu<PERSON>tu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const removeSavedCard = async (
    hotelToken: string | undefined,
    ksGuid: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (!hotelToken) return null;

    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.removeSavedCard, {
        method: "PUT",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ksGuid: ksGuid,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const unsubscribeHotel = async (
    event: FormEvent,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (!hotelToken) return null;

    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.unsubscribeHotel, {
        method: "POST",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        closeModal();
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 6000,
        title: "Üyeliği İptal Etme",
        description: "Üyeliği başarıyla iptal ettiniz.",
      });
      closeModal();
      setTimeout(() => {
        window.location.reload();
      }, 1500);
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return { setPrimaryCard, removeSavedCard, unsubscribeHotel };
};
