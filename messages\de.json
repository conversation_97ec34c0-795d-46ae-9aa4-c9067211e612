{"Page": {"description": "<p><PERSON><PERSON> ist ein Fehler aufgetreten.</p><p><PERSON><PERSON> können versuchen, die <retry>Seite neu zu laden</retry>, die Sie besucht haben.</p>", "title": "Etwas ist schief gelaufen!"}, "Layout": {"description": "Dies ist ein ein<PERSON><PERSON> Beispiel, das die Verwendung von <code>next-intl</code> mit dem Next.js App Router demonstriert. Versuchen Sie, die Sprache in der oberen rechten Ecke zu ändern und sehen <PERSON>, wie sich der Inhalt ändert.", "title": "next-intl Beispiel"}, "Component": {"title": "next-intl Beispiel"}, "LangDropdown": {"language": "<PERSON><PERSON><PERSON>"}, "TodayContainer": {"totalReservation": "Gesamtreservierung", "totalDailyRate": "Gesamt Tagesrate", "totalGuest": "Gesamtgäste", "cancelReservation": "Reservierung stornieren"}, "TodayRoomCard": {"closeAll": "Alle schließen", "openAll": "Alle öffnen"}, "LoginPage": {"login": "Anmelden", "hotelLogin": "Hotelanmeldung", "loginUsername": "Benutzername / E-Mail", "loginPassword": "Passwort", "loginForgotPassword": "Passwort vergessen?", "loginLogin": "Anmelden", "loginNewuser": "<PERSON><PERSON><PERSON>?", "loginCreateaccount": "<PERSON><PERSON> er<PERSON>", "loginWelcome": "<PERSON><PERSON><PERSON><PERSON>"}, "CreateUserContainer": {"signup": "Registrieren", "login": "Anmelden", "name": "Name", "validName": "<PERSON>te geben Si<PERSON> einen gültigen Namen ein", "surname": "Nachname", "validSurName": "Bitte geben Si<PERSON> einen gültigen Nachnamen ein", "email": "E-Mail", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "dateOfBirth": "Geburtsdatum", "username": "<PERSON><PERSON><PERSON><PERSON>", "validUsername": "Ihr Benutzername muss mindestens 4 Zeichen lang sein und darf nur Kleinbuchstaben (a-z) und Zahlen (0-9) enthalten. Sonderzeichen oder Leerzeichen sind nicht erlaubt.", "password": "Passwort", "validPassword": "Ihr Passwort muss mindestens 8 Zeichen lang sein und einen Großbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten. Sonderzeichen (!@#$%^&*()_+.-) sind erlaubt, aber Leerzeichen nicht.", "passwordConfirm": "Passwort bestätigen", "validPasswordConfirm": "Das Passwort und die Bestätigung stimmen nicht überein", "phoneNumber": "Telefonnummer", "phoneNumberInfo": "Geben Sie Ihre Telefonnummer mit einer führenden 0 ein", "validPhoneNumber": "Ihre Telefonnummer muss mit 0 beginnen und darf keine Leerzeichen, Buchstaben oder Sonderzeichen enthalten.", "genderMale": "<PERSON><PERSON><PERSON><PERSON>", "genderFemale": "<PERSON><PERSON><PERSON>", "genderOther": "<PERSON>ch möchte keine <PERSON> machen", "account": "Haben <PERSON> ein Konto?"}, "VerifyAccountContainer": {"accountVerification": "Kontoüberprüfung", "verificationCode": "Bitte geben Si<PERSON> den an Ihre E-Mail gesendeten Bestätigungscode ein!", "confirm": "Bestätigen"}, "ForgotPasswordContainer": {"forgotPassword": "Passwort vergessen", "email": "E-Mail", "emailDesc": "Bitte geben Sie die E-Mail-Adresse ein, mit der Sie registriert sind", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "send": "Senden", "sendMessage": "Der Link zum Zurücksetzen des Passworts wurde an Ihre registrierte E-Mail-Adresse gesendet.<br /> Bitte überprüfen Sie Ihre E-Mail.", "returnHomepage": "Zur Startseite zurückkehren"}, "ResetPasswordContainer": {"changePassword": "Passwort ändern", "show": "Anzeigen", "hide": "Ausblenden", "newPassword": "Neues Passwort", "newPasswordValid": "Ihr Passwort muss mindestens 8 Zeichen lang sein und einen Großbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten. Sonderzeichen (!@#$%^&*()_+.) sind erlaubt, aber Leerzeichen nicht.", "newPasswordConfirm": "Neues Passwort bestätigen", "newValidPasswordConfirm": "Das neue Passwort und die Bestätigung stimmen nicht überein", "save": "Speichern"}, "AuthHeader": {"back": "Zurück", "approve": "<PERSON>n <PERSON> zurückgehen, werden Ihre Informationen zurückgesetzt. Bestätigen Sie das?"}, "HotelEmailValidation": {"emailVerification": "E-Mail-Bestätigung", "emailAdress": "<PERSON>te geben Sie Ihre E-Mail-Adresse ein", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "send": "Senden", "emailCode": "Geben Sie den an Ihre E-Mail-Adresse gesendeten Code ein", "confirm": "Bestätigen"}, "HotelPhoneValidation": {"phoneVerification": "Telefonnummernüberprüfung", "phoneNumber": "Bitte geben Sie Ihre Telefonnummer ein", "phoneNumberInfo": "Geben Sie Ihre Telefonnummer mit einer führenden 0 ein", "validPhoneNumber": "Ihre Telefonnummer muss mit 0 beginnen und darf keine Leerzeichen, Buchstaben oder Sonderzeichen enthalten.", "send": "Senden", "phoneCode": "Geben Sie den an Ihr Telefon gesendeten Code ein", "confirm": "Bestätigen", "confirmSendCode": "Möchten Sie den Code senden?", "change": "Ändern"}, "CreateHotelContainer": {"send": "Senden", "next": "<PERSON><PERSON>", "previous": "Zurück"}, "HotelFirstStep": {"hotelOwnerInfo": "Hotelbesitzerinformationen", "name": "Name", "validName": "<PERSON>te geben Si<PERSON> einen gültigen Namen ein", "surname": "Nachname", "validSurName": "Bitte geben Si<PERSON> einen gültigen Nachnamen ein", "dateOfBirth": "Geburtsdatum", "username": "<PERSON><PERSON><PERSON><PERSON>", "validUsername": "Ihr Benutzername muss mindestens 4 Zeichen lang sein und darf nur Kleinbuchstaben (a-z) und Zahlen (0-9) enthalten. Sonderzeichen oder Leerzeichen sind nicht erlaubt.", "password": "Passwort", "validPassword": "Ihr Passwort muss mindestens 8 Zeichen lang sein und einen Großbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten. Sonderzeichen (!@#$%^&*()_+.-) sind erlaubt, aber Leerzeichen nicht.", "passwordConfirm": "Passwort bestätigen", "validPasswordConfirm": "Das Passwort und die Bestätigung stimmen nicht überein", "gender": "Geschlecht", "genderMale": "<PERSON><PERSON><PERSON><PERSON>", "genderFemale": "<PERSON><PERSON><PERSON>", "genderOther": "<PERSON>ch möchte keine <PERSON> machen"}, "HotelSecondStep": {"hotelInfo": "Hotelinformationen", "hotelName": "Hotelname *", "hotelAddress": "<PERSON><PERSON><PERSON>", "hotelCity": "Stadt *", "hotelDistrict": "Bezirk *", "hotelNeighbourhood": "Nachbarschaft *", "hotelNeighbourhoodDesc": "Die Nachbarschaft, in der sich Ihr Hotel befindet", "hotelStreet": "Straße *", "hotelStreetDesc": "Die Straße, in der sich Ihr Hotel befindet", "hotelBuildingName": "Gebäudename *", "hotelBuildingNameDesc": "<PERSON> Gebäude, in dem sich Ihr Hotel befindet", "hotelBuildingNumber": "Hausnummer *", "hotelBuildingNumberDesc": "Die Hausnummer, in der sich Ihr Hotel befindet", "hotelPostalCode": "<PERSON><PERSON><PERSON><PERSON> *", "hotelPostalCodeDesc": "Die Postleitzahl Ihres Hotels", "hotelCompanyInfo": "Unternehmensinformationen", "hotelCompanyName": "Unternehmensname *", "hotelCompanyNameDesc": "<PERSON><PERSON> Unternehmensname", "hotelCompanyTaxNumber": "Steuernummer *", "hotelCompanyTaxNumberDesc": "Die Steuernummer Ihres Hotels", "hotelCompanyTaxOffice": "Finanzamt *", "hotelCompanyTaxOfficeDesc": "Das Finanzamt, bei dem Ihr Hotel registriert ist", "hotelPhoneNumber": "Hoteltelefonnummer", "hotelPhoneNumberDesc": "Geben Sie die Telefonnummer Ihres Hotels ein"}, "FileSection": {"hotelUploadHotelLicense": "Hotelgenehmigung hochladen", "hotelUploadHotelPhoto": "Hotelprofilfoto hochladen"}, "HotelThirdStep": {"hotelFeatures": "Hotelausstattung", "petTaxi": "Haustier-Taxi", "petCoiffeur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petNursery": "Kinderkrippe", "pool": "Schwimmbad", "playground": "Spielplatz", "dogWalking": "Hundespaziergang", "hotelAcceptedPetTypes": "Akzeptierte Haustierarten", "cat": "<PERSON><PERSON>", "dog": "<PERSON>nd", "horse": "<PERSON><PERSON>d", "turtle": "Schildkröte", "rabbit": "<PERSON><PERSON><PERSON>", "hotelAdditionalServices": "Zusätzliche Dienstleistungen", "veterinary": "Tierarzt", "specialNutrition": "Spezielle Ernährung", "grooming": "Pflege", "camera": "<PERSON><PERSON><PERSON>", "photoInfoServices": "Foto-Informationsdienst", "specialCondition": "Tiere mit besonderen Bedürfnissen"}, "SuccessPage": {"success": "Ihre Hotelregistrierung wurde erfolgreich abgeschlossen", "returnHomepage": "<PERSON>e können sich anmelden oder zur Startseite zurückkehren", "homepage": "Startseite", "hotelLogin": "Hotelanmeldung"}, "Step4ImagesUpload": {"hotelImages": "Hotelbilder"}, "Nav": {"navToday": "<PERSON><PERSON>", "navHotelİnformations": "Hotelinformationen", "navRooms": "<PERSON><PERSON>", "navCalendar": "<PERSON><PERSON><PERSON>", "navDashboard": "Dashboard", "navReservations": "Reservierungen", "navPromoCode": "Aktionscode", "navBilling": "Rechnungsinformationen", "navUser": "Benutzerinformationen", "navHotelLanding": "Hotel-Website", "navPolicy": "Richtlinien und Vereinbarungen"}, "UnloggedHeader": {"headerPetOwner": "Haben <PERSON> ein <PERSON>?", "headerHotelOwner": "Besitzen Sie ein Hotel?", "headerCreateHotel": "Hotel erstellen", "headerLogin": "Anmelden", "headerCreateUser": "<PERSON><PERSON><PERSON> er<PERSON>"}, "PetOwnerAvatarDropdown": {"myAccount": "<PERSON><PERSON>", "myFavorites": "<PERSON><PERSON>", "myPets": "<PERSON><PERSON>", "darkTheme": "<PERSON><PERSON><PERSON>a", "logout": "Abmelden"}, "HotelAvatarDropdown": {"myAccount": "<PERSON><PERSON>", "darkTheme": "<PERSON><PERSON><PERSON>a", "logout": "Abmelden"}, "NavMobile": {"myAccount": "<PERSON><PERSON>", "logout": "Abmelden", "createAccount": "<PERSON><PERSON> er<PERSON>", "login": "Anmelden"}, "NavMobileHotel": {"myAccount": "<PERSON><PERSON>", "logout": "Abmelden"}, "HotelInformationsNav": {"hotelInfo": "Hotelinformationen", "hotelAddress": "Hoteladresse", "hotelFeatures": "Hotelausstattung", "hotelAcceptedPetTypes": "Akzeptierte Haustierarten", "hotelAdditionalServices": "Zusätzliche Dienstleistungen", "hotelPhotos": "Hotelbilder", "hotelDocuments": "Hoteldokumente"}, "HotelInformations": {"hotelName": "Hotelname", "hotelCompanyTaxNumber": "Steuern<PERSON>mer", "hotelCompanyTaxOffice": "Finanzamt", "hotelLogo": "Hotel-Logo", "save": "Speichern", "hotelUploadPhoto": "Foto hochladen", "hotelTypeImage": "PNG, JPG, GIF Bildtyp"}, "HotelAddress": {"cityName": "Stadt", "region": "Bezirk", "district": "Nachbarschaft", "streetName": "Straße", "buildingName": "Gebäudename", "buildingNumber": "Hausnummer", "postalZone": "<PERSON><PERSON><PERSON><PERSON>", "save": "Speichern"}, "HotelFeatures": {"hotelFeatures": "Hotelausstattung", "petTaxi": "Haustier-Taxi", "petCoiffeur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petGrooming": "<PERSON><PERSON><PERSON>pf<PERSON><PERSON>", "petNursery": "Kinderkrippe", "pool": "Schwimmbad", "playground": "Spielplatz", "dogWalking": "Hundespaziergang", "save": "Speichern", "FeaturesCategories": {"healthAndSafety": "Gesundheit und Sicherheit", "comfortAndAccommodation": "Komfort und Unterkunft", "activitiesAndEntertainment": "Aktivitäten und Unterhaltung", "extraServices": "Zusätzliche Dienstleistungen"}, "FeaturesLabels": {"veterinaryServices": "24/7 Tierarztservice", "cameraSurveillance": "Kamerasystem", "securityCameras": "Sicherheitskameras", "emergencyEquipment": "Notfallausrüstung", "hygienicToiletAreas": "Hygienische Toilettenbereiche", "climateControl": "K<PERSON>aanlagen", "individualRooms": "Einzelzimmer", "comfortableBeds": "<PERSON><PERSON> und bequeme <PERSON>", "indoorShelters": "Innenschutzhütten", "outdoorShelters": "Außenschutzhütten", "playAreas": "Spielbereiche", "indoorPlayAreas": "Innenspielbereiche", "gardenArea": "Gartenbereich", "trainingField": "<PERSON>sfeld", "playPool": "Spielpool", "socializationActivities": "Sozialisierungsaktivitäten", "liveCameraAccess": "Live-Kamera-Zugriff", "photoUpdates": "Foto-Updates", "specialDietMeals": "Spezielle Diätmahlzeiten", "petSpa": "Haustier-Spa und Pflegedienste", "pickupDropoff": "Abhol- und Bringdienst"}}, "HotelPetTypes": {"hotelAcceptedPetTypes": "Akzeptierte Haustierarten", "cat": "<PERSON><PERSON>", "dog": "<PERSON>nd", "smallDogBreed": "Kleine Hu<PERSON>asse", "largeDogBreed": "Große Hunderasse", "rabbit": "<PERSON><PERSON><PERSON>", "hamster": "<PERSON><PERSON>", "guineaPig": "Meerschweinchen", "ferret": "<PERSON><PERSON><PERSON>", "hedgehog": "<PERSON><PERSON>", "chinchilla": "<PERSON><PERSON><PERSON>", "turtle": "Schildkröte", "snake": "Schlang<PERSON>", "iguana": "Echs<PERSON>", "parrot": "<PERSON><PERSON><PERSON>", "canary": "<PERSON><PERSON><PERSON>", "budgerigar": "<PERSON><PERSON><PERSON><PERSON>", "cockatiel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fish": "<PERSON><PERSON>", "freshwaterFish": "Süßwasserfisch", "saltwaterFish": "Salzwasserfisch", "horse": "<PERSON><PERSON>d", "pony": "Pony", "donkey": "<PERSON><PERSON>", "goat": "Ziege", "sheep": "<PERSON><PERSON><PERSON>", "alpaca": "Alpaka", "llama": "<PERSON>", "exoticBird": "Exotischer Vogel", "monkey": "<PERSON><PERSON>", "rooster": "<PERSON>", "other": "<PERSON><PERSON>", "save": "Speichern"}, "HotelAdditionalServices": {"hotelAdditionalServices": "Zusätzliche Dienstleistungen", "veterinary": "Tierarzt", "specialNutrition": "Spezielle Ernährung", "grooming": "Pflege", "camera": "<PERSON><PERSON><PERSON>", "photoInfoServices": "Foto-Informationsdienst", "specialCondition": "Tiere mit besonderen Bedürfnissen", "save": "Speichern"}, "HotelPhotosContainer": {"hotelUploadPhoto": "Foto hochladen", "save": "Speichern"}, "HotelPhotoCard": {"hotelRemovePhoto": "Foto entfernen", "hotelDeletePhoto": "Möchten Sie das ausgewählte Foto löschen?", "hotelCancel": "Abbrechen", "hotelDelete": "Löschen"}, "PostingContainer": {"PostingContainer": "<PERSON>s gibt keine zuvor erstellte Zimmergruppe. Bitte erstellen Sie eine Zimmergruppe."}, "HotelRoomCard": {"hotelRoomCount": "<PERSON><PERSON><PERSON>:", "roomCapacity": "Zimmerkapazität:", "petType": "Haustiertyp:"}, "UpdateAndCreateNewRoomModal": {"hotelAddNewRoomGroup": "Neue Zimmergruppe hinzufügen", "hotelAddMultipleRooms": "<PERSON><PERSON><PERSON> Zimmer hinzufügen", "hotelRoomName": "Zimmername", "hotelRoomCapacity": "Zimmerkapazität", "hotelRoomGroupName": "Zimmergruppenname", "hotelNumberOfRooms": "<PERSON><PERSON><PERSON>", "hotelRoomNameStartingNumber": "Zimmername Startnummer", "petType": "Haustierart", "roomFeatures": "Zimmermerkmale", "roomFeaturesDesc": "Nachdem Sie Ihre Zimmermerkmale eingegeben haben, drücken Sie die Eingabetaste oder die Hinzufügen-Schaltfläche.", "add": "Hinzufügen", "roomInformation": "Bereich für Zimmerinformationen", "roomInformationDesc": "Bitte geben Si<PERSON> eine kurze Beschreibung des Zimmers an", "hotelPhotos": "Hotelbilder", "hotelUploadPhoto": "Foto hochladen", "save": "Speichern", "cancel": "Abbrechen", "roomUpdate": "Zimmeraktualisierung", "cat": "<PERSON><PERSON>", "dog": "<PERSON>nd", "roomType": "Zimmertyp", "standard": "Standard", "private": "Privat", "shared": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "DeleteRoomModal": {"RemoveRoom": "<PERSON><PERSON>", "RemoveRoomConfirm": "Möchten Sie das Zimmer entfernen?", "cancel": "Abbrechen", "confirm": "Bestätigen"}, "HotelCalendarContainer": {"roomGroups": "Zimmergruppen", "roomGroupName": "Gruppenname", "NumberOfRooms": "<PERSON><PERSON><PERSON>", "select": "Auswählen"}, "HotelCalendarRange": {"selectRoom": "<PERSON><PERSON> au<PERSON>wählen", "activeDays": "Aktive Tage", "passiveDays": "Passive Tage", "reservedDays": "Reserved Tage", "noPriceDays": "Tage ohne Preisangabe", "locale": "de"}, "HotelCalendarSideBarRange": {"roomCapacity": "Tägliche Zimmerkapazität", "petType": "Haustierart", "roomType": "Zimmertyp", "clearSelection": "Auswahl löschen", "closetoReservations": "Nahe an Reservierungen", "opentoReservations": "F<PERSON>r Reservierungen öffnen", "locale": "de-DE", "changePrice": "<PERSON><PERSON><PERSON>, um den Preis zu ä<PERSON>n", "totalPrice": "Gesamtbetrag, den der Gast zahlen wird", "clickForDetails": "Klicken Sie für Details"}, "PriceSummary": {"priceBreakdown": "Preisaufstellung", "night": "<PERSON><PERSON>", "nightlyRate": "Ihr durchschnittlicher Nachtpreis", "serviceFee": "Servicegebühr", "totalPrice": "Gesamtbetrag, den der Gast zahlen wird", "yourEarnings": "<PERSON><PERSON><PERSON>"}, "UpdateRoomPrice": {"priceUpdate": "Preisaktualisierung", "newRoomRate": "Geben Sie den neuen Zimmerpreis ein", "price": "Preis:", "serviceFee": "Servicegebühr:", "totalPrice": "Gesamtbetrag, den der Gast zahlen wird:", "yourEarnings": "<PERSON><PERSON><PERSON>:", "cancel": "Abbrechen", "save": "Speichern"}, "HotelCalendarDrawerRangeMobile": {"dateUpdate": "Datumsaktualisierung", "closetoReservations": "Nahe an Reservierungen", "opentoReservations": "F<PERSON>r Reservierungen öffnen", "locale": "de-DE", "changePrice": "<PERSON><PERSON><PERSON>, um den Preis zu ä<PERSON>n", "totalPrice": "Gesamtbetrag, den der Gast zahlen wird", "clickForDetails": "Klicken Sie für Details", "close": "Schließen", "back": "Zurück"}, "ReservationList": {"roomName": "Zimmername", "petOwner": "Haustierbesitzer", "petName": "Haustiername", "startDate": "Check-in-Datum", "endDate": "Check-out-<PERSON><PERSON>", "channel": "<PERSON><PERSON>", "date": "Datum", "total": "Gesamt", "status": "Status", "action": "Aktion", "confirmed": "Bestätigt", "booked": "Gebucht", "checkedIn": "Eingescheckt", "declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "waitingForBook": "<PERSON><PERSON>", "confirm": "Bestätigen", "reject": "<PERSON><PERSON><PERSON><PERSON>", "reservationNotFound": "Reservierung nicht gefunden.", "showHideColumns": "Spalten anzeigen/ausblenden"}, "BillingContainer": {"companyType": "Unternehmensart *", "soleProprietorship": "Einzelunternehmen", "stockCompany": "Aktiengesellschaft", "fullName": "Name Nachname *", "alias": "Unternehmensbezeichnung *", "identityNumber": "Identitätsnummer", "birthDate": "Geburtsdatum", "gsmNumber": "Telefonnummer *", "address": "<PERSON><PERSON><PERSON> *", "city": "Stadt *", "district": "Bezirk *", "selectCity": "Stadt auswählen", "selectDistrict": "Bezirk auswählen", "email": "E-Mail *", "authorizedPersonIdentityNumber": "Identitätsnummer der autorisierten Person", "authorizedPersonBirthDate": "Geburtsdatum der autorisierten Person", "taxOffice": "Finanzamt", "save": "Speichern", "ibanTitle": "IBAN-Titel"}, "ReservationActionButtons": {"reservationConfirmation": "Reservierungsbestätigung", "reservationConfirmationDesc": "Möchten Sie die ausgewählte Reservierung bestätigen?", "confirm": "Bestätigen", "reservationCancellation": "Reservierungsstornierung", "reservationCancellationDesc": "Möchten Sie die ausgewählte Reservierung stornieren?", "cancel": "Abbrechen"}, "ReservationDetail": {"details": "Detail", "reservationDetails": "Reservierungsdetails", "roomInformation": "Zimmerinformationen", "roomGroupName": "Zimmergruppenname", "roomName": "Zimmername", "roomCapacity": "Zimmerkapazität", "petType": "Haustierart", "accommodationInformation": "Unterkunftsinformationen", "checkInDate": "Check-in-Datum", "checkOutDate": "Check-out-<PERSON><PERSON>", "stayDuration": "Aufenthaltsdauer", "petInformation": "Haustierinformationen", "petName": "Haustiername", "petAge": "Haust<PERSON>lter", "petKind": "Haustierart", "petBreed": "Haustier-Rasse", "petOwnerInformation": "Haustierbesitzerinformationen", "petOwnerName": "Name des Haustierbesitzers", "phone": "Telefon", "email": "E-Mail", "night": "<PERSON><PERSON>"}, "HotelUserTable": {"noUsers": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "role": "<PERSON><PERSON>"}, "AddNewAndUpdateHotelUser": {"addUser": "Benutzer hinzufügen", "addNewUser": "Neuen Benutzer hinzufügen", "updateUser": "Benutzer aktualisieren", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "gender": "Geschlecht", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "email": "E-Mail", "dateOfBirth": "Geburtsdatum", "phone": "<PERSON><PERSON><PERSON><PERSON>", "bio": "Biografie", "validFirstName": "Bitte geben Si<PERSON> einen gültigen Vornamen ein", "validLastName": "Bitte geben Si<PERSON> einen gültigen Nachnamen ein", "validUsername": "Ihr Benutzername muss mindestens 4 Zeichen lang sein und darf nur Kleinbuchstaben (a-z) und Zahlen (0-9) enthalten. Sonderzeichen oder Leerzeichen sind nicht erlaubt.", "validPassword": "Ihr Passwort muss mindestens 8 Zeichen lang sein und mindestens einen Großbuchstaben, einen Kleinbuchstaben und eine Zahl enthalten. Sonderzeichen (!@#$%^&*()_+.-) sind erlaubt, aber keine Leerzeichen.", "validEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "validDateOfBirth": "Bitte geben Si<PERSON> ein gültiges Geburtsdatum ein", "validPhone": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "cancel": "Abbrechen", "confirm": "Bestätigen"}, "DeleteHotelUser": {"removeUserTitle": "Benutzer entfernen", "removeUserConfirmation": "Sind <PERSON> sicher, dass Sie diesen Benutzer entfernen möchten?", "cancel": "Abbrechen", "confirm": "Bestätigen"}, "Policy": {"cancellationPolicy": "Stornierungsrichtlinie", "checkInTime": "Check-in-Zeit", "checkOutTime": "Check-out-Zeit", "description": "Beschreibung", "cancellationOption": "Stornierungsoption", "rules": "Regeln", "untilDateRange": "<PERSON>is zu einem bestimmten Datum", "noCancellation": "<PERSON><PERSON>", "last24Hours": "Letzte 24 Stunden", "last48Hours": "Letzte 48 Stunden", "lastWeek": "Letzte Woche", "last10Days": "Letzte 10 Tage", "lastMonth": "Letzter Monat", "newRulesAdd": "Neue Regel hinzufügen", "save": "Speichern"}, "UpdatePolicy": {"updatePolicy": "Richtlinie aktualisieren", "updatePolicyTitle": "Richtlinie aktualisieren", "description": "Beschreibung", "rules": "Regeln", "addNewRule": "Neue Regel hinzufügen", "cancel": "Abbrechen", "save": "Speichern", "close": "Schließen"}, "CheckInOutTimePicker": {"checkInLabel": "Check-in-Zeit", "checkOutLabel": "Check-out-Zeit", "checkOutError": "Die Check-out-Zeit darf nicht vor der Check-in-Zeit liegen!"}, "AddNewRule": {"newRuleLabel": "Neue Regel", "ruleTitlePlaceholder": "Regeltitel", "ruleDescriptionPlaceholder": "Regelbeschreibung", "updateButton": "Aktualisieren", "addButton": "Hinzufügen", "cancelButton": "Abbrechen"}, "CancellationOption": {"cancellationOption": "Stornierungsoption", "select": "<PERSON><PERSON><PERSON><PERSON>", "noCancellation": "<PERSON><PERSON>", "cancelUntilDateRange": "<PERSON>is zu einem bestimmten Datum", "other1": "In offen umwandeln", "other2": "Versicherte Buchung", "other3": "Jederzeit stornieren", "other4": "Teilweise Rückerstattung", "dateRange": "Datumsbereich", "last24Hours": "Letzte 24 Stunden", "last48Hours": "Letzte 48 Stunden", "lastWeek": "Letzte Woche", "last10Days": "Letzte 10 Tage", "lastMonth": "Letzter Monat"}, "UserActionButtons": {"editUser": "<PERSON><PERSON><PERSON> bearbeiten", "deleteUser": "Benutzer löschen"}, "AcceptDeclineModal": {"cancel": "Abbrechen"}, "PaginationDropdown": {"pageSelect": "Seite auswählen"}, "ReservationPagination": {"previous": "Zurück", "next": "<PERSON><PERSON>"}, "DeleteFileModal": {"title": "<PERSON><PERSON> ent<PERSON>nen", "confirmMessage": "<PERSON><PERSON>chten Sie die ausgewählte Datei entfernen?", "confirmButton": "Bestätigen", "cancelButton": "Abbrechen"}, "HotelFiles": {"uploadButtonText": "Hochladen", "saveButtonText": "Speichern"}, "FileStep": {"hotelFiles": "Hoteldateien", "license": "<PERSON><PERSON><PERSON>", "veterinaryContract": "Veterinärvertrag"}, "HotelAccountInformation": {"accountEditInfo": "Kontoinformationen bearbeiten", "userInfo": "Benutzerinformationen", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-Mail", "phoneNumber": "Telefonnummer", "fullName": "Vollständiger Name", "role": "<PERSON><PERSON>", "hotelInfo": "Hotelinformationen", "hotelName": "Hotelname", "hotelPhoneNumber": "Hotel Telefonnummer", "taxOffice": "Finanzamt", "taxNumber": "Steuern<PERSON>mer", "currency": "Währung", "status": "Status"}}