import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type {
  CheckoutDataStates,
  CheckoutDataTypes,
} from "./checkout-data-types";

const initialState: CheckoutDataStates = {
  checkoutData: {
    hotelCustomer: null,
    pet: [],
    servicePet: [],
    transportationServiceChoice: null,
    channel: "",
    paymentType: "",
    agreementsChecked: false,
  },
};

const checkoutDataSlice = createSlice({
  name: "checkoutData",
  initialState,
  reducers: {
    setCheckoutData: (state, action: PayloadAction<CheckoutDataTypes>) => {
      state.checkoutData = action.payload;
    },
  },
});

export const { setCheckoutData } = checkoutDataSlice.actions;
export default checkoutDataSlice.reducer;
