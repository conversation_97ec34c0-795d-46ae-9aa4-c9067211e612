"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import ChatButton from "../../(components)/(today)/ChatButton";
import ImageModal from "@/components/ImageModal";
import { slugifyName } from "@/utils/formatFileName";
import GallerySlider from "@/components/GallerySlider";

const ReservationDetail = ({ reservationData }: { reservationData: any }) => {
  const [isImageOpen, setIsImageOpen] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState<string | string[]>("");
  const [imageFileName, setImageFileName] = useState("photo");
  const [sheetIsOpen, setSheetIsOpen] = useState(false);
  const translate = useTranslations("ReservationDetail");

  const documentPhotos =
    reservationData?.pet?.documentPhotos?.length > 0
      ? reservationData.pet.documentPhotos
      : reservationData?.hotelPet?.documentPhotos || [];

  const documentPhotosArray = Object.values(
    reservationData?.pet?.documentPhotos ||
      reservationData?.hotelPet?.documentPhotos ||
      {}
  ).map((photo: any) => photo?.src || photo?.img800?.src);

  return (
    <>
      <Sheet open={sheetIsOpen} onOpenChange={setSheetIsOpen}>
        <SheetTrigger className="font-medium">
          {translate("details")}
        </SheetTrigger>
        <SheetContent className="overflow-y-auto p-6 text-gray-800 dark:text-gray-200 dark:bg-gray-900">
          <SheetHeader>
            <SheetTitle className="text-xl font-bold mb-6 dark:text-white">
              📋 {translate("reservationDetails")}
            </SheetTitle>
          </SheetHeader>
          <div className="space-y-6 text-sm leading-relaxed">
            {/* Oda Bilgileri */}
            <section>
              <h3 className="text-lg font-medium mb-2 dark:text-white">
                🛏️ {translate("roomInformation")}
              </h3>
              <ul className="space-y-1">
                <li>
                  {translate("roomGroupName")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.room?.roomGroup?.roomGroupName}
                  </span>
                </li>
                <li>
                  {translate("roomName")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.room?.roomName}
                  </span>
                </li>
                <li>
                  {translate("roomCapacity")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.room?.roomCapacity}
                  </span>
                </li>
                <li>
                  {translate("petType")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {Array.isArray(reservationData?.room?.petType)
                      ? reservationData.room.petType
                          .map((type: any) => translate(type))
                          .join(", ")
                      : translate(reservationData?.room?.petType || "-")}
                  </span>
                </li>
              </ul>
            </section>

            <Separator className="dark:border-gray-700" />

            {/* Konaklama Bilgileri */}
            <section>
              <h3 className="text-lg font-medium mb-2 dark:text-white">
                📅 {translate("accommodationInformation")}
              </h3>
              <ul className="space-y-1">
                <li>
                  {translate("checkInDate")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {formatDateToDayMonthYear(reservationData?.startDate)}
                  </span>
                </li>
                <li>
                  {translate("checkOutDate")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {formatDateToDayMonthYear(reservationData?.endDate)}
                  </span>
                </li>
                <li>
                  {translate("stayDuration")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.nights} {translate("night")}
                  </span>
                </li>
              </ul>
            </section>

            <Separator className="dark:border-gray-700" />

            {/* Pet Bilgileri */}
            <section>
              <h3 className="text-lg font-medium mb-2 dark:text-white">
                🐶 {translate("petInformation")}
              </h3>
              <ul className="space-y-1">
                <li>
                  {translate("petName")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.pet?.name ||
                      reservationData?.hotelPet?.name}
                  </span>
                </li>
                <li>
                  {translate("petAge")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.pet?.age ||
                      reservationData?.hotelPet?.age}
                  </span>
                </li>
                <li>
                  {translate("petKind")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.pet?.kind
                      ? translate(reservationData.pet.kind)
                      : reservationData?.hotelPet?.kind
                        ? translate(reservationData.hotelPet.kind)
                        : "-"}
                  </span>
                </li>
                <li>
                  {translate("petBreed")}:{" "}
                  <span className="font-semibold dark:text-gray-300">
                    {reservationData?.pet?.breed ||
                      reservationData?.hotelPet?.breed}
                  </span>
                </li>
              </ul>

              {(reservationData?.pet?.images?.length > 0 ||
                reservationData?.hotelPet?.images?.length > 0) && (
                <div className="grid grid-cols-2 gap-4 mt-4">
                  {(
                    reservationData?.pet?.images ||
                    reservationData?.hotelPet?.images
                  )?.map((image: any) => (
                    <div
                      key={image._id}
                      className="bg-gray-100 p-2 shadow dark:bg-gray-800"
                    >
                      <Image
                        src={image?.img800 || image?.src}
                        width={150}
                        height={150}
                        alt="pet"
                        className="rounded-lg"
                        onClick={() => {
                          setIsImageOpen(true);
                          setImageUrl(image?.src);
                          const rawName =
                            reservationData?.pet?.name ||
                            reservationData?.hotelPet?.name ||
                            "pet";
                          const fileName = slugifyName(rawName);
                          setImageFileName(fileName);
                        }}
                      />
                      <Button
                        onClick={() => {
                          setIsImageOpen(true);
                          setImageUrl(image?.src);
                          const rawName =
                            reservationData?.pet?.name ||
                            reservationData?.hotelPet?.name ||
                            "pet";
                          const fileName = slugifyName(rawName);
                          setImageFileName(fileName);
                        }}
                        variant="outline"
                        className="w-full mt-2 text-xs"
                      >
                        {translate("showPhoto")}
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {(reservationData?.pet?.documentPhotos?.length > 0 ||
                reservationData?.hotelPet?.documentPhotos?.length > 0) && (
                <>
                  <Separator className="my-4 dark:border-gray-700" />
                  <h4 className="font-medium text-base mb-2 dark:text-white">
                    💉 {translate("vaccinationReport")}
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-100 p-2 shadow dark:bg-gray-800">
                      <GallerySlider
                        uniqueID={`StayCard2_${reservationData?._id}`}
                        ratioClass="aspect-w-12 aspect-h-11"
                        galleryImgs={
                          documentPhotos.length > 0 &&
                          documentPhotos.map((img: any) => img.img800 || img)
                        }
                        imageClass="rounded-lg"
                      />
                      <Button
                        onClick={() => {
                          setIsImageOpen(true);
                          setImageUrl(documentPhotosArray);
                          const rawName =
                            reservationData?.pet?.name ||
                            reservationData?.hotelPet?.name ||
                            "pet";
                          const fileName = slugifyName(rawName);
                          setImageFileName(fileName + "-asi-karnesi");
                        }}
                        variant="outline"
                        className="w-full mt-2 text-xs"
                      >
                        {translate("showPhoto")}
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </section>

            <Separator className="dark:border-gray-700" />

            {/* Pet Sahibi Bilgileri */}
            <section>
              <h3 className="text-lg font-medium mb-2 dark:text-white">
                👤 {translate("petOwnerInformation")}
              </h3>
              <ul className="space-y-1">
                <li>
                  {translate("petOwnerName")}:{" "}
                  <span className="font-semibold capitalize dark:text-gray-300">
                    {reservationData?.petOwner?.fullName ||
                      reservationData?.hotelCustomer?.fullName}
                  </span>
                </li>
                <li>
                  {translate("phone")}:{" "}
                  <a
                    href={`tel:${reservationData?.petOwner?.phone || reservationData?.hotelCustomer?.phone}`}
                    className="font-semibold text-blue-600 hover:underline dark:text-blue-400"
                  >
                    {reservationData?.petOwner?.phone ||
                      reservationData?.hotelCustomer?.phone}
                  </a>
                </li>
                <li>
                  {translate("email")}:{" "}
                  <a
                    href={`mailto:${reservationData?.petOwner?.email || reservationData?.hotelCustomer?.email}`}
                    className="font-semibold text-blue-600 hover:underline dark:text-blue-400"
                  >
                    {reservationData?.petOwner?.email ||
                      reservationData?.hotelCustomer?.email}
                  </a>
                </li>
                {reservationData?.petOwner && (
                  <li>
                    <div className="mt-5">
                      <ChatButton petOwnerId={reservationData?.petOwner?._id} />
                    </div>
                  </li>
                )}
              </ul>
            </section>
          </div>
        </SheetContent>
      </Sheet>
      <ImageModal
        isOpen={isImageOpen}
        setIsOpen={setIsImageOpen}
        imageUrl={imageUrl}
        fileName={imageFileName}
        setModalIsOpen={setSheetIsOpen}
      />
    </>
  );
};

export default ReservationDetail;
