import type { FC } from "react";
import React from "react";
import { cookies } from "next/headers";
import HotelAvatarDropdown from "./HotelAvatarDropdown";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";
import Link from "next/link";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import HotelMenuBar from "@/shared/HotelMenuBar";
import { Button } from "@/components/ui/button";
import NotificationDropdownWrapper from "./NotificationDropdownWrapper";
import AccountSwitch from "./AccountSwitch";
import { Crown, Sparkles } from "lucide-react";

export interface LoggedHotelHeaderProps {
  className?: string;
}

const LoggedHotelHeader: FC<LoggedHotelHeaderProps> = async ({
  className = "",
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const propertyTypesCookie =
    cookieStore.get("propertyTypes")?.value || undefined;
  const propertyTypes = propertyTypesCookie
    ? JSON.parse(propertyTypesCookie)
    : [];
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div className={`MainNav2 relative z-10 ${className}`}>
      <div className={`flex justify-between px-4 lg:container md:h-20`}>
        <div className="hidden flex-1 justify-start space-x-3 sm:space-x-8 md:flex lg:space-x-10">
          <Link href="/hotel/account" className="self-center">
            <PawBookingLogo className="size-16 self-center" />
          </Link>
          <div className="hidden h-10 self-center border-l border-neutral-300 dark:border-neutral-500 lg:block"></div>
          <div className="flex items-center">
            <AccountSwitch propertyTypes={propertyTypes} />
          </div>
        </div>
        <div className="hidden flex-1 shrink-0 justify-end text-neutral-700 dark:text-neutral-100 md:flex lg:flex-none">
          <div className="flex space-x-2 lg:space-x-1">
            {membershipData?.data?.membershipType === "free" && (
              <div className="flex items-center justify-center">
                <Link href="/hotel/checkout">
                  <Button className="group relative bg-secondary-6000 hover:bg-secondary-500 text-white font-semibold px-2 py-2 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out border-0 overflow-hidden">
                    {/* Arka plan parıltı efekti */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

                    {/* İkon ve metin */}
                    <div className="relative flex items-center gap-2">
                      <span className="text-sm font-bold tracking-wide">
                        Plus Üye Ol
                      </span>
                      <Sparkles className="w-3 h-3 text-white/80 animate-pulse" />
                    </div>

                    {/* Alt gölge efekti */}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
                  </Button>
                </Link>
              </div>
            )}
            <SwitchDarkMode className="mx-0.5" />
            <LangDropdownSingle className="hidden lg:flex" />
            <NotificationDropdownWrapper hotelToken={hotelToken} />
            {hotelData?.data && (
              <HotelAvatarDropdown
                logo={hotelData?.data?.logo}
                legalName={hotelData?.data?.hotelName}
              />
            )}
            {/* <HotelMenuBar /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoggedHotelHeader;
