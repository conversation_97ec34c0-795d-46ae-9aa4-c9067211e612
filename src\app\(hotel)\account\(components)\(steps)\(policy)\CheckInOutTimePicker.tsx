"use client";
import React from "react";
import type { FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";

interface CheckInOutTimePickerProps {
  timeHandler: (date: string, name: string) => void;
  checks: {
    checkInStartTime: string | undefined;
    checkInEndTime: string | undefined;
    checkOutTime: string | undefined;
  };
}

const CheckInOutTimePicker: FC<CheckInOutTimePickerProps> = ({
  timeHandler,
  checks,
}) => {
  const translate = useTranslations("CheckInOutTimePicker");
  const { checkInStartTime, checkInEndTime, checkOutTime } = checks;
  // const checkInDateValidate = new Date(`1970-01-01T${checkInStartTime}:00`);
  // const checkInEndDateValidate = new Date(`1970-01-01T${checkInEndTime}:00`);
  // const checkOutDateValidate = new Date(`1970-01-01T${checkOutTime}:00`);

  // const isValidCheckIn = checkInEndDateValidate > checkInDateValidate;
  // const isValid = checkInDateValidate > checkOutDateValidate;

  const timeData = Array.from({ length: 24 * 4 }, (_, index) => {
    const hours = Math.floor(index / 4);
    const minutes = (index % 4) * 15;
    return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
  });

  return (
    <>
      <div className="flex max-md:flex-col gap-3">
        <div className="max-md:basis-1/2 max-md:mb-2">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm mb-1">
            {translate("checkInStartTimeLabel")}
          </p>
          <Select
            onValueChange={(selected) =>
              timeHandler(selected, "checkInStartTime")
            }
            value={checks.checkInStartTime}
          >
            <SelectTrigger className="max-w-[180px]">
              <SelectValue placeholder={translate("checkInStartTimeLabel")} />
            </SelectTrigger>
            <SelectContent>
              {timeData.map((time, index) => {
                return (
                  <SelectItem key={index} value={time}>
                    {time}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <div className="max-md:basis-1/2">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm mb-1">
            {translate("checkInEndTimeLabel")}
          </p>
          <Select
            onValueChange={(selected) =>
              timeHandler(selected, "checkInEndTime")
            }
            value={checks.checkInEndTime}
          >
            <SelectTrigger className="max-w-[180px]">
              <SelectValue placeholder={translate("checkInEndTimeLabel")} />
            </SelectTrigger>
            <SelectContent>
              {timeData.map((time, index) => {
                return (
                  <SelectItem key={index} value={time}>
                    {time}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="mt-5">
        <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm mb-1">
          {translate("checkOutLabel")}
        </p>
        <Select
          onValueChange={(selected) => timeHandler(selected, "checkOutDate")}
          value={checks.checkOutTime}
        >
          <SelectTrigger className="max-w-[180px]">
            <SelectValue placeholder={translate("checkOutLabel")} />
          </SelectTrigger>
          <SelectContent>
            {timeData.map((time, index) => {
              return (
                <SelectItem key={index} value={time}>
                  {time}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      {/* {!isValid && checkOutTime && (
        <div className="text-sm text-red-500 mt-2">
          {translate("checkOutError")}
        </div>
      )}
      {!isValidCheckIn && checkInEndTime && (
        <div className="text-sm text-red-500 mt-2">
          {translate("checkInError")}
        </div>
      )} */}
    </>
  );
};

export default CheckInOutTimePicker;
