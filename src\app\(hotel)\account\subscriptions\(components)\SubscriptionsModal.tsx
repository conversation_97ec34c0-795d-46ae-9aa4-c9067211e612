"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import IconPlus from "@/shared/icons/Plus";
import AddSubscription from "./AddSubscription";
import type { RoomGroupListType } from "@/types/hotel/roomGroupType";

interface SubscriptionsModalProps {
  hotelToken: string | undefined;
  roomGroupData: RoomGroupListType;
}

const SubscriptionsModal: FC<SubscriptionsModalProps> = ({
  hotelToken,
  roomGroupData,
}) => {
  const [addHotelSubscriptionModal, setAddHotelSubscriptionModal] =
    useState<boolean>(false);

  const closeModal = () => {
    setAddHotelSubscriptionModal(false);
  };

  return (
    <>
      <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
        <div className="mt-4 md:my-8">
          <h2 className="text-xl md:text-2xl font-semibold">
            Üyelik Kartı Oluşturma ve Düzenleme
          </h2>
          <span className="text-sm text-neutral-500 dark:text-neutral-300">
            Bu bölümde oteliniz için yeni üye kartları oluşturabilir veya mevcut
            üye kartlarını düzenleyebilirsiniz.
          </span>
        </div>
        <Button
          onClick={() => setAddHotelSubscriptionModal(true)}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
        >
          <IconPlus />
          Yeni Üyelik Kartı Ekle
        </Button>
      </div>
      <Dialog open={addHotelSubscriptionModal}>
        <DialogContent
          onInteractOutside={closeModal}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>Yeni Üyelik Kartı Ekleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable space-y-3">
            <AddSubscription
              hotelToken={hotelToken}
              roomGroupData={roomGroupData}
              closeModal={closeModal}
            />
          </div>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SubscriptionsModal;
