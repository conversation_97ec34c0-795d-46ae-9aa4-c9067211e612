import React from "react";
import type { FC } from "react";
import DeleteHotelService from "../DeleteHotelService";
import UpdateTransportationService from "../(updateService)/UpdateTransportationService";
import { transportationServiceApiTypes } from "@/types/hotel/services/serviceTypes";

export interface TransportationCardProps {
  service: transportationServiceApiTypes;
  hotelToken: string | undefined;
}

const TransportationCard: FC<TransportationCardProps> = ({
  service,
  hotelToken,
}) => {
  const { serviceName, isActive } = service.serviceData;
  const { initialPrice, maxDistance, distancePrice } =
    service.serviceData.serviceDetails;
  const { _id } = service;

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex justify-between items-center">
        <div className="flex">
          <span className="relative flex h-2.5 w-2.5 mt-[7px] mr-2">
            <span
              className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                isActive ? "bg-green-700" : "bg-red-700"
              } opacity-75`}
            ></span>
            <span
              className={`relative inline-flex rounded-full h-2.5 w-2.5 ${
                isActive ? "bg-green-600" : "bg-red-600"
              }`}
            ></span>
          </span>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <h2 className="font-semibold capitalize text-neutral-900 dark:text-white">
                <span className="line-clamp-1">{serviceName}</span>
              </h2>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Maksimum Mesafe</div>
              <div> {maxDistance} KM</div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Başlangıç Fiyatı:</div>
              <div> {initialPrice}</div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>KM Başı Fiyat:</div>
              <div> {distancePrice}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdateTransportationService
          service={service}
          hotelToken={hotelToken}
        />
        <DeleteHotelService
          hotelToken={hotelToken}
          serviceId={_id}
          serviceName={serviceName}
        />
      </div>
    </div>
  );
};

export default TransportationCard;
