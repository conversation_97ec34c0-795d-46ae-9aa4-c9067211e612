import React from "react";
import AuthHeader from "./(components)/AuthHeader";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value || undefined;

  if (token?.includes("HT_")) {
    redirect("/account");
  }

  return (
    <div>
      <AuthHeader />
      {children}
    </div>
  );
}
