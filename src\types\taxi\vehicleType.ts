interface FileType {
  _id: string;
  src: string;
  size: number;
  mimeType: string;
  originalname: string;
  s3Path: string;
  docType: string;
  createdAt: string;
  updatedAt: string;
}

interface ImageSizeType {
  size: number;
  src: string;
  width: number;
  height: number;
}

export interface ImageType {
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimeType: string;
  fit: string;
  tags: string;
  createdAt: string;
  updatedAt: string;
  processed: boolean;
  img800: ImageSizeType;
  img400: ImageSizeType;
  img200: ImageSizeType;
  img100: ImageSizeType;
}

export interface VehicleDataApiTypes {
  _id: string;
  petTaxi: string; // ObjectId (Ref)
  acceptedPetTypes: string[];
  vehicleFeatures: string[];
  vehicleName: string;
  vehiclePlate: string;
  vehicleCapacity: number;
  vehicleDescription: string;
  images: ImageType[];
  documents: FileType[];
  passive: boolean;
  cleaningCertificate: boolean | null;
  vehicleType: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleDataTypes {
  acceptedPetTypes: string[];
  vehicleFeatures: string[];
  vehicleName: string;
  vehiclePlate: string;
  vehicleCapacity: number;
  images: ImageType[];
  documents: FileType[];
  passive: boolean;
  cleaningCertificate: boolean | null;
  vehicleType: string;
}

export type VehicleListType = VehicleDataApiTypes[];
