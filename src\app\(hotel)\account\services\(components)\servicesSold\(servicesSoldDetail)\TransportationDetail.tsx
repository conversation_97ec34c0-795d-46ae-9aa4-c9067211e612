import React from "react";
import type { <PERSON> } from "react";
import { Separator } from "@/components/ui/separator";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface TransportationDetailProps {
  servicesSoldData: any;
}

const TransportationDetail: FC<TransportationDetailProps> = ({
  servicesSoldData,
}) => {
  return (
    <>
      <div className="space-y-1">
        <h3 className="mb-2 text-lg font-semibold">Hizmet Bilgileri</h3>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Hizmet Adı:</p>
          <p>{servicesSoldData?.serviceName}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Hizmet Tarihi:</p>
          <p>{formatDateToDayMonthYear(servicesSoldData?.serviceDate)}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Başlangıç Noktası:</p>
          <p>{servicesSoldData?.serviceDetails?.startPoint}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Varış Noktası:</p>
          <p>{servicesSoldData?.serviceDetails?.endPoint}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Mesafe:</p>
          <p>{servicesSoldData?.serviceDetails?.distance} km</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Not:</p>
          <p>{servicesSoldData?.note || "-"}</p>
        </div>
      </div>
      <Separator />
    </>
  );
};

export default TransportationDetail;
