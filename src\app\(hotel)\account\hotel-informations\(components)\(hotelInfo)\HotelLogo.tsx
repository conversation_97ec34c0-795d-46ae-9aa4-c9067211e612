"use client";
import type { FC } from "react";
import React, { useState } from "react";
import IconCancel from "@/shared/icons/Cancel";
import { useTranslations } from "next-intl";
import Input from "@/shared/Input";
import Image from "next/image";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelLogoProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const HotelLogo: FC<HotelLogoProps> = ({ hotelData, hotelToken }) => {
  const translate = useTranslations("HotelInformations");
  const { hotelLogoHandler } = useHotelInformationsActions();
  const [image, setImage] = useState<string | null>(
    hotelData.logo?.src || null
  );
  const [photoFileObject, setPhotoFileObject] = useState<File | null>();
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const removePhoto = () => {
    // TODO: Endpointten logo'yu sil
    setImage(null);
    setPhotoFileObject(null);
  };

  return (
    <div>
      <div className="mb-5 text-sm font-medium text-neutral-700 dark:text-neutral-300">
        {translate("hotelLogo")}
      </div>
      <form
        onSubmit={(event) =>
          hotelLogoHandler(
            event,
            photoFileObject,
            hotelData._id,
            hotelToken,
            setLoading,
            setPhotoFileObject,
            setDisabled
          )
        }
      >
        <div className="flex max-md:flex-col md:items-center gap-5">
          {image && (
            <div className="mt-2 flex justify-start gap-3">
              <div className="relative">
                <Image src={image} width={150} height={150} alt="hotel-logo" />
                <IconCancel
                  onClick={removePhoto}
                  className="absolute right-2 top-2 size-6 cursor-pointer rounded-full bg-white text-secondary-6000 shadow-md hover:bg-secondary-6000 hover:text-white"
                />
              </div>
            </div>
          )}

          <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000 sm:w-1/2 md:w-1/4">
            <div className="space-y-1 text-center">
              <svg
                className="mx-auto size-12 text-neutral-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
                aria-hidden="true"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                ></path>
              </svg>
              <div className="text-sm text-neutral-6000 dark:text-neutral-300">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
                >
                  <span>{translate("hotelUploadPhoto")}</span>
                  <Input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    accept="image/*"
                    multiple={false}
                    onChange={(e) => {
                      const fileObject = e.target.files![0];
                      setPhotoFileObject(fileObject);
                      if (fileObject) {
                        const photoUrl = URL.createObjectURL(fileObject);
                        setImage(photoUrl);
                      }
                    }}
                  />
                </label>
                {/* <p className="pl-1">or drag and drop</p> */}
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                {translate("hotelTypeImage")}
              </p>
            </div>
          </div>
        </div>
        <div className="mt-10 flex justify-end">
          <Button
            type="submit"
            className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
            disabled={disabled || !photoFileObject}
          >
            {loading ? <LoadingSpinner /> : translate("save")}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default HotelLogo;
