import React from "react";
import type { <PERSON> } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Tit<PERSON>,
} from "@/components/ui/card";
import UpdatePolicy from "./UpdatePolicy";
import { useTranslations } from "next-intl";
interface PolicyProps {
  cancellationPolicyInformationsTaxiData: any;
  petTaxiToken: string | undefined;
}

const Policy: FC<PolicyProps> = ({
  cancellationPolicyInformationsTaxiData,
  petTaxiToken,
}) => {
  const translate = useTranslations("Policy");
  const {
    serviceHoursStart,
    serviceHoursEnd,
    description,
    cancellationPolicyType,
    dateRange,
    specialRules,
  } = cancellationPolicyInformationsTaxiData;

  const cancelType =
    cancellationPolicyType === "CANCEL_UNTIL_DATE_RANGE"
      ? translate("untilDateRange")
      : translate("noCancellation");

  const dateRangeNames: Record<string, string> = {
    last_24_hours: translate("last24Hours"),
    last_48_hours: translate("last48Hours"),
    last_week: translate("lastWeek"),
    last_10_days: translate("last10Days"),
    last_month: translate("lastMonth"),
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{translate("cancellationPolicy")}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-1">
            <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
              Hizmet Başlangıç Saati:
            </p>
            <p className="text-sm">{serviceHoursStart}</p>
          </div>
          <div className="flex items-center gap-1">
            <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
              Hizmet Bitiş Saati:
            </p>
            <p className="text-sm">{serviceHoursEnd}</p>
          </div>
          <div>
            <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
              {translate("description")}:
            </p>
            <p className="text-sm">{description}</p>
          </div>
          <div className="flex items-center gap-1">
            <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
              {translate("cancellationOption")}:
            </p>
            <p className="text-sm">{cancelType}</p>
          </div>
          {cancellationPolicyType === "CANCEL_UNTIL_DATE_RANGE" && (
            <div className="flex items-center gap-1">
              <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
                {translate("cancellationOption")}:
              </p>
              <p className="text-sm">{dateRangeNames[dateRange]}</p>
            </div>
          )}
        </div>
        <div className="mt-5">
          <p className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold">
            {translate("rules")}
          </p>
          <ul className="list-disc pl-3">
            {specialRules.map((rule: any) => {
              return (
                <li key={rule._id}>
                  <span className="text-sm text-neutral-700 dark:text-neutral-300 text-md font-semibold capitalize">
                    {rule.title}
                  </span>
                  <span className="block text-sm">{rule.rule}</span>
                </li>
              );
            })}
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <UpdatePolicy
          cancellationPolicyInformationsTaxiData={
            cancellationPolicyInformationsTaxiData
          }
          petTaxiToken={petTaxiToken}
        />
      </CardFooter>
    </Card>
  );
};

export default Policy;
