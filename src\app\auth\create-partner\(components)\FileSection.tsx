"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";

const FileSection = () => {
  const translate = useTranslations("FileSection");

  const [fileObject, setFileObject] = useState<FileList | null>(null);

  return (
    <>
      <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
        <div className="space-y-1 text-center">
          <svg
            className="mx-auto size-12 text-neutral-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
          </svg>
          <div className="text-sm text-neutral-6000 dark:text-neutral-300">
            <label
              htmlFor="file-upload"
              className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
            >
              <span>{translate("hotelUploadHotelLicense")}</span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                accept="application/pdf"
                multiple={false}
                onChange={(event) => {
                  setFileObject(event.target.files);
                }}
              />
            </label>
            {/* <p className="pl-1">or drag and drop</p> */}
          </div>
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            PNG, JPG, GIF up to 1MB
          </p>
          {fileObject && fileObject[0] && (
            <div>
              <p>{fileObject[0].name}</p>
            </div>
          )}
        </div>
      </div>
      <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
        <div className="space-y-1 text-center">
          <svg
            className="mx-auto size-12 text-neutral-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            ></path>
          </svg>
          <div className="text-sm text-neutral-6000 dark:text-neutral-300">
            <label
              htmlFor="file-upload"
              className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
            >
              <span>{translate("hotelUploadHotelPhoto")}</span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                accept="image/*,.pdf"
                multiple={true}
              />
            </label>
          </div>
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            PNG, JPG
          </p>
        </div>
      </div>
    </>
  );
};

export default FileSection;
