import type { FC } from "react";
import React from "react";
import PartnerHeader from "@/app/(components)/(Header)/PartnerHeader";
import Layout from "@/app/(components)/(hotelHomePage)/Layout";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import { cookies } from "next/headers";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export interface HotelHomePageProps {}

const HotelHomePage: FC<HotelHomePageProps> = async () => {
  const cookieStore = await cookies();
  const mobileParams = cookieStore.get("mobileParams")?.value || undefined;
  const queryParams = new URLSearchParams(mobileParams);
  const mobile = queryParams.get("mobile");

  return (
    <>
      <div className="sticky top-0 z-50 bg-transparent md:block hidden">
        <PartnerHeader />
      </div>
      <div className="relative flex justify-between items-center p-5 md:hidden">
        <PawBookingLogo className="size-20 self-center" />
        <Link href={"/login"}>
          <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center p-4">
            Giriş Yap
          </Button>
        </Link>
      </div>
      <div className="z-0">
        <Layout mobile={mobile} />
      </div>
    </>
  );
};

export default HotelHomePage;
