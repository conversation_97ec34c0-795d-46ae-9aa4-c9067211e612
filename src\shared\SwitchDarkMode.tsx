"use client";

import React from "react";
import { MoonIcon } from "@heroicons/react/24/solid";
import { SunIcon } from "@heroicons/react/24/outline";
import { useThemeMode } from "@/hooks/useThemeMode";
export interface SwitchDarkModeProps {
  className?: string;
}
const SwitchDarkMode: React.FC<SwitchDarkModeProps> = ({ className = "" }) => {
  const { _toogleDarkMode, isDarkMode, toDark, toLight } = useThemeMode();

  return (
    <button
      onClick={_toogleDarkMode}
      className={`flex size-12 items-center justify-center self-center rounded-full text-2xl text-neutral-700 opacity-80 hover:opacity-100 focus:outline-none dark:text-neutral-300 dark:hover:opacity-100 md:text-3xl ${className}`}
    >
      <span className="sr-only">Enable dark mode</span>
      {isDarkMode ? (
        <MoonIcon className="size-6" aria-hidden="true" />
      ) : (
        <SunIcon className="size-6" aria-hidden="true" />
      )}
    </button>
  );
};

export default SwitchDarkMode;
