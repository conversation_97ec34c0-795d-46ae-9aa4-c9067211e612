"use client";
import React, { useState, useEffect } from "react";
import IconChevronUp from "@/shared/icons/ChevronUp";

const ScrollToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.scrollY > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <>
      <div
        onClick={scrollToTop}
        className={`fixed bottom-12 right-10 cursor-pointer rounded-full bg-secondary-6000 p-2 text-white duration-300 max-lg:hidden ${isVisible ? "visible opacity-100" : "invisible opacity-0"}`}
      >
        <IconChevronUp />
      </div>
    </>
  );
};

export default ScrollToTop;
