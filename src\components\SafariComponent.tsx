"use client";
import React from "react";
import { motion } from "framer-motion";
import Safari from "@/components/ui/safari";
import safariImage from "@/images/pawbooking-safari-calendar-image.png";

export default function SafariComponent() {
  return (
    <div className="relative drop-shadow-xl">
      <motion.div
        initial={{ x: "-120%" }}
        animate={{ x: 0 }}
        transition={{ duration: 1, ease: "easeOut" }}
        className="relative"
      >
        <Safari
          url="pawbooking.co"
          className="size-full"
          src={safariImage.src}
        />
      </motion.div>
    </div>
  );
}
