import React from "react";
import { ContainerScroll } from "@/components/ui/container-scroll-animation";

import Image from "next/image";

export function HeroScroll() {
  return (
    <div className="flex flex-col overflow-hidden contar">
      <ContainerScroll
        titleComponent={
          <>
            <h1 className="text-3xl font-semibold text-black dark:text-white">
              Gelişmiş <br />
              <span className="text-2xl md:text-[3rem] font-bold mb-2 leading-none">
                Yönetim Ekranları
              </span>
            </h1>
          </>
        }
        childrens={
          <Image
            src={`https://i.hizliresim.com/2swgqo5.jpg`}
            alt="hero"
            height={720}
            width={1400}
            className="mx-auto rounded-2xl object-cover h-full object-left-top"
            draggable={false}
          />
        }
        childrenMobil={
          <Image
            src={`https://i.hizliresim.com/5sx11f9.jpg`}
            alt="hero"
            height={720}
            width={1400}
            className="mx-auto rounded-2xl object-cover h-full object-left-top"
            draggable={false}
          />
        }
      />
    </div>
  );
}
