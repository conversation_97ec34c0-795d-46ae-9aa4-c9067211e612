/**
 * @type {import('next').NextConfig}
 */

const createNextIntlPlugin = require("next-intl/plugin");
const nextPWA = require("next-pwa");
const { withSentryConfig } = require("@sentry/nextjs");

const withNextIntl = createNextIntlPlugin();

const isDev = process.env.NEXT_PUBLIC_APP_ENV === "development";

const nextConfig = {
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "X-Robots-Tag",
            value:
              process.env.NEXT_PUBLIC_APP_ENV === "production"
                ? "all"
                : "noindex, nofollow",
          },
        ],
      },
    ];
  },
  webpack(config) {
    config.ignoreWarnings = [
      ...(config.ignoreWarnings ?? []),
      (warning, { requestShortener }) => {
        const isOpenTelemetryModule =
          !!warning.module &&
          (/@opentelemetry\/instrumentation/.test(
            warning.module.readableIdentifier(requestShortener)
          ) ||
            /@prisma\/instrumentation/.test(
              warning.module.readableIdentifier(requestShortener)
            ));

        const isCriticalDependencyMessage = /Critical dependency/.test(
          warning.message
        );

        return isOpenTelemetryModule && isCriticalDependencyMessage;
      },
    ];

    return config;
  },
  reactStrictMode: true,
  basePath: "",
  output: "standalone",
  poweredByHeader: false,
  cleanDistDir: true,
  crossOrigin: "anonymous",
  experimental: {
    typedRoutes: true,
    webpackBuildWorker: true,
    serverSourceMaps: false,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  compress: false,
  pageExtensions: ["ts", "tsx", "jsx", "mdx"],
  images: {
    // disableStaticImages: true,
    unoptimized: true,
    remotePatterns: [
      {
        // protocol: "https",
        hostname: "**",
      },
      // {
      //   // protocol: "https",
      //   hostname: "images.pexels.com",
      //   port: "",
      //   pathname: "/**",
      // },
      // {
      //   protocol: "https",
      //   hostname: "images.unsplash.com",
      //   port: "",
      //   pathname: "/**",
      // },
      // {
      //   protocol: "https",
      //   hostname: "a0.muscache.com",
      //   port: "",
      //   pathname: "/**",
      // },
      // {
      //   protocol: "https",
      //   hostname: "www.gstatic.com",
      //   port: "",
      //   pathname: "/**",
      // },
      // {
      //   protocol: "https",
      //   hostname: "eu2.contabostorage.com",
      //   port: "",
      //   pathname: "/**",
      // },
    ],
  },
};

const PWAConfig = {
  dest: "public", // Destination directory for the PWA files
  disable: isDev, // Disable PWA in development mode
  register: true, // Register the PWA service worker
  skipWaiting: true, // Skip waiting for service worker activation
  cacheOnFrontEndNav: true,
};

const withPWA = nextPWA(PWAConfig);

const sentryConfig = {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: "pawbooking",
  project: "partner-pawbooking-co",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
};

module.exports = !isDev
  ? () => {
      const plugins = [withPWA, withNextIntl];
      return plugins.reduce(
        (acc, next) => next(acc),
        withSentryConfig(nextConfig, sentryConfig)
      );
    }
  : () => {
      const plugins = [withNextIntl];
      return plugins.reduce((acc, next) => next(acc), nextConfig);
    };
