import React from "react";
import HotelAddress from "../(components)/(hotelAddress)/HotelAddress";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import HotelMap from "../(components)/(hotelAddress)/HotelMap";

const HotelAddressPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();

  return (
    <>
      {hotelData && (
        <>
          <div className="mt-4 md:mt-8">
            <h2 className="text-xl md:text-2xl font-semibold">
              Adres Bilgileri
            </h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300">
              Bu adım gelecek olan şifreler ile alt üye işyeri hesabınızın
              takibini yapmak için önemlidir
            </span>
          </div>
          <HotelAddress hotelData={hotelData?.data} hotelToken={hotelToken} />
          <div className="mt-16">
            <h2 className="text-2xl font-semibold">Harita Bilgileri</h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300">
              Bu adım otel detay sayfasında görünecek olan harita alanı içindir.
              Google Maps üzerindeki paylaş alanından HTML alanı buraya
              yapıştırmanızı bekliyoruz.
            </span>
          </div>
          <HotelMap
            hotelData={hotelData?.data}
            hotelToken={hotelToken}
            method="PUT"
          />
        </>
      )}
    </>
  );
};

export default HotelAddressPage;
