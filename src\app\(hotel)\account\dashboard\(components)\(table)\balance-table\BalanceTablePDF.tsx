"use client";

import { Document, Page, Text, View, Image } from "@react-pdf/renderer";
import PawBooking<PERSON>ogo from "public/img/pawlogo.png";
import { styles } from "./pdfStyles";
import { formatCellValue } from "./BalanceTableUtils";

interface BalanceTablePDFProps {
  data: any[];
  columns: any[];
}

const BalanceTablePDF = ({ data, columns }: BalanceTablePDFProps) => {
  const visibleColumns = columns.filter(
    (col) => col.getIsVisible() && col.id !== "expander"
  );

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image src={PawBookingLogo.src} style={styles.logo} />
        </View>

        <View style={styles.table}>
          {/* Table Header */}
          <View style={[styles.tableRow]}>
            {visibleColumns.map((col) => {
              const isWideColumn = col.id === "_id";

              return (
                <Text
                  key={col.id}
                  style={[
                    styles.tableCell,
                    styles.headerCell,
                    isWideColumn
                      ? { flexBasis: 120, flexGrow: 0, flexShrink: 0 }
                      : { flex: 1 },
                  ]}
                >
                  {typeof col.columnDef.header === "string"
                    ? col.columnDef.header
                    : col.id}
                </Text>
              );
            })}
          </View>
          {/* Table Body */}
          {data.map((row, index) => (
            <View key={index} style={styles.tableRow}>
              {visibleColumns.map((col) => {
                const isWideColumn = col.id === "_id";

                return (
                  <Text
                    key={col.id}
                    style={[
                      styles.tableCell,
                      isWideColumn
                        ? { flexBasis: 120, flexGrow: 0, flexShrink: 0 }
                        : { flex: 1 },
                    ]}
                  >
                    {col.id === "petOwner_fullName"
                      ? row.petOwner?.fullName || "-"
                      : formatCellValue(row[col.id], col.id)}
                  </Text>
                );
              })}
            </View>
          ))}
        </View>
      </Page>
    </Document>
  );
};

export default BalanceTablePDF;
