"use client";
import React, { useRef } from "react";
import type { MotionValue } from "framer-motion";
import { useScroll, useTransform, motion } from "framer-motion";

export const ContainerScroll = ({
  titleComponent,
  childrens,
  childrenMobil,
}: {
  titleComponent: string | React.ReactNode;
  childrens: React.ReactNode;
  childrenMobil: React.ReactNode;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
  });
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  const scaleDimensions = () => {
    return isMobile ? [0.7, 0.9] : [1.05, 1];
  };

  const rotate = useTransform(scrollYProgress, [0, 1], [20, 0]);
  const scale = useTransform(scrollYProgress, [0, 1], scaleDimensions());
  const translate = useTransform(scrollYProgress, [0, 1], [0, -100]);

  return (
    <div
      className="h-[70rem] md:h-[80rem] flex items-center justify-center relative p-2 md:p-20"
      ref={containerRef}
    >
      <div
        className="pt-10 md:pt-40 w-full relative"
        style={{
          perspective: "1000px",
        }}
      >
        <Header translate={translate} titleComponent={titleComponent} />
        <Card
          rotate={rotate}
          translate={translate}
          scale={scale}
          childrens={childrens}
          childrenMobil={childrenMobil}
        />
        <div className="flex  flex-row justify-center items-center w-full gap-10 mt-16 px-10 md:px-0">
          <div className="flex justify-center items-center w-16">
            <svg
              role="img"
              width="100%"
              height="100%"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <title />
              <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-12.9-1.801" />
            </svg>
          </div>
          <div className="flex justify-center items-center w-16">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              viewBox="0 0 64 64"
            >
              <path d="M54.166,19.783c-0.258,0.162-6.401,3.571-6.401,11.13c0.29,8.621,7.752,11.644,7.88,11.644	c-0.128,0.162-1.127,4.119-4.085,8.267C49.213,54.398,46.607,58,42.65,58c-3.764,0-5.115-2.381-9.458-2.381	c-4.664,0-5.984,2.381-9.555,2.381c-3.957,0-6.756-3.795-9.232-7.335c-3.216-4.633-5.95-11.903-6.047-18.883	c-0.065-3.699,0.644-7.335,2.444-10.423c2.541-4.312,7.077-7.238,12.031-7.335c3.795-0.128,7.173,2.606,9.49,2.606	c2.22,0,6.37-2.606,11.065-2.606C45.415,14.026,50.82,14.636,54.166,19.783z M32.002,13.285c-0.676-3.378,1.19-6.756,2.927-8.911	C37.149,1.769,40.655,0,43.678,0c0.193,3.378-1.03,6.691-3.216,9.104C38.5,11.71,35.122,13.671,32.002,13.285z"></path>
            </svg>
          </div>
          <div className="flex justify-center items-center w-16">
            <svg
              height="100%"
              viewBox="0 0 48 48"
              width="100%"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M0 0h48v48H0z" fill="none" />
              <path d="M12 36c0 1.1.9 2 2 2h2v7c0 1.66 1.34 3 3 3s3-1.34 3-3v-7h4v7c0 1.66 1.34 3 3 3s3-1.34 3-3v-7h2c1.1 0 2-.9 2-2V16H12v20zM7 16c-1.66 0-3 1.34-3 3v14c0 1.66 1.34 3 3 3s3-1.34 3-3V19c0-1.66-1.34-3-3-3zm34 0c-1.66 0-3 1.34-3 3v14c0 1.66 1.34 3 3 3s3-1.34 3-3V19c0-1.66-1.34-3-3-3zM31.06 4.32l2.61-2.61c.39-.39.39-1.02 0-1.41-.39-.39-1.02-.39-1.41 0L29.3 3.25C27.7 2.46 25.91 2 24 2c-1.92 0-3.72.46-5.33 1.26L15.7.29c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41l2.62 2.62C13.94 6.51 12 10.03 12 14h24c0-3.98-1.95-7.5-4.94-9.68zM20 10h-2V8h2v2zm10 0h-2V8h2v2z" />
            </svg>
          </div>
          <div className="flex justify-center items-center w-16">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="100%"
              height="100%"
              viewBox="0 -196 512 512"
              version="1.1"
              preserveAspectRatio="xMidYMid"
            >
              <g>
                <path
                  d="M0,118.067201 L20.0767036,118.067201 L20.0767036,65.8336281 C20.0767036,55.5319609 26.8493059,48.0471816 36.5248058,48.0471816 C45.8774864,48.0471816 51.6828689,53.9224284 51.6828689,63.4191233 L51.6828689,118.065347 L71.1946592,118.065347 L71.1946592,65.3496558 C71.1946592,55.1285476 77.8060582,48.0461515 87.4813517,48.0461515 C97.3178485,48.0461515 102.800618,54.0017513 102.800618,64.4645365 L102.800618,118.066171 L122.87693,118.066171 L122.87693,59.5568223 C122.87693,42.4947891 111.830892,31.1468846 94.9790592,31.1468846 C82.6429825,31.1468846 72.4849047,37.7463386 68.371228,48.2894768 L67.887453,48.2894768 C64.74306,37.3439555 56.1158974,31.1468846 44.022761,31.1468846 C32.4124087,31.1468846 23.3821348,37.6659856 19.6730143,47.5652696 L19.2698822,47.5652696 L19.2698822,32.7566231 L0,32.7566231 L0,118.067201 L0,118.067201 Z M166.822061,104.304916 C158.114194,104.304916 152.309224,99.8783716 152.309224,92.8763285 C152.309224,86.1159624 157.872492,81.7698951 167.547785,81.1260409 L187.301484,79.9188091 L187.301484,86.4379101 C187.301484,96.7395773 178.190505,104.304916 166.822061,104.304916 L166.822061,104.304916 Z M160.774977,119.435469 C171.740309,119.435469 182.464147,113.72134 187.382808,104.46591 L187.78594,104.46591 L187.78594,118.067407 L207.136527,118.067407 L207.136527,59.3149391 C207.136527,42.1723469 193.349208,30.9853545 172.144474,30.9853545 C150.374805,30.9853545 136.749928,42.413818 135.862381,58.3486428 L154.487244,58.3486428 C155.777283,51.2662466 161.824367,46.6787082 171.338457,46.6787082 C181.255659,46.6787082 187.302743,51.8295417 187.302743,60.7629415 L187.302743,66.8796594 L164.726026,68.1673678 C143.924403,69.4550761 132.233552,78.5495939 132.233552,93.6804766 C132.233552,109.052418 144.247016,119.435469 160.77541,119.435469 L160.774977,119.435469 Z M295.265169,63.1780643 C293.894507,45.2307058 279.945903,30.9853545 257.371043,30.9853545 C232.538306,30.9853545 216.411785,48.1279467 216.411785,75.4102639 C216.411785,103.097025 232.537274,119.75482 257.53204,119.75482 C278.979715,119.75482 293.733634,107.19956 295.345667,88.1245815 L276.398192,88.1245815 C274.543755,97.7823945 267.932232,103.577082 257.773329,103.577082 C245.034141,103.577082 236.810709,93.2754151 236.810709,75.4082036 C236.810709,57.8632282 244.954055,47.2393249 257.612332,47.2393249 C268.335757,47.2393249 274.705248,54.08025 276.3179,63.1747678 L295.265375,63.1747678 L295.265169,63.1780643 Z M357.271674,0 C323.328164,0 302.041486,23.0983981 302.041486,60.0401765 C302.041486,96.9819549 323.3261,120 357.271674,120 C391.134685,120 412.421363,96.9819549 412.421363,60.0401765 C412.421363,23.0983981 391.136749,0 357.271674,0 Z M357.271674,17.7060934 C377.992799,17.7060934 391.215184,34.1244785 391.215184,60.0397644 C391.215184,85.8742853 377.992386,102.293082 357.271674,102.293082 C336.470051,102.293082 323.328164,85.8746974 323.328164,60.0397644 C323.328164,34.1248905 336.470464,17.7060934 357.271674,17.7060934 Z M420.890212,85.3921673 C421.777119,106.801092 439.353871,119.999588 466.12208,119.999588 C494.261446,119.999588 512,106.15662 512,84.1044589 C512,66.8009546 502.002093,57.0625826 478.378484,51.67069 L464.994276,48.6123311 C450.723141,45.2321481 444.837466,40.7251687 444.837466,32.9989183 C444.837466,23.3411054 453.706537,16.9025634 466.84863,16.9025634 C480.152133,16.9025634 489.262286,23.4216644 490.230332,34.2866267 L510.064735,34.2866267 C509.58096,13.8442045 492.649207,0.000618100029 467.010453,0.000618100029 C441.692661,0.000618100029 423.712611,13.9239394 423.712611,34.5276857 C423.712611,51.1069828 433.871721,61.408856 455.317538,66.3186306 L470.394896,69.8599317 C485.069143,73.3206737 491.035522,78.1494772 491.035522,86.5195818 C491.035522,96.1773947 481.279524,103.098879 467.251329,103.098879 C453.060898,103.098879 442.338093,96.0970417 441.048054,85.3927854 L420.891244,85.3927854 L420.890212,85.3921673 Z"
                  fill="#000000"
                  fillRule="nonzero"
                ></path>
              </g>
            </svg>
          </div>
        </div>
        <div className="flex justify-center mt-5 text-lg font-semibold">
          <span className="text-primary-500 mr-1.5">PawBooking</span>Şimdi Tüm
          Platformlarda
        </div>
      </div>
    </div>
  );
};

export const Header = ({ translate, titleComponent }: any) => {
  return (
    <motion.div
      style={{
        translateY: translate,
      }}
      className="div max-w-5xl mx-auto text-center"
    >
      {titleComponent}
    </motion.div>
  );
};

export const Card = ({
  rotate,
  scale,
  childrens,
  childrenMobil,
}: {
  rotate: MotionValue<number>;
  scale: MotionValue<number>;
  translate: MotionValue<number>;
  childrens: React.ReactNode;
  childrenMobil: React.ReactNode;
}) => {
  return (
    <motion.div
      style={{
        rotateX: rotate,
        scale,
      }}
    >
      <div className="flex flex-col md:flex-row gap-36 md:gap-10 items-center">
        <div
          className="max-w-5xl -mt-56 md:-mt-12 mx-auto h-[30rem] md:h-[40rem] w-full border-4 border-[#6C6C6C] p-2 md:p-6 bg-[#222222] rounded-[30px] shadow-2xl"
          style={{
            boxShadow:
              "0 0 #0000004d, 0 9px 20px #0000004a, 0 37px 37px #00000042, 0 84px 50px #00000026, 0 149px 60px #0000000a, 0 233px 65px #00000003",
          }}
        >
          <div className="h-full w-full overflow-hidden rounded-2xl bg-gray-100 dark:bg-zinc-900 md:rounded-2xl md:p-4">
            {childrens}
          </div>
        </div>

        <div
          className="max-w-5xl -mt-12 mx-auto h-[25rem] md:h-[35rem] w-56 md:w-80 border-4 border-[#6C6C6C] p-2 md:p-2 bg-[#222222] rounded-[30px] shadow-2xl"
          style={{
            boxShadow:
              "0 0 #0000004d, 0 9px 20px #0000004a, 0 37px 37px #00000042, 0 84px 50px #00000026, 0 149px 60px #0000000a, 0 233px 65px #00000003",
          }}
        >
          <div className="h-full w-full overflow-hidden rounded-2xl bg-gray-100 dark:bg-zinc-900 md:rounded-2xl md:p-2">
            {childrenMobil}
          </div>
        </div>
      </div>
    </motion.div>
  );
};
