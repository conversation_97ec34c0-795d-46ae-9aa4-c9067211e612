import React from "react";
import type { <PERSON> } from "react";
import { Separator } from "@/components/ui/separator";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface GroomingDetailProps {
  servicesSoldData: any;
}

const GroomingDetail: FC<GroomingDetailProps> = ({ servicesSoldData }) => {
  return (
    <>
      <div className="space-y-1">
        <h3 className="mb-2 text-lg font-semibold">Hizmet Bilgileri</h3>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Hizmet Adı:</p>
          <p>{servicesSoldData?.serviceName}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Hizmet Tarihi:</p>
          <p>{formatDateToDayMonthYear(servicesSoldData?.serviceDate)}</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Hizmet Süresi:</p>
          <p>{servicesSoldData?.serviceDetails?.duration} dakika</p>
        </div>
        <div className="flex gap-1 text-sm capitalize">
          <p className="font-semibold">Not:</p>
          <p>{servicesSoldData?.note || "-"}</p>
        </div>
      </div>
      <Separator />
    </>
  );
};

export default GroomingDetail;
