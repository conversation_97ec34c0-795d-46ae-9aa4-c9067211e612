"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import Input from "@/shared/Input";
import Link from "next/link";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";

interface ChartDateFilterProps {
  startDate: string | string[];
  endDate: string | string[];
  display: string | string[];
}

const ChartDateFilter: FC<ChartDateFilterProps> = ({
  startDate,
  endDate,
  display,
}) => {
  const { toast } = useToast();
  const [filterDates, setFilterDates] = useState<{
    startDate: string | string[];
    endDate: string | string[];
  }>({
    startDate: startDate,
    endDate: endDate,
  });
  const [isDateValid, setIsDateValid] = useState<boolean>(true);
  const [displaySetting, setDisplaySetting] = useState(display);
  const [showDateInputs, setShowDateInputs] = useState<boolean>(true);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    setFilterDates((prevState) => {
      const updatedDates = {
        ...prevState,
        [name]: value,
      };

      const isValid = validateDates(
        updatedDates.startDate as string,
        updatedDates.endDate as string
      );

      if (!isValid) {
        setIsDateValid(false);
      } else {
        setIsDateValid(true);
      }

      return updatedDates;
    });
  };

  const validateDates = (startDate: string, endDate: string): boolean => {
    if (!startDate || !endDate) return false;

    const start = new Date(startDate);
    const end = new Date(endDate);

    return start <= end;
  };

  const displaySettingHandler = (displaySetting: string) => {
    if (displaySetting === "day") {
      const adjustedDate = adjustDateToTimezone(new Date());
      return {
        startDate: adjustedDate && adjustedDate.toISOString().split("T")[0],
        endDate: adjustedDate && adjustedDate.toISOString().split("T")[0],
      };
    }

    if (displaySetting === "week") {
      const adjustedStartDate = adjustDateToTimezone(
        new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000)
      );
      const adjustedEndDate = adjustDateToTimezone(new Date());
      return {
        startDate:
          adjustedStartDate && adjustedStartDate.toISOString().split("T")[0],
        endDate: adjustedEndDate && adjustedEndDate.toISOString().split("T")[0],
      };
    }

    if (displaySetting === "month") {
      const today = new Date();
      const firstDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        1
      );
      const lastDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth() + 1,
        0
      );
      const adjustedFirstDayOfMonth = adjustDateToTimezone(firstDayOfMonth);
      const adjustedLastDayOfMonth = adjustDateToTimezone(lastDayOfMonth);

      return {
        startDate:
          adjustedFirstDayOfMonth &&
          adjustedFirstDayOfMonth.toISOString().split("T")[0],
        endDate:
          adjustedLastDayOfMonth &&
          adjustedLastDayOfMonth.toISOString().split("T")[0],
      };
    }
  };
  const selectedDates = displaySettingHandler(displaySetting as string);

  return (
    <div>
      <div className="flex items-center justify-end gap-3 max-md:flex-col mb-2">
        {/* <div className="flex items-center gap-2">
        <Switch onClick={() => setShowDateInputs((prev) => !prev)} />
        <span className="nc-Label text-sm font-medium text-neutral-700 dark:text-neutral-300">
          {showDateInputs ? "Özel Aralık" : "Standart Aralık"}
        </span>
      </div> */}
        <div className="flex justify-between gap-3 max-sm:flex-col">
          {showDateInputs && (
            <div className="flex gap-3">
              <Input
                type="date"
                name="startDate"
                id="startDate-id"
                className="rounded-md dark:[color-scheme:dark]"
                onChange={handleChange}
                value={filterDates.startDate}
              />
              <Input
                type="date"
                name="endDate"
                id="endDate-id"
                className="rounded-md dark:[color-scheme:dark]"
                onChange={handleChange}
                value={filterDates.endDate}
              />
            </div>
          )}
          {showDateInputs && isDateValid && (
            <Link
              className="w-full"
              scroll={false}
              href={`/account/dashboard?startDate=${filterDates.startDate}&endDate=${filterDates.endDate}`}
            >
              <Button className="bg-secondary-6000 text-white hover:bg-secondary-700 w-full">
                Uygula
              </Button>
            </Link>
          )}
          {showDateInputs && !isDateValid && (
            <Button
              onClick={() => {
                toast({
                  variant: "warning",
                  duration: 3000,
                  title: "Tarih",
                  description: "Geçerli tarih aralığı giriniz",
                });
              }}
              className="bg-secondary-6000 hover:bg-secondary-700 max-md:w-full"
            >
              Uygula
            </Button>
          )}
        </div>
        {!showDateInputs && (
          <div className="flex items-center gap-2">
            <Select
              value={displaySetting as string}
              onValueChange={(selected) => setDisplaySetting(selected)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Günlük</SelectItem>
                <SelectItem value="week">Haftalık</SelectItem>
                <SelectItem value="month">Aylık</SelectItem>
              </SelectContent>
            </Select>
            <Link
              scroll={false}
              href={`/account/dashboard?startDate=${selectedDates?.startDate}&endDate=${selectedDates?.endDate}&display=${displaySetting}`}
            >
              <Button className="bg-secondary-6000 text-white hover:bg-secondary-700">
                Uygula
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartDateFilter;
