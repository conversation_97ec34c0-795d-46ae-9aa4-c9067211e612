"use server";
import { redirect } from "next/navigation";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { cookies } from "next/headers";

export default async function finishOrder(orderId: string, data: any) {
  const cookieStore = await cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;

  try {
    const response = await fetch(`${HOTEL_API_PATHS.finishOrder}/${orderId}`, {
      cache: "no-cache",
      method: "PUT",
      headers: {
        ...(hotelToken && { hotelToken: hotelToken }),
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const result = await response.json();

    if (result.status === 401) {
      redirect("/");
    } else if (result.status === 404) {
      redirect("/404");
    }

    if (!response.ok || !result.success) {
      console.error("Network response was not ok");
      return undefined;
    }
    return result;
  } catch (err: unknown) {
    console.error("Error fetching data:", err);
    return undefined;
  }
}
