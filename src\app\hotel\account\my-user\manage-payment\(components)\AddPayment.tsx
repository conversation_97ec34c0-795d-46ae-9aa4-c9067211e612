"use client";
import type { ChangeE<PERSON>, FC } from "react";
import { useState } from "react";
import { CreditCardIcon, WalletIcon } from "lucide-react";
import { usePaymentInputs } from "react-payment-inputs";
import images, { type CardImages } from "react-payment-inputs/images";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useReceivePayment } from "@/hooks/hotel/useReceivePayment";
import LoadingSpinner from "@/shared/icons/Spinner";

interface AddPaymentModalProps {
  hotelToken: string | undefined;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

interface CardData {
  cardNumber: string;
  cardHolder: string;
  expiredDate: string;
  cvv: string;
}

const AddPaymentModal: FC<AddPaymentModalProps> = ({
  hotelToken,
  isModalOpen,
  setIsModalOpen,
}) => {
  const { addNewCard } = useReceivePayment();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState();
  const [userCardData, setUserCardData] = useState<CardData>({
    cardNumber: "",
    cardHolder: "",
    expiredDate: "",
    cvv: "",
  });
  const { meta, getCardNumberProps, getCardImageProps } = usePaymentInputs();

  function closeModal() {
    setIsModalOpen(false);
    setLoading(false);
    setUserCardData({
      cardNumber: "",
      cardHolder: "",
      expiredDate: "",
      cvv: "",
    });
  }

  const handleCardNumberChange = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, "");
    value = value.replace(/(.{4})/g, "$1 ");

    if (value.length > 19) {
      value = value.slice(0, 19);
    }

    setUserCardData((prevState) => ({
      ...prevState,
      cardNumber: value.trim(),
    }));
  };

  const handleExpiryDateChange = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, "");
    if (value.length > 4) {
      value = value.slice(0, 4);
    }

    if (value.length >= 2) {
      value = value.slice(0, 2) + "/" + value.slice(2);
    }

    setUserCardData((prevState) => ({
      ...prevState,
      expiredDate: value,
    }));
  };

  const handleCVCChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 3) {
      setUserCardData((prevState) => ({
        ...prevState,
        cvv: value,
      }));
    }
  };

  const buttonDisabled = Object.values(userCardData).every(
    (value) => value !== ""
  );

  const handlePayment = async () => {
    const [expiryMonth, expiryYear] = userCardData.expiredDate.split("/");

    const modifiedCardData = {
      cardNumber: Number(userCardData.cardNumber.replace(/\s/g, "")),
      cardHolder: userCardData.cardHolder,
      cvv: userCardData.cvv,
      expiryMonth: Number(expiryMonth),
      expiryYear: Number(expiryYear),
    };

    const result = await addNewCard(
      hotelToken,
      modifiedCardData,
      setLoading,
      closeModal
    );

    setData(result);
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent onInteractOutside={closeModal}>
        <div className="flex flex-col gap-2">
          <div
            className="flex size-11 shrink-0 items-center justify-center rounded-full border"
            aria-hidden="true"
          >
            <WalletIcon className="opacity-80" size={16} />
          </div>
          <DialogHeader>
            <DialogTitle className="text-left">Yeni kart ekle</DialogTitle>
            <DialogDescription className="text-left">
              Yeni bir ödeme yöntemi ekleyin.
            </DialogDescription>
          </DialogHeader>
        </div>

        <div className="space-y-5">
          <div className="space-y-4">
            <div>
              <Label>Kart Üzerindeki İsim</Label>
              <Input
                type="text"
                placeholder="Kart Üzerindeki İsim"
                value={userCardData.cardHolder}
                onChange={(e) =>
                  setUserCardData((prevState) => ({
                    ...prevState,
                    cardHolder: e.target.value,
                  }))
                }
              />
            </div>
            <div>
              <Label>Kart Numarası</Label>
              <div className="relative">
                <Input
                  {...getCardNumberProps({
                    onChange: (e: any) => {
                      handleCardNumberChange(e);
                    },
                  })}
                  placeholder="1234 5678 9012 3456"
                  value={userCardData.cardNumber}
                  className="peer pe-9 [direction:inherit]"
                />
                <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 peer-disabled:opacity-50">
                  {meta.cardType ? (
                    <svg
                      className="overflow-hidden rounded-sm"
                      {...getCardImageProps({
                        images: images as unknown as CardImages,
                      })}
                      width={20}
                    />
                  ) : (
                    <CreditCardIcon size={16} aria-hidden="true" />
                  )}
                </div>
              </div>
            </div>
            <div className="flex gap-4">
              <div className="flex-1 space-y-2">
                <Label>Son Kullanma Tarihi</Label>
                <Input
                  placeholder="Ay/Yıl"
                  value={userCardData.expiredDate}
                  onChange={handleExpiryDateChange}
                  className="[direction:inherit]"
                />
              </div>
              <div className="flex-1 space-y-2">
                <Label>CVV/CVC</Label>
                <Input
                  placeholder="cvv/cvc"
                  value={userCardData.cvv}
                  onChange={handleCVCChange}
                  className="[direction:inherit]"
                />
              </div>
            </div>
          </div>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={() => {
              handlePayment();
              setLoading(true);
            }}
            disabled={!buttonDisabled || loading ? true : false}
          >
            {loading ? <LoadingSpinner /> : "Kaydet"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddPaymentModal;
