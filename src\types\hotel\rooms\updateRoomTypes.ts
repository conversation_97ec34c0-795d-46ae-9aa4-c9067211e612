import type { ChangeEvent, KeyboardEvent } from "react";
import type {
  RoomGroupDataApiTypes,
  RoomImage,
} from "@/types/hotel/roomGroupType";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

export interface updateRoomInputsTypes {
  roomGroupName: string;
  roomCapacity: number | string;
  petType: string[];
  roomImages: RoomImage[];
  roomDescription: string;
  roomFeatures: string[];
}
export interface UpdateRoomInputsProps {
  updateRoomInputs: updateRoomInputsTypes;
  roomFeatureNames: string;
  handlePetTypeCheckboxChange: (event: ChangeEvent<HTMLInputElement>) => void;
  handleChange: (event: ChangeEvent<HTMLInputElement>) => void;
  hotelFeaturesRemoveHandler: (index: number) => void;
  roomFeaturesHandler: (event: KeyboardEvent<HTMLInputElement>) => void;
  roomFeaturesNameHandler: (event: ChangeEvent<HTMLInputElement>) => void;
  textAreaHandleChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  handleRoomFeatures: () => void;
  acceptedPetTypes: string[] | [];
}

export interface UpdateRoomModalProps {
  hotel: HotelDataApiTypes;
  hotelToken: string | undefined;
  roomGroup: RoomGroupDataApiTypes;
  acceptedPetTypes: string[] | [];
}

export interface RoomPhotosProps {
  loading: boolean;
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  initialInputData: updateRoomInputsTypes;
  newImage: any;
  setNewImage: any;
  hotelToken: string | undefined;
}
