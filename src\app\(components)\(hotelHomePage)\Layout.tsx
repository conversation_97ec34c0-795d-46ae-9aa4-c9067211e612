"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import PawBookingPartnerHomepage from "@/app/(homePage)/(components)/homeComponent";
import { PricingSection } from "@/app/(components)/(hotelHomePage)/pricing";

const HomePageLayout = ({ mobile }: { mobile: any }) => {
  const router = useRouter();

  useEffect(() => {
    if (mobile) {
      router.push("/login");
    }
  }, [mobile, router]);

  if (mobile) return null;

  return (
    <div>
      <div className="absolute inset-0 -z-10">
        <div className="h-full w-full bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:35px_35px] opacity-30 [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
      </div>
      <PawBookingPartnerHomepage />
      {/*<Hero />*/}
      {/* <MarqueePartnerHotel /> */}
      {/*<Grid />*/}
      {/*<HeroScroll />*/}
      <PricingSection />
      {/*/!* <AnimatedTestimonialsHotel /> *!/*/}
      {/*<FaqSection />*/}
    </div>
  );
};

export default HomePageLayout;
