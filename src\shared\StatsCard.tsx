import React from "react";
import type { FC } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import IconMinus from "@/shared/icons/Minus";
import IconCalendar from "@/shared/icons/Calendar";
import IconChart from "@/shared/icons/Chart";
import IconPawGuest from "@/shared/icons/PawGuest";
import IconCancel from "@/shared/icons/Cancel";
import IconChevronUp from "./icons/ChevronUp";
import IconChevronDown from "./icons/ChevronDown";

interface StatsCardProps {
  title: string;
  todayValue: any;
  yesterdayValue?: string | number;
  percentageChange?: string;
  totalRoom?: any;
  icon: string;
  cardColor: string;
}

const StatsCard: FC<StatsCardProps> = ({
  title,
  todayValue,
  yesterdayValue,
  percentageChange,
  totalRoom,
  icon,
  cardColor,
}) => {
  const iconHandler = (iconName: string) => {
    if (iconName === "IconCalendar") {
      return IconCalendar;
    } else if (iconName === "IconChart") {
      return IconChart;
    } else if (iconName === "IconPawGuest") {
      return IconPawGuest;
    } else {
      return IconCancel;
    }
  };

  const StatCardIcon = iconHandler(icon);

  const cardColorHandler = (color: string) => {
    if (color === "blue") {
      return "#7ba3e9";
    } else if (color === "green") {
      return "#0D9488";
    } else if (color === "gray") {
      return "#6b7280";
    } else if (color === "red") {
      return "#ee3939";
    } else {
      return "#facc15";
    }
  };

  const cardBorderColor = cardColorHandler(cardColor);

  return (
    <Card style={{ borderColor: cardBorderColor }} className="px-3 pb-1 border">
      <CardHeader className="px-2 pt-2 pb-0">
        <CardTitle className="flex items-center justify-between gap-1.5 text-xs sm:text-sm font-medium text-neutral-500 dark:text-neutral-400 lg:text-base">
          {title}
          <div
            style={{ backgroundColor: cardBorderColor }}
            className="rounded-md p-1"
          >
            <StatCardIcon className="size-5 text-white" fill="currentColor" />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="text-start font-medium lg:text-xl pt-0 pb-1 px-2">
        <p>
          {todayValue || parseInt(todayValue, 10) >= 0 ? `${todayValue}` : ""}
          {totalRoom && `/${totalRoom}`}
        </p>
        <div className="flex items-center gap-1 pt-2">
          {!percentageChange ? (
            <IconMinus className="size-6 text-gray-400" />
          ) : (
            <div
              className={`${
                percentageChange.includes("-")
                  ? "text-white bg-[#ee3939]"
                  : "text-[#14a23e] bg-[#e6f5eb]"
              } font-medium px-1.5 py-0.5 text-xs md:text-sm rounded-xl w-16`}
            >
              <div className="flex items-center justify-center gap-0.5">
                <span className="text-xs md:text-sm">%</span>
                {parseFloat(percentageChange.replace("-", ""))}
                {percentageChange.includes("-") ? (
                  <IconChevronDown className="size-3" strokeWidth={3.5} />
                ) : (
                  <IconChevronUp className="size-3" strokeWidth={3.5} />
                )}
              </div>
            </div>
          )}
          {yesterdayValue && (
            <p className="text-xs font-medium">
              (Dün: <span className="text-amber-600">{yesterdayValue}</span> )
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StatsCard;
