"use client";
import React, { useState } from "react";
import type { FC } from "react";
import Link from "next/link";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import finishOrder from "@/actions/(protected)/hotel/finishOrder";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import type { Route } from "@/routers/types";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";

interface FooterLoggedButtonsProps {
  orderData: any;
  buttonValue: number;
  hotelToken: string | undefined;
  reservationPetData: any;
  servicePetData: any;
}

const FooterLoggedInButtons: FC<FooterLoggedButtonsProps> = ({
  orderData,
  buttonValue,
  hotelToken,
  reservationPetData,
  servicePetData,
}) => {
  const { toast } = useToast();
  const { updateReservation, updateService, updateOrder } =
    useHotelAtDoorReservation();
  const [loading, setLoading] = useState<boolean>(false);
  const [secondLoading, setSecondLoading] = useState<boolean>(false);
  const [backLoading, setBackLoading] = useState<boolean>(false);
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );
  const router = useRouter();

  const loadingHandler = () => {
    setSecondLoading(true);

    setTimeout(() => {
      setSecondLoading(false);
    }, 800);
  };

  const backLoadingHandler = () => {
    setBackLoading(true);

    setTimeout(() => {
      setBackLoading(false);
    }, 800);
  };

  const backButtonHandler = () => {
    if (buttonValue === 2) return `/hotel/account/at-door-reservation`;
    if (buttonValue === 3)
      return `/hotel/account/at-door-reservation?orderId=${orderData?._id}&customerStep=true`;
    if (buttonValue === 4)
      return `/hotel/account/at-door-reservation?orderId=${orderData?._id}&petStep=true`;
    if (buttonValue === 5)
      return `/hotel/account/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`;
    return `/hotel/account/at-door-reservation`;
  };
  const backButtonValue = backButtonHandler();

  const prepareReservationData = () =>
    orderData?.reservations.map((reservation: any) => ({
      reservationId: reservation?._id,
      data: {
        hotelCustomer: checkoutData.hotelCustomer,
      },
    }));

  const prepareServiceData = () =>
    orderData?.services.map((service: any) => ({
      serviceId: service?._id,
      data: {
        hotelCustomer: checkoutData.hotelCustomer,
      },
    }));

  const handleUpdateReservation = async () => {
    const reservationUpdates = prepareReservationData();

    if (reservationUpdates) {
      await updateReservation({
        updates: reservationUpdates,
        setLoading,
        hotelToken,
        route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&petStep=true`,
      });
    }
  };

  const handleUpdateService = async () => {
    const serviceUpdates = prepareServiceData();
    if (serviceUpdates) {
      await updateService({
        updates: serviceUpdates,
        setLoading,
        hotelToken,
        route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&petStep=true`,
      });
    }
  };

  const handleUpdateOrder = async () => {
    await updateOrder({
      hotelToken,
      requestBody: {
        orderId: orderData?._id,
        data: {
          hotelCustomer: checkoutData.hotelCustomer,
        },
      },
      setLoading,
      route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&petStep=true`,
    });
  };

  const handleFinishOrder = async () => {
    setLoading(true);

    try {
      const result = await finishOrder(orderData?._id, {
        channel: checkoutData.channel,
        paymentType: checkoutData.paymentType,
      });

      if (result?.success) {
        setLoading(false);
        router.push("/hotel/account");
        toast({
          variant: "success",
          duration: 6000,
          title: "Rezervasyon Oluşturma",
          description: "Rezervasyon başarılı bir şekilde oluşturuldu.",
        });
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error("An error occurred while finishing the order:", error);
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2 sm:gap-5">
      {buttonValue > 1 && (
        <Link href={backButtonValue as Route}>
          <ButtonSecondary disabled={backLoading} onClick={backLoadingHandler}>
            {backLoading ? <LoadingSpinner /> : "Geri"}
          </ButtonSecondary>
        </Link>
      )}
      {buttonValue === 2 && (
        <ButtonPrimary
          disabled={loading || !checkoutData.hotelCustomer}
          onClick={() => {
            handleUpdateOrder();
            orderData?.reservations?.length > 0 && handleUpdateReservation();
            orderData?.services?.length > 0 && handleUpdateService();
          }}
        >
          {loading ? <LoadingSpinner /> : "Devam Et"}
        </ButtonPrimary>
      )}
      {buttonValue === 3 &&
        orderData?.reservations?.length > 0 &&
        orderData?.services?.length === 0 && (
          <ButtonPrimary
            onClick={() =>
              updateReservation({
                updates: reservationPetData,
                setLoading,
                hotelToken,
                route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              })
            }
            disabled={
              !(checkoutData.pet.length === orderData?.reservations?.length) ||
              loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 3 &&
        orderData?.services?.length > 0 &&
        orderData?.reservations?.length === 0 && (
          <ButtonPrimary
            onClick={() =>
              updateService({
                updates: servicePetData,
                setLoading,
                hotelToken,
                route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              })
            }
            disabled={
              !(
                checkoutData.servicePet.filter(Boolean).length ===
                orderData?.services?.length
              ) || loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 3 &&
        orderData?.services?.length > 0 &&
        orderData?.reservations?.length > 0 && (
          <ButtonPrimary
            onClick={() => {
              updateReservation({
                updates: reservationPetData,
                setLoading,
                hotelToken,
                route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              });
              updateService({
                updates: servicePetData,
                setLoading,
                hotelToken,
                route: `/hotel/account/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              });
            }}
            disabled={
              !(
                checkoutData.pet.length === orderData?.reservations?.length &&
                checkoutData.servicePet.filter(Boolean).length ===
                  orderData?.services?.length
              ) || loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 4 && (
        <Link
          href={`/hotel/account/at-door-reservation?orderId=${orderData?._id}&paymentStep=true`}
        >
          <ButtonPrimary disabled={secondLoading} onClick={loadingHandler}>
            {secondLoading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        </Link>
      )}
      {buttonValue === 5 && (
        <ButtonPrimary
          disabled={
            checkoutData.channel === "" ||
            checkoutData.paymentType === "" ||
            loading
          }
          onClick={handleFinishOrder}
        >
          {loading ? <LoadingSpinner /> : "Oluştur"}
        </ButtonPrimary>
      )}
    </div>
  );
};

export default FooterLoggedInButtons;
