import * as React from "react";
import { Section, Row, Text, Hr, Column } from "@react-email/components";
import Layout from "emails/_components/Layout";

const NewPetOwnerWelcome = () => {
  return (
    <Layout>
      <Section style={{ marginTop: 16, marginBottom: 16, padding: 16 }}>
        <Section>
          <Row>
            <Text
              style={{
                margin: "0px",
                fontSize: 24,
                lineHeight: "32px",
                fontWeight: 600,
                color: "rgb(17,24,39)",
              }}
            >
              Hoş Geldiniz, PawBooking Dünyasına <PERSON>din<PERSON> !
            </Text>
            <Text
              style={{
                marginTop: 8,
                fontSize: 16,
                lineHeight: "24px",
                color: "rgb(107,114,128)",
              }}
            >
              Tebrikler! Artık PawBooking platformunda yerinizi aldınız. İşte
              harika bir deneyim yaşamak için atmanız gereken adımlar:
            </Text>
          </Row>
        </Section>
        <Hr
          style={{
            marginTop: 24,
            marginBottom: 24,
            borderColor: "rgb(209,213,219)",
          }}
        />

        {[
          "Profilinizi <PERSON>",
          "<PERSON><PERSON><PERSON><PERSON>kley<PERSON>",
          "Pet Otellerini Keşfedin",
          "Rezervasyon Yapın",
        ].map((step, index) => (
          <Section
            key={index}
            style={{ display: "flex", alignItems: "center", width: "100%" }}
          >
            <Row>
              <Column style={{ verticalAlign: "baseline" }}>
                <table
                  style={{
                    display: "flex",
                    width: "100%",
                    alignItems: "center",
                    gap: "12px;",
                  }}
                >
                  <td
                    align="center"
                    style={{
                      height: 40,
                      width: 40,
                      backgroundColor: "",
                      borderRadius: 9999,
                      padding: "0px",
                      marginRight: "10px",
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: 600,
                        margin: "0px",
                        color: "rgb(255, 255, 255)",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      🐾
                    </Text>
                  </td>
                </table>
              </Column>
              <Column style={{ width: "90%" }}>
                <Text
                  style={{
                    margin: "0px",
                    fontSize: 20,
                    lineHeight: "28px",
                    fontWeight: 600,
                    color: "rgb(17,24,39)",
                  }}
                >
                  {step}
                </Text>
                <Text
                  style={{
                    margin: "0px",
                    marginTop: 8,
                    fontSize: 16,
                    lineHeight: "24px",
                    color: "rgb(107,114,128)",
                  }}
                >
                  {index === 0
                    ? "Kişisel bilgilerinizi eksiksiz doldurun. Fotoğraf ekleyerek profilinizi daha güvenilir hale getirin."
                    : index === 1
                      ? "Evcil hayvanınızın bilgilerini ekleyin. Tür, yaş, sağlık bilgileri gibi detaylarla profilinizi tamamlayın."
                      : index === 2
                        ? "Size en uygun pet otellerini keşfedin. Konum, hizmetler ve müşteri yorumlarını inceleyerek seçim yapın."
                        : "Hemen rezervasyon yaparak evcil hayvanınıza harika bir konaklama deneyimi sunun!"}
                </Text>
              </Column>
            </Row>
            <Hr
              style={{
                marginTop: 24,
                marginBottom: 24,
                borderColor: "rgb(209,213,219)",
              }}
            />
          </Section>
        ))}

        <Section>
          <Row>
            <Text
              style={{
                margin: "0px",
                fontSize: 16,
                lineHeight: "24px",
                color: "rgb(107,114,128)",
              }}
            >
              Daha fazla bilgiye ihtiyacınız olursa, destek ekibimizle her zaman
              iletişime geçebilirsiniz. Mutlu rezervasyonlar dileriz!
            </Text>
          </Row>
        </Section>
      </Section>
    </Layout>
  );
};

export default NewPetOwnerWelcome;
