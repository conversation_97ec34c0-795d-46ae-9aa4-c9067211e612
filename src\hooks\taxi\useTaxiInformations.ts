import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import {
  ImageResizeFitType,
  uploadMultiToS3,
  uploadSingleToS3,
} from "@/lib/s3BucketHelper";
import { PET_TAXI_API_PATHS, S3_API_PATHS } from "@/utils/apiUrls";
import type { PetTaxiAddressApiTypes } from "@/types/taxi/taxiInformationType";
import { useRouter } from "next/navigation";

export const usePetTaxiInformationsActions = () => {
  const { toast } = useToast();
  const router = useRouter();

  // UPDATES PetTaxi INFORMATIONS
  const handlePetTaxiInformations = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    PetTaxiInformations: {
      petTaxiName: string;
      petTaxiDescription: string;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            petTaxiName: PetTaxiInformations.petTaxiName,
            petTaxiDescription: PetTaxiInformations.petTaxiDescription,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Bilgi Güncelleme",
          description: "Taksi bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES PetTaxi ADDRESS
  const handlePetTaxiAddress = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    petTaxiAddress: PetTaxiAddressApiTypes,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            address: {
              cityName: petTaxiAddress.cityName,
              region: petTaxiAddress.region,
              district: petTaxiAddress.district,
              streetName: petTaxiAddress.streetName,
              buildingName: petTaxiAddress.buildingName,
              buildingNumber: petTaxiAddress.buildingNumber,
              postalZone: petTaxiAddress.postalZone,
            },
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Adres Güncelleme",
          description: "Adres bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const addPetTaxiMap = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    petTaxiMap: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    method: string,
    resetInputs?: () => void
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: method,
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            googleMapUrl: petTaxiMap,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          if (resetInputs) {
            resetInputs();
          }
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Harita Ekleme",
          description: "Harita başarıyla eklendi.",
        });
        if (resetInputs) {
          resetInputs();
        }
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES PetTaxi ACCEPTED PET TYPES
  const handlePetTaxiPetTypes = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    acceptedPetTypes: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            acceptedPetTypes: acceptedPetTypes,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Evcil Hayvan Tür Güncelleme",
          description: "Tür bilgileri başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES PetTaxi CARE SERVICES
  const handlePetTaxiCareServices = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    careServices: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            careServices: careServices,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Bakım Hizmetleri Güncelleme",
          description: "Bakım hizmetleri başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES PetTaxi FEATURES
  const handlePetTaxiFeatures = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    petTaxiFeatures: string[],
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            petTaxiFeatures: petTaxiFeatures,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Taksi Özellikleri Güncelleme",
          description: "Taksi Özellikleri başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES PetTaxi PHOTOS
  const uploadPhotoHandler = async (
    petTaxiImageArray: string[],
    petTaxiToken: string | undefined
  ) => {
    if (petTaxiToken) {
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            images: petTaxiImageArray,
          }),
        });

        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorData = await response.json();
          const errorMessage = errorData.error;
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: errorMessage || "Bilinmeyen bir hata oluştu",
          });
          throw new Error("Network response was not ok");
        }

        if (data.success) {
          toast({
            variant: "success",
            duration: 3500,
            title: "Fotoğraf Değiştirme",
            description: "Fotoğraf güncellendi.",
          });
          revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // DELETES SELECTED PHOTO
  const deletePetTaxiPhoto = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    imageId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeletePhotoIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    revalitePath: string,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.imageUpload}/deleteImage?imageId=${imageId}`,
          {
            method: "DELETE",
            headers: {
              petTaxiToken: petTaxiToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeletePhotoIsOpen(false);
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Fotoğraf Silme",
          description: "Fotoğraf başarıyla silindi.",
        });
        revalidatePathHandler(revalitePath);
        setDeletePhotoIsOpen(false);
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPLOADS IMAGES TO S3
  const photoInputHandler = async (
    photoFileObject: FileList | null,
    petTaxiToken: string | undefined,
    petTaxiId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setImage: React.Dispatch<React.SetStateAction<string[] | []>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<React.SetStateAction<FileList | null>>
  ) => {
    if (photoFileObject && petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await uploadMultiToS3(
          photoFileObject,
          `petTaxi/${petTaxiId}/photos/`,
          petTaxiToken,
          ImageResizeFitType.fill
        );

        if (response.success) {
          setLoading(false);
          setImage([]);
          setPhotoFileObject(null);
          setTimeout(() => {
            setDisabled(false);
          }, 1000);
          const idsArray = response.data.map((item: any) => item._id);
          uploadPhotoHandler(idsArray, petTaxiToken);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  const putPetTaxiFiles = async (
    files: string[],
    petTaxiToken: string | undefined
  ) => {
    if (petTaxiToken) {
      try {
        const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            files,
          }),
        });

        const data = await response.json();

        if (data.success) {
          revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        }
      } catch (error) {
        console.error("Error updating files:", error);
      }
    }
  };

  const uploadFileHandler = async (
    event: FormEvent,
    files: File[] | FileList | null,
    petTaxiToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    filePath: string,
    doctype: string,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (files && files?.length > 0 && petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      let filesArray: FileList | null = null;

      if (files instanceof FileList) {
        filesArray = files;
      } else if (Array.isArray(files)) {
        const dataTransfer = new DataTransfer();
        files.forEach((file) => dataTransfer.items.add(file));
        filesArray = dataTransfer.files;
      }

      if (filesArray && filesArray.length > 0) {
        try {
          const response = await uploadMultiToS3(
            filesArray,
            filePath,
            petTaxiToken,
            undefined,
            doctype
          );

          if (response.success) {
            toast({
              variant: "success",
              duration: 3500,
              title: "Dosya Yükleme",
              description: "Dosyalar başarıyla yüklendi.",
            });

            const idsArray = response.data.map((item: any) => item._id);
            await putPetTaxiFiles(idsArray, petTaxiToken);
            setLoading(false);
            setTimeout(() => {
              setDisabled(false);
            }, 3000);
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          } else {
            toast({
              variant: "error",
              duration: 3500,
              title: "Hata",
              description: "Dosya yükleme sırasında bir hata oluştu.",
            });
            setLoading(false);
            setDisabled(false);
          }
        } catch (error) {
          console.error("Dosya yükleme hatası:", error);
        }
      }
    }
  };

  // DELETES SELECTED TAXI FILE
  const deletePetTaxiFileHandler = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    fileId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDeleteFileIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${S3_API_PATHS.fileUpload}/deleteFile?fileId=${fileId}`,
          {
            method: "DELETE",
            headers: {
              petTaxiToken: petTaxiToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDeleteFileIsOpen(false);
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Dosya Kaldırma",
          description: "Dosya başarıyla kaldırıldı.",
        });
        revalidatePathHandler(
          "/petTaxi/account/petTaxi-informations/petTaxi-files"
        );
        setDeleteFileIsOpen(false);
        setLoading(false);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  // Taksi logosu yükleme işleyicisi
  const petTaxiLogoHandler = async (
    event: FormEvent,
    file: File | null | undefined,
    petTaxiId: string | undefined,
    petTaxiToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<
      React.SetStateAction<File | null | undefined>
    >,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (file && petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await uploadSingleToS3(
          file,
          `petTaxi/${petTaxiId}/logo/`,
          ImageResizeFitType.fill,
          petTaxiToken
        );

        if (response.success) {
          const logoId = response.data[0]._id;
          await updatePetTaxiLogo(logoId, petTaxiToken);
          toast({
            variant: "success",
            duration: 3500,
            title: "Logo Yükleme",
            description: "Taksi logosu başarıyla güncellendi.",
          });
          revalidatePathHandler("/petTaxi/account/petTaxi-informations");
        } else {
          setLoading(false);
          setDisabled(false);
          setPhotoFileObject(null);
          throw new Error("Logo yükleme başarısız oldu.");
        }
      } catch (error) {
        console.error("Logo yükleme hatası:", error);
        toast({
          variant: "error",
          duration: 3500,
          title: "Hata",
          description: "Logo yükleme sırasında bir hata oluştu.",
        });
      } finally {
        setLoading(false);
        setPhotoFileObject(null);
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      }
    }
  };

  // Taksi logosunu güncelleme
  const updatePetTaxiLogo = async (logoId: string, petTaxiToken: string) => {
    try {
      const response = await fetch(PET_TAXI_API_PATHS.myPetTaxi, {
        method: "POST",
        headers: {
          petTaxiToken: petTaxiToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          logo: logoId,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Logo güncelleme hatası:", error);
      throw error;
    }
  };

  // UPDATES PetTaxi STATUS
  const handlePetTaxiStatusUpdate = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    newStatus: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.updatePetTaxiStatus, {
          method: "PUT",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            newStatus,
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        router.push("/petTaxi/account");
        revalidatePathHandler("/petTaxi/account");
        setLoading(false);
      } catch (error) {
        console.error("Error updating petTaxi status:", error);
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: "Taksi durumu güncellenirken bir hata oluştu.",
        });
      }
    }
  };

  return {
    handlePetTaxiInformations,
    handlePetTaxiAddress,
    handlePetTaxiPetTypes,
    handlePetTaxiCareServices,
    handlePetTaxiFeatures,
    photoInputHandler,
    uploadFileHandler,
    petTaxiLogoHandler,
    deletePetTaxiPhoto,
    deletePetTaxiFileHandler,
    handlePetTaxiStatusUpdate,
    addPetTaxiMap,
  };
};
