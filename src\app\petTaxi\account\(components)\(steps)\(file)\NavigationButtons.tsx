"use client";
import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface NavigationButtonsProps {
  currentStep: number;
  filteredStepsLength: number;
  completedSteps: Set<number>;
  isTransitioning: boolean;
  onPrevStep: () => void;
  onNextStep: () => void;
}

const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  currentStep,
  filteredStepsLength,
  completedSteps,
  isTransitioning,
  onPrevStep,
  onNextStep,
}) => {
  return (
    <div className="flex justify-between mt-6">
      {currentStep > 0 ? (
        <Button
          variant="outline"
          onClick={onPrevStep}
          disabled={isTransitioning}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="w-4 h-4" />
          Önceki
        </Button>
      ) : (
        <div></div>
      )}

      <Button
        onClick={onNextStep}
        disabled={
          currentStep === filteredStepsLength - 1 ||
          isTransitioning ||
          !completedSteps.has(currentStep)
        }
        className="bg-secondary-6000 hover:bg-secondary-700 flex items-center gap-2"
      >
        Sonraki
        <ChevronRight className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default NavigationButtons;
