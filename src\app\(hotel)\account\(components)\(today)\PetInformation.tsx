import React from "react";
import type { FC } from "react";
import Image from "next/image";
import catAvatar from "@/images/cat-avatar.png";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface PetInformationProps {
  selectedRoom: any;
}

const PetInformation: FC<PetInformationProps> = ({ selectedRoom }) => {
  return (
    <div className="my-4">
      <div className="flex justify-center items-center gap-1">
        <div className="rounded-full border bg-amber-400 p-3">
          <Image
            src={catAvatar}
            width={50}
            height={50}
            alt="catPhoto"
            className="rounded-full object-cover"
          />
        </div>
        <div className="flex flex-col items-center justify-center">
          <div className="text-xl font-medium">
            {selectedRoom?.reservation?.pet?.name}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 my-7">
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Yaş</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.age}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Tür</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.kind}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Cins</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.breed}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Renk</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.color}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Cinsiyet</div>
          </div>
          <div>
            <div className="text-sm capitalize">
              {selectedRoom?.reservation?.pet?.gender === "male"
                ? "Erkek"
                : "Dişi"}
            </div>
          </div>
        </div>

        <div className="flex flex-col items-center rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Çip Numarası</div>
          </div>
          <div>
            <div className="text-sm">
              {selectedRoom?.reservation?.pet?.microChipNumber}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">İç Parazit</div>
          </div>
          <div>
            <div className="whitespace-nowrap text-sm">
              {selectedRoom?.reservation?.pet?.internalParasiteTreatment ===
              true
                ? `Evet - ${new Date(selectedRoom?.reservation?.pet?.internalParasiteTreatmentDate).toLocaleDateString()}`
                : "Hayır"}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Dış Parazit</div>
          </div>
          <div>
            <div className="whitespace-nowrap text-sm">
              {selectedRoom?.reservation?.pet?.externalParasiteTreatment ===
              true
                ? `Evet - ${new Date(selectedRoom?.reservation?.pet?.externalParasiteTreatmentDate).toLocaleDateString()}`
                : "Hayır"}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center rounded-lg border py-2">
          <div>
            <div className="text-sm font-semibold">Kısırlaştırılmış</div>
          </div>
          <div>
            <div className="text-sm">
              {selectedRoom?.reservation?.pet?.infertile === true
                ? "Evet"
                : "Hayır"}
            </div>
          </div>
        </div>
      </div>
      <Accordion type="multiple">
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-sm">Aşılar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.vaccines?.length > 0 ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">Aşı</div>
                    <div className="font-medium">Tarihi</div>
                  </div>
                  <div className="space-y-2">
                    {selectedRoom.reservation.pet.vaccines.map(
                      (vaccine: any) => {
                        const formattedDate = formatDateToDayMonthYear(
                          vaccine.treatmentDate.split("T")[0]
                        );
                        return (
                          <div
                            key={vaccine._id}
                            className="flex items-center justify-between capitalize"
                          >
                            <div className="mr-4">{vaccine.vaccineName}</div>
                            <div>{formattedDate}</div>
                          </div>
                        );
                      }
                    )}
                  </div>
                </div>
              ) : (
                <div>Aşı bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger className="text-sm">Alışkanlıklar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.specified?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom.reservation?.pet?.specified}
                </div>
              ) : (
                <div>Alışkanlık bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger className="text-sm">Alerjiler</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.allergicTo?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom.reservation.pet.allergicTo.map(
                    (allergy: string, index: number) => (
                      <div className="capitalize" key={index}>
                        {allergy}
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div>Alerji bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger className="text-sm">Hastalıklar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.hereditaryDiseases?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom.reservation.pet.hereditaryDiseases.map(
                    (hereditaryDiseases: string, index: number) => (
                      <div className="capitalize" key={index}>
                        {hereditaryDiseases}
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div>Alerji bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-5">
          <AccordionTrigger className="text-sm">İlaçlar</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.medicine?.length > 0 ? (
                <div className="capitalize">
                  {selectedRoom.reservation?.pet?.medicine}
                </div>
              ) : (
                <div>İlaç bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-6">
          <AccordionTrigger className="text-sm">
            Beslenme Alışkanlıkları
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.feedingHabits?.length > 0 ? (
                <div>{selectedRoom.reservation?.pet?.feedingHabits}</div>
              ) : (
                <div>Beslenme Alışkanlığı bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-7">
          <AccordionTrigger className="text-sm">Açıklama</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              {selectedRoom.reservation?.pet?.description?.length > 0 ? (
                <div className="space-y-2">
                  {selectedRoom.reservation?.pet?.description}
                </div>
              ) : (
                <div>Açıklama bilgisi bulunamadı.</div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default PetInformation;
