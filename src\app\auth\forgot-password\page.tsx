import React from "react";
import ForgotPasswordContainer from "./(components)/ForgotPasswordContainer";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "PawBooking Şifremi Unuttum | PawBooking Pet Booking System",
};

const ForgotPassword = ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  return (
    <>
      <ForgotPasswordContainer role={searchParams?.role} />
    </>
  );
};

export default ForgotPassword;
