{
  "root": true,
  "env": {
    "node": true,
    "browser": true,
    "es6": true,
    "serviceworker": true,
    "worker": true
  },
  "parser": "@typescript-eslint/parser",
  "extends": [
    "next/core-web-vitals",
    "eslint:recommended",
    "plugin:@typescript-eslint/strict",
    "plugin:@typescript-eslint/stylistic",
    // "plugin:tailwindcss/recommended",
    "plugin:compat/recommended",
    "plugin:prettier/recommended",
    "prettier"
  ],
  "plugins": ["compat", "prettier", "@typescript-eslint"],
  "parserOptions": {
    "projectService": true
  },
  "rules": {
    "linebreak-style": [2, "unix", "windows"],
    "semi": [2, "always"],
    "no-multiple-empty-lines": [
      2,
      {
        "max": 1
      }
    ],
    "react/display-name": 0,
    "@next/next/no-img-element": [0],
    "jsx-a11y/alt-text": [1],
    "no-unused-vars": 0,
    "@typescript-eslint/no-unused-vars": 1,
    "no-useless-catch": 1,
    "@typescript-eslint/no-non-null-assertion": 1,
    "@typescript-eslint/no-explicit-any": 1,
    "@typescript-eslint/consistent-type-exports": 2,
    "@typescript-eslint/consistent-type-imports": 2
  }
}
