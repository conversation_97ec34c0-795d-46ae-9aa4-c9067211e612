"use client";
import React, { useState } from "react";
import ShimmerButton from "@/components/ui/shimmer-button";

export default function ShimmerButtonComponent({
  children,
}: {
  children?: React.ReactNode;
}) {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div className="w-full">
      <ShimmerButton
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        shimmerSize="0.15em"
        background={`${isHovered ? "#dc3309" : "#F63C07"}`}
        className="shadow-md duration-700 lg:min-w-full"
      >
        <span className="whitespace-pre-wrap text-center text-sm font-medium tracking-tight text-white dark:from-white dark:to-slate-900/10 lg:text-base">
          {children || `This is Button`}
        </span>
      </ShimmerButton>
    </div>
  );
}
