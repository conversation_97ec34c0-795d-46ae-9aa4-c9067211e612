"use client";
import React, { useEffect, useRef } from "react";
import type { FC } from "react";
import PetOwnerDetail from "./PetOwnerDetail";
import HotelDetail from "./HotelDetail";

interface ContactDetailContainerProps {
  contactDetail: any;
}

const ContactDetailContainer: FC<ContactDetailContainerProps> = ({
  contactDetail,
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
  }, []);
  return (
    <div
      ref={scrollRef}
      className="max-lg:invisible lg:visible duration-300 max-lg:basis-0 lg:basis-2/6 h-[calc(100vh)] sm:h-[calc(100vh-200px)] overflow-auto hiddenScrollbar"
    >
      {contactDetail?.type === "petOwner" ? (
        <PetOwnerDetail contactDetail={contactDetail?.data} />
      ) : (
        <HotelDetail contactDetail={contactDetail?.data} />
      )}
    </div>
  );
};

export default ContactDetailContainer;
