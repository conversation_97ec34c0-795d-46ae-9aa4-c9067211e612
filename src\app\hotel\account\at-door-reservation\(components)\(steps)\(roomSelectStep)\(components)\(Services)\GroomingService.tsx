"use client";
import React, { useState } from "react";
import type { FC, ChangeEvent } from "react";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { format } from "date-fns";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { Calendar } from "@/components/ui/calendar";
import { createLocalDate } from "@/utils/createLocalDate";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";
import { Separator } from "@/components/ui/separator";

interface GroomingServiceProps {
  service: any;
  hotelToken: string | undefined;
  hotelId: string;
  onClose?: () => void;
}

const GroomingService: FC<GroomingServiceProps> = ({
  service,
  hotelToken,
  hotelId,
  onClose,
}) => {
  const { addItemToCart } = useHotelAtDoorReservation();
  const [disabled, setDisabled] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({
    note: "",
    serviceDate: "",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const today = format(new Date(), "yyyy-MM-dd");
  const calendarLanguage = datePickerLanguageHandler("tr");
  const monthFormatter = new Intl.DateTimeFormat(
    calendarLanguage.firstValue === "tr" ? "tr" : "en",
    {
      month: "long",
    }
  );

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const handleChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormData((prevState: any) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const buttonDisabledHandler = () => {
    if (formData.serviceDate.trim() === "") {
      return { errorText: "", disabled: true };
    }

    const isValidDate = new Date(formData.serviceDate) >= new Date(today);
    if (!isValidDate) {
      return {
        errorText:
          "Hizmet tarihi boş bırakılamaz ve seçilecek tarih bugünden önce olamaz.",
        disabled: true,
      };
    }

    return { errorText: "", disabled: false };
  };

  const isButtonDisabled = buttonDisabledHandler();

  const addToCartHandler = () => {
    const requestBody = {
      itemType: "service",
      itemData: {
        ...service?.serviceData,
        note: formData.note,
        serviceDate: formData.serviceDate,
        serviceType: service?.serviceType,
        serviceHotelId: service?._id,
        hotel: hotelId,
        pet: null,
      },
      selectedItems: calculatedRoomData?.selectedItems || [],
      totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
    };

    addItemToCart(hotelToken, requestBody, setLoading, onClose, setDisabled);
  };

  return (
    <form
      className="space-y-3"
      onSubmit={(event) => {
        event.preventDefault();
        addToCartHandler();
      }}
    >
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Hizmet Adı:</p>
          <p className="capitalize font-medium text-sm">
            {service?.serviceData?.serviceName}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Hizmet Süresi:</p>
          <p className="capitalize font-medium text-sm">
            {service?.serviceData?.serviceDetails?.duration} dakika
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Açıklama:</p>
          <p className="text-sm font-medium">
            {service?.serviceData?.description}
          </p>
        </div>
        <Separator className="mt-2" />
        <div className="flex flex-col gap-1 mt-2">
          <p className="text-sm font-semibold">Hizmet Tarihi:</p>
          <Calendar
            mode="single"
            locale={calendarLanguage.secondValue}
            formatters={{
              formatMonthDropdown: (date) => monthFormatter.format(date),
            }}
            disabled={{ before: new Date() }}
            defaultMonth={createLocalDate(formData.serviceDate)}
            startMonth={
              new Date(new Date().getFullYear(), new Date().getMonth())
            }
            endMonth={new Date(2050, 11)}
            required
            selected={createLocalDate(formData.serviceDate)}
            onSelect={(selectedDate) => {
              const adjustedStartDate = adjustDateToTimezone(selectedDate);
              const dateToString = adjustedStartDate
                ?.toISOString()
                .split("T")[0];
              setFormData((prev: any) => ({
                ...prev,
                serviceDate: dateToString,
              }));
            }}
            className="rounded-md border shadow-sm w-full max-w-2xl"
            classNames={{
              today: "bg-transparent text-foreground rounded-md",
            }}
            {...({ disableAutoUnselect: true } as any)}
            captionLayout="dropdown"
          />
          <p className="text-red-500 text-[12px] mt-1">
            {isButtonDisabled.errorText}
          </p>
        </div>
        <div className="flex flex-col gap-1 mt-3">
          <p className="text-sm font-semibold">Not:</p>
          <Textarea name="note" value={formData.note} onChange={handleChange} />
        </div>
      </div>
      <div className="flex items-center gap-1">
        <p className="max-sm:text-sm font-medium">Fiyat:</p>
        <p className="font-bold">
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(service?.serviceData?.total)) + "₺"}
        </p>
      </div>
      <div className="flex justify-end gap-5 pb-1">
        <Button variant="ghost" type="button" onClick={onClose}>
          İptal
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
          disabled={disabled || isButtonDisabled.disabled}
        >
          {loading ? <LoadingSpinner /> : "Ekle"}
        </Button>
      </div>
    </form>
  );
};

export default GroomingService;
