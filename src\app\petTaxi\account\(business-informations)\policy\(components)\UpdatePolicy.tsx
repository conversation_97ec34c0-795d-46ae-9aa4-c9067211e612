"use client";
import React, { useState } from "react";
import type { FC, ChangeEvent } from "react";
import IconEdit from "@/shared/icons/Edit";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import type { PolicyDataTypes } from "../../../(components)/(steps)/(policy)/PolicyStep";
import ServiceHoursTimePicker from "../../../(components)/(steps)/(policy)/ServiceHoursTimePicker";
import AddNewRule from "../../../(components)/(steps)/(policy)/AddNewRule";
import RuleCard from "../../../(components)/(steps)/(policy)/RuleCard";
import CancellationOption from "../../../(components)/(steps)/(policy)/CancellationOption";
import { useCancellationPolicyTaxi } from "@/hooks/policy/useCancellationPolicyTaxi";
import Textarea from "@/shared/Textarea";
import { useTranslations } from "next-intl";

interface UpdatePolicyProps {
  petTaxiToken: string | undefined;
  cancellationPolicyInformationsTaxiData: any;
}

const UpdatePolicy: FC<UpdatePolicyProps> = ({
  cancellationPolicyInformationsTaxiData,
  petTaxiToken,
}) => {
  const translate = useTranslations("UpdatePolicy");
  const { updateCancellationPolicyInformationHandler } =
    useCancellationPolicyTaxi();
  const [policyData, setPolicyData] = useState<PolicyDataTypes>(
    cancellationPolicyInformationsTaxiData
  );
  const [addNewRule, setAddNewRule] = useState<boolean>(false);
  const [newRule, setNewRule] = useState<{ title: string; rule: string }>({
    title: "",
    rule: "",
  });
  const [editRule, setEditRule] = useState<boolean>(false);
  const [selectedRuleId, setSelectedRuleId] = useState<number | null>(null);
  const [updatePolicy, setUpdatePolicy] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const closeModal = () => {
    setLoading(false);
    setUpdatePolicy(false);
  };

  // checks if all inputs are true
  const isPolicyDataValid = (): boolean => {
    const {
      serviceHoursStart,
      serviceHoursEnd,
      cancellationPolicyType,
      description,
      dateRange,
    } = policyData;

    if (!serviceHoursStart || !serviceHoursEnd) return false;

    // const checkInDateValidate = new Date(`1970-01-01T${checkInStartTime}:00`);
    // const checkInEndDateValidate = new Date(`1970-01-01T${checkInEndTime}:00`);
    // const checkOutDateValidate = new Date(`1970-01-01T${checkOutTime}:00`);

    // const isValidCheckIn = checkInEndDateValidate > checkInDateValidate;
    // const isValid = checkInDateValidate > checkOutDateValidate;

    const isDateRangeValid =
      cancellationPolicyType !== "CANCEL_UNTIL_DATE_RANGE" ||
      Boolean(dateRange);

    return (
      Boolean(serviceHoursStart) &&
      Boolean(serviceHoursEnd) &&
      Boolean(cancellationPolicyType) &&
      Boolean(description) &&
      isDateRangeValid
    );
  };

  const isAllValid = isPolicyDataValid();

  const resetNewRule = () => {
    setNewRule({
      title: "",
      rule: "",
    });
    setAddNewRule(false);
    setEditRule(false);
  };

  // adds service hours
  const timeHandler = (date: string, name: string) => {
    if (name === "serviceHoursStart") {
      setPolicyData({ ...policyData, serviceHoursStart: date });
    } else if (name === "serviceHoursEnd") {
      setPolicyData({ ...policyData, serviceHoursEnd: date });
    }
  };

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setNewRule({ ...newRule, [name]: value });
  };

  // adds new rule
  const addRuleHandler = () => {
    setPolicyData({
      ...policyData,
      specialRules: [
        ...policyData.specialRules,
        { title: newRule.title.trim(), rule: newRule.rule.trim() },
      ],
    });
    resetNewRule();
  };

  // deletes selected rule
  const deleteRuleHandler = (ruleId: number) => {
    const filteredRules = policyData.specialRules.filter(
      (_, index) => index !== ruleId
    );
    setPolicyData({ ...policyData, specialRules: filteredRules });
  };

  // finds the selected rule and opens edit inputs
  const editRuleHandler = (ruleId: number) => {
    setSelectedRuleId(ruleId);
    setEditRule(true);
    setAddNewRule(true);
    const selectedRule = policyData.specialRules.find(
      (_, index) => index === ruleId
    );
    if (selectedRule) {
      setNewRule(selectedRule);
    }
  };

  // updates selected rule
  const updateRuleHandler = () => {
    setPolicyData({
      ...policyData,
      specialRules: policyData.specialRules.map((rule, index) =>
        index === selectedRuleId
          ? { title: newRule.title, rule: newRule.rule }
          : rule
      ),
    });
    resetNewRule();
  };

  const resetInputs = () => {
    setUpdatePolicy(false);
    setPolicyData(cancellationPolicyInformationsTaxiData);
  };

  return (
    <>
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger>
            <IconEdit
              onClick={() => setUpdatePolicy(true)}
              className="cursor-pointer size-5 hover:text-secondary-6000 duration-200"
            />
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">{translate("updatePolicy")}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Dialog open={updatePolicy}>
        <DialogContent
          onOpenAutoFocus={(event) => event.preventDefault()}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-6xl"
          onInteractOutside={resetInputs}
          onClick={(e) => e.stopPropagation()}
        >
          <DialogHeader>
            <DialogTitle>{translate("updatePolicyTitle")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) => {
              updateCancellationPolicyInformationHandler(
                event,
                petTaxiToken,
                policyData,
                setLoading,
                setDisabled,
                closeModal
              );
            }}
          >
            <div className="mb-4 mt-2">
              <ServiceHoursTimePicker
                timeHandler={timeHandler}
                checks={{
                  serviceHoursStart: policyData.serviceHoursStart,
                  serviceHoursEnd: policyData.serviceHoursEnd,
                }}
              />
            </div>
            <div className="max-w-md">
              <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold mb-3 max-md:text-sm">
                {translate("description")}
              </p>
              <Textarea
                onChange={(event) => {
                  setPolicyData({
                    ...policyData,
                    description: event.target.value,
                  });
                }}
                value={policyData.description}
              />
            </div>
            <div>
              {addNewRule && (
                <AddNewRule
                  newRule={newRule}
                  handleChange={handleChange}
                  editRule={editRule}
                  updateRuleHandler={updateRuleHandler}
                  addRuleHandler={addRuleHandler}
                  resetNewRule={resetNewRule}
                />
              )}
              <div className="mt-5">
                <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold mb-3 max-md:text-sm">
                  {translate("rules")}
                </p>
                <Button
                  type="button"
                  onClick={() => setAddNewRule(true)}
                  className="bg-secondary-6000 hover:bg-secondary-700 text-white mb-5 max-md:text-xs"
                >
                  {translate("addNewRule")}
                </Button>
                {policyData.specialRules.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {policyData.specialRules.map((rule, index) => {
                      return (
                        <RuleCard
                          key={index}
                          rules={rule}
                          index={index}
                          deleteRuleHandler={deleteRuleHandler}
                          editRuleHandler={editRuleHandler}
                        />
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
            <div className="mt-5 space-y-3">
              <CancellationOption
                policyData={policyData}
                setPolicyData={setPolicyData}
              />
            </div>
            <div className="flex justify-end gap-5 mt-5">
              <Button variant="outline" type="button" onClick={resetInputs}>
                {translate("cancel")}
              </Button>
              <Button
                disabled={
                  disabled ||
                  JSON.stringify(cancellationPolicyInformationsTaxiData) ===
                    JSON.stringify(policyData) ||
                  !isAllValid
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : translate("save")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">{translate("close")}</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdatePolicy;
