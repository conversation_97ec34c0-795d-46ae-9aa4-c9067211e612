"use client";
import React from "react";
import type { FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";

interface ServiceHoursTimePickerProps {
  timeHandler: (date: string, name: string) => void;
  checks: {
    serviceHoursStart: string | undefined;
    serviceHoursEnd: string | undefined;
  };
}

const ServiceHoursTimePicker: FC<ServiceHoursTimePickerProps> = ({
  timeHandler,
  checks,
}) => {
  const translate = useTranslations("CheckInOutTimePicker");
  const { serviceHoursStart, serviceHoursEnd } = checks;
  // const checkInDateValidate = new Date(`1970-01-01T${checkInStartTime}:00`);
  // const checkInEndDateValidate = new Date(`1970-01-01T${checkInEndTime}:00`);
  // const checkOutDateValidate = new Date(`1970-01-01T${checkOutTime}:00`);

  // const isValidCheckIn = checkInEndDateValidate > checkInDateValidate;
  // const isValid = checkInDateValidate > checkOutDateValidate;

  const timeData = Array.from({ length: 24 * 4 }, (_, index) => {
    const hours = Math.floor(index / 4);
    const minutes = (index % 4) * 15;
    return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
  });

  return (
    <>
      <div className="flex max-md:flex-col gap-3">
        <div className="max-md:basis-1/2 max-md:mb-2">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm mb-1">
            Hizmet Başlangıç Saati
          </p>
          <Select
            onValueChange={(selected) =>
              timeHandler(selected, "serviceHoursStart")
            }
            value={checks.serviceHoursStart}
          >
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Hizmet Başlangıç Saati" />
            </SelectTrigger>
            <SelectContent>
              {timeData.map((time, index) => {
                return (
                  <SelectItem key={index} value={time}>
                    {time}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <div className="max-md:basis-1/2">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold max-md:text-sm mb-1">
            Hizmet Bitiş Saati
          </p>
          <Select
            onValueChange={(selected) =>
              timeHandler(selected, "serviceHoursEnd")
            }
            value={checks.serviceHoursEnd}
          >
            <SelectTrigger className="w-[220px]">
              <SelectValue placeholder="Hizmet Bitiş Saati" />
            </SelectTrigger>
            <SelectContent>
              {timeData.map((time, index) => {
                return (
                  <SelectItem key={index} value={time}>
                    {time}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>
    </>
  );
};

export default ServiceHoursTimePicker;
