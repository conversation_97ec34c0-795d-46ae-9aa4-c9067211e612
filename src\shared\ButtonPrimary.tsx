import type { ButtonProps } from "./Button";
import Button from "./Button";
import React from "react";

export interface ButtonPrimaryProps extends ButtonProps {}

const ButtonPrimary: React.FC<ButtonPrimaryProps> = ({
  className = "",
  buttonClass = "",
  ...args
}) => {
  return (
    <Button
      buttonClass={buttonClass || ""}
      className={`bg-secondary-6000 text-neutral-50 hover:bg-secondary-700 disabled:bg-opacity-70 dark:disabled:bg-gray-500 ${className}`}
      {...args}
    />
  );
};

export default ButtonPrimary;
