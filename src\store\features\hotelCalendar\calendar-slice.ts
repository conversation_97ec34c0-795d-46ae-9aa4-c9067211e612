import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { AllocationState, AllocationType } from "./calendar-types";

//TODO: buradaki anyi düzelt sonra

const initialState: AllocationState = {
  selectedRoomId: "",
  rooms: [],
  selectedRoom: null,
  selectedRoomGroupId: "",
  allocations: [],
};

const allocationSlice = createSlice({
  name: "hotelCalendar",
  initialState,
  reducers: {
    setSelectedRoomId: (
      state,
      action: PayloadAction<string | string[] | undefined>
    ) => {
      state.selectedRoomId = action.payload;
    },
    setselectedRoomGroupId: (
      state,
      action: PayloadAction<string | string[] | undefined>
    ) => {
      state.selectedRoomGroupId = action.payload;
    },
    setSelectedRoom: (state, action: PayloadAction<string>) => {
      state.selectedRoom =
        state.rooms.find((r: any) => r._id === action.payload) || null;
    },
    setRooms: (state, action: PayloadAction<any>) => {
      state.rooms = action.payload;
    },
    setAllocations: (state, action: PayloadAction<AllocationType[]>) => {
      state.allocations = action.payload;
    },
  },
});

export const {
  setSelectedRoomId,
  setselectedRoomGroupId,
  setSelectedRoom,
  setRooms,
  setAllocations,
} = allocationSlice.actions;
export default allocationSlice.reducer;
