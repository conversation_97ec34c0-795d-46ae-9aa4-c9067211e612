import React from "react";
import type { FC } from "react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { formatPhoneNumber } from "./TodayRoomCard";
import petOwnerAvatar from "@/images/avatars/Image-1.png";

interface PetOwnerInformationProps {
  selectedRoom: any;
}

const PetOwnerInformation: FC<PetOwnerInformationProps> = ({
  selectedRoom,
}) => {
  return (
    <>
      <Separator />
      <div className="flex max-md:flex-col justify-center items-center gap-2 py-3 mt-4 shadow-sm rounded-md border">
        <div className="flex justify-center">
          <div className="rounded-full border-2">
            <Image
              src={petOwnerAvatar}
              width={100}
              height={100}
              alt="catPhoto"
              className="rounded-full object-cover"
            />
          </div>
        </div>
        <div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              {/* <div className="mr-1">
            <i className="las la-user text-2xl text-gray-500"></i>
          </div> */}
              <div className="text-sm font-semibold">Pet Sahibi:</div>
            </div>
            <div className="text-sm">
              {selectedRoom.reservation?.petOwner?.fullName}
            </div>
          </div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              {/* <div className="mr-1">
            <i className="las la-envelope text-2xl text-gray-500"></i>
          </div> */}
              <div className="text-sm font-semibold">E-posta:</div>
            </div>
            <div className="text-sm">
              {selectedRoom.reservation?.petOwner?.email}
            </div>
          </div>
          <div className="mt-2 flex gap-1">
            <div className="flex flex-row items-center">
              {/* <div className="mr-1">
            <i className="las la-phone text-2xl text-gray-500"></i>
          </div> */}
              <div className="text-sm font-semibold">Telefon:</div>
            </div>
            <div className="text-sm">
              {formatPhoneNumber(
                selectedRoom.reservation?.petOwner?.phone || ""
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PetOwnerInformation;
