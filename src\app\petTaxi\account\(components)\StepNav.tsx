import React from "react";
import type { FC } from "react";
import IconPaperClip from "@/shared/icons/PaperClip";
import IconCreditCard from "@/shared/icons/CreditCard";
import IconNewspaper from "@/shared/icons/Newspaper";
import IconPawGuest from "@/shared/icons/PawGuest";
import IconFeatures from "@/shared/icons/Features";
import IconCheckCircle from "@/shared/icons/CheckCircle";
import IconImage from "@/shared/icons/Image";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";

interface StepNavprops {
  taxiData: PetTaxiDataApiTypes;
  checkSubMerchant: boolean;
}

interface TaxiInfoItem {
  id: number;
  icon: JSX.Element;
  label: string;
  style: string;
}

const StepNav: FC<StepNavprops> = ({ taxiData, checkSubMerchant }) => {
  // Renders a specific icon based on the provided condition
  const iconHandler = (value: boolean, IconComponent: React.ElementType) => {
    if (value) {
      return <IconCheckCircle className="size-5 text-[#3f894e]" />;
    } else {
      return <IconComponent className="size-5 text-gray-800 dark:text-white" />;
    }
  };

  // Renders a specific color based on the provided condition
  const lineHandler = (firstCondition: boolean, secondCondition: boolean) => {
    const checkedCircle = "bg-[#bff2d4]";

    return firstCondition
      ? checkedCircle
      : secondCondition
        ? "bg-[#d5e4f2] dark:bg-[#7ba9cf]"
        : "bg-gray-200 dark:bg-gray-700";
  };

  // Step Icons
  const fileIconComponent = iconHandler(
    taxiData?.files?.length >= 5,
    IconPaperClip
  );
  const billingIconComponent = iconHandler(checkSubMerchant, IconCreditCard);
  const petTypeIconComponent = iconHandler(
    taxiData?.acceptedPetTypes?.length > 0,
    IconPawGuest
  );
  const petTaxiFeaturesIconComponent = iconHandler(
    taxiData?.petTaxiFeatures?.length > 0,
    IconFeatures
  );
  const petTaxiPhotosIconComponent = iconHandler(
    taxiData?.images?.length > 0,
    IconImage
  );
  const policyIconComponent = iconHandler(
    !!taxiData?.policy?.serviceHoursEnd,
    IconNewspaper
  );

  // Step circle and line styles
  const filelineStyle = lineHandler(taxiData?.files?.length >= 5, true);
  const billingLineStyle = lineHandler(
    checkSubMerchant,
    taxiData?.files?.length >= 5
  );
  const petTypeLineStyle = lineHandler(
    taxiData?.acceptedPetTypes?.length > 0,
    checkSubMerchant
  );
  const petTaxiFeaturesLineStyle = lineHandler(
    taxiData?.petTaxiFeatures?.length > 0,
    taxiData?.acceptedPetTypes?.length > 0
  );
  const petTaxiPhotosLineStyle = lineHandler(
    taxiData?.images?.length > 0,
    taxiData?.petTaxiFeatures?.length > 0
  );
  const petTaxiPolicyLineStype = lineHandler(
    !!taxiData?.policy?.serviceHoursEnd,
    !!taxiData?.googleMapUrl
  );

  const taxiInfoItems: TaxiInfoItem[] = [
    {
      id: 1,
      icon: fileIconComponent,
      label: "Belgeler",
      style: filelineStyle,
    },
    {
      id: 2,
      icon: billingIconComponent,
      label: "Ödeme",
      style: billingLineStyle,
    },
    {
      id: 3,
      icon: petTypeIconComponent,
      label: "Pet Türü",
      style: petTypeLineStyle,
    },
    {
      id: 4,
      icon: petTaxiFeaturesIconComponent,
      label: "Özellikler",
      style: petTaxiFeaturesLineStyle,
    },
    {
      id: 5,
      icon: petTaxiPhotosIconComponent,
      label: "Fotoğraflar",
      style: petTaxiPhotosLineStyle,
    },
    {
      id: 6,
      icon: policyIconComponent,
      label: "İptal Politikası",
      style: petTaxiPolicyLineStype,
    },
  ];

  return (
    <>
      <div className="max-lg:hidden grid grid-cols-6 mb-5 gap-5">
        <div className={`w-full h-1 ${filelineStyle}`}></div>
        <div className={`w-full h-1 ${billingLineStyle}`}></div>
        <div className={`w-full h-1 ${petTypeLineStyle}`}></div>
        <div className={`w-full h-1 ${petTaxiFeaturesLineStyle}`}></div>
        <div className={`w-full h-1 ${petTaxiPhotosLineStyle}`}></div>
        <div className={`w-full h-1 ${petTaxiPolicyLineStype}`}></div>
      </div>
      <ol className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-2 items-center w-full">
        {taxiInfoItems.map((item) => (
          <li key={item.id} className="flex w-full gap-2 items-center">
            <span
              className={`flex items-center justify-center w-8 h-8 rounded-full lg:h-10 lg:w-10 shrink-0 ${item.style}`}
            >
              {item.icon}
            </span>
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              {item.label}
            </p>
          </li>
        ))}
      </ol>
    </>
  );
};

export default StepNav;
