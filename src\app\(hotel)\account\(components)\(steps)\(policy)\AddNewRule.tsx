"use client";
import React from "react";
import type { FC, ChangeEvent } from "react";
import Label from "@/components/Label";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

interface AddNewRuleProps {
  newRule: {
    title: string;
    rule: string;
  };
  editRule: boolean;
  handleChange: (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => void;
  updateRuleHandler: () => void;
  addRuleHandler: () => void;
  resetNewRule: () => void;
}

const AddNewRule: FC<AddNewRuleProps> = ({
  newRule,
  handleChange,
  editRule,
  updateRuleHandler,
  addRuleHandler,
  resetNewRule,
}) => {
  const translate = useTranslations("AddNewRule");
  const { title, rule } = newRule;
  const ruleButtonDisabled = title && rule;

  return (
    <div className="mt-5">
      <Label>{translate("newRuleLabel")}</Label>
      <div className="max-w-lg space-y-3 mt-2">
        <Input
          name="title"
          type="text"
          placeholder={translate("ruleTitlePlaceholder")}
          onChange={handleChange}
          value={title}
        />
        <Textarea
          name="rule"
          placeholder={translate("ruleDescriptionPlaceholder")}
          onChange={handleChange}
          value={rule}
        />
        <div className="flex gap-2">
          {editRule ? (
            <Button
              onClick={updateRuleHandler}
              disabled={!ruleButtonDisabled}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white max-md:text-xs"
            >
              {translate("updateButton")}
            </Button>
          ) : (
            <Button
              onClick={addRuleHandler}
              disabled={!ruleButtonDisabled}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white max-md:text-xs"
            >
              {translate("addButton")}
            </Button>
          )}

          <Button
            onClick={resetNewRule}
            variant="outline"
            className="max-md:text-xs"
          >
            {translate("cancelButton")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddNewRule;
