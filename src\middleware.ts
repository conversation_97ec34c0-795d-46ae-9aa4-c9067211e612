import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const searchParams = request.nextUrl.searchParams.toString();

  // Get the existing cookie to prevent unnecessary re-setting
  const existingCookie = request.cookies.get("mobileParams")?.value;

  // Only update the cookie if the searchParams are different
  if (!existingCookie) {
    response.cookies.set("mobileParams", searchParams, {
      httpOnly: true,
      secure: process.env.APP_ENV === "production",
      sameSite: "strict",
      path: "/",
    });
  }

  const propertyTypesCookie = request.cookies.get("propertyTypes")?.value;
  const pathname = request.nextUrl.pathname;

  response.cookies.set("pathname", pathname, {
    path: "/",
  });

  const propertyTypes = propertyTypesCookie
    ? JSON.parse(propertyTypesCookie)
    : [];

  const hasPetHotel = propertyTypes.includes("petHotel");
  const hasPetTaxi = propertyTypes.includes("petTaxi");

  if (hasPetHotel && hasPetTaxi) {
    return response;
  }

  if (hasPetHotel && !hasPetTaxi) {
    if (pathname.startsWith("/petTaxi")) {
      return NextResponse.redirect(new URL("/hotel/account", request.url));
    }
    return response;
  }

  if (hasPetTaxi && !hasPetHotel) {
    if (pathname.startsWith("/hotel")) {
      return NextResponse.redirect(new URL("/petTaxi/account", request.url));
    }
    return response;
  }

  return response;
}

export const config = {
  matcher: "/((?!api|_next/static|_next/image|favicon.ico).*)",
};
