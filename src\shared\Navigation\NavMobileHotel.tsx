"use client";
import React from "react";
import ButtonClose from "@/shared/ButtonClose";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import Link from "next/link";
import { useLogin } from "@/hooks/useLogin";
import PawBooking<PERSON>ogo from "../PawBookingLogo";
import { useTranslations } from "next-intl";

export interface NavMobileProps {
  onClickClose?: () => void;
}

const NavMobileHotel: React.FC<NavMobileProps> = ({ onClickClose }) => {
  const { logoutUser } = useLogin();

  const translate = useTranslations("NavMobileHotel");

  return (
    <div className="h-screen w-full divide-y-2 divide-neutral-100 overflow-y-auto bg-white py-2 shadow-lg ring-1 transition dark:divide-neutral-800 dark:bg-neutral-900 dark:ring-neutral-700">
      <div className="px-5 py-6">
        {/* <Logo /> */}
        <PawBookingLogo className="size-16" />
        <span className="absolute right-2 top-2 p-1">
          <ButtonClose onClick={onClickClose} />
        </span>
      </div>
      <div className="flex items-center justify-between px-2 py-6 md:px-5">
        <Link
          href="/hotel/account"
          className="flex items-center gap-2 space-x-1"
        >
          <div className="text-sm font-medium">{translate("myAccount")}</div>
        </Link>
        <SwitchDarkMode className="mx-0.5" />
      </div>
      <div
        onClick={() => {
          logoutUser();
        }}
        className="cursor-pointer pt-5 text-center text-sm font-medium"
      >
        {translate("logout")}
      </div>
    </div>
  );
};

export default NavMobileHotel;
