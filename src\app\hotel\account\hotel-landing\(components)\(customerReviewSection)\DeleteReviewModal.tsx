"use client";
import React, { useState } from "react";
import type { FC } from "react";
import IconDelete from "@/shared/icons/Delete";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

interface DeleteReviewModalProps {
  reviews: any;
  customerReviewsSectionData: any;
  hotelToken: string | undefined;
}

const DeleteReviewModal: FC<DeleteReviewModalProps> = ({
  reviews,
  customerReviewsSectionData,
  hotelToken,
}) => {
  const { deleteCustomerReviewSectionHandler } = useHotelLandingActions();
  const [deleteReviewIsOpen, setDeleteReviewIsOpen] = useState<boolean>(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | undefined>(
    undefined
  );
  const [loading, setLoading] = useState<boolean>(false);
  const filteredReviews = customerReviewsSectionData?.reviews?.filter(
    (review: any) => review._id !== selectedReviewId
  );

  const closeModal = () => {
    setDeleteReviewIsOpen(false);
    setSelectedReviewId(undefined);
  };

  return (
    <>
      <IconDelete
        onClick={() => {
          setDeleteReviewIsOpen(true);
          setSelectedReviewId(reviews._id);
        }}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />

      <Dialog open={deleteReviewIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Yorum Silme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteCustomerReviewSectionHandler(
                event,
                customerReviewsSectionData,
                filteredReviews,
                hotelToken,
                setLoading,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Seçili yorum silinsin mi?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteReviewModal;
