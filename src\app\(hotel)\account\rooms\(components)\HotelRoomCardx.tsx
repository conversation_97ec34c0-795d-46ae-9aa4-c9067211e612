import React from "react";
import type { FC } from "react";
import type { Route } from "@/routers/types";
import Image from "next/image";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import type { RoomGroupDataApiTypes } from "@/types/hotel/roomGroupType";
import { useTranslations } from "next-intl";
import DeleteRoomModal from "./(deleteRoom)/DeleteRoomModal";
import UpdateRoomModal from "./(updateRoom)/UpdateRoomModal";
import { PET_TYPES } from "@/app/(enums)/enums";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

export interface HotelRoomCardxProps {
  hotel: HotelDataApiTypes;
  className?: string;
  roomGroup: RoomGroupDataApiTypes;
  size?: "default" | "small";
  href?: Route<string>;
  hotelToken: string | undefined;
  acceptedPetTypes: string[] | [];
}

const HotelRoomCardx: FC<HotelRoomCardxProps> = ({
  size = "default",
  className = "",
  hotel,
  roomGroup,
  hotelToken,
  acceptedPetTypes,
}) => {
  const translate = useTranslations("HotelRoomCard");
  const { _id, roomGroupName, roomCount, images, petType, rooms, updatedAt } =
    roomGroup;

  const roomImage =
    images?.length > 0 && images[0]?.src ? images[0]?.src : defaultHotelImage;
  const petTypeNames = petType
    .map((petType) => PET_TYPES[petType as keyof typeof PET_TYPES])
    .filter(Boolean); // eskiden kalan sadece dog tipi undefined dönüyor onu filterlamak için

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex justify-between items-center">
        <div className="space-y-2 basis-3/4">
          <div className="flex items-center space-x-2">
            <h2
              className={`font-semibold capitalize text-neutral-900 dark:text-white ${
                size === "default" ? "text-base" : "text-base"
              }`}
            >
              <span className="line-clamp-1">{roomGroupName}</span>
            </h2>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>{translate("hotelRoomCount")}</div>
            <div> {roomCount}</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>{translate("roomCapacity")}</div>
            <div> {rooms[0].roomCapacity}</div>
          </div>
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            {/* <div>
              <IconPawGuest className="size-5 dark:hidden" fill="#6b7280" />
              <IconPawGuest
                className="size-5 hidden dark:block"
                fill="#9ca3af"
              />
            </div> */}
            <div>{translate("petType")}</div>
            <div className="capitalize flex flex-wrap">
              {petTypeNames.map((pet, index) => {
                return (
                  <p key={index}>
                    {pet}
                    {index < petTypeNames.length - 1 && <span>,&nbsp;</span>}
                  </p>
                );
              })}
            </div>
          </div>
        </div>
        <div className="relative w-20 h-20">
          <Image
            className="object-cover rounded-full border"
            src={roomImage}
            fill
            alt=""
          />
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdateRoomModal
          key={`${updatedAt}-${roomGroup?.images?.length}`}
          hotel={hotel}
          roomGroup={roomGroup}
          hotelToken={hotelToken}
          acceptedPetTypes={acceptedPetTypes}
        />
        <DeleteRoomModal roomGroup={roomGroup} hotelToken={hotelToken} />
      </div>
    </div>
  );
};

export default HotelRoomCardx;
