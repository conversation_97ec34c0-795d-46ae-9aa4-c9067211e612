import * as React from "react";
import { Section, Row, Text, Hr, Column } from "@react-email/components";

import Layout from "emails/_components/Layout";

const NewMerchantWelcome = () => {
  return (
    <Layout>
      <Section style={{ marginTop: 16, marginBottom: 16, padding: 16 }}>
        <Section>
          <Row>
            <Text
              style={{
                margin: "0px",
                fontSize: 24,
                lineHeight: "32px",
                fontWeight: 600,
                color: "rgb(17,24,39)",
              }}
            >
              Hoş Geldiniz, PawBooking Takımına Katıldınız!
            </Text>
            <Text
              style={{
                marginTop: 8,
                fontSize: 16,
                lineHeight: "24px",
                color: "rgb(107,114,128)",
              }}
            >
              Tebrikler! Artık PawBooking platformunda yerinizi aldınız. İşte
              başarılı bir başlangıç için atmanız gereken adımlar:
            </Text>
          </Row>
        </Section>
        <Hr
          style={{
            marginTop: 24,
            marginBottom: 24,
            borderColor: "rgb(209,213,219)",
          }}
        />

        {[
          "Profilinizi Tamamlayın",
          "Odalarınızı ve Hizmetlerinizi Ekleyin",
          "Rezervasyonlarınızı Yönetin",
          "Müşterilerinizle İletişimde Kalın",
        ].map((step, index) => (
          <Section
            key={index}
            style={{ display: "flex", alignItems: "center", width: "100%" }}
          >
            <Row>
              <Column style={{ verticalAlign: "baseline" }}>
                <table
                  style={{
                    display: "flex",
                    width: "100%",
                    alignItems: "center",
                    gap: "12px;",
                  }}
                >
                  <td
                    align="center"
                    style={{
                      height: 40,
                      width: 40,
                      backgroundColor: "#64D352",
                      borderRadius: 9999,
                      padding: "0px",
                      marginRight: "10px",
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: 600,
                        margin: "0px",
                        color: "rgb(255, 255, 255)",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <svg
                        fill="#FFFFFF"
                        width="16px"
                        height="16px"
                        viewBox="0 0 512 512"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <title>ionicons-v5-n</title>
                        <path d="M490.39,182.75c-5.55-13.19-14.77-22.7-26.67-27.49l-.16-.06a46.46,46.46,0,0,0-17-3.2h-.64c-27.24.41-55.05,23.56-69.19,57.61-10.37,24.9-11.56,51.68-3.18,71.64,5.54,13.2,14.78,22.71,26.73,27.5l.13.05a46.53,46.53,0,0,0,17,3.2c27.5,0,55.6-23.15,70-57.65C497.65,229.48,498.78,202.72,490.39,182.75Z" />
                        <path d="M381.55,329.61c-15.71-9.44-30.56-18.37-40.26-34.41C314.53,250.8,298.37,224,256,224s-58.57,26.8-85.39,71.2c-9.72,16.06-24.6,25-40.36,34.48-18.07,10.86-36.74,22.08-44.8,44.16a66.93,66.93,0,0,0-4.65,25c0,35.95,28,65.2,62.4,65.2,17.75,0,36.64-6.15,56.63-12.66,19.22-6.26,39.09-12.73,56.27-12.73s37,6.47,56.15,12.73C332.2,457.85,351,464,368.8,464c34.35,0,62.3-29.25,62.3-65.2a67,67,0,0,0-4.75-25C418.29,351.7,399.61,340.47,381.55,329.61Z" />
                        <path d="M150,188.85c11.9,14.93,27,23.15,42.52,23.15a42.88,42.88,0,0,0,6.33-.47c32.37-4.76,52.54-44.26,45.92-90C242,102.3,234.6,84.39,224,71.11,212.12,56.21,197,48,181.49,48a42.88,42.88,0,0,0-6.33.47c-32.37,4.76-52.54,44.26-45.92,90C132,157.67,139.4,175.56,150,188.85Z" />
                        <path d="M313.16,211.53a42.88,42.88,0,0,0,6.33.47c15.53,0,30.62-8.22,42.52-23.15,10.59-13.29,17.95-31.18,20.75-50.4h0c6.62-45.72-13.55-85.22-45.92-90a42.88,42.88,0,0,0-6.33-.47C315,48,299.88,56.21,288,71.11c-10.6,13.28-18,31.19-20.76,50.44C260.62,167.27,280.79,206.77,313.16,211.53Z" />
                        <path d="M111.59,308.8l.14-.05c11.93-4.79,21.16-14.29,26.69-27.48,8.38-20,7.2-46.75-3.15-71.65C120.94,175.16,92.85,152,65.38,152a46.4,46.4,0,0,0-17,3.2l-.14.05C36.34,160,27.11,169.54,21.58,182.73c-8.38,20-7.2,46.75,3.15,71.65C39.06,288.84,67.15,312,94.62,312A46.4,46.4,0,0,0,111.59,308.8Z" />
                      </svg>
                    </Text>
                  </td>
                </table>
              </Column>
              <Column style={{ width: "90%" }}>
                <Text
                  style={{
                    margin: "0px",
                    fontSize: 20,
                    lineHeight: "28px",
                    fontWeight: 600,
                    color: "rgb(17,24,39)",
                  }}
                >
                  {step}
                </Text>
                <Text
                  style={{
                    margin: "0px",
                    marginTop: 8,
                    fontSize: 16,
                    lineHeight: "24px",
                    color: "rgb(107,114,128)",
                  }}
                >
                  {index === 0
                    ? "Otelinizin bilgilerini eksiksiz doldurun. Fotoğraflar, açıklamalar ve konum bilgileri ekleyerek müşterilerin ilgisini çekin."
                    : index === 1
                      ? "Odalarınızı tanımlayın, fiyatlandırma yapın ve ek hizmetlerinizi ekleyin. Daha fazla detay, daha fazla müşteri demektir!"
                      : index === 2
                        ? "Gelen rezervasyonları kolayca yönetin, müsaitlik durumunu güncelleyin ve onay süreçlerini hızlandırın."
                        : "Müşteri yorumlarına yanıt verin, sorulara hızlı dönüş yaparak güven oluşturun ve memnuniyet sağlayın."}
                </Text>
              </Column>
            </Row>
            <Hr
              style={{
                marginTop: 24,
                marginBottom: 24,
                borderColor: "rgb(209,213,219)",
              }}
            />
          </Section>
        ))}

        <Section>
          <Row>
            <Text
              style={{
                margin: "0px",
                fontSize: 16,
                lineHeight: "24px",
                color: "rgb(107,114,128)",
              }}
            >
              Daha fazla bilgiye ihtiyacınız olursa, destek ekibimizle her zaman
              iletişime geçebilirsiniz. Başarılar dileriz!
            </Text>
          </Row>
        </Section>
      </Section>
    </Layout>
  );
};

export default NewMerchantWelcome;
