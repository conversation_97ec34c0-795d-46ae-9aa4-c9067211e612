import { MessageCircle, Inbox } from "lucide-react";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import { House, User } from "lucide-react";
import Link from "next/link";

const EmptyMessage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] px-4 text-center max-md:pt-20">
      <div className="relative mb-6">
        {/* Background circle with subtle gradient */}
        <div className="w-24 h-24 bg-gradient-to-br from-muted to-muted/50 rounded-full flex items-center justify-center">
          <div className="w-16 h-16 bg-background rounded-full flex items-center justify-center shadow-sm">
            <MessageCircle className="w-8 h-8 text-muted-foreground" />
          </div>
        </div>
        {/* Small decorative inbox icon */}
        <div className="absolute -top-1 -right-1 w-6 h-6 bg-primary-6000 rounded-full flex items-center justify-center">
          <Inbox className="w-3 h-3 text-white" />
        </div>
      </div>

      <div className="space-y-2 max-w-sm">
        <h3 className="text-xl font-semibold text-foreground">
          Henüz mesaj yok
        </h3>
        <p className="text-muted-foreground leading-relaxed">
          İlk mesajınızı aldığınızda burada görünecek. Şimdilik her şey sessiz
          ve sakin.
        </p>
      </div>
      {/* Subtle decorative elements */}
      <div className="mt-6 flex space-x-2">
        <div className="w-2 h-2 bg-muted rounded-full animate-pulse"></div>
        <div className="w-2 h-2 bg-muted rounded-full animate-pulse"></div>
        <div className="w-2 h-2 bg-muted rounded-full animate-pulse"></div>
      </div>
      <div className="mt-8 flex max-md:flex-col gap-3">
        <Link href="/hotel/account">
          <ButtonPrimary className="gap-1 w-48">
            <House className="size-4" />
            Ana Sayfaya Dön
          </ButtonPrimary>
        </Link>
        <Link href="/hotel/account">
          <ButtonSecondary className="gap-1 w-48">
            <User className="size-4" />
            Hesabım
          </ButtonSecondary>
        </Link>
      </div>
    </div>
  );
};

export default EmptyMessage;
