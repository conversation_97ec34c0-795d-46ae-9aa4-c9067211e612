"use client";
import React from "react";
import TurkishFlag from "@/shared/TurkishFlag";
import UKFlag from "@/shared/UKFlag";
import { useLocale } from "next-intl";
import type { Locale } from "@/i18n/config";
import { setUserLocale } from "@/actions/locale";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverClose,
} from "@/components/ui/popover";
import { GlobeAltIcon } from "@heroicons/react/24/outline";

const LanguageWithFlags = () => {
  const locale = useLocale();

  async function onChange(value: string) {
    const locale = value as Locale;
    await setUserLocale(locale);
  }

  const TriggerIconHandler = () => {
    if (locale === "tr") {
      return (
        <div className="flex items-center gap-1 justify-center">
          {/* <TurkishFlag className="size-8" /> */}
          <GlobeAltIcon className="size-[18px] opacity-80 hover:opacity-100" />
          <p className="text-sm font-medium">TR</p>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-1 justify-center">
        {/* <UKFlag className="size-8" /> */}
        <GlobeAltIcon className="size-[18px] opacity-80 hover:opacity-100" />
        <p className="text-sm font-medium">EN</p>
      </div>
    );
  };

  return (
    <div className="self-center">
      <Popover>
        <PopoverTrigger className="flex justify-center items-center">
          {TriggerIconHandler()}
        </PopoverTrigger>
        <PopoverContent className="flex justify-between w-60">
          <PopoverClose asChild>
            <button
              disabled={locale === "tr"}
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => onChange("tr")}
            >
              <TurkishFlag className="size-10" />
              <p className="text-sm font-medium">Türkçe</p>
            </button>
          </PopoverClose>
          <PopoverClose asChild>
            <button
              disabled={locale === "en"}
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => onChange("en")}
            >
              <UKFlag className="size-10" />
              <p className="text-sm font-medium">English</p>
            </button>
          </PopoverClose>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default LanguageWithFlags;
