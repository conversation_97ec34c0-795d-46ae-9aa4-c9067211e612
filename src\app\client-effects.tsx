"use client";

import { useEffect } from "react";
import { useMobile } from "@/hooks/useMobile";
import { setLocalStorageItem } from "@/utils/localStorage";
import { useThemeMode } from "@/hooks/useThemeMode";

const ClientEffects = ({ queryParams }: any) => {
  const { isMobile, lang, expoPushToken, theme } = useMobile(queryParams);
  const { toDark, toLight } = useThemeMode();

  useEffect(() => {
    if (theme && isMobile) {
      if (theme === "dark") {
        toDark();
      } else if (theme === "light") {
        toLight();
      }
    }

    if (expoPushToken) {
      setLocalStorageItem("expoPushToken", expoPushToken);
    }

    if (isMobile) {
      document.documentElement.classList.add("hiddenScrollbar");
    } else {
      document.documentElement.classList.remove("hiddenScrollbar");
    }
  }, [expoPushToken, isMobile, lang, theme, toDark, toLight]);

  return null;
};

export default ClientEffects;
