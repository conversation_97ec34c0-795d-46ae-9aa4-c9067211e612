import React from "react";
import IconCheck from "@/shared/icons/Check";
import ParticlesContainer from "@/components/ParticlesContainer";
import ConfettiFireworks from "./(components)/ConfettiContainer";
import ButtonPrimary from "@/shared/ButtonPrimary";

const ReservationSuccessPage = () => {
  return (
    <div className="container flex min-h-screen flex-col items-center space-y-4 py-16 md:py-32">
      <IconCheck className="size-12 rounded-full bg-green-500 p-2 text-white md:size-16" />
      <h2 className="text-center text-lg font-semibold md:text-2xl">
        Rezervasyonunuz Başarıyla Tamamlandı!
      </h2>
      <p className="text-center md:text-lg">
        Dostunuz için rezervasyonunuz başarıyla oluşturuldu. Rezervasyon
        detaylarınıza ilişkin bilgileri içeren bir e-posta, tarafınıza iletildi.
      </p>
      <p className="text-center md:text-lg">
        Lütfen e-posta gelen kutunuzu kontrol edin. Evcil dostunuzun konforu
        bizim için çok önemli! <br className="max-md:hidden" /> Herhangi bir
        sorunuz varsa, bizimle iletişime geçmekten çekinmeyin. Keyifli bir
        deneyim dileriz! 🐾
      </p>
      <div className="flex gap-3">
        <a href="https://www.pawbooking.co" rel="noopener noreferrer">
          <ButtonPrimary>Ana Sayfa</ButtonPrimary>
        </a>
      </div>
      <ConfettiFireworks />
      <div className="opacity-50">
        <ParticlesContainer />
      </div>
    </div>
  );
};

export default ReservationSuccessPage;
