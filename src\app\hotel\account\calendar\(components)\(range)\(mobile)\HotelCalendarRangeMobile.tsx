"use client";
import type { FC } from "react";
import React, { useEffect, useRef } from "react";
import DatePicker from "react-datepicker";
import HotelCalendarDatePickerRangeMobile from "./HotelCalendarDatePickerRangeMobile";
import {
  setSelectedRoom,
  setSelectedRoomId,
} from "@/store/features/hotelCalendar/calendar-slice";
import { useDispatch } from "react-redux";
import type { AllocationType } from "@/store/features/hotelCalendar/calendar-types";
import { registerLocale } from "react-datepicker";
import { tr } from "date-fns/locale";
import IconChevronLeft from "@/shared/icons/ChevronLeft";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

interface HotelCalendarRangeMobileProps {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedRoomCalendarData: AllocationType[];
  startDate: Date | undefined;
  endDate: Date | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const HotelCalendarRangeMobile: FC<HotelCalendarRangeMobileProps> = ({
  setOpen,
  startDate,
  endDate,
  setStartDate,
  setEndDate,
  selectedRoomCalendarData,
  setDrawerOpen,
}) => {
  const translate = useTranslations("HotelCalendarRange");
  registerLocale("tr", tr);
  const dispatch = useDispatch();
  const router = useRouter();
  const calendarContainerRef = useRef<HTMLDivElement>(null);

  const backRoomChoose = () => {
    dispatch(setSelectedRoomId(""));
    dispatch(setSelectedRoom(""));
    setStartDate(undefined);
    setEndDate(undefined);
    router.push("/hotel/account/calendar");
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  useEffect(() => {
    if (endDate !== undefined) {
      setOpen(true);
      setDrawerOpen(true);
    } else {
      setOpen(false);
      setDrawerOpen(false);
    }
  }, [endDate, setOpen]);

  //scrolls to today
  useEffect(() => {
    const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
    const todayElement = document.getElementById(today);

    if (todayElement && calendarContainerRef.current) {
      // calendarContainerRef.current.scrollTop = 0;
      todayElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
    }
  }, []);

  const renderSectionCheckIndate = () => {
    return (
      <div
        ref={calendarContainerRef}
        className="listingSection__wrap h-[600px] overflow-auto 2xl:h-[750px]"
      >
        {/* HEADING */}
        <div className="flex items-center justify-between">
          <div
            className="ml-3 inline-flex cursor-pointer items-center justify-center gap-1 rounded-xl bg-secondary-6000 px-4 py-2 text-center text-sm font-medium text-white duration-200 hover:bg-secondary-700"
            onClick={backRoomChoose}
          >
            <IconChevronLeft className="size-4" />
            <div>{translate("selectRoom")}</div>
          </div>
          <div className="mr-2 space-y-2">
            <div className="flex items-center gap-1">
              <span className="size-3.5 rounded-full bg-[#58d68d]"></span>
              <span className="text-xs font-medium text-neutral-700 dark:text-neutral-400">
                {translate("activeDays")}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="size-3.5 rounded-full bg-[#FFB3B3]"></span>
              <span className="text-xs font-medium text-neutral-700 dark:text-neutral-400">
                {translate("reservedDays")}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="size-3.5 rounded-full bg-[#6c757d]"></span>
              <span className="text-xs font-medium text-neutral-700 dark:text-neutral-400">
                {translate("passiveDays")}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="size-3.5 rounded-full bg-[#fd7e14]"></span>
              <span className="text-xs font-medium text-neutral-700 dark:text-neutral-400">
                {translate("noPriceDays")}
              </span>
            </div>
          </div>
        </div>

        <div className="customallocation !mt-0">
          <DatePicker
            minDate={new Date()}
            onChange={onChangeDate}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            monthsShown={3}
            showPopperArrow={false}
            inline
            locale={translate("locale")}
            renderDayContents={(day, date) => (
              <HotelCalendarDatePickerRangeMobile
                dayOfMonth={day}
                date={date}
                selectedRoomCalendarData={selectedRoomCalendarData}
              />
            )}
          />
        </div>
      </div>
    );
  };

  return renderSectionCheckIndate();
};

export default HotelCalendarRangeMobile;
