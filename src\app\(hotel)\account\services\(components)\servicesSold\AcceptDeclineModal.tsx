"use client";
import type { FC } from "react";
import React, { useState } from "react";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import type { ParameterTypes } from "./ServicesSoldActionButtons";
import { useReservationTable } from "@/hooks/hotel/useReservationTable";
import { useTranslations } from "next-intl";

interface AcceptDeclineModalProps {
  modalIsOpen: boolean;
  parameter: ParameterTypes;
  setModalIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  rowOriginal: any;
  hotelToken: string | undefined;
}

const AcceptDeclineModal: FC<AcceptDeclineModalProps> = ({
  modalIsOpen,
  parameter,
  setModalIsOpen,
  rowOriginal,
  hotelToken,
}) => {
  const translate = useTranslations("AcceptDeclineModal");
  const [loading, setLoading] = useState<boolean>(false);
  const { handleSuccessItem, confirmItem } = useReservationTable(setLoading);
  const { text, status } = parameter;
  const { receiptNo, hotelSubMerchantGuid, _id } = rowOriginal;

  return (
    <Dialog open={modalIsOpen}>
      <DialogContent onInteractOutside={() => setModalIsOpen(false)}>
        <DialogHeader>
          <DialogTitle>{text.heading}</DialogTitle>
          <DialogDescription className="sr-only"></DialogDescription>
        </DialogHeader>
        <div>
          <div className="mb-4">
            <p className="text-gray-500 dark:text-neutral-200 max-sm:text-center">
              {text.description}
            </p>
          </div>
          <div className="flex justify-center gap-5">
            <Button
              className="w-full md:w-1/3"
              variant="outline"
              onClick={() => setModalIsOpen(false)}
              type="button"
            >
              {translate("cancel")}
            </Button>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full md:w-1/3"
              onClick={() => {
                rowOriginal.status === "booked"
                  ? handleSuccessItem({
                      virtualPosTransactionId: receiptNo,
                      hotelParamGuid: hotelSubMerchantGuid,
                      itemId: _id,
                      itemType: "services",
                      hotelToken,
                      status,
                    })
                  : confirmItem(
                      {
                        pysOrderGuid: rowOriginal.pysOrderGuid,
                        status,
                        itemId: rowOriginal._id,
                        itemType: "services",
                      },
                      hotelToken
                    );
              }}
              type="button"
            >
              {loading ? <LoadingSpinner /> : text.button}
            </Button>
          </div>
        </div>
        <DialogClose
          onClick={() => setModalIsOpen(false)}
          className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="size-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default AcceptDeclineModal;
