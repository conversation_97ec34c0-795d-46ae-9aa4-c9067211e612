"use client";

import { usePathname, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  Info,
  MapPin,
  Settings,
  PawPrint,
  Image,
  FileText,
} from "lucide-react";
import { Route } from "next";

const tabs = [
  {
    id: 0,
    label: "Taksi Bilgileri",
    path: "/petTaxi/account/pet-taxi-informations",
    icon: <Info className="w-6 h-6" />,
  },
  {
    id: 1,
    label: "Taksi Adresi",
    path: "/petTaxi/account/pet-taxi-informations/pet-taxi-address",
    icon: <MapPin className="w-6 h-6" />,
  },
  {
    id: 2,
    label: "Taksi Özellikleri",
    path: "/petTaxi/account/pet-taxi-informations/pet-taxi-features",
    icon: <Settings className="w-6 h-6" />,
  },
  {
    id: 3,
    label: "Kabul Edilen <PERSON>ü<PERSON>",
    path: "/petTaxi/account/pet-taxi-informations/pet-taxi-pet-types",
    icon: <PawPrint className="w-6 h-6" />,
  },
  {
    id: 4,
    label: "Taksi Fotoğrafları",
    path: "/petTaxi/account/pet-taxi-informations/pet-taxi-photos",
    icon: <Image className="w-6 h-6" />,
  },
  {
    id: 5,
    label: "Taksi Belgeleri",
    path: "/petTaxi/account/pet-taxi-informations/pet-taxi-files",
    icon: <FileText className="w-6 h-6" />,
  },
];

function PetTaxiInformationsNav({ className }: { className?: string }) {
  const router = useRouter();
  const pathname = usePathname();
  const activeTab = tabs.findIndex((tab) => tab.path === pathname);

  const handleTabClick = (id: number) => {
    const tab = tabs[id];
    if (tab) router.push(tab.path as Route);
  };

  return (
    <div className="flex flex-col items-center w-full">
      <div className={cn("flex justify-between w-full space-x-4 rounded-full", className)}>
        {tabs.map((tab, index) => (
          <motion.button
            key={tab.id}
            whileTap={"tapped"}
            whileHover={"hovered"}
            onClick={() => handleTabClick(tab.id)}
            className={cn(
              "relative px-1.5 tracking-normal cursor-pointer transition focus-visible:outline-1 focus-visible:ring-1 focus-visible:outline-none flex gap-1 items-center",
              activeTab === tab.id
                ? "text-secondary-6000 dark:text-white font-medium"
                : "text-neutral-500 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-white"
            )}
            style={{ WebkitTapHighlightColor: "transparent" }}
          >
            {activeTab === tab.id && (
              <motion.span
                layoutId="bubble"
                className="absolute bottom-0 w-full left-0 z-10 bg-secondary-6000 dark:bg-white rounded-full h-1"
                transition={{ type: "spring", bounce: 0.19, duration: 0.4 }}
              />
            )}
            <motion.div
              initial={{ scale: 0 }}
              animate={{
                scale: 1,
                transition: {
                  type: "spring",
                  bounce: 0.2,
                  damping: 7,
                  duration: 0.4,
                  delay: index * 0.05,
                },
              }}
              variants={{
                default: { scale: 1 },
                hovered: { scale: 1.1 },
                tapped: { scale: 0.9 },
              }}
              className="relative"
              transition={{ type: "spring" }}
            >
              <div className="size-10 flex items-center justify-center my-2">
                {tab.icon}
              </div>
            </motion.div>
            <span>{tab.label}</span>
          </motion.button>
        ))}
      </div>
    </div>
  );
}

export default PetTaxiInformationsNav;
