import type React from "react";
import { useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useDispatch } from "react-redux";
import { setAllocations } from "@/store/features/hotelCalendar/calendar-slice";

export const useHotelCalendarActions = () => {
  const dispatch = useDispatch();
  const { toast } = useToast();

  //TODO: dateleri dinamik yap
  // fetches the price information and active/inactive status of days within a specific date range.
  const getRoomAllocation = useCallback(
    async (
      hotelToken: string | undefined,
      selectedRoomId: string | string[] | undefined
    ) => {
      if (hotelToken && selectedRoomId) {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/roomAllocations?pageSize=999&startDate=2024-07-01&endDate=2025-09-30&room=${selectedRoomId}`,
            {
              headers: {
                hotelToken: hotelToken,
                "Content-Type": "application/json",
              },
            }
          );
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          const data = await response.json();
          if (data.success) {
            dispatch(setAllocations(data.data.docs));
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      }
    },
    [dispatch]
  );

  // adds and updates price for selected dates
  const setRoomPrice = async (
    hotelToken: string | undefined,
    startDateToString: string | undefined,
    endDateToString: string | undefined,
    selectedRoomId: string | string[] | undefined,
    newRoomPrice: string | undefined,
    setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (hotelToken && startDateToString && endDateToString) {
      setLoading(true);
      setDisabled(true);
      const roomsArray =
        typeof selectedRoomId === "string" ? [selectedRoomId] : selectedRoomId;
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/roomAllocations`,
          {
            method: "POST",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              rooms: roomsArray,
              startDate: startDateToString,
              endDate: endDateToString,
              price: Number(newRoomPrice),
            }),
          }
        );

        const data = await response.json();
        if (!response.ok && !data.success) {
          const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        setFetchAgain((prevState) => !prevState);
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
        toast({
          variant: "success",
          duration: 3000,
          title: "Fiyat güncelleme",
          description: "Fiyat güncellendi",
        });
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // gets information of selected room
  const fetchSelectedRoomData = useCallback(
    async (
      hotelToken: string | undefined,
      selectedRoomId: string | string[] | undefined
    ) => {
      if (hotelToken) {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/rooms/${selectedRoomId}`,
            {
              cache: "no-cache",
              headers: {
                hotelToken: hotelToken,
                "Content-Type": "application/json",
              },
            }
          );
          const result = await response.json();
          if (!response.ok || !result.success) {
            throw new Error("Network response was not ok");
          }
          return result;
        } catch (err: any) {
          console.error("Error fetching data:", err);
        }
      }
    },
    []
  );

  // opens and closes the selected day(s) for reservations
  const calendarDateStatusHandler = async (
    hotelToken: string | undefined,
    startDateToString: string | undefined,
    endDateToString: string | undefined,
    selectedRoomId: string | string[] | undefined,
    setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>,
    checked: boolean
  ) => {
    const setType = checked ? "setPassive" : "setActive";
    if (hotelToken && startDateToString && endDateToString) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/roomAllocations/${setType}`,
          {
            method: "POST",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              room: selectedRoomId,
              startDate: startDateToString,
              endDate: endDateToString,
            }),
          }
        );

        const data = await response.json();
        if (!response.ok && !data.success) {
          const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }
        setFetchAgain((prevState) => !prevState);
        toast({
          variant: "success",
          duration: 3000,
          title: "Tarih güncelleme",
          description: "Tarih durumu güncellendi",
        });
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  return {
    getRoomAllocation,
    setRoomPrice,
    fetchSelectedRoomData,
    calendarDateStatusHandler,
  };
};
