"use client";
import React from "react";
import type { FC } from "react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import PetOwnerInformation from "../PetOwnerInformation";
import VetInformation from "../VetInformation";
import RoomInformation from "../RoomInformation";
import PetInformation from "../PetInformation";
import { But<PERSON> } from "@/components/ui/button";
import BalanceInformation from "../Balance";

interface TodayRoomCardMobileProps {
  selectedRoom: any;
  isOpen: boolean;
  setIsOpen: any;
  hotelToken: string | undefined;
  onImageClick: (url: string | string[], fileName: string) => void;
  activeTab: string;
  setActiveTab: any;
}

const TodayRoomCardMobile: FC<TodayRoomCardMobileProps> = ({
  selectedRoom,
  isOpen,
  setIsOpen,
  hotelToken,
  onImageClick,
  activeTab,
  setActiveTab,
}) => {
  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerContent className="px-4 h-[calc(100vh-40px)]">
        <Tabs
          defaultValue="roomInformation"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="mt-2 flex shrink-0 bg-white text-black dark:bg-inherit dark:text-inherit overflow-auto max-sm:justify-normal">
            {(selectedRoom?.reservation?.status === "confirmed" ||
              selectedRoom?.reservation?.status === "waitingForCheckIn" ||
              selectedRoom?.reservation?.status === "checkedIn" ||
              selectedRoom?.reservation?.status === "waitingForCheckOut" ||
              selectedRoom?.reservation?.status === "checkedOut") && (
              <TabsTrigger
                value="balance"
                className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
              >
                Ödeme Bilgileri
              </TabsTrigger>
            )}
            <TabsTrigger
              value="roomInformation"
              className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
            >
              Oda Bilgileri
            </TabsTrigger>
            {(selectedRoom?.reservation?.status === "confirmed" ||
              selectedRoom?.reservation?.status === "waitingForCheckIn" ||
              selectedRoom?.reservation?.status === "checkedIn" ||
              selectedRoom?.reservation?.status === "waitingForCheckOut" ||
              selectedRoom?.reservation?.status === "checkedOut") && (
              <TabsTrigger
                value="petInformation"
                className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
              >
                Pet Bilgileri
              </TabsTrigger>
            )}
            {(selectedRoom?.reservation?.status === "confirmed" ||
              selectedRoom?.reservation?.status === "waitingForCheckIn" ||
              selectedRoom?.reservation?.status === "checkedIn" ||
              selectedRoom?.reservation?.status === "waitingForCheckOut" ||
              selectedRoom?.reservation?.status === "checkedOut") && (
              <TabsTrigger
                value="contactInformation"
                className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
              >
                İletişim Bilgileri
              </TabsTrigger>
            )}
          </TabsList>
          <TabsContent value="balance">
            <div className="mt-4 overflow-y-auto h-[calc(100vh-200px)]">
              <DrawerHeader>
                <DrawerTitle>Ödeme Bilgileri</DrawerTitle>
                <DrawerDescription></DrawerDescription>
              </DrawerHeader>
              <Separator className="my-2" />
              <BalanceInformation
                selectedRoom={selectedRoom}
                hotelToken={hotelToken}
              />
            </div>
          </TabsContent>
          <TabsContent value="roomInformation">
            <div className="mt-4 overflow-y-auto h-[calc(100vh-200px)]">
              <DrawerHeader>
                <DrawerTitle>Oda Bilgileri</DrawerTitle>
                <DrawerDescription></DrawerDescription>
              </DrawerHeader>
              <Separator className="my-2" />
              <RoomInformation
                selectedRoom={selectedRoom}
                hotelToken={hotelToken}
                setIsOpen={setIsOpen}
              />
            </div>
          </TabsContent>
          <TabsContent value="petInformation">
            <div className="mt-4 overflow-y-auto h-[calc(100vh-220px)]">
              <DrawerHeader>
                <DrawerTitle>Pet Bilgileri</DrawerTitle>
                <DrawerDescription></DrawerDescription>
              </DrawerHeader>
              <Separator className="my-2" />
              <PetInformation
                selectedRoom={selectedRoom}
                onImageClick={onImageClick}
              />
            </div>
          </TabsContent>
          <TabsContent value="contactInformation">
            <div className="mt-2">
              <Tabs defaultValue="petOwnerContact">
                <TabsList className="mt-2 flex shrink-0 gap-1 bg-white text-black dark:bg-inherit dark:text-inherit">
                  <TabsTrigger
                    value="petOwnerContact"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
                  >
                    Pet Sahibi
                  </TabsTrigger>
                  <TabsTrigger
                    value="veterinaryContact"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white text-xs"
                  >
                    Sorumlu Veteriner
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="petOwnerContact">
                  <PetOwnerInformation selectedRoom={selectedRoom} />
                </TabsContent>
                <TabsContent value="veterinaryContact">
                  <VetInformation selectedRoom={selectedRoom} />
                </TabsContent>
              </Tabs>
            </div>
          </TabsContent>
        </Tabs>
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button className="rounded-xl" variant="outline">
              Kapat
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default TodayRoomCardMobile;
