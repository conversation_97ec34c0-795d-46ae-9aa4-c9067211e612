import React from "react";
import type { FC } from "react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import ButtonSecondary from "@/shared/ButtonSecondary";
import CheckoutDetail from "../CheckoutDetail";

interface MobileCheckoutDrawerProps {
  selectedTier: any;
}

const MobileCheckoutDrawer: FC<MobileCheckoutDrawerProps> = ({
  selectedTier,
}) => {
  return (
    <Drawer>
      <DrawerTrigger className="underline">Detayı gör</DrawerTrigger>
      <DrawerContent className="max-h-[calc(100vh-50px)]">
        <DrawerHeader>
          <DrawerTitle className="sr-only"></DrawerTitle>
          <DrawerDescription className="sr-only"></DrawerDescription>
        </DrawerHeader>
        <div className="overflow-y-auto flex w-full flex-col space-y-6 border border-neutral-200 p-3 dark:border-neutral-700 sm:space-y-8 sm:rounded-2xl sm:p-4">
          {selectedTier && <CheckoutDetail selectedTier={selectedTier} />}
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <ButtonSecondary>Kapat</ButtonSecondary>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileCheckoutDrawer;
