"use client";
import React, { useRef, useEffect } from "react";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";

const HotelInformationsNav = () => {
  const translate = useTranslations("HotelInformationsNav");
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  interface NavItem {
    name: string;
    path: string;
    id: number;
  }
  const navList: NavItem[] = [
    {
      name: translate("hotelInfo"),
      path: "/hotel/account/hotel-informations",
      id: 1,
    },
    {
      name: translate("hotelAddress"),
      path: "/hotel/account/hotel-informations/hotel-address",
      id: 2,
    },
    {
      name: translate("hotelFeatures"),
      path: "/hotel/account/hotel-informations/hotel-features",
      id: 3,
    },
    {
      name: translate("hotelAcceptedPetTypes"),
      path: "/hotel/account/hotel-informations/hotel-pet-types",
      id: 4,
    },
    {
      name: translate("hotelPhotos"),
      path: "/hotel/account/hotel-informations/hotel-photos",
      id: 5,
    },
    {
      name: translate("hotelDocuments"),
      path: "/hotel/account/hotel-informations/hotel-files",
      id: 6,
    },
  ];

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = navList.findIndex((item) => item.path === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname]);

  return (
    <div
      ref={navContainerRef}
      className="hiddenScrollbar flex justify-between gap-3 overflow-y-hidden bg-transparent px-1 pb-5 text-sm md:overflow-x-auto md:pb-1.5 md:text-base"
    >
      {navList.map((navItem) => (
        <Link
          href={navItem.path as Route}
          key={navItem.id}
          className={`${pathname === navItem.path ? "bg-secondary-6000 text-white font-medium" : "bg-transparent"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 text-neutral-700 dark:text-neutral-200`}
        >
          {navItem.name}
        </Link>
      ))}
    </div>
  );
};

export default HotelInformationsNav;
