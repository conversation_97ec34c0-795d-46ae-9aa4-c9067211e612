"use client";
import React from "react";
import { useTranslations } from "next-intl";

interface FileStepHeaderProps {
  currentStep: number;
  filteredStepsLength: number;
  currentStepName?: string;
}

const FileStepHeader: React.FC<FileStepHeaderProps> = ({
  currentStep,
  filteredStepsLength,
  currentStepName,
}) => {
  const translate = useTranslations("FileStep");

  return (
    <div className="mt-5 md:mt-0 mb-8">
      <p className="text-xl font-semibold mb-2">{translate("hotelFiles")}</p>
      <p className="text-sm text-neutral-600 dark:text-neutral-400">
        {currentStep + 1} / {filteredStepsLength} - {currentStepName}
      </p>
    </div>
  );
};

export default FileStepHeader;
