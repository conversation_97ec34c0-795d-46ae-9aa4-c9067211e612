import type { FormEvent } from "react";
import { PUBLIC_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";
import slug from "slug";
import { useRouter } from "next/navigation";
import { ImageResizeFitType, uploadMultiToS3 } from "@/lib/s3BucketHelper";
import { useSearchParams } from "next/navigation";

export const useHotelPetPublic = () => {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  const orderIdParam = searchParams.get("orderId");
  const addPetParam = searchParams.get("addPet");
  // ADDS NEW PET
  const addNewPetHandler = async (
    event: FormEvent,
    hotelId: string,
    hotelCustomerId: string,
    petInformations: PetInformationTypes,
    vaccineFile: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeAddNewPetModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    try {
      const response = await fetch(PUBLIC_API_PATHS.addHotelPet, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: petInformations.name,
          age: petInformations.age,
          kind: petInformations.kind,
          breed: petInformations.breed,
          gender: petInformations.gender,
          color: petInformations.color,
          specified: petInformations.specified,
          infertile: petInformations.infertile,
          microChipNumber: petInformations.microChipNumber,
          vaccines: petInformations.vaccines,
          internalParasiteTreatment: petInformations.internalParasiteTreatment,
          externalParasiteTreatment: petInformations.externalParasiteTreatment,
          treatmentDate: petInformations.internalTreatmentDate,
          externalTreatmentDate: petInformations.externalTreatmentDate,
          internalTreatmentDate: petInformations.internalTreatmentDate,
          allergicTo: petInformations.allergicTo,
          hereditaryDiseases: petInformations.hereditaryDiseases,
          operationHistory: petInformations.operationHistory,
          medicine: petInformations.medicine,
          feedingHabits: petInformations.feedingHabits,
          description: petInformations.description,
          documentPhotos: vaccineFile,
          hotel: hotelId,
          owner: hotelCustomerId,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 6000,
        title: "Evcil Hayvan Ekleme",
        description: "Evcil hayvan başarıyla eklendi.",
      });
      revalidatePathHandler("/");
      closeAddNewPetModal();
      setLoading(false);
      setDisabled(false);
      if (orderIdParam && addPetParam) {
        router.push(`/at-door-reservation?orderId=${orderIdParam}`);
        return;
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const documentPhotosHandler = async (
    event: FormEvent,
    file: FileList | undefined,
    hotelId: string,
    hotelCustomerId: string,
    petInformations: PetInformationTypes,
    closeAddNewPetModal: () => void,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    hotelToken?: string | undefined
  ) => {
    event.preventDefault();
    setLoading(true);
    setDisabled(true);
    // if vaccine report does not exist skip upload step
    if (!file) {
      return addNewPetHandler(
        event,
        hotelId,
        hotelCustomerId,
        petInformations,
        [],
        setLoading,
        closeAddNewPetModal,
        setDisabled
      );
    }
    try {
      const response = await uploadMultiToS3(
        file,
        `hotel/${hotelId}/hotelCustomers/${hotelCustomerId}/petVaccine/${slug(petInformations.name)}`,
        hotelToken,
        ImageResizeFitType.contain
      );
      if (response.success) {
        const documentPhotoId = response.data.map((item: any) => item._id);
        addNewPetHandler(
          event,
          hotelId,
          hotelCustomerId,
          petInformations,
          documentPhotoId,
          setLoading,
          closeAddNewPetModal,
          setDisabled
        );
        revalidatePathHandler("/");
      } else {
        setLoading(false);
        setDisabled(false);
        throw new Error("Aşı karnesi başarısız oldu.");
      }
    } catch (error) {
      console.error("Aşı karnesi yükleme hatası:", error);
    }
  };

  return {
    documentPhotosHandler,
  };
};
