import React from "react";
import PostingContainer from "./(components)/PostingContainer";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import getRoomGroups from "@/actions/(protected)/hotel/getRoomGroups";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";

const RoomsPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const roomGroupData = await getRoomGroups();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div className="container">
      <PostingContainer
        hotelData={hotelData?.data}
        hotelToken={hotelToken}
        roomGroupData={roomGroupData?.data?.data}
        membershipData={membershipData?.data}
      />
    </div>
  );
};

export default RoomsPage;
