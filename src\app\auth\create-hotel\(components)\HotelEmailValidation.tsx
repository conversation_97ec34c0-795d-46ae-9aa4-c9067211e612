"use client";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { emailRegex } from "../create-hotel-regex";
import { useMailStepActions } from "@/hooks/createHotelSteps/useMailStep";
import { useTranslations } from "next-intl";

export default function HotelEmailValidation() {
  const translate = useTranslations("HotelEmailValidation");
  const { hotelSecondMailStepHandler, handleCheckEMail } = useMailStepActions();
  const [hotelEmail, setHotelEmail] = useState<string | null>(null);
  const [authCode, setAuthCode] = useState<string | null>(null);
  const [disabled, setDisabled] = useState<boolean>(false);
  const validMail = hotelEmail && emailRegex.test(hotelEmail);
  const mailAuthStep = useSelector(
    (state: RootState) => state.hotelAuth.mailAuthStep
  );

  return (
    <div>
      <h2 className="pb-5 text-2xl font-semibold">
        {translate("emailVerification")}
      </h2>
      <div className="space-y-5">
        {mailAuthStep ? (
          <div>
            <div className="nc-Label mb-1 text-sm font-medium text-neutral-700 dark:text-neutral-300">
              {translate("emailCode")}
            </div>
            <InputOTP
              onChange={(e) => setAuthCode(e)}
              maxLength={6}
              pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
              value={authCode || ""}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>
        ) : (
          <FormItem label={translate("emailAdress")}>
            <Input
              type="email"
              name="email"
              value={hotelEmail || ""}
              onChange={(e) =>
                setHotelEmail(e.target.value.toLowerCase().trim())
              }
            />
            {!validMail && hotelEmail && (
              <span className="mt-3 block text-xs text-red-500">
                {translate("validEmail")}
              </span>
            )}
          </FormItem>
        )}
        <div className="flex items-center gap-2">
          {!mailAuthStep ? (
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={() =>
                handleCheckEMail(hotelEmail, setHotelEmail, setDisabled)
              }
              disabled={disabled || !validMail}
            >
              {translate("send")}
            </Button>
          ) : (
            <Button
              onClick={() =>
                hotelSecondMailStepHandler(
                  hotelEmail,
                  authCode,
                  setAuthCode,
                  setDisabled
                )
              }
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={disabled || !(authCode && authCode.length === 6)}
            >
              {translate("confirm")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
