"use server";
import { redirect } from "next/navigation";
import { PUBLIC_API_PATHS } from "@/utils/apiUrls";

export default async function finishOrderPublic(orderId: string, data: any) {
  try {
    const response = await fetch(`${PUBLIC_API_PATHS.finishOrder}/${orderId}`, {
      cache: "no-cache",
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    const result = await response.json();

    if (result.status === 401) {
      redirect("/");
    } else if (result.status === 404) {
      redirect("/404");
    }

    if (!response.ok || !result.success) {
      console.error("Network response was not ok");
      return undefined;
    }
    return result;
  } catch (err: unknown) {
    console.error("Error fetching data:", err);
    return undefined;
  }
}
