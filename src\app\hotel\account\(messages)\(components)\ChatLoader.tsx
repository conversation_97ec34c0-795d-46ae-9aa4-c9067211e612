"use client";
import React, { useEffect } from "react";
import type { FC } from "react";
import type { RootState } from "@/store";
import { useSelector, useDispatch } from "react-redux";
import { setIsLoading } from "@/store/features/liveChat/live-chat-slice";
import { LoaderOne } from "@/components/ui/loader";

interface ChatLoaderProps {
  selectedId: string | string[] | undefined;
}

const ChatLoader: FC<ChatLoaderProps> = ({ selectedId }) => {
  const dispatch = useDispatch();
  const loading = useSelector(
    (state: RootState) => state.liveChatText.isLoading
  );

  useEffect(() => {
    dispatch(setIsLoading(false));
  }, [selectedId, dispatch]);

  useEffect(() => {
    if (selectedId) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [selectedId]);

  return (
    <>
      {loading && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <LoaderOne />
        </div>
      )}
    </>
  );
};

export default ChatLoader;
