export interface PetTaxiAddressApiTypes {
  country?: {
    identificationCode: string;
    name: string;
  };
  streetName: string;
  buildingName: string;
  buildingNumber: string;
  cityName: string;
  postalZone: string;
  region: string;
  district: string;
  blockName?: string;
  citySubdivisionName?: string;
  postbox?: string;
  room?: string;
}

export interface PetTaxiInformationApiTypes {
  petTaxiName: string;
  petTaxiDescription: string;
}
