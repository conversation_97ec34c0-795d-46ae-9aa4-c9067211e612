export interface RoomImage {
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  img800: ImageSize;
  img400: ImageSize;
  img200: ImageSize;
  img100: ImageSize;
  tags: string;
  createdAt: string;
  updatedAt: string;
}

interface ImageSize {
  src: string;
  width: number;
  height: number;
  size: number;
}

interface Room {
  _id: string;
  hotel: string;
  petType: string;
  roomName: string;
  roomCapacity: number;
  roomNameStartingNumber: number;
  roomDescription: string;
  roomFeatures: string[];
  nonAcceptedBreedTypes: string[];
  images: string[];
  roomGroup: string;
  passive: boolean;
  reviews: any[];
  createdAt: string;
  updatedAt: string;
}

export interface RoomGroupDataApiTypes {
  _id: string;
  roomGroupName: string;
  roomCount: number;
  hotel: string;
  description: string;
  features: string[];
  nonAcceptedBreedTypes: string[];
  petType: string[];
  images: RoomImage[];
  passive: boolean;
  rooms: Room[];
  createdAt: string;
  updatedAt: string;
}

export type RoomGroupListType = RoomGroupDataApiTypes[];
