"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { useHotelPet } from "@/hooks/hotel/useHotelPets";
import IconDelete from "@/shared/icons/Delete";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

interface DeletePetProps {
  hotelToken: string | undefined;
  petId: string;
  petName: string;
}

const DeletePet: FC<DeletePetProps> = ({ hotelToken, petId, petName }) => {
  const { deletePetHandler } = useHotelPet();
  const [loading, setLoading] = useState<boolean>(false);
  const [deletePetIsOpen, setDeletePetIsOpen] = useState(false);

  function closeDeletePetModal() {
    setDeletePetIsOpen(false);
    setLoading(false);
  }

  function openDeletePetModal() {
    setDeletePetIsOpen(true);
  }
  return (
    <div>
      <IconDelete
        onClick={openDeletePetModal}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
      />
      <Dialog open={deletePetIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            closeDeletePetModal();
          }}
        >
          <DialogHeader>
            <DialogTitle>Evcil Hayvan Kaldırma</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deletePetHandler(
                event,
                hotelToken,
                petId,
                setLoading,
                closeDeletePetModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                {petName}'i silmek istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <ButtonSecondary type="button" onClick={closeDeletePetModal}>
                Vazgeç
              </ButtonSecondary>
              <ButtonPrimary type="submit">
                {loading ? <LoadingSpinner /> : "Onayla"}
              </ButtonPrimary>
            </div>
          </form>
          <DialogClose
            onClick={closeDeletePetModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DeletePet;
