"use client";
import React, { useEffect, useState } from "react";
import type { FC, ChangeEvent } from "react";
import DistanceCalculator from "@/components/DistanceCalculator";
import { Separator } from "@/components/ui/separator";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { Calendar } from "@/components/ui/calendar";
import { createLocalDate } from "@/utils/createLocalDate";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";
import { useSearchParams } from "next/navigation";
import { useJsApiLoader } from "@react-google-maps/api";

interface TransportationServiceProps {
  service: any;
  hotelToken: string | undefined;
  hotelId: string;
  onClose?: () => void;
  hotelLocation: string;
  startDate: Date | string | undefined;
}

const TransportationService: FC<TransportationServiceProps> = ({
  service,
  hotelToken,
  hotelId,
  onClose,
  hotelLocation,
  startDate,
}) => {
  const { addItemToCart } = useHotelAtDoorReservation();
  const [disabled, setDisabled] = useState<boolean>(false);
  const [formData, setFormData] = useState<{
    serviceDate: string;
    note: string;
    distance: string;
    startLocation: string;
    endLocation: string;
    totalPrice: number;
  }>({
    serviceDate: startDate
      ? typeof startDate === "string"
        ? startDate
        : `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, "0")}-${String(startDate.getDate()).padStart(2, "0")}`
      : "",
    note: "",
    distance: "",
    startLocation: "",
    endLocation: "",
    totalPrice: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const today = format(new Date(), "yyyy-MM-dd");
  const calendarLanguage = datePickerLanguageHandler("tr");
  const monthFormatter = new Intl.DateTimeFormat(
    calendarLanguage.firstValue === "tr" ? "tr" : "en",
    {
      month: "long",
    }
  );
  const searchParams = useSearchParams();

  const API_KEY = "AIzaSyCXi01Rpagu_wbKsoLUMW0UwWNhfn7xg6A";

  const handleDistanceChange = (newDistance: string | null) => {
    setFormData({ ...formData, distance: newDistance ?? "" });
  };

  const handleLocationsChange = (start: string, end: string) => {
    setFormData({ ...formData, startLocation: start, endLocation: end });
  };

  useEffect(() => {
    setFormData({
      ...formData,
      totalPrice:
        (Number(service?.serviceData?.serviceDetails?.initialPrice) || 0) +
        Number(formData.distance || 0) *
          (Number(service?.serviceData?.serviceDetails?.distancePrice) || 0),
    });
  }, [
    formData.distance,
    service?.serviceData?.serviceDetails?.initialPrice,
    service?.serviceData?.serviceDetails?.distancePrice,
  ]);

  useEffect(() => {
    const newStartDate = searchParams.get("startDate") || "";
    if (!newStartDate) return;
    setFormData((prev) => ({ ...prev, serviceDate: newStartDate }));
  }, [searchParams]);

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: API_KEY,
    libraries: ["places"],
  });

  if (loadError) return <div>Harita yüklenemedi.</div>;
  if (!isLoaded) return <></>;

  const handleChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormData((prevState: any) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const buttonDisabledHandler = () => {
    const isFormDataValid = Object.entries(formData).every(([key, value]) => {
      // if (key === "note") return true;
      if (typeof value === "string") {
        return value.trim() !== "";
      }
      return Boolean(value);
    });

    if (!isFormDataValid) {
      return { errorText: "", disabled: true };
    }

    const isValidDate = new Date(formData.serviceDate) >= new Date(today);
    if (!isValidDate) {
      return {
        errorText:
          "Hizmet tarihi boş bırakılamaz ve seçilecek tarih bugünden önce olamaz.",
        disabled: true,
      };
    }

    return { errorText: "", disabled: false };
  };

  const isButtonDisabled = buttonDisabledHandler();

  const addToCartHandler = () => {
    const requestBody = {
      itemType: "service",
      itemData: {
        ...service?.serviceData,
        note: formData.note,
        serviceDate: formData.serviceDate,
        serviceDetails: {
          ...service?.serviceData?.serviceDetails,
          startPoint: formData.startLocation,
          endPoint: formData.endLocation,
          distance: formData.distance,
        },
        serviceType: service?.serviceType,
        serviceHotelId: service?._id,
        hotel: hotelId,
        pet: null,
      },
      selectedItems: calculatedRoomData?.selectedItems || [],
      totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
    };

    addItemToCart(hotelToken, requestBody, setLoading, onClose, setDisabled);
  };

  return (
    <form
      className="space-y-3"
      onSubmit={(event) => {
        event.preventDefault();
        addToCartHandler();
      }}
    >
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Hizmet Adı:</p>
          <p className="text-sm capitalize font-medium">
            {service?.serviceData?.serviceName}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Başlangıç Fiyatı:</p>
          <p className="text-sm capitalize font-medium">
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(
              Number(service?.serviceData?.serviceDetails?.initialPrice)
            ) + "₺"}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Maksimum Mesafe:</p>
          <p className="text-sm font-medium">
            {service?.serviceData?.serviceDetails?.maxDistance} km
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Km Başı Fiyat:</p>
          <p className="text-sm capitalize font-medium">
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(
              Number(service?.serviceData?.serviceDetails?.distancePrice)
            ) + "₺"}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <p className="text-sm font-semibold">Açıklama:</p>
          <p className="text-sm font-medium">
            {service?.serviceData?.description}
          </p>
        </div>
        <Separator className="mt-2" />
        <div className="flex flex-col gap-1 z-[1000] mt-3">
          <p className="text-sm font-semibold">Adres Seçimi:</p>
          <DistanceCalculator
            onDistanceChange={handleDistanceChange}
            onLocationsChange={handleLocationsChange}
            hotelLocation={hotelLocation}
            setFormData={setFormData}
          />
        </div>
        <div className="flex flex-col gap-1 mt-2">
          <p className="text-sm font-semibold">Hizmet Tarihi:</p>
          <Calendar
            mode="single"
            locale={calendarLanguage.secondValue}
            formatters={{
              formatMonthDropdown: (date) => monthFormatter.format(date),
            }}
            disabled={{ before: new Date() }}
            defaultMonth={createLocalDate(formData.serviceDate)}
            startMonth={
              new Date(new Date().getFullYear(), new Date().getMonth())
            }
            endMonth={new Date(2050, 11)}
            required
            selected={createLocalDate(formData.serviceDate)}
            onSelect={(selectedDate) => {
              const adjustedStartDate = adjustDateToTimezone(selectedDate);
              const dateToString = adjustedStartDate
                ?.toISOString()
                .split("T")[0];
              setFormData((prev: any) => ({
                ...prev,
                serviceDate: dateToString,
              }));
            }}
            className="rounded-md border shadow-sm w-full"
            classNames={{
              today: "bg-transparent text-foreground rounded-md",
            }}
            {...({ disableAutoUnselect: true } as any)}
            captionLayout="dropdown"
          />
          <p className="text-red-500 text-[12px] mt-1">
            {isButtonDisabled.errorText}
          </p>
        </div>
        <div className="flex flex-col gap-1 mt-3">
          <p className="text-sm font-semibold">Açık Adres:</p>
          <Textarea
            name="note"
            placeholder="Lütfen açık adresi yazınız"
            value={formData.note}
            onChange={handleChange}
          />
        </div>
      </div>
      <div className="flex items-center gap-1">
        <p className="max-sm:text-sm font-medium">Fiyat:</p>
        <p className="font-bold">
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(formData.totalPrice)) + "₺"}
        </p>
      </div>
      <div className="flex justify-end gap-5 pb-1">
        <Button variant="ghost" type="button" onClick={onClose}>
          İptal
        </Button>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
          disabled={disabled || isButtonDisabled.disabled}
        >
          {loading ? <LoadingSpinner /> : "Ekle"}
        </Button>
      </div>
    </form>
  );
};

export default TransportationService;
