import React from "react";
import type { FC } from "react";
import Image from "next/image";
import Link from "next/link";
import IconArrowRightLong from "@/shared/icons/ArrowRightLong";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import { PET_TYPES } from "@/app/(enums)/enums";

interface CalendarRoomCardProps {
  room: any;
}

const CalendarRoomCard: FC<CalendarRoomCardProps> = ({ room }) => {
  const petTypeNames = room.petType
    .map((petType: any) => PET_TYPES[petType as keyof typeof PET_TYPES])
    .filter(Boolean); // eskiden kalan sadece dog tipi undefined dönüyor onu filterlamak için
  return (
    <Link
      href={`/account/calendar?selectedRoom=${room.rooms[0]._id}&selectedRoomGroup=${room._id}`}
      className="group hover:border-secondary-6000/45 cursor-pointer border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm duration-200"
    >
      <div className="flex justify-between items-center">
        <div className="space-y-2 basis-3/4">
          <div className="flex items-center space-x-2">
            <h2
              className={`font-semibold capitalize text-neutral-900 dark:text-white`}
            >
              <span className="line-clamp-1">{room.roomGroupName}</span>
            </h2>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>Oda Sayısı:</div>
            <div> {room.roomCount}</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>Oda Kapasitesi:</div>
            <div> {room.rooms[0].roomCapacity}</div>
          </div>
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            <div>Evcil Hayvan Tipi:</div>
            <div className="capitalize flex flex-wrap">
              {petTypeNames.map((pet: any, index: number) => {
                return (
                  <p key={index}>
                    {pet}
                    {index < petTypeNames.length - 1 && <span>,&nbsp;</span>}
                  </p>
                );
              })}
            </div>
          </div>
        </div>
        <div className="relative w-20 h-20">
          <Image
            className="object-cover rounded-full border"
            src={room.images[0] ? room.images[0].src : defaultHotelImage}
            fill
            alt=""
          />
        </div>
      </div>
      <div className="flex justify-end mt-1 mr-2">
        <IconArrowRightLong className="size-6 group-hover:text-secondary-6000" />
      </div>
    </Link>
  );
};

export default CalendarRoomCard;
