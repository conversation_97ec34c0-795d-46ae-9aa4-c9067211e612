"use client";
import { useEffect, useState } from "react";
import Label from "@/components/Label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { useToast } from "@/components/ui/use-toast";
import AgreementContainer from "./(agreements)/AgreementContainer";

interface PaymentStepProps {
  orderData: any;
}
const PaymentStep: React.FC<PaymentStepProps> = ({ orderData }) => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const [channel, setChannel] = useState("");
  const [paymentType, setPaymentType] = useState("");
  const [agreementsChecked, setAgreementsChecked] = useState(false);

  const handleChannelChange = (newChannel: string) => {
    setChannel(newChannel);
  };

  const handlePaymentTypeChange = (newPaymentType: string) => {
    setPaymentType(newPaymentType);
  };

  useEffect(() => {
    dispatch(
      setCheckoutData({
        ...checkoutData,
        channel,
        paymentType,
        agreementsChecked,
      })
    );
  }, [channel, paymentType, agreementsChecked]);

  function formatIban(iban: string | undefined): string {
    if (!iban) return "";
    return iban
      .replace(/\s+/g, "")
      .replace(/(.{4})/g, "$1 ")
      .trim();
  }

  return (
    <div>
      <div className="text-xl font-bold">Kanal ve Ödeme Tipi</div>
      <div className="flex flex-col mt-5 space-y-5 min-w-[300px] w-64">
        <div>
          <Label>Kanal</Label>
          <Select value={channel} onValueChange={handleChannelChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Kanal Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="atTheDoor">Kapıda</SelectItem>
              <SelectItem value="telephone">Telefon</SelectItem>
              <SelectItem value="whatsapp">Whatsapp</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Ödeme Tipi</Label>
          <Select value={paymentType} onValueChange={handlePaymentTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Ödeme Tipi Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cash">Nakit</SelectItem>
              <SelectItem value="creditCard">Kredi Kartı</SelectItem>
              <SelectItem value="transfer">Havale</SelectItem>
              <SelectItem value="online">Online</SelectItem>
            </SelectContent>
          </Select>
          {paymentType === "transfer" && (
            <Card className="mt-6 w-full bg-neutral-50 dark:bg-neutral-800 shadow-sm border">
              <CardHeader className="pb-2">
                <div className="text-base font-semibold">
                  Banka Havalesi Bilgileri
                </div>
              </CardHeader>
              <CardContent className="space-y-4 text-sm">
                <div className="flex flex-col gap-2">
                  <div className="font-medium mb-1">IBAN:</div>
                  <div className="font-mono select-text text-gray-700 dark:text-neutral-400">
                    {formatIban(orderData?.ibanNo)}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        formatIban(orderData?.ibanNo) || ""
                      );
                      toast({
                        variant: "success",
                        duration: 3000,
                        description: "IBAN başarıyla panoya kopyalandı.",
                      });
                    }}
                  >
                    Kopyala
                  </Button>
                </div>
                <div className="text-gray-800 leading-relaxed dark:text-neutral-400">
                  <span className="font-medium">Açıklama:</span> Lütfen açıklama
                  kısmına{" "}
                  <span className="font-semibold text-blue-700">
                    isim soyisim
                  </span>{" "}
                  yazınız.
                </div>
              </CardContent>
            </Card>
          )}
          <AgreementContainer
            orderData={orderData}
            agreementsChecked={agreementsChecked}
            setAgreementsChecked={setAgreementsChecked}
          />
        </div>
      </div>
    </div>
  );
};

export default PaymentStep;
