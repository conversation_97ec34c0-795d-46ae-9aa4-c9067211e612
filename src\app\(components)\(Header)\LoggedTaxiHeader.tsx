// to do petTaxi
import type { FC } from "react";
import React from "react";
import { cookies } from "next/headers";
import HotelAvatarDropdown from "./HotelAvatarDropdown";
import PawBookingLogo from "@/shared/PawBookingLogo";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";
import Link from "next/link";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import { Button } from "@/components/ui/button";
import NotificationDropdownWrapper from "./NotificationDropdownWrapper";
import AccountSwitch from "./AccountSwitch";
import { Sparkles } from "lucide-react";

export interface LoggedTaxiHeaderProps {
  className?: string;
}

const LoggedTaxiHeader: FC<LoggedTaxiHeaderProps> = async ({
  className = "",
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const propertyTypesCookie =
    cookieStore.get("propertyTypes")?.value || undefined;
  const propertyTypes = propertyTypesCookie
    ? JSON.parse(propertyTypesCookie)
    : [];
  const hotelData = await getMyHotel();

  return (
    <div className={`MainNav2 relative z-10 ${className}`}>
      <div className={`flex justify-between px-4 lg:container md:h-20`}>
        <div className="hidden flex-1 justify-start space-x-3 sm:space-x-8 md:flex lg:space-x-10">
          <Link href="/petTaxi/account" className="self-center">
            <PawBookingLogo className="size-16 self-center" />
          </Link>
          <div className="hidden h-10 self-center border-l border-neutral-300 dark:border-neutral-500 lg:block"></div>
          <div className="flex items-center">
            <AccountSwitch propertyTypes={propertyTypes} />
          </div>
        </div>
        <div className="hidden flex-1 shrink-0 justify-end text-neutral-700 dark:text-neutral-100 md:flex lg:flex-none">
          <div className="flex space-x-2 lg:space-x-1">
            <SwitchDarkMode className="mx-0.5" />
            <LangDropdownSingle className="hidden lg:flex" />
            <NotificationDropdownWrapper hotelToken={hotelToken} />
            {/* {hotelData?.data && (
              <HotelAvatarDropdown
                logo={hotelData?.data?.logo}
                legalName={hotelData?.data?.hotelName}
              />
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoggedTaxiHeader;
