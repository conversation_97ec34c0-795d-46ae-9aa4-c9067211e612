"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import PromoCode from "./(discounts)/PromoCode";
import Installment from "./(discounts)/Installment";
import IconPlus from "@/shared/icons/Plus";

interface DiscountsModalProps {
  hotelToken: string | undefined;
}

const DiscountsModal: FC<DiscountsModalProps> = ({ hotelToken }) => {
  const [addDiscountsModal, setAddDiscountsModal] = useState<boolean>(false);
  const [selectedDiscount, setSelectedDiscount] = useState<string>("");

  const closeModal = () => {
    setAddDiscountsModal(false);
    setSelectedDiscount("");
  };

  return (
    <>
      <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
        <div className="mt-4 md:my-8">
          <h2 className="text-xl md:text-2xl font-semibold">
            Kampanya Oluşturma ve Düzenleme
          </h2>
          <span className="text-sm text-neutral-500 dark:text-neutral-300"></span>
        </div>
        <Button
          onClick={() => setAddDiscountsModal(true)}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
        >
          <IconPlus />
          Yeni Kampanya Ekle
        </Button>
      </div>
      <Dialog open={addDiscountsModal}>
        <DialogContent
          onInteractOutside={closeModal}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>Yeni Kampanya Ekleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable space-y-3">
            <Select
              value={selectedDiscount}
              onValueChange={(selected) => setSelectedDiscount(selected)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Kampanya Seç" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="promoCode">Promosyon Kodu</SelectItem>
                <SelectItem value="installment">Taksit</SelectItem>
              </SelectContent>
            </Select>
            {selectedDiscount === "promoCode" && (
              <PromoCode hotelToken={hotelToken} closeModal={closeModal} />
            )}
            {selectedDiscount === "installment" && (
              <Installment hotelToken={hotelToken} closeModal={closeModal} />
            )}
          </div>
          {!selectedDiscount && (
            <div className="mt-7 flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                İptal
              </Button>
            </div>
          )}
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DiscountsModal;
