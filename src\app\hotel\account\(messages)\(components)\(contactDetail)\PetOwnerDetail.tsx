import React from "react";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface PetOwnerDetailProps {
  contactDetail: any;
}

const PetOwnerDetail: FC<PetOwnerDetailProps> = ({ contactDetail }) => {
  return (
    <div className="w-full space-y-5 p-2">
      <div className="p-3 text-center border-b border-gray-200">
        <Avatar className="w-16 h-16 mx-auto mb-3">
          <AvatarImage
            className="object-cover"
            src={contactDetail?.petOwnerImage?.src}
          />
          <AvatarFallback className="bg-blue-100 text-blue-600 text-xl uppercase">
            {contactDetail?.fullName?.slice(0, 1)}
          </AvatarFallback>
        </Avatar>
        <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-1 capitalize">
          {contactDetail?.fullName}
        </h2>
      </div>
      {contactDetail?.petDetails?.length > 0 && (
        <>
          <div>
            <h4 className="text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-1">
              Evcil Hayvanları
            </h4>
            <Separator className="w-20 mb-3" />
          </div>
          <div className="grid grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3">
            {contactDetail?.petDetails?.map((pet: any, index: number) => {
              return (
                <div
                  key={index}
                  className="border rounded-lg p-3 flex flex-col items-center gap-2"
                >
                  <Avatar className="w-14 h-14">
                    <AvatarImage src={pet?.images[0]?.src} />
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-xl uppercase">
                      {pet?.name?.slice(0, 1)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="capitalize text-neutral-800 dark:text-neutral-200 font-medium text-center">
                      {pet?.name}
                    </p>
                    <p className="capitalize text-sm text-neutral-500 dark:text-neutral-400 text-center">
                      {pet?.breed}
                    </p>
                    <p className="capitalize text-sm text-neutral-500 dark:text-neutral-400 text-center">
                      {pet?.birthDate}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}
      <div className="h-10">{/* do not remove */}</div>
    </div>
  );
};

export default PetOwnerDetail;
