import React from "react";
import getChatHistory from "@/actions/(protected)/liveChat/getChatHistory";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import getChatContacts from "@/actions/(protected)/liveChat/getChatContacts";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import ChatListContainer from "../(components)/(sideBar)/ChatListContainer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ChatDetail from "../(components)/(chat)/ChatDetail";
import LiveChatInput from "../(components)/(chat)/LiveChatInput";
import ChatLoader from "../(components)/ChatLoader";
import OpenFirstChat from "../(components)/OpenFirstChat";
import EmptyMessage from "../(components)/EmptyMessage";
import { LoaderOne } from "@/components/ui/loader";
import getContactDetail from "@/actions/(protected)/liveChat/getContactDetail";
import ContactDetailContainer from "../(components)/(contactDetail)/ContactDetailContainer";

const MessagesPage = async ({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const messagesParam = searchParams;
  const hotelData = await getMyHotel();
  const chatHistory = await getChatHistory(
    hotelData?.data?._id,
    messagesParam?.id,
    messagesParam?.page
  );
  const chatContacts = await getChatContacts(hotelData?.data?._id);
  const contactDetail = await getContactDetail(messagesParam?.id);

  if (!hotelToken) {
    redirect("/");
  }

  if (chatContacts?.data?.contacts?.length === 0 && !messagesParam?.id) {
    return (
      <div className="container">
        <EmptyMessage />
      </div>
    );
  }

  const sortedChatList = chatContacts?.data?.contacts
    ?.slice()
    ?.sort((a: any, b: any) => {
      const dateA = new Date(a?.lastMessage?.content?.createdAt || 0).getTime();
      const dateB = new Date(b?.lastMessage?.content?.createdAt || 0).getTime();
      return dateB - dateA;
    });

  return (
    <Card
      className={`border-t-0 h-[calc(100vh-150px)] md:h-[calc(100vh-170px)] rounded-none 2xl:container ${!messagesParam?.id ? "overflow-y-auto" : "overflow-hidden"} md:overflow-hidden`}
    >
      <CardContent className="md:flex p-0">
        <div className="md:basis-2/6 lg:basis-1/4 max-md:hidden">
          <CardHeader
            className={`pb-3 pt-4 px-5 border-b ${!messagesParam?.id ? "border-r" : ""}`}
          >
            <CardTitle className="text-lg">Mesajlar</CardTitle>
          </CardHeader>
          <div
            className={`max-h-[calc(100vh-230px)] overflow-auto hiddenScrollbar ${!messagesParam?.id ? "border-r" : ""}`}
          >
            <ChatListContainer
              selectedId={messagesParam.id}
              hotelId={hotelData?.data?._id}
              chatContacts={chatContacts?.data}
            />
          </div>
        </div>
        <div className="md:basis-4/6 lg:basis-3/4">
          {chatHistory?.data ? (
            <div className="md:flex">
              <div className="flex-1 lg:basis-4/6 lg:border-r md:border-l">
                <div className="flex flex-col h-[calc(100vh-170px)] relative">
                  <ChatDetail
                    chatHistory={chatHistory?.data}
                    hotelData={hotelData?.data}
                    selectedId={messagesParam.id}
                    pageParam={messagesParam?.page}
                    contactDetail={contactDetail?.data}
                  />
                  <LiveChatInput
                    selectedId={messagesParam.id}
                    hotelId={hotelData?.data?._id}
                    petOwnerToken={hotelToken}
                  />
                </div>
              </div>

              {contactDetail && (
                <ContactDetailContainer
                  key={messagesParam?.id as string}
                  contactDetail={contactDetail?.data}
                />
              )}
            </div>
          ) : (
            <div className="flex-1 items-center justify-center pb-5 sm:pb-16 md:py-5 md:px-10">
              <div className="md:hidden px-2 py-3">
                <h3 className="font-semibold text-lg sticky top-0 bg-white dark:bg-background z-20 py-2 border-b text-center">
                  Mesajlar
                </h3>
                {/* <NewChatContainer
                  hotelList={hotelList}
                  selectedId={selectedId}
                  isCenter
                /> */}
                <div className="space-y-5 overflow-y-auto pb-14">
                  <ChatListContainer
                    selectedId={messagesParam.id}
                    hotelId={hotelData?.data?._id}
                    chatContacts={chatContacts?.data}
                    isCenter
                  />
                </div>
              </div>
              <div className="flex flex-col gap-5 justify-center items-center h-screen max-lg:hidden">
                <LoaderOne />
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <ChatLoader selectedId={messagesParam.id} />
      {chatContacts?.data?.contacts?.length > 0 && !messagesParam?.id && (
        <OpenFirstChat firstMessageId={sortedChatList[0]?.id} />
      )}
    </Card>
  );
};

export default MessagesPage;
