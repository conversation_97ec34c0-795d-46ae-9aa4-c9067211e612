"use client";
import React, { useState, useEffect } from "react";
import PropertyTypeSelect from "./PropertyTypeSelect";
import PartnerEmailValidation from "./PartnerEmailValidation";
import PartnerPhoneValidation from "./PartnerPhoneValidation";
import PartnerFirstStep from "./PartnerFirstStep";
import PartnerSecondStep from "./PartnerSecondStep";
// import HotelThirdStep from "./HotelThirdStep";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import {
  setStepState,
  setPartner,
} from "@/store/features/partnerAuth/partner-auth-slice";
import LoadingSpinner from "@/shared/icons/Spinner";
import { usePartnerSignupActions } from "@/hooks/createPartnerSteps/usePartnerSignupActions";
import { useTranslations } from "next-intl";
import {
  onlyLetterRegex,
  usernameRegex,
  passwordRegex,
} from "../create-partner-regex";

interface StateType {
  thirdStep: boolean;
  fourthStep: boolean;
}

const CreatePartnerContainer = () => {
  const translate = useTranslations("CreateHotelContainer");
  const { handlePartnerInfo } = usePartnerSignupActions();
  const dispatch = useDispatch();
  const owner = useSelector((state: RootState) => state.partnerAuth.owner);
  const partner = useSelector((state: RootState) => state.partnerAuth.partner);
  const hotelFeatures = useSelector(
    (state: RootState) => state.partnerAuth.hotelFeatures
  );
  const stepState = useSelector(
    (state: RootState) => state.partnerAuth.stepState
  );
  const [buttonStates, setButtonStates] = useState<StateType>({
    thirdStep: false,
    fourthStep: false,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [agreementCheck, setAgreementCheck] = useState<boolean>(false);
  const [rePassword, setRepassword] = useState<string | number | null>(null);

  const firstNameCheck =
    owner.firstName && onlyLetterRegex.test(owner.firstName);
  const lastNameCheck = owner.lastName && onlyLetterRegex.test(owner.lastName);
  const userNameCheck = owner.userName && usernameRegex.test(owner.userName);
  const passwordCheck = owner.password && passwordRegex.test(owner.password);
  const rePasswordCheck = owner.password && owner.password === rePassword;
  const allOwnerInputsTrue =
    firstNameCheck &&
    lastNameCheck &&
    userNameCheck &&
    passwordCheck &&
    rePasswordCheck;

  // checks if required fields are true
  const isOwnerAllTrue = () => {
    const ownerInputsNotEmpty = Object.values(owner).every(
      (value) => value !== null && value.trim() !== ""
    );

    return ownerInputsNotEmpty;
  };

  // checks if required fields are true
  const isHotelAllTrue = () => {
    const hotelInputsNotEmpty = Object.entries(partner).every(
      ([key, value]) => {
        if (key === "partnerPhoneNumber") return true;

        if (partner.propertyType === "petTaxi" && key === "partnerDescription")
          return true;

        return value !== null && value.trim() !== "";
      }
    );
    return hotelInputsNotEmpty;
  };

  // checks if required fields are true
  useEffect(() => {
    const OwnerInputCheck = isOwnerAllTrue();
    const hotelInputCheck = isHotelAllTrue();

    setButtonStates({
      thirdStep: OwnerInputCheck && Boolean(allOwnerInputsTrue),
      fourthStep: hotelInputCheck && agreementCheck,
    });
  }, [owner, partner, hotelFeatures, agreementCheck, allOwnerInputsTrue]);

  const nextBtnText = stepState === 5 ? translate("send") : translate("next");

  return (
    <div>
      <div className="mb-7">
        <span className="text-4xl font-semibold">{stepState}</span>{" "}
        <span className="text-lg text-neutral-500 dark:text-neutral-400">
          / 5
        </span>
      </div>
      <div className="listingSection__wrap">
        {stepState === 1 && <PropertyTypeSelect />}
        {stepState === 2 && <PartnerEmailValidation />}
        {stepState === 3 && <PartnerPhoneValidation />}
        {stepState === 4 && (
          <PartnerFirstStep
            rePassword={rePassword}
            setRepassword={setRepassword}
          />
        )}
        {stepState === 5 && (
          <PartnerSecondStep
            setAgreementCheck={setAgreementCheck}
            agreementCheck={agreementCheck}
          />
        )}
      </div>
      {(stepState === 2 || (stepState >= 4 && stepState <= 5)) && (
        <div
          className={`mt-10 flex ${stepState === 2 ? "justify-center" : "justify-end"} space-x-5`}
        >
          {stepState === 2 && (
            <div className="flex items-center justify-center gap-4 w-full text-gray-700 text-sm font-medium">
              <div className="flex-grow border-t border-gray-300" />
              <div
                onClick={() => {
                  dispatch(setPartner({ ...partner, propertyType: null }));
                  dispatch(setStepState(1));
                }}
                className="px-3 text-secondary-6000 hover:text-secondary-700 cursor-pointer"
              >
                İşletme tipi seçimine geri dön
              </div>
              <div className="flex-grow border-t border-gray-300" />
            </div>
          )}
          {stepState > 4 && (
            <Button
              variant="outline"
              onClick={() => {
                dispatch(setStepState(stepState - 1));
                window.scrollTo(0, 0);
              }}
              type="button"
            >
              {translate("previous")}
            </Button>
          )}
          {stepState === 5 ? (
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={
                stepState === 5 && !buttonStates.fourthStep ? true : false
              }
              onClick={() => {
                handlePartnerInfo(setLoading);
              }}
              type="button"
            >
              {loading ? <LoadingSpinner /> : nextBtnText}
            </Button>
          ) : (
            stepState !== 2 && (
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                onClick={() => {
                  dispatch(setStepState(stepState + 1));
                  window.scrollTo(0, 0);
                }}
                disabled={
                  stepState === 4 && !buttonStates.thirdStep
                    ? true
                    : stepState === 5 && !buttonStates.fourthStep
                      ? true
                      : false
                }
                type="button"
              >
                {nextBtnText}
              </Button>
            )
          )}
        </div>
      )}
    </div>
  );
};

export default CreatePartnerContainer;
