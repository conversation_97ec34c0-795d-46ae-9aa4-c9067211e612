import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import type { SubMerchant } from "@/app/hotel/account/billing/BillingContainer"; // to do petTaxi
import { revalidatePathHandler } from "@/lib/revalidate";

export const usePetTaxiBilling = () => {
  const { toast } = useToast();

  // CREATES PET TAXI SUBMERCHANT
  const createSubMerchant = async (
    event: FormEvent,
    billingData: SubMerchant,
    petTaxiToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/petTaxi/account/petTaxiSubMerchant/addSubMerchantWithoutParam`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            petTaxiToken: petTaxiToken,
          },
          body: JSON.stringify({
            companyType: Number(billingData.companyType),
            fullName: billingData.fullName,
            alias: billingData.alias,
            identityNumber: billingData.identityNumber,
            birthDate: billingData.birthDate,
            gsmNumber: billingData.gsmNumber,
            ibanNo: billingData.ibanNo.replace(/\s+/g, ""),
            ibanAlias: billingData.ibanAlias,
            address: billingData.address,
            city: Number(billingData.city),
            district: Number(billingData.district),
            email: billingData.email.toLowerCase(),
            website: billingData.website,
            mcc_code: billingData.mcc_code,
            authorizedPersonIdentityNumber:
              billingData.authorizedPersonIdentityNumber,
            authorizedPersonBirthDate: billingData.authorizedPersonBirthDate,
            taxOffice: billingData.taxOffice,
          }),
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      toast({
        variant: "success",
        title: "Başarılı",
        description: "Fatura bilgileri başarıyla kaydedildi.",
      });
      revalidatePathHandler("/petTaxi/account/billing");
      setLoading(false);
    }
  };

  // UPDATES PET TAXI SUBMERCHANT
  const updateSubMerchant = async (
    event: FormEvent,
    billingData: SubMerchant,
    petTaxiToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/petTaxi/account/petTaxiSubMerchant`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            petTaxiToken: petTaxiToken,
          },
          body: JSON.stringify({
            companyType: Number(billingData.companyType),
            fullName: billingData.fullName,
            alias: billingData.alias,
            identityNumber: billingData.identityNumber,
            birthDate: billingData.birthDate,
            gsmNumber: billingData.gsmNumber,
            ibanNo: billingData.ibanNo.replace(/\s+/g, ""),
            ibanAlias: billingData.ibanAlias,
            address: billingData.address,
            city: Number(billingData.city),
            district: Number(billingData.district),
            email: billingData.email.toLowerCase(),
            website: billingData.website,
            mcc_code: billingData.mcc_code,
            authorizedPersonIdentityNumber:
              billingData.authorizedPersonIdentityNumber,
            authorizedPersonBirthDate: billingData.authorizedPersonBirthDate,
            taxOffice: billingData.taxOffice,
          }),
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      toast({
        variant: "success",
        title: "Başarılı",
        description: "Fatura bilgileri başarıyla güncellendi.",
      });
      revalidatePathHandler("/petTaxi/account/billing");
      setLoading(false);
    }
  };

  const addSubMerchantToParam = async (
    event: FormEvent,
    billingData: SubMerchant,
    petTaxiToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/petTaxi/account/petTaxiSubMerchant/addSubMerchantToParam`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            petTaxiToken: petTaxiToken,
          },
          body: JSON.stringify({
            identityNumber: billingData.identityNumber,
          }),
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      toast({
        variant: "success",
        title: "Başarılı",
        description: "Fatura bilgileri başarıyla güncellendi.",
      });
      revalidatePathHandler("/petTaxi/account/billing");
      setLoading(false);
    }
  };

  return {
    createSubMerchant,
    updateSubMerchant,
    addSubMerchantToParam,
  };
};
