"use client";
import React, { useState } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { pdf } from "@react-pdf/renderer";
import { BalanceTableComponentProps } from "@/types/hotel/balanceTableType";
import { createColumns } from "./balance-table/BalanceTableColumns";
import BalanceTableHeader from "./balance-table/BalanceTableHeader";
import BalanceTableContent from "./balance-table/BalanceTableContent";
import BalanceTablePagination from "./balance-table/BalanceTablePagination";
import BalanceTablePDF from "./balance-table/BalanceTablePDF";
import { globalFilterFn } from "./balance-table/BalanceTableUtils";

const BalanceTable = ({ orderList }: BalanceTableComponentProps) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState("");
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRowExpanded = (rowId: string) => {
    setExpandedRows((prev) => {
      if (prev[rowId]) {
        return {};
      }
      return { [rowId]: true };
    });
  };
  const columns = createColumns(expandedRows, toggleRowExpanded);

  const table = useReactTable({
    data: orderList,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: globalFilterFn,
  });

  const handleOpenPDF = async () => {
    const blob = await pdf(
      <BalanceTablePDF data={orderList} columns={table.getAllColumns()} />
    ).toBlob();

    const url = URL.createObjectURL(blob);
    window.open(url, "_blank");
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <BalanceTableHeader
        table={table}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        handleOpenPDF={handleOpenPDF}
      />

      {/* Table */}
      <BalanceTableContent
        table={table}
        expandedRows={expandedRows}
        toggleRowExpanded={toggleRowExpanded}
        columns={columns}
      />

      {/* Pagination */}
      <BalanceTablePagination table={table} />
    </div>
  );
};

export default BalanceTable;
