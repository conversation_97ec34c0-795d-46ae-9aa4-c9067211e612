"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import IconCheck from "@/shared/icons/Check";
import IconXMark from "@/shared/icons/Xmark";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";

interface ServicesSoldActionButtonsProps {
  rowOriginal: any;
  hotelToken: string | undefined;
}

export interface ParameterTypes {
  status: string;
  text: {
    heading: string;
    description: string;
    button: string;
  };
}
const AcceptDeclineModal = dynamic(() => import("./AcceptDeclineModal"));

const ServicesSoldActionButtons: FC<ServicesSoldActionButtonsProps> = ({
  rowOriginal,
  hotelToken,
}) => {
  const translate = useTranslations("ReservationActionButtons");
  const { status } = rowOriginal;
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);
  const [parameter, setParameter] = useState<ParameterTypes>({
    status: "1",
    text: {
      heading: "Hizmet Onaylama",
      description: "Seçili hizmeti onaylamak istiyor musunuz?",
      button: translate("confirm"),
    },
  });

  return (
    <div className="flex justify-center gap-4">
      {status === "booked" || status === "waitingForApproval" ? (
        <>
          <Button
            className="!bg-[#2f855a] text-white"
            onClick={() => {
              setModalIsOpen(true);
              setParameter({
                status: "1",
                text: {
                  heading: "Hizmet Onaylama",
                  description: "Seçili hizmeti onaylamak istiyor musunuz?",
                  button: translate("confirm"),
                },
              });
            }}
          >
            <IconCheck strokeWidth={2.5} />
          </Button>
          <Button
            className="!bg-[#c53030] text-white hover:!bg-[#a12222]"
            onClick={() => {
              setModalIsOpen(true);
              setParameter({
                status: "0",
                text: {
                  heading: "Hizmet İptal",
                  description: "Seçili hizmeti iptal etmek istiyor musunuz?",
                  button: translate("confirm"),
                },
              });
            }}
          >
            <IconXMark strokeWidth={2.5} />
          </Button>
        </>
      ) : (
        "-"
      )}
      <AcceptDeclineModal
        modalIsOpen={modalIsOpen}
        setModalIsOpen={setModalIsOpen}
        parameter={parameter}
        rowOriginal={rowOriginal}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default ServicesSoldActionButtons;
