import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export const useReceivePayment = () => {
  const { toast } = useToast();
  const router = useRouter();

  const addNewCard = async (
    hotelToken: string | undefined,
    cardBody: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal?: () => void
  ) => {
    if (!hotelToken) return null;

    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.addNewCard, {
        method: "POST",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardBody: cardBody,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata o<PERSON>.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        if (closeModal) closeModal();
        throw new Error("Network response was not ok");
      }
      if (closeModal) {
        closeModal();
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
      return data;
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const receivePayment = async (
    hotelToken: string | undefined,
    cardBody: any,
    subscriptionType: string | undefined,
    amount: number,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (!hotelToken) return;

    try {
      const cardResult = await addNewCard(hotelToken, cardBody, setLoading);
      if (!cardResult) {
        return;
      }

      setLoading(true);
      const response = await fetch(HOTEL_API_PATHS.receivePayment, {
        method: "POST",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          subscriptionType: subscriptionType,
          amount: amount,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }

      router.push("/hotel/checkout/subscription-success");
      return data;
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const cardValidationHandler = async (
    hotelToken: string | undefined,
    binNumber: string,
    setValidationResult: any
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(HOTEL_API_PATHS.checkCardValidation, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ binNumber: binNumber }),
        });
        const data = await response.json();
        setValidationResult(data.data.data);

        if (!response.ok || !data.success) {
          throw new Error("Network response was not ok");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  return { receivePayment, addNewCard, cardValidationHandler };
};
