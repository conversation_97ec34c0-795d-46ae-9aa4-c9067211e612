"use client";
import React, { useState, useRef } from "react";
import type { FC } from "react";
import HotelAvatar from "@/shared/HotelAvatar";
import type { PetOwnerApiTypes } from "@/types/petOwner/petOwnerTypes";
import { isOnlyEmoji, splitTextWithEmojis } from "@/utils/regex/emojiRegex";
import defaultHotelImage from "@/images/default-hotel-photo.webp";
import MessageOptions from "./MessageOptions";
import { CheckCheck, FileText, Download } from "lucide-react";
import Image from "next/image";
import ChatImageModal from "./ChatImageModal";

interface ChatProps {
  message: any;
  hotelData: any;
  selectedId: string | string[] | undefined;
  toImage: string;
}

// Converts an ISO timestamp string to a local time string in "HH:mm" format.
// Returns the hours and minutes according to the user's local timezone.
export function formatTimeHHmm(timestamp: string): string {
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${hours}:${minutes}`;
}

const Chat: FC<ChatProps> = ({ message, hotelData, selectedId, toImage }) => {
  const [isOptionOpen, setIsOptionOpen] = useState<boolean>(false);
  const [isImageOpen, setIsImageOpen] = useState<boolean>(false);
  const timerRef = useRef<number | null>(null);
  const isHotel = hotelData?._id === message?.from;
  const onlyEmoji = message?.text && isOnlyEmoji(message?.text);
  const parts =
    message?.text?.trim() !== ""
      ? splitTextWithEmojis(message?.text || "")
      : [{ isEmoji: false, text: "" }];
  const time = formatTimeHHmm(message?.timestamp);
  const isOptionStyle = isOptionOpen && message?.text && !message?.isDeleted;
  const isMessageDeleted = message?.isDeleted;

  const userPhotoHandler = () => {
    if (isHotel) {
      return (
        hotelData?.logo?.img800?.src ||
        hotelData?.logo?.src ||
        defaultHotelImage
      );
    } else {
      return toImage || defaultHotelImage;
    }
  };

  const userPhoto = userPhotoHandler();

  const handleTouchStart = () => {
    if (isMessageDeleted || isOptionOpen) return;

    timerRef.current = window.setTimeout(() => {
      setIsOptionOpen(true);
    }, 500);
  };

  const handleTouchEnd = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = `/api/download?url=${encodeURIComponent(message?.fileUrl)}&filename=${message?.fileName || "chat-file.pdf"}`;
    link.click();
  };

  return (
    <div className={`flex items-end gap-3 ${isHotel && "flex-row-reverse"}`}>
      {message?.fileUrl && message?.fileType !== "application/pdf" && (
        <ChatImageModal
          isOpen={isImageOpen}
          setIsOpen={setIsImageOpen}
          imageUrl={message?.fileUrl}
        />
      )}
      <HotelAvatar logo={userPhoto} sizeClass="w-8 h-8" imgUrl={userPhoto} />
      <p
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onContextMenu={(e) => e.preventDefault()}
        className={`${isHotel ? "bg-blue-600 text-white" : "bg-white text-neutral-800"} ${onlyEmoji && "text-xl sm:text-2xl"} ${isOptionStyle && "opacity-70"} group relative shadow-sm py-2.5 px-3.5 rounded-2xl select-none max-w-[calc(100%-5rem)] lg:max-w-[calc(100%-10rem)] xl:max-w-[calc(100%-20rem)] [overflow-wrap:anywhere] border border-gray-200 dark:border-neutral-800`}
      >
        {!isMessageDeleted ? (
          <>
            {message?.fileUrl && message?.fileType !== "application/pdf" && (
              <span className="flex justify-end mb-3">
                <Image
                  onClick={() => setIsImageOpen(true)}
                  className="rounded-lg cursor-pointer"
                  src={message?.fileUrl}
                  width={200}
                  height={200}
                  alt=""
                />
              </span>
            )}
            {message?.fileUrl && message?.fileType === "application/pdf" && (
              <span className="flex items-center gap-1">
                <span className="flex gap-0.5">
                  <FileText className="size-5" />
                  <span>{message?.fileName || "Dosya"}</span>
                </span>
                <Download
                  onClick={handleDownload}
                  className="size-5 cursor-pointer mr-3"
                />
              </span>
            )}
            {parts?.map(
              (part: { text: string; isEmoji: boolean }, index: number) =>
                part.isEmoji ? (
                  <span
                    key={`${message._id}-${index}`}
                    className="text-xl leading-none align-middle"
                  >
                    {part.text}
                  </span>
                ) : (
                  <span key={`${message._id}-${index}`}>{part.text}</span>
                )
            )}
            <span
              className={`block text-xs text-right mt-1 space-x-2 ${isHotel ? "text-white/75" : "text-black/75"}`}
            >
              {message?.isEdited && <span>Düzenlendi</span>}
              <span>{time}</span>
              {isHotel && (
                <CheckCheck
                  className={`${message?.seen && "text-green-500"} size-4 inline-block duration-200`}
                />
              )}
            </span>
          </>
        ) : (
          <span className="italic">Bu mesaj silindi</span>
        )}
        {!isMessageDeleted && (
          <MessageOptions
            text={message?.text}
            messageId={message?._id}
            selectedId={selectedId}
            hotelId={hotelData?._id}
            isOptionOpen={isOptionOpen}
            setIsOptionOpen={setIsOptionOpen}
            isHotel={isHotel}
          />
        )}
      </p>
    </div>
  );
};

export default Chat;
