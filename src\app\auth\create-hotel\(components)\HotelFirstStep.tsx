"use client";
import type { ChangeEvent, FocusEvent } from "react";
import React, { useState } from "react";
import type { FC } from "react";
import Input from "@/shared/Input";
import FormItem from "@/shared/FormItem";
import { setOwner } from "@/store/features/hotelAuth/hotel-auth-slice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import IconEye from "@/shared/icons/Eye";
import IconEyeOff from "@/shared/icons/EyeOff";
import {
  onlyLetterRegex,
  usernameRegex,
  passwordRegex,
} from "../create-hotel-regex";
import { useToast } from "@/components/ui/use-toast";
import { useTranslations } from "next-intl";

interface HotelFirstStepProps {
  rePassword: string | number | null;
  setRepassword: React.Dispatch<React.SetStateAction<string | number | null>>;
}

const HotelFirstStep: FC<HotelFirstStepProps> = ({
  rePassword,
  setRepassword,
}) => {
  const translate = useTranslations("HotelFirstStep");
  const dispatch = useDispatch();
  const { toast } = useToast();
  const owner = useSelector((state: RootState) => state.hotelAuth.owner);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    dispatch(
      setOwner({
        ...owner,
        [name]: name === "userName" ? value.trim() : value.replace(/\s+/g, " "),
      })
    );
  };

  const handleCheckboxChange = (value: string) => {
    dispatch(
      setOwner({
        ...owner,
        gender: value,
      })
    );
  };

  const handleCheck = async (event: FocusEvent<HTMLInputElement>) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URI}/hotel/auth/check/username/${event.target.value}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();
    if (data.data.inUse) {
      toast({
        variant: "warning",
        duration: 3000,
        title: "Uyarı",
        description: `${event.target.value} a ait kayıtlı bir kullanıcı zaten var.`,
      });
      dispatch(
        setOwner({
          ...owner,
          userName: null,
        })
      );
    }
  };

  const firstNameCheck =
    owner.firstName && onlyLetterRegex.test(owner.firstName);
  const lastNameCheck = owner.lastName && onlyLetterRegex.test(owner.lastName);
  const userNameCheck = owner.userName && usernameRegex.test(owner.userName);
  const passwordCheck = owner.password && passwordRegex.test(owner.password);
  const rePasswordCheck = owner.password && owner.password === rePassword;

  const renderRadio = (
    name: string,
    id: string,
    label: string,
    defaultChecked?: boolean
  ) => {
    return (
      <div className="flex items-center">
        <input
          onChange={() => handleCheckboxChange(id)}
          defaultChecked={defaultChecked}
          id={id}
          name={name}
          type="radio"
          className="!checked:bg-primary-500 size-6 border-neutral-300 bg-transparent text-primary-500 focus:ring-primary-500"
        />
        <label
          htmlFor={name}
          className="ml-3 block text-sm font-medium text-neutral-700 dark:text-neutral-300"
        >
          {label}
        </label>
      </div>
    );
  };

  return (
    <>
      <h2 className="pb-5 text-2xl font-semibold">
        {translate("hotelOwnerInfo")}
      </h2>
      <div className="mb-5 w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      {/* FORM */}
      <div className="space-y-8">
        {/* ITEM */}
        <FormItem label={translate("name")}>
          <Input
            name="firstName"
            onChange={handleChange}
            value={owner.firstName || ""}
          />
          {!firstNameCheck && owner.firstName && (
            <span className="mt-3 block text-xs text-red-500">
              {translate("validName")}
            </span>
          )}
        </FormItem>
        <FormItem label={translate("surname")}>
          <Input
            name="lastName"
            onChange={handleChange}
            value={owner.lastName || ""}
          />
          {!lastNameCheck && owner.lastName && (
            <span className="mt-3 block text-xs text-red-500">
              {translate("validSurName")}
            </span>
          )}
        </FormItem>
        <FormItem label={translate("dateOfBirth")}>
          <Input
            type="date"
            className="mt-1"
            name="dateOfBirth"
            min="1900-01-01"
            max="2006-12-31"
            value={owner.dateOfBirth || ""}
            onChange={handleChange}
          />
        </FormItem>
        <FormItem label={translate("username")}>
          <Input
            name="userName"
            autoComplete="new-username"
            onBlur={handleCheck}
            onChange={handleChange}
            value={(owner.userName && owner.userName) || ""}
          />
          {!userNameCheck && owner.userName && (
            <span className="mt-3 block text-xs text-red-500">
              {translate("validUsername")}
            </span>
          )}
        </FormItem>
        <FormItem className="relative" label={translate("password")}>
          <Input
            type={`${showPassword === true ? "text" : "password"}`}
            name="password"
            autoComplete="new-password"
            onChange={handleChange}
            value={owner.password || ""}
          />
          <IconEye
            className={`absolute right-4 top-10 size-5 cursor-pointer ${
              showPassword ? "hidden" : "block"
            }`}
            onClick={() => setShowPassword(true)}
          />
          <IconEyeOff
            className={`absolute right-4 top-10 size-5 cursor-pointer ${
              showPassword ? "block" : "hidden"
            }`}
            onClick={() => setShowPassword(false)}
          />
          {!passwordCheck && owner.password && (
            <span className="mt-3 block text-xs text-red-500">
              {translate("validPassword")}
            </span>
          )}
        </FormItem>
        <FormItem className="relative" label={translate("passwordConfirm")}>
          <Input
            type={`${showPassword === true ? "text" : "password"}`}
            name="repassword"
            autoComplete="new-password-valid"
            onChange={(e) => {
              setRepassword(e.target.value);
            }}
            value={rePassword || ""}
          />
          <IconEye
            className={`absolute right-4 top-10 size-5 cursor-pointer ${
              showPassword ? "hidden" : "block"
            }`}
            onClick={() => setShowPassword(true)}
          />
          <IconEyeOff
            className={`absolute right-4 top-10 size-5 cursor-pointer ${
              showPassword ? "block" : "hidden"
            }`}
            onClick={() => setShowPassword(false)}
          />
          {!rePasswordCheck && rePassword && (
            <span className="mt-3 block text-xs text-red-500">
              {translate("validPasswordConfirm")}
            </span>
          )}
        </FormItem>
        <div>
          <div className="nc-Label text-sm font-medium text-neutral-700 dark:text-neutral-300">
            {translate("gender")}
          </div>
          <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
            {renderRadio(
              "gender",
              "male",
              translate("genderMale"),
              owner.gender === "male"
            )}
            {renderRadio(
              "gender",
              "female",
              translate("genderFemale"),
              owner.gender === "female"
            )}
            {renderRadio(
              "gender",
              "other",
              translate("genderOther"),
              owner.gender === "other"
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default HotelFirstStep;
