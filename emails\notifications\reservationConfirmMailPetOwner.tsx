import React, { ReactNode } from "react";
import mailImage from "../PetOwnerConfirmation.png";
import { Img, Link, Section } from "@react-email/components";

const ReservationConfirmMailPetOwner = () => {
  return (
    <div
      style={{
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#fff7f3",
        padding: "30px",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          maxWidth: "600px",
          background: "#ffffff",
          padding: "20px",
          borderRadius: "10px",
          textAlign: "center",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div style={{ position: "relative" }}>
          <Img
            src="https://i.hizliresim.com/gkjzi1p.png"
            style={{ width: "100%", height: "auto" }}
          />
          <div
            style={{
              position: "absolute",
              top: "15%",
              left: "20px",
              transform: "translate(-50%,)",
            }}
          >
            <h2>
              {" "}
              <p>Rezervasyonunuz Onaylandı! 🎉</p>
            </h2>
            <p>
              <br />
              Sevimli dostunuz için yaptığınız rezervasyon otel tarafından
              onaylandı! 🐾
            </p>
            <div
              style={{
                textAlign: "left",
                paddingLeft: "7px",
                marginBottom: "25px",
              }}
            >
              <p>
                🗓️ <strong>Giriş Tarihi:</strong> [Tarih]
              </p>
              <p>
                🏨 <strong>Otel Adı:</strong> [Otel Adı]
              </p>
              <p>
                📍 <strong>Adres:</strong> [Otel Adresi]
              </p>
            </div>
            <div
              style={{
                textAlign: "left",
                paddingLeft: "13px",
                marginBottom: "25px",
              }}
            >
              <p>
                Konaklama detaylarını görmek ve hazırlıklarınızı tamamlamak için{" "}
                <a
                  href="https://pawbooking.co/user/reservations"
                  style={{
                    color: "#FF5722",
                    fontWeight: "bold",
                    textDecoration: "none",
                  }}
                >
                  pawbooking.co
                </a>{" "}
                adresini ziyaret edebilirsiniz.
              </p>
            </div>{" "}
            <p>
              Sevgiler,
              <br /> <strong>PawBooking Ekibi 🧡</strong>
            </p>
            <p></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReservationConfirmMailPetOwner;
