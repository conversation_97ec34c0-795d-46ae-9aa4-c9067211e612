import React from "react";
import IconMinus from "@/shared/icons/Minus";
import IconCalendar from "@/shared/icons/Calendar";
import IconChart from "@/shared/icons/Chart";
import IconPawGuest from "@/shared/icons/PawGuest";
import IconCancel from "@/shared/icons/Cancel";
import IconChevronUp from "./icons/ChevronUp";
import IconChevronDown from "./icons/ChevronDown";
import { useTranslations } from "next-intl";

interface StatsCardProps {
  title: string;
  todayValue: any;
  yesterdayValue?: string | number;
  percentageChange?: string;
  price?: boolean;
  totalRoom?: any;
  icon: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  todayValue,
  yesterdayValue,
  percentageChange,
  totalRoom,
  price = false,
  icon,
}) => {
  const translate = useTranslations("TodayContainer");
  const iconHandler = (iconName: string) => {
    if (iconName === "IconCalendar") {
      return IconCalendar;
    } else if (iconName === "IconChart") {
      return IconChart;
    } else if (iconName === "IconPawGuest") {
      return IconPawGuest;
    } else {
      return IconCancel;
    }
  };

  const StatCardIcon = iconHandler(icon);

  return (
    <div className="relative rounded-2xl bg-white px-3.5 py-2 md:px-7 md:py-4 drop-shadow dark:bg-neutral-800 max-lg:shrink-0 lg:basis-1/4 min-h-36">
      <div className="mb-3 flex items-center gap-1.5 text-neutral-500 dark:text-neutral-400">
        <StatCardIcon className="size-5" />
        <h2 className="max-md:text-sm">{title}</h2>
      </div>
      <div>
        <p className="mb-2 text-xl font-medium md:text-2xl lg:text-3xl">
          {todayValue || parseInt(todayValue, 10) >= 0 ? `${todayValue}` : ""}
          {totalRoom && `/${totalRoom}`}
        </p>
        {yesterdayValue && (
          <>
            <p className="text-amber-600">{yesterdayValue}</p>
            <p className="text-xs font-medium">{translate("yesterday")}</p>
          </>
        )}
      </div>
      <div>
        {!percentageChange ? (
          <IconMinus className="absolute right-9 top-[52px] size-10 text-gray-400" />
        ) : (
          <div
            className={`${
              percentageChange.includes("-")
                ? "text-white bg-[#ee3939]"
                : "text-[#14a23e] bg-[#e6f5eb]"
            } absolute bottom-[26px] right-2 font-medium px-1.5 py-0.5 text-xs md:text-sm rounded-xl`}
          >
            <div className="flex items-center gap-0.5">
              <span className="text-xs md:text-sm">%</span>
              {parseFloat(percentageChange.replace("-", ""))}
              {percentageChange.includes("-") ? (
                <IconChevronDown className="size-3" strokeWidth={3.5} />
              ) : (
                <IconChevronUp className="size-3" strokeWidth={3.5} />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatsCard;
