import React from "react";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import SingleHotelFile from "../(components)/(hotelFiles)/SingleHotelFile";
import MultipleHotelFiles from "../(components)/(hotelFiles)/MultipleHotelFiles";

const HotelFilesPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();

  return (
    <>
      <div className="mt-4 md:my-8">
        <h2 className="text-xl md:text-2xl font-semibold">
          Belgeler ve Evraklar
        </h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Bu alanda Belgeleri<PERSON>zi yükleyerek ruhsat, veteriner ve şirket
          bilgilerinizi belgeleyebilirsiniz.
        </span>
      </div>
      {hotelData && (
        <div className="mt-5 space-y-5">
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData.data._id}/files/`}
            docType="license"
            name="İşletme Ruhsatı"
            hotelData={hotelData?.data}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData.data._id}/files/`}
            docType="taxCertificate"
            name="Vergi Levhası"
            hotelData={hotelData?.data}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData.data._id}/files/`}
            docType="identificationDocumentFront"
            name="Kimlik Ön Yüz"
            hotelData={hotelData?.data}
          />
          <SingleHotelFile
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData.data._id}/files/`}
            docType="identificationDocumentBack"
            name="Kimlik Arka Yüz"
            hotelData={hotelData?.data}
          />
          {hotelData?.data.propertyType === "petHotel" && (
            <SingleHotelFile
              hotelToken={hotelToken}
              filePath={`hotel/${hotelData.data._id}/files/`}
              docType="veterinaryContract"
              name="Veteriner Sözleşmesi"
              hotelData={hotelData?.data}
            />
          )}
          <MultipleHotelFiles
            hotelToken={hotelToken}
            filePath={`hotel/${hotelData.data._id}/files/`}
            docType="other"
            name="Diğer Belgeler"
            hotelData={hotelData?.data}
          />
        </div>
      )}
    </>
  );
};

export default HotelFilesPage;
