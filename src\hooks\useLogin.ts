"use client";
import { useEffect } from "react";
import useLocalStorage from "./useLocalStorage";
import { v4 as uuid } from "uuid";
import { createGlobalState } from "react-hooks-global-state";
import { useRouter } from "next/navigation";

const initialState = { isLoggedIn: false };
const { useGlobalState } = createGlobalState(initialState);

export const useLogin = () => {
  // const [token, setToken] = useLocalStorage("token", null);
  // const [user, setUser] = useLocalStorage("user", null);
  // const [hotelStatus, setHotelStatus] = useLocalStorage("hotel-status", null);
  // const [deviceId, setDeviceId] = useLocalStorage("deviceId", uuid());
  const [isLoggedIn, setIsLoggedIn] = useGlobalState("isLoggedIn");

  const router = useRouter();

  const logoutUser = async () => {
    try {
      await fetch("/api/delete-cookie", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tokenName: "token" }),
      });
      location.href = "/login";
    } catch (err) {
      alert(err || "error");
    }
  };

  return {
    isLoggedIn,
    logoutUser,
  };
};
