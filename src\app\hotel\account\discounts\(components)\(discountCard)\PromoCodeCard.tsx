import React from "react";
import type { FC } from "react";
import { format } from "date-fns";
import DeletePromoCode from "../deleteDiscount/DeletePromoCode";
import UpdatePromoCode from "../updateDiscount/UpdatePromoCode";

export interface PromoCodeCardProps {
  promosData: any;
  hotelToken: string | undefined;
}
const PromoCodeCard: FC<PromoCodeCardProps> = ({ promosData, hotelToken }) => {
  const formatDate = (date: string) => {
    if (!date) return "";
    return format(new Date(date), "dd.MM.yyyy");
  };

  const getDiscountTypeLabel = (discountType: string) => {
    switch (discountType) {
      case "percentage":
        return "Yüzde İndirimi";
      case "fixedAmount":
        return "Sabit Tutar İndirimi";
      case "freeNights":
        return "Ücretsiz Gece";
      case "freeService":
        return "Ücretsiz Hizmet";
      case "custom":
        return "Özel";
      default:
        return "";
    }
  };

  const getPromoTypeLabel = (promoType: string) => {
    switch (promoType) {
      case "reservationDiscount":
        return "Konaklama İndirimi";
      case "serviceDiscount":
        return "Hizmet İndirimi";
      case "subscriptionDiscount":
        return "Üyelik Kartı İndirimi";
      case "orderDiscount":
        return "Rezervasyon İndirimi";
      case "firstReservation":
        return "İlk Rezervasyon";
      default:
        return "";
    }
  };

  const getDiscountValue = (discountType: string, discount: number) => {
    switch (discountType) {
      case "percentage":
        return `${discount}%`;
      case "fixedAmount":
        return `${discount} TL`;
      case "freeNights":
        return `${discount} Gece`;
      case "freeService":
        return "Ücretsiz Hizmet";
      case "custom":
        return "Özel";
      default:
        return "";
    }
  };

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex justify-between items-center">
        <div className="flex">
          <span className="relative flex h-2.5 w-2.5 mt-[7px] mr-2">
            <span
              className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                promosData?.isActive ? "bg-green-700" : "bg-red-700"
              } opacity-75`}
            ></span>
            <span
              className={`relative inline-flex rounded-full h-2.5 w-2.5 ${
                promosData?.isActive ? "bg-green-600" : "bg-red-600"
              }`}
            ></span>
          </span>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <h2 className="font-semibold capitalize text-neutral-900 dark:text-white">
                <span className="line-clamp-1">{promosData?.code}</span>
              </h2>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>İndirim Tipi:</div>
              <div className="font-semibold">
                {" "}
                {getDiscountTypeLabel(promosData?.discountType)}
              </div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Promosyon Tipi:</div>
              <div className="font-semibold">
                {" "}
                {getPromoTypeLabel(promosData?.promoType)}
              </div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>İndirim Tutarı:</div>
              <div className="font-semibold">
                {" "}
                {getDiscountValue(
                  promosData?.discountType,
                  promosData?.discount
                )}
              </div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Kullanılan / Toplam:</div>
              <div className="font-semibold">
                {" "}
                {promosData?.usedCount} / {promosData?.usageLimit || "Sınırsız"}
              </div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Kampanya Tarih Aralığı:</div>
              <div className="font-semibold">
                {" "}
                {formatDate(promosData?.startDate)} /{" "}
                {formatDate(promosData?.endDate)}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdatePromoCode promosData={promosData} hotelToken={hotelToken} />
        <DeletePromoCode
          hotelToken={hotelToken}
          promoId={promosData?._id}
          promoCode={promosData?.code}
        />
      </div>
    </div>
  );
};

export default PromoCodeCard;
