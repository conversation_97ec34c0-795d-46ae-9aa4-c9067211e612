import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelCustomers = () => {
  const { toast } = useToast();

  // ADDS NEW HOTEL USER
  const addHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    hotelCustomer: {
      fullName: string;
      email: string;
      phone: string;
    },
    hotelId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeCustomerModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.addHotelCustomer, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            fullName: hotelCustomer.fullName.trim(),
            email: hotelCustomer.email.toLowerCase(),
            phone: hotelCustomer.phone,
            hotel: hotelId,
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeCustomerModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Ekleme",
          description: "Müşteri başarıyla eklendi.",
        });
        revalidatePathHandler("/account/hotel-customer");
        closeCustomerModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // DELETES SELECTED HOTEL USER
  const removeHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    id: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.removeHotelCustomer}/${id}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Silme",
          description: "Müşteri başarıyla silindi.",
        });
        revalidatePathHandler("/account/hotel-customer");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  // UPDATES SELECTED HOTEL USER
  const updateHotelCustomerHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    updatedHotelCustomer: {
      fullName: string;
      email: string;
      phone: string;
    },
    hotelCustomerId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeCustomerUpdateModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateHotelCustomer}/${hotelCustomerId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              fullName: updatedHotelCustomer.fullName.trim(),
              email: updatedHotelCustomer.email.toLowerCase(),
              phone: updatedHotelCustomer.phone,
            }),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeCustomerUpdateModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Müşteri Güncelleme",
          description: "Müşteri başarıyla güncellendi.",
        });
        revalidatePathHandler("/account/hotel-customer");
        closeCustomerUpdateModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    addHotelCustomerHandler,
    removeHotelCustomerHandler,
    updateHotelCustomerHandler,
  };
};
