"use client";
import React, { useState } from "react";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X, Trash2 } from "lucide-react";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import LoadingSpinner from "@/shared/icons/Spinner";

interface DeleteCalculatedSubscriptionProps {
  hotelToken: string | undefined;
  removedItemId: string;
  subscriptionId: string;
}

const DeleteCalculatedSubscriptionDetail: FC<
  DeleteCalculatedSubscriptionProps
> = ({ hotelToken, removedItemId, subscriptionId }) => {
  const { removeItemFromCart } = useHotelAtDoorReservation();

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const [deleteSubscriptionIsOpen, setDeleteSubscriptionIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <div className="flex justify-end">
      <Dialog open={deleteSubscriptionIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            setDeleteSubscriptionIsOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>Üyelik Kartı Kaldırma</DialogTitle>
            <DialogDescription className="text-base">
              Seçili üyelik kartını kaldırmak istiyor musun?
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-5">
            <ButtonSecondary
              type="button"
              className="max-sm:basis-1/2"
              onClick={() => setDeleteSubscriptionIsOpen(false)}
            >
              Vazgeç
            </ButtonSecondary>
            <ButtonPrimary
              disabled={loading}
              className="max-sm:basis-1/2"
              onClick={() => {
                removeItemFromCart(
                  hotelToken,
                  removedItemId,
                  calculatedRoomData?.selectedItems,
                  calculatedRoomData?.totalOrderPrice,
                  "subscription",
                  subscriptionId,
                  setLoading,
                  setDeleteSubscriptionIsOpen
                );
              }}
            >
              {loading ? <LoadingSpinner /> : "Onayla"}
            </ButtonPrimary>
          </div>
          <DialogClose
            onClick={() => setDeleteSubscriptionIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
      <Trash2
        onClick={() => setDeleteSubscriptionIsOpen(true)}
        className="size-6 cursor-pointer text-xl text-neutral-500 duration-200 hover:text-primary-6000"
      />
    </div>
  );
};

export default DeleteCalculatedSubscriptionDetail;
