import type { S3Image } from "./s3Image";

export interface RoomReviewType {
  reviewDate?: string;
  reviewerName?: string;
  comment?: string;
  review?: 1 | 2 | 3 | 4 | 5;
}

export interface Room {
  _id?: string;
  owner?: any | string;
  hotel?: any | string;
  roomName?: string;
  roomType?: "standard" | "private" | "shared";
  roomCapacity?: number;
  roomDescription?: string;
  rules: {
    cancellationPolicy?: string;
    checkInTime?: string;
    checkOutTime?: string;
    specialNotes?: string;
  };
  roomFeatures?: string[];
  images?: S3Image[] | any[];
  reviews?: RoomReviewType[];
  passive?: boolean;
  roomGroupName?: string;
}
