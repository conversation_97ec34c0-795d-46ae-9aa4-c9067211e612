"use client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Calendar,
  CreditCard,
  Gift,
  Home,
  MessageSquare,
  Shield,
  Store,
  Wallet,
} from "lucide-react";
import { useRouter } from "next/navigation";

const features = [
  {
    icon: Calendar,
    title: "Takvim Yönetimi",
    description:
      "Rezervasyonlarınızı ve fiyatlandırmalarınızı kolayca takvim üzerinden yönetin",
    color: "bg-blue-50 text-blue-600",
  },
  {
    icon: Gift,
    title: "Kampanya & Kuponlar",
    description:
      "Özel kampanyalar ve indirim kuponları oluşturarak satışlarınızı artırın",
    color: "bg-purple-50 text-purple-600",
  },
  {
    icon: Home,
    title: "Oda Yönetimi",
    description:
      "Tüm odalarınızı kolayca satışa açın ve müsaitlik durumunu güncelleyin",
    color: "bg-green-50 text-green-600",
  },
  {
    icon: CreditCard,
    title: "Güvenli Ödeme",
    description:
      "SSL sertifikalı güvenli online ödeme sistemi ile güvenle tahsilat yapın",
    color: "bg-emerald-50 text-emerald-600",
  },
  {
    icon: MessageSquare,
    title: "Müşteri Yorumları",
    description:
      "Müşteri yorumlarını yönetin ve remarketing kampanyaları düzenleyin",
    color: "bg-orange-50 text-orange-600",
  },
  {
    icon: Store,
    title: "Kapıda Rezervasyon",
    description: "Walk-in müşteriler için anında rezervasyon alma imkanı",
    color: "bg-pink-50 text-pink-600",
  },
  {
    icon: Wallet,
    title: "Ödeme Planları",
    description:
      "Esnek ödeme planları oluşturun ve müşterilerinize kolaylık sağlayın",
    color: "bg-indigo-50 text-indigo-600",
  },
  {
    icon: Shield,
    title: "Güvenlik & Destek",
    description:
      "7/24 teknik destek ve güvenli altyapı ile hizmetinizi kesintisiz sürdürün",
    color: "bg-red-50 text-red-600",
  },
];

export function FeaturesSection() {
  const router = useRouter();

  return (
    <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            Partner Olarak Neler Yapabilirsiniz?
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            İşletmenizi büyütmek için ihtiyacınız olan tüm araçlar tek
            platformda
          </p>
        </div>

        {/* Mobile: Single column, Tablet: 2 columns, Desktop: 4 columns */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm hover:scale-105"
            >
              <CardHeader className="pb-3 sm:pb-4">
                <div
                  className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg ${feature.color} flex items-center justify-center mb-3 sm:mb-4`}
                >
                  <feature.icon className="w-5 h-5 sm:w-6 sm:h-6" />
                </div>
                <CardTitle className="text-base sm:text-lg leading-tight">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-gray-600 text-sm sm:text-base leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mobile CTA */}
        <div className="text-center mt-8 sm:mt-12 lg:hidden">
          <p className="text-sm text-gray-600 mb-4">
            Tüm özellikleri keşfetmek için hemen başlayın
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center max-w-sm mx-auto">
            <button
              onClick={() => router.push("/auth/create-partner")}
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-medium text-sm transition-colors"
            >
              Ücretsiz Dene
            </button>
            <button
              onClick={() => window.open("https://pawbooking.co", "_blank")}
              className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium text-sm hover:bg-gray-50 transition-colors"
            >
              Daha Fazla Bilgi
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
