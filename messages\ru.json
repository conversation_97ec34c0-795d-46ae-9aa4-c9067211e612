{"Page": {"description": "<p>К сожалению, произошла ошибка.</p><p>Вы можете попробовать <retry>перезагрузить страницу</retry>, которую вы посещали.</p>", "title": "Что-то пошло не так!"}, "Layout": {"description": "Это базовый пример, который демонстрирует использование <code>next-intl</code> с маршрутизатором Next.js. Попробуйте изменить локаль в правом верхнем углу и посмотрите, как изменяется содержимое.", "title": "Пример next-intl"}, "Component": {"title": "Пример next-intl"}, "LangDropdown": {"language": "Язык"}, "TodayContainer": {"totalReservation": "Всего бронирований", "totalDailyRate": "Общая дневная ставка", "totalGuest": "Всего гостей", "cancelReservation": "Отменить бронирование"}, "TodayRoomCard": {"closeAll": "Закрыть все", "openAll": "Открыть все"}, "LoginPage": {"login": "Вход", "hotelLogin": "Вход в отель", "loginUsername": "Имя пользователя / Электронная почта", "loginPassword": "Пароль", "loginForgotPassword": "Забыли пароль?", "loginLogin": "Войти", "loginNewuser": "Новый пользователь?", "loginCreateaccount": "Создать аккаунт", "loginWelcome": "Добро пожаловать"}, "CreateUserContainer": {"signup": "Зарегистрироваться", "login": "Вход", "name": "Имя", "validName": "Пожалуйста, введите действительное имя", "surname": "Фамилия", "validSurName": "Пожалуйста, введите действительную фамилию", "email": "Электронная почта", "validEmail": "Пожалуйста, введите действительную электронную почту", "dateOfBirth": "Дата рождения", "username": "Имя пользователя", "validUsername": "Ваше имя пользователя должно содержать не менее 4 символов и состоять только из строчных букв (a-z) и цифр (0-9). Специальные символы или пробелы не допускаются.", "password": "Пароль", "validPassword": "Ваш пароль должен содержать не менее 8 символов и включать одну заглавную букву, одну строчную букву и одну цифру. Специальные символы (!@#$%^&*()_+.-) допускаются, но пробелы не допускаются.", "passwordConfirm": "Подтвердите пароль", "validPasswordConfirm": "Пароль и подтверждение пароля не совпадают", "phoneNumber": "Номер телефона", "phoneNumberInfo": "Введите номер телефона с ведущей 0", "validPhoneNumber": "Ваш номер телефона должен начинаться с 0 и не должен содержать пробелов, букв или специальных символов.", "genderMale": "Мужчина", "genderFemale": "Женщина", "genderOther": "Не хочу указывать", "account": "У вас есть аккаунт?"}, "VerifyAccountContainer": {"accountVerification": "Подтверждение аккаунта", "verificationCode": "Пожалуйста, введите код подтверждения, отправленный на вашу электронную почту!", "confirm": "Подтвердить"}, "ForgotPasswordContainer": {"forgotPassword": "Забыли пароль", "email": "Электронная почта", "emailDesc": "Пожалуйста, введите адрес электронной почты, с которым вы зарегистрированы", "validEmail": "Пожалуйста, введите действительную электронную почту", "send": "Отправить", "sendMessage": "Ссылка для сброса пароля была отправлена на ваш зарегистрированный адрес электронной почты.<br /> Пожалуйста, проверьте свою электронную почту.", "returnHomepage": "Вернуться на главную страницу"}, "ResetPasswordContainer": {"changePassword": "Смена пароля", "show": "Показать", "hide": "Скрыть", "newPassword": "Новый пароль", "newPasswordValid": "Ваш пароль должен содержать не менее 8 символов и включать одну заглавную букву, одну строчную букву и одну цифру. Специальные символы (!@#$%^&*()_+.) допускаются, но пробелы не допускаются.", "newPasswordConfirm": "Подтвердите новый пароль", "newValidPasswordConfirm": "Новый пароль и подтверждение нового пароля не совпадают", "save": "Сохранить"}, "AuthHeader": {"back": "Назад", "approve": "Если вы вернетесь, ваша информация будет сброшена. Вы подтверждаете?"}, "HotelEmailValidation": {"emailVerification": "Подтверждение электронной почты", "emailAdress": "Пожалуйста, введите ваш адрес электронной почты", "validEmail": "Пожалуйста, введите действительную электронную почту", "send": "Отправить", "emailCode": "Введите код, отправленный на ваш адрес электронной почты", "confirm": "Подтвердить"}, "HotelPhoneValidation": {"phoneVerification": "Подтверждение номера телефона", "phoneNumber": "Пожалуйста, введите ваш номер телефона", "phoneNumberInfo": "Введите номер телефона с ведущей 0", "validPhoneNumber": "Ваш номер телефона должен начинаться с 0 и не должен содержать пробелов, букв или специальных символов.", "send": "Отправить", "phoneCode": "Введите код, отправленный на ваш телефон", "confirm": "Подтвердить", "confirmSendCode": "Вы подтверждаете отправку кода?", "change": "Изменить"}, "CreateHotelContainer": {"send": "Отправить", "next": "Далее", "previous": "Назад"}, "HotelFirstStep": {"hotelOwnerInfo": "Информация о владельце отеля", "name": "Имя", "validName": "Пожалуйста, введите действительное имя", "surname": "Фамилия", "validSurName": "Пожалуйста, введите действительную фамилию", "dateOfBirth": "Дата рождения", "username": "Имя пользователя", "validUsername": "Ваше имя пользователя должно содержать не менее 4 символов и состоять только из строчных букв (a-z) и цифр (0-9). Специальные символы или пробелы не допускаются.", "password": "Пароль", "validPassword": "Ваш пароль должен содержать не менее 8 символов и включать одну заглавную букву, одну строчную букву и одну цифру. Специальные символы (!@#$%^&*()_+.-) допускаются, но пробелы не допускаются.", "passwordConfirm": "Подтвердите пароль", "validPasswordConfirm": "Пароль и подтверждение пароля не совпадают", "gender": "Пол", "genderMale": "Мужчина", "genderFemale": "Женщина", "genderOther": "Не хочу указывать"}, "HotelSecondStep": {"hotelInfo": "Информация об отеле", "hotelName": "Название отеля *", "hotelAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hotelCity": "Город *", "hotelDistrict": "Район *", "hotelNeighbourhood": "Микр<PERSON><PERSON><PERSON><PERSON><PERSON>н *", "hotelNeighbourhoodDesc": "Микрор<PERSON>й<PERSON>н, в котором находится ваш отель", "hotelStreet": "Улица *", "hotelStreetDesc": "Улица, на которой находится ваш отель", "hotelBuildingName": "Название здания *", "hotelBuildingNameDesc": "Здание, в котором находится ваш отель", "hotelBuildingNumber": "Номер здания *", "hotelBuildingNumberDesc": "Номер здания, в котором находится ваш отель", "hotelPostalCode": "Почтовый индекс *", "hotelPostalCodeDesc": "Почтовый индекс вашего отеля", "hotelCompanyInfo": "Информация о компании", "hotelCompanyName": "Название компании *", "hotelCompanyNameDesc": "Название вашей компании", "hotelCompanyTaxNumber": "Налоговый номер *", "hotelCompanyTaxNumberDesc": "Налоговый номер вашего отеля", "hotelCompanyTaxOffice": "Налоговая инспекция *", "hotelCompanyTaxOfficeDesc": "Налоговая инспекция, в которой зарегистрирован ваш отель", "hotelPhoneNumber": "Номер телефона отеля", "hotelPhoneNumberDesc": "Введите номер телефона вашего отеля"}, "FileSection": {"hotelUploadHotelLicense": "Загрузить лицензию отеля", "hotelUploadHotelPhoto": "Загрузить фото профиля отеля"}, "HotelThirdStep": {"hotelFeatures": "Особенности отеля", "petTaxi": "Такси для домашних животных", "petCoiffeur": "Парикмахер для домашних животных", "petNursery": "Детский сад", "pool": "Бассейн", "playground": "Игровая площадка", "dogWalking": "Прогулка с собакой", "hotelAcceptedPetTypes": "Допустимые виды домашних животных", "cat": "Кошка", "dog": "Собака", "horse": "Лошадь", "turtle": "Черепаха", "rabbit": "Кролик", "hotelAdditionalServices": "Дополнительные услуги", "veterinary": "Вете<PERSON><PERSON><PERSON><PERSON>", "specialNutrition": "Специальное питание", "grooming": "Уход", "camera": "Камера", "photoInfoServices": "Услуга фотоинформации", "specialCondition": "Животные с особыми потребностями"}, "SuccessPage": {"success": "Регистрация вашего отеля прошла успешно", "returnHomepage": "Вы можете войти или вернуться на главную страницу", "homepage": "Главная страница", "hotelLogin": "Вход в отель"}, "Step4ImagesUpload": {"hotelImages": "Изображения отеля"}, "Nav": {"navToday": "Сегодня", "navHotelİnformations": "Информация об отеле", "navRooms": "Номера", "navCalendar": "Календарь", "navDashboard": "Панель управления", "navReservations": "Бронирования", "navPromoCode": "Промо-код", "navBilling": "Информация о платежах", "navUser": "Информация о пользователе", "navHotelLanding": "Сайт отеля", "navPolicy": "Политики и соглашения"}, "UnloggedHeader": {"headerPetOwner": "У вас есть домашнее животное?", "headerHotelOwner": "Вы владелец отеля?", "headerCreateHotel": "Создать отель", "headerLogin": "Вход", "headerCreateUser": "Создать аккаунт"}, "PetOwnerAvatarDropdown": {"myAccount": "<PERSON>ой аккаунт", "myFavorites": "Мои избранные", "myPets": "Мои домашние животные", "darkTheme": "Темная тема", "logout": "Выйти"}, "HotelAvatarDropdown": {"myAccount": "<PERSON>ой аккаунт", "darkTheme": "Темная тема", "logout": "Выйти"}, "NavMobile": {"myAccount": "<PERSON>ой аккаунт", "logout": "Выйти", "createAccount": "Создать аккаунт", "login": "Вход"}, "NavMobileHotel": {"myAccount": "<PERSON>ой аккаунт", "logout": "Выйти"}, "HotelInformationsNav": {"hotelInfo": "Информация об отеле", "hotelAddress": "Адрес отеля", "hotelFeatures": "Особенности отеля", "hotelAcceptedPetTypes": "Допустимые виды домашних животных", "hotelAdditionalServices": "Дополнительные услуги", "hotelPhotos": "Фотографии отеля", "hotelDocuments": "Документы отеля"}, "HotelInformations": {"hotelName": "Название отеля", "hotelCompanyTaxNumber": "Налоговый номер", "hotelCompanyTaxOffice": "Налоговая инспекция", "hotelLogo": "Логотип отеля", "save": "Сохранить", "hotelUploadPhoto": "Загрузить фото", "hotelTypeImage": "Изображение типа PNG, JPG, GIF"}, "HotelAddress": {"cityName": "Город", "region": "Рай<PERSON>н", "district": "Микр<PERSON><PERSON><PERSON><PERSON><PERSON>н", "streetName": "Улица", "buildingName": "Название здания", "buildingNumber": "Номер здания", "postalZone": "Почтовый индекс", "save": "Сохранить"}, "HotelFeatures": {"hotelFeatures": "Особенности отеля", "petTaxi": "Такси для домашних животных", "petCoiffeur": "Парикмахер для домашних животных", "petGrooming": "Уход за домашними животными", "petNursery": "Детский сад", "pool": "Бассейн", "playground": "Игровая площадка", "dogWalking": "Прогулка с собакой", "save": "Сохранить", "FeaturesCategories": {"healthAndSafety": "Здоровье и безопасность", "comfortAndAccommodation": "Комфорт и размещение", "activitiesAndEntertainment": "Активности и развлечения", "extraServices": "Дополнительные услуги"}, "FeaturesLabels": {"veterinaryServices": "Ветеринарные услуги 24/7", "cameraSurveillance": "Система видеонаблюдения", "securityCameras": "Системы безопасности", "emergencyEquipment": "Оборудование для экстренных ситуаций", "hygienicToiletAreas": "Гигиенические туалетные зоны", "climateControl": "Системы климат-контроля", "individualRooms": "Индивидуальные номера", "comfortableBeds": "Мягкие и удобные кровати", "indoorShelters": "Закрытые укрытия", "outdoorShelters": "Открытые укрытия", "playAreas": "Игровые зоны", "indoorPlayAreas": "Закрытые игровые зоны", "gardenArea": "Садовая зона", "trainingField": "Тренировочная площадка", "playPool": "Игровой бассейн", "socializationActivities": "Деятельности по социализации", "liveCameraAccess": "Доступ к живому видео", "photoUpdates": "Обновления с фотографиями", "specialDietMeals": "Специальные диетические блюда", "petSpa": "Спа и услуги по уходу за домашними животными", "pickupDropoff": "Услуга забора и доставки"}}, "HotelPetTypes": {"hotelAcceptedPetTypes": "Допустимые виды домашних животных", "cat": "Кошка", "dog": "Собака", "smallDogBreed": "Маленькая порода собак", "largeDogBreed": "Большая порода собак", "rabbit": "Кролик", "hamster": "Хомяк", "guineaPig": "Морская свинка", "ferret": "Х<PERSON><PERSON><PERSON><PERSON>", "hedgehog": "Еж", "chinchilla": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а", "turtle": "Черепаха", "snake": "Змея", "iguana": "Игу<PERSON>на", "parrot": "Попугай", "canary": "Канарейка", "budgerigar": "Неразлучник", "cockatiel": "Какаду", "fish": "Рыба", "freshwaterFish": "Пресноводная рыба", "saltwaterFish": "Морская рыба", "horse": "Лошадь", "pony": "Пони", "donkey": "<PERSON><PERSON><PERSON><PERSON>", "goat": "Коза", "sheep": "Овца", "alpaca": "Алпака", "llama": "Лама", "exoticBird": "Экзотическая птица", "monkey": "Обезьяна", "rooster": "Петух", "other": "Другое", "save": "Сохранить"}, "HotelAdditionalServices": {"hotelAdditionalServices": "Дополнительные услуги", "veterinary": "Вете<PERSON><PERSON><PERSON><PERSON>", "specialNutrition": "Специальное питание", "grooming": "Уход", "camera": "Камера", "photoInfoServices": "Услуга фотоинформации", "specialCondition": "Животные с особыми потребностями", "save": "Сохранить"}, "HotelPhotosContainer": {"hotelUploadPhoto": "Загрузить фото", "save": "Сохранить"}, "HotelPhotoCard": {"hotelRemovePhoto": "Удалить фото", "hotelDeletePhoto": "Вы хотите удалить выбранное фото?", "hotelCancel": "Отмена", "hotelDelete": "Удалить"}, "PostingContainer": {"PostingContainer": "У вас нет ранее созданной группы комнат. Пожалуйста, создайте группу комнат."}, "HotelRoomCard": {"hotelRoomCount": "Количество комнат:", "roomCapacity": "Вместимость комнаты:", "petType": "Тип питомца:"}, "UpdateAndCreateNewRoomModal": {"hotelAddNewRoomGroup": "Добавить новую группу комнат", "hotelAddMultipleRooms": "Добавить несколько комнат", "hotelRoomName": "Название комнаты", "hotelRoomCapacity": "Вместимость комнаты", "hotelRoomGroupName": "Название группы комнат", "hotelNumberOfRooms": "Количество комнат", "hotelRoomNameStartingNumber": "Начальный номер комнаты", "petType": "Тип домашнего животного", "roomFeatures": "Особенности комнаты", "roomFeaturesDesc": "После ввода особенностей вашей комнаты нажмите Enter или кнопку добавления.", "add": "Добавить", "roomInformation": "Информация о комнате", "roomInformationDesc": "Пожалуйста, предоставьте краткое описание комнаты", "hotelPhotos": "Фотографии отеля", "hotelUploadPhoto": "Загрузить фото", "save": "Сохранить", "cancel": "Отмена", "roomUpdate": "Обновление комнаты", "cat": "Кошка", "dog": "Собака", "roomType": "Ти<PERSON> комнаты", "standard": "Стандартный", "private": "Частный", "shared": "Общий"}, "DeleteRoomModal": {"RemoveRoom": "Удалить комнату", "RemoveRoomConfirm": "Вы хотите удалить комнату?", "cancel": "Отмена", "confirm": "Подтвердить"}, "HotelCalendarContainer": {"roomGroups": "Группы комнат", "roomGroupName": "Название группы", "NumberOfRooms": "Количество комнат", "select": "Выбрать"}, "HotelCalendarRange": {"selectRoom": "Выбрать комнату", "activeDays": "Активные дни", "passiveDays": "Пассивные дни", "noPriceDays": "Дни без указанной цены", "locale": "ru"}, "HotelCalendarSideBarRange": {"roomCapacity": "Ежедневная вместимость комнаты", "petType": "Тип домашнего животного", "roomType": "Ти<PERSON> комнаты", "clearSelection": "Очистить выбор", "closetoReservations": "Закрыть для бронирования", "opentoReservations": "Открыть для бронирования", "locale": "ru-RU", "changePrice": "Нажмите, чтобы изменить цену", "totalPrice": "Общая сумма, которую должен заплатить гость", "clickForDetails": "Нажмите для подробностей"}, "PriceSummary": {"priceBreakdown": "Разбивка цены", "night": "ночь", "nightlyRate": "Ваша средняя цена за ночь", "serviceFee": "Сервисный сбор", "totalPrice": "Общая сумма, которую должен заплатить гость", "yourEarnings": "Ваши доходы"}, "UpdateRoomPrice": {"priceUpdate": "Обновление цены", "newRoomRate": "Введите новую цену комнаты", "price": "Цена:", "serviceFee": "Сервисный сбор:", "totalPrice": "Общая сумма, которую должен заплатить гость:", "yourEarnings": "Ваши доходы:", "cancel": "Отмена", "save": "Сохранить"}, "HotelCalendarDrawerRangeMobile": {"dateUpdate": "Обновление даты", "closetoReservations": "Закрыть для бронирования", "opentoReservations": "Открыть для бронирования", "locale": "ru-RU", "changePrice": "Нажмите, чтобы изменить цену", "totalPrice": "Общая сумма, которую должен заплатить гость", "clickForDetails": "Нажмите для подробностей", "close": "Закрыть", "back": "Назад"}, "ReservationList": {"roomName": "Название комнаты", "petOwner": "Владелец домашнего животного", "petName": "Имя домашнего животного", "startDate": "Дата заезда", "endDate": "Дата выезда", "channel": "<PERSON><PERSON><PERSON><PERSON>", "date": "Дата", "total": "Всего", "status": "Статус", "action": "Действие", "confirmed": "Подтверждено", "booked": "Забронировано", "checkedIn": "Зарегистрировано", "declined": "Отклонено", "waitingForBook": "В корзине", "confirm": "Подтвердить", "reject": "Отклонить", "reservationNotFound": "Бронирование не найдено.", "showHideColumns": "Показать/Скрыть столбцы"}, "BillingContainer": {"companyType": "Тип компании *", "soleProprietorship": "Индивидуальное предприятие", "stockCompany": "Акционерное общество", "fullName": "Имя Фамилия *", "alias": "Название компании *", "identityNumber": "Идентификационный номер", "birthDate": "Дата рождения", "gsmNumber": "Номер телефона *", "address": "Адрес *", "city": "Город *", "district": "Район *", "selectCity": "Выберите город", "selectDistrict": "Выберите район", "email": "Электронная почта *", "authorizedPersonIdentityNumber": "Идентификационный номер уполномоченного лица", "authorizedPersonBirthDate": "Дата рождения уполномоченного лица", "taxOffice": "Налоговая инспекция", "save": "Сохранить", "ibanTitle": "Название IBAN"}, "ReservationActionButtons": {"reservationConfirmation": "Подтверждение бронирования", "reservationConfirmationDesc": "Вы хотите подтвердить выбранное бронирование?", "confirm": "Подтвердить", "reservationCancellation": "Отмена бронирования", "reservationCancellationDesc": "Вы хотите отменить выбранное бронирование?", "cancel": "Отменить"}, "ReservationDetail": {"details": "Детали", "reservationDetails": "Детали бронирования", "roomInformation": "Информация о комнате", "roomGroupName": "Название группы комнат", "roomName": "Название комнаты", "roomCapacity": "Вместимость комнаты", "petType": "Тип домашнего животного", "accommodationInformation": "Информация о размещении", "checkInDate": "Дата заезда", "checkOutDate": "Дата выезда", "stayDuration": "Продолжительность проживания", "petInformation": "Информация о домашнем животном", "petName": "Имя домашнего животного", "petAge": "Возраст домашнего животного", "petKind": "Вид домашнего животного", "petBreed": "Порода домашнего животного", "petOwnerInformation": "Информация о владельце домашнего животного", "petOwnerName": "Имя владельца домашнего животного", "phone": "Телефон", "email": "Электронная почта", "night": "ночь"}, "HotelUserTable": {"noUsers": "Пользователей нет", "firstName": "Имя", "username": "Имя пользователя", "email": "Электронная почта", "role": "Роль"}, "AddNewAndUpdateHotelUser": {"addUser": "Добавить пользователя", "addNewUser": "Добавить нового пользователя", "updateUser": "Обновить пользователя", "firstName": "Имя", "lastName": "Фамилия", "gender": "Пол", "male": "Мужчина", "female": "Женщина", "other": "Другое", "username": "Имя пользователя", "password": "Пароль", "email": "Электронная почта", "dateOfBirth": "Дата рождения", "phone": "Номер мобильного телефона", "bio": "О себе", "validFirstName": "Пожалуйста, введите действительное имя", "validLastName": "Пожалуйста, введите действительную фамилию", "validUsername": "Ваше имя пользователя должно содержать не менее 4 символов и состоять только из строчных букв (a-z) и цифр (0-9). Специальные символы или пробелы не допускаются.", "validPassword": "Ваш пароль должен содержать не менее 8 символов и включать хотя бы одну заглавную букву, одну строчную букву и одну цифру. Специальные символы (!@#$%^&*()_+.-) допускаются, но пробелы не допускаются.", "validEmail": "Пожалуйста, введите действительную электронную почту", "validDateOfBirth": "Пожалуйста, введите действительную дату рождения", "validPhone": "Пожалуйста, введите действительный номер телефона", "cancel": "Отмена", "confirm": "Подтвердить"}, "DeleteHotelUser": {"removeUserTitle": "Удаление пользователя", "removeUserConfirmation": "Вы хотите удалить этого пользователя?", "cancel": "Отмена", "confirm": "Подтвердить"}, "Policy": {"cancellationPolicy": "Политика отмены", "checkInTime": "Время заезда", "checkOutTime": "Время выезда", "description": "Описание", "cancellationOption": "Вариант отмены", "rules": "Правила", "untilDateRange": "До определенной даты", "noCancellation": "Без отмены", "last24Hours": "Последние 24 часа", "last48Hours": "Последние 48 часов", "lastWeek": "Последняя неделя", "last10Days": "Последние 10 дней", "lastMonth": "Последний месяц", "newRulesAdd": "Добавить новое правило", "save": "Сохранить"}, "UpdatePolicy": {"updatePolicy": "Обновить политику", "updatePolicyTitle": "Обновление политики", "description": "Описание", "rules": "Правила", "addNewRule": "Добавить новое правило", "cancel": "Отмена", "save": "Сохранить", "close": "Закрыть"}, "CheckInOutTimePicker": {"checkInLabel": "Время заезда", "checkOutLabel": "Время выезда", "checkOutError": "Время выезда не может быть раньше времени заезда!"}, "AddNewRule": {"newRuleLabel": "Новое правило", "ruleTitlePlaceholder": "Название правила", "ruleDescriptionPlaceholder": "Описание правила", "updateButton": "Обновить", "addButton": "Добавить", "cancelButton": "Отмена"}, "CancellationOption": {"cancellationOption": "Вариант отмены", "select": "Выбрать", "noCancellation": "Без отмены", "cancelUntilDateRange": "До определенной даты", "other1": "Конвертация в кредит", "other2": "Страховка бронирования", "other3": "Отмена в любое время", "other4": "Частичный возврат", "dateRange": "Период", "last24Hours": "Последние 24 часа", "last48Hours": "Последние 48 часов", "lastWeek": "Последняя неделя", "last10Days": "Последние 10 дней", "lastMonth": "Последний месяц"}, "UserActionButtons": {"editUser": "Редактировать пользователя", "deleteUser": "Удалить пользователя"}, "AcceptDeclineModal": {"cancel": "Отмена"}, "PaginationDropdown": {"pageSelect": "Выбрать страницу"}, "ReservationPagination": {"previous": "Предыдущая", "next": "Следующая"}, "DeleteFileModal": {"title": "Удаление файла", "confirmMessage": "Вы хотите удалить выбранный файл?", "confirmButton": "Подтвердить", "cancelButton": "Отмена"}, "HotelFiles": {"uploadButtonText": "Загрузить", "saveButtonText": "Сохранить"}, "FileStep": {"hotelFiles": "Файлы отеля", "license": "Лицензия", "veterinaryContract": "Ветеринарный контракт"}, "HotelAccountInformationRU": {"accountEditInfo": "Редактировать информацию о аккаунте", "userInfo": "Информация о пользователе", "username": "Имя пользователя", "email": "Электронная почта", "phoneNumber": "Номер телефона", "fullName": "Полное имя", "role": "Роль", "hotelInfo": "Информация об отеле", "hotelName": "Название отеля", "hotelPhoneNumber": "Телефон отеля", "taxOffice": "Налоговая инспекция", "taxNumber": "Налоговый номер", "currency": "Валюта", "status": "Статус"}}