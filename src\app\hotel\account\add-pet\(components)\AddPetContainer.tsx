"use client";
import React, { useState } from "react";
import type { FC } from "react";
import SelectPet from "./SelectPet";
import PetInformations from "./PetInformations";
import PetHealthInformations from "./PetHealthInformations";
import PetHabits from "./PetHabits";
import VaccinationReport from "./VaccinationReport";
import AddPetStepNav from "./AddPetStepNav";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";
import { breedList } from "@/types/petOwner/petTypes";
import { useHotelPet } from "@/hooks/hotel/useHotelPets";
import { useHotelPetPublic } from "@/hooks/public/useHotelPetsPublic";
import AddPetFooterButtons from "./AddPetFooterButtons";

interface AddPetContainerProps {
  hotelToken?: string | undefined;
  hotelCustomerId: any;
  hotelId: string;
  isCheckout?: boolean;
  isPublic?: boolean;
  selectedPetTypes?: string[];
}

const AddPetContainer: FC<AddPetContainerProps> = ({
  hotelToken,
  hotelCustomerId,
  hotelId,
  isCheckout = false,
  isPublic = false,
  selectedPetTypes = [],
}) => {
  const initialData = {
    name: "",
    age: "",
    kind: "",
    breed: "",
    gender: "",
    color: "",
    specified: "",
    infertile: null,
    microChipNumber: "",
    vaccines: [],
    internalParasiteTreatment: false,
    externalParasiteTreatment: false,
    internalTreatmentDate: "",
    externalTreatmentDate: "",
    allergicTo: [],
    hereditaryDiseases: [],
    operationHistory: [],
    medicine: "",
    feedingHabits: "",
    description: "",
    documentPhotos: [],
  };
  const { documentPhotosHandler } = useHotelPet();
  const { documentPhotosHandler: publicDocumentPhotosHandler } =
    useHotelPetPublic();
  const [step, setStep] = useState<number>(1);
  const [petInformations, setPetInformations] =
    useState<PetInformationTypes>(initialData);
  const [descriptions, setDescriptions] = useState({
    allergic: "",
    diseases: "",
    operation: "",
    internalParasite: "",
    externalParasite: "",
    medicines: "",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | undefined>(
    undefined
  );
  const [image, setImage] = useState<string[] | []>([]);
  const breedListArray = breedList[petInformations.kind] || [];

  const isPetInfoComplete = () => {
    if (breedListArray.length > 0) {
      if (
        petInformations.name.trim() &&
        petInformations.age &&
        petInformations.breed &&
        petInformations.gender &&
        petInformations.color.trim() &&
        petInformations.microChipNumber.trim() &&
        petInformations.infertile
      ) {
        return true;
      } else {
        return false;
      }
    } else if (breedListArray.length === 0) {
      if (
        petInformations.name.trim() &&
        petInformations.age &&
        petInformations.gender &&
        petInformations.color.trim() &&
        petInformations.microChipNumber.trim() &&
        petInformations.infertile
      ) {
        return true;
      } else {
        return false;
      }
    }
  };

  const isPetHealthInfoComplete = () => {
    if (
      (descriptions.internalParasite === "true" &&
        !petInformations.internalTreatmentDate.trim()) ||
      (descriptions.externalParasite === "true" &&
        !petInformations.externalTreatmentDate.trim()) ||
      (descriptions.allergic === "true" &&
        petInformations.allergicTo.length === 0) ||
      (descriptions.diseases === "true" &&
        petInformations.hereditaryDiseases.length === 0) ||
      (descriptions.operation === "true" &&
        petInformations.operationHistory.length === 0) ||
      (descriptions.medicines === "true" && !petInformations.medicine.trim())
    ) {
      return false;
    }

    return true;
  };

  const isPetInfoValid = isPetInfoComplete();
  const isPetHealthInfoValid = isPetHealthInfoComplete();

  const closeAddNewPetModal = () => {
    setLoading(false);
    setImage([]);
    setStep(1);
    setPetInformations(initialData);
  };

  const buttonsConfig: Record<number, { text: string; disabled: boolean }> = {
    1: { text: "İleri", disabled: !petInformations.kind },
    2: { text: "İleri", disabled: !isPetInfoValid },
    3: { text: "İleri", disabled: !isPetHealthInfoValid },
    4: { text: "İleri", disabled: false },
  };

  return (
    <form
      onSubmit={(event) => {
        if (isPublic) {
          publicDocumentPhotosHandler(
            event,
            photoFileObject,
            hotelId,
            hotelCustomerId,
            petInformations,
            closeAddNewPetModal,
            setLoading,
            setDisabled
          );
        } else {
          documentPhotosHandler(
            event,
            photoFileObject,
            hotelToken,
            hotelId,
            hotelCustomerId,
            petInformations,
            closeAddNewPetModal,
            setLoading,
            setDisabled
          );
        }
      }}
      className={isCheckout || isPublic ? "" : "container"}
    >
      {!isCheckout && !isPublic && <AddPetStepNav step={step} />}
      {step === 1 && (
        <SelectPet
          petInformations={petInformations}
          setPetInformations={setPetInformations}
          isCheckout={isCheckout}
          isPublic={isPublic}
          selectedPetTypes={selectedPetTypes}
        />
      )}
      {step === 2 && (
        <PetInformations
          breedListArray={breedListArray}
          petInformations={petInformations}
          setPetInformations={setPetInformations}
        />
      )}
      {step === 3 && (
        <PetHealthInformations
          petInformations={petInformations}
          setPetInformations={setPetInformations}
          descriptions={descriptions}
          setDescriptions={setDescriptions}
        />
      )}
      {step === 4 && (
        <PetHabits
          petInformations={petInformations}
          setPetInformations={setPetInformations}
        />
      )}
      {step === 5 && (
        <VaccinationReport
          image={image}
          setImage={setImage}
          setPhotoFileObject={setPhotoFileObject}
        />
      )}
      <p className="mt-5 md:mt-5 text-sm">* doldurulması zorunlu alanlar</p>
      <AddPetFooterButtons
        step={step}
        setStep={setStep}
        isPetInfoValid={isPetInfoValid}
        isPetHealthInfoValid={isPetHealthInfoValid}
        kind={petInformations.kind}
        // isVaccinationReport={image.length > 0}
        isVaccinationReport={true}
        loading={loading}
        disabled={disabled}
        isCheckout={isCheckout}
        isPublic={isPublic}
      />
    </form>
  );
};

export default AddPetContainer;
