import type { FC } from "react";
import React from "react";
import Nav from "@/app/hotel/account/(components)/Nav";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import RedirectComponent from "./(components)/(steps)/RedirectComponent";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import ReservationsButton from "./(components)/ReservationsButton";

export interface CommonLayoutProps {
  children?: React.ReactNode;
}

const AccountLayout: FC<CommonLayoutProps> = async ({ children }) => {
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);
  return (
    <div className="nc-CommonLayoutAccount min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="border-b border-neutral-200 bg-white dark:border-neutral-700 dark:bg-neutral-800">
        {hotelData?.data?.status === "approved" && (
          <Nav membershipData={membershipData?.data} />
        )}
      </div>
      {/* {!!hotelData.data.actionRequired && (
        <div className="container">
          <div className="border-2 border-yellow-300 bg-secondary-6000 dark:text-black w-full p-4 mt-4 flex md:justify-between items-center rounded-xl flex-col md:flex-row">
            <div className="relative w-full overflow-hidden p-2 sm:hidden">
              <style>
                {`
          @keyframes marquee {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
          }
        `}
              </style>
              <div
                className="text-white font-medium whitespace-nowrap text-sm"
                style={{
                  animation: "marquee 10s linear infinite",
                }}
              >
                Aksiyon alınması gereken rezervasyonlarınız var.
              </div>
            </div>
            <p className="text-center max-sm:text-sm md:text-start text-white max-sm:hidden">
              Aksiyon alınması gereken rezervasyonlarınız var.
            </p>
            <ReservationsButton />
          </div>
        </div>
      )} */}
      <div className="pb-24 lg:pb-32">{children}</div>
      {hotelData?.data?.status !== "approved" && <RedirectComponent />}
    </div>
  );
};

export default AccountLayout;
