"use client";
import React from "react";
import { Button } from "@/components/ui/button";

interface ActionButtonsProps {
  isDisabled: boolean;
  onButtonClick: (section: "reservation" | "service" | "subscription") => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  isDisabled,
  onButtonClick,
}) => {

  return (
    <div className="flex flex-col md:flex-row gap-5 w-full max-md:w-[95vw] max-md:max-w-md pt-2 pb-4 px-10 justify-center">
      <Button
        className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
        onClick={() => onButtonClick("reservation")}
        disabled={isDisabled}
      >
        Ye<PERSON>
      </Button>
      <Button
        className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
        onClick={() => onButtonClick("service")}
        disabled={isDisabled}
      >
        <PERSON><PERSON>
      </Button>
      <Button
        className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
        onClick={() => onButtonClick("subscription")}
        disabled={isDisabled}
      >
        Yeni Üyelik Kartı Ekle
      </Button>
    </div>
  );
};

export default ActionButtons;
