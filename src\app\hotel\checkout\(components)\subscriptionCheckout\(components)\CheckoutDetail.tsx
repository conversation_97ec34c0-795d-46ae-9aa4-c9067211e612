import React from "react";
import type { FC } from "react";
import PawPlus from "@/shared/PawPlus";

interface CheckoutDetailProps {
  selectedTier: any;
}

const CheckoutDetail: FC<CheckoutDetailProps> = ({ selectedTier }) => {
  return (
    <div>
      <div className="flex flex-col">
        <div className="w-full shrink-0 space-y-4">
          <div className="flex h-64 w-full justify-center items-center">
            <PawPlus width="200" height="200" />
          </div>
        </div>
        <div className="space-y-1 py-5">
          <div>
            <span className="text-xl md:text-2xl font-semibold capitalize">
              PawBooking {selectedTier?.tierId} Üyelik
            </span>
          </div>
          <div className="w-10 border-b border-neutral-200  dark:border-neutral-700"></div>
          {/* <StartRating /> */}
        </div>
      </div>
      <div className="mt-2">
        <div className="font-semibold">{selectedTier?.frequency} abonelik</div>
        <div className="mt-2 text-sm font-medium text-gray-700">
          {selectedTier?.frequency === "Aylık"
            ? "Her ay otomatik olarak yenilenir."
            : "Ücret tek seferde tahsil edilir."}
        </div>
      </div>
      <div className="mt-8 flex flex-col space-y-4">
        <div className="border-b border-neutral-200 dark:border-neutral-700"></div>
        <div className="flex items-center justify-between font-semibold">
          <span>Toplam</span>
          <span className="text-xl md:text-2xl">
            {new Intl.NumberFormat("tr-TR", {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(Number(selectedTier?.price)) + "₺"}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CheckoutDetail;
