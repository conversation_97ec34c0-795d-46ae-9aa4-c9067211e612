import React, { ReactNode } from "react";
import mailImage from "../PetOwnerConfirmation.png";
import { Img, Link, Section } from "@react-email/components";
const Layout: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <div
      style={{
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#fff7f3",
        padding: "20px",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          maxWidth: "600px",
          background: "#ffffff",
          padding: "20px",
          borderRadius: "10px",
          textAlign: "center",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        {children}
      </div>
    </div>
  );
};

const confirmationMailHotelOwner: React.FC<{ username: string }> = ({
  username,
}) => {
  return (
    <Layout>
      {/* Header */}

      {/* Content */}
      {/* <div style={{ padding: "20px 0" }}>
        <h2 style={{ color: "#333" }}><PERSON><PERSON> geldiniz, {username}! 🎊</h2>
        <p style={{ fontSize: "16px", color: "#333" }}>
          Kaydınız onaylandı! Artık pet otellerini keşfedebilir, hemen
          rezervasyon yapabilirsiniz. 🐶🐱🐾
        </p>
      </div> */}

      {/* Image & Button */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "20px",
          backgroundColor: "white",
          borderRadius: "10px",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* <img
          src="https://hizliresim.com/re4rt85"
          alt="Welcome"
          style={{ width: "100%", borderRadius: "10px" }}
        /> */}
        <div
          style={{
            position: "relative",
          }}
        >
          <Img src="https://i.hizliresim.com/csckypq.png" height={320} />
          <div
            style={{
              position: "absolute",
              top: "45%",
              left: "65px",
              transform: "translateY(-50%)",
            }}
          >
            <h2
              style={{
                color: "#333",
                top: "50%",
                left: "50px",
              }}
            >
              Tebrikler!
            </h2>
            <p>
              Oteliniz artık sistemimizde! Hemen giriş yaparak rezervasyon
              <br />
              almaya başlayabilirsiniz. 🚀
              <a
                href="https://partner.pawbooking.co/"
                style={{
                  color: "black",
                  textDecoration: "none",
                  fontWeight: "bold",
                  borderRadius: "5px",
                  marginTop: "20px",
                  fontSize: "16px",
                  display: "block",
                }}
              >
                🏡 Şimdi Giriş Yap
              </a>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default confirmationMailHotelOwner;
