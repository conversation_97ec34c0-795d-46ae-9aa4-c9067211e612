"use client";
import React from "react";
import type { FC } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import defaultHotelImage from "@/images/default-hotel-photo.webp";
import { formatTimeHHmm } from "../(chat)/Chat";
import { CheckCheck, FileImage, FileText } from "lucide-react";
import {
  setIsLoading,
  setText,
  setIsEdit,
} from "@/store/features/liveChat/live-chat-slice";
import { useDispatch } from "react-redux";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { revalidatePathHandler } from "@/lib/revalidate";

interface ChatListProps {
  contact: any;
  selectedId: string | string[] | undefined;
  hotelId: string;
  isCenter?: boolean;
}

export function formatMessageDateOrTime(createdAt: string): string {
  const date = new Date(createdAt);

  const now = new Date();

  const messageDay = date.getDate();
  const messageMonth = date.getMonth();
  const messageYear = date.getFullYear();

  const nowDay = now.getDate();
  const nowMonth = now.getMonth();
  const nowYear = now.getFullYear();

  const isToday =
    messageDay === nowDay &&
    messageMonth === nowMonth &&
    messageYear === nowYear;

  if (isToday) {
    return formatTimeHHmm(createdAt);
  }

  const day = messageDay.toString().padStart(2, "0");
  const month = (messageMonth + 1).toString().padStart(2, "0");

  return `${day}.${month}.${messageYear}`;
}

const ChatList: FC<ChatListProps> = ({
  contact,
  selectedId,
  hotelId,
  isCenter = false,
}) => {
  const dispatch = useDispatch();

  const openChatHandler = () => {
    if (selectedId === contact?.id) return;

    dispatch(setIsLoading(true));
    dispatch(setIsEdit(false));
    dispatch(setText(""));
    revalidatePathHandler("/hotel/account/messages");
  };

  const checkColorHandler = () => {
    if (contact?.lastMessage?.content?.seen) {
      return `text-green-500`;
    }
  };
  const checkIconColor = checkColorHandler();
  const isHotel = contact?.lastMessage?.content?.from === hotelId;
  const messageTime = formatMessageDateOrTime(
    contact?.lastMessage?.content?.createdAt
  );

  return (
    <Link
      onClick={openChatHandler}
      href={`/hotel/account/messages?id=${contact?.id}` as Route}
      className={`relative flex items-center rounded-lg gap-3 p-4 cursor-pointer border-b border-gray-100 dark:border-neutral-700 last:border-b-0 ${selectedId === contact?.id ? "bg-gray-100 dark:bg-neutral-900" : "hover:bg-gray-50 dark:hover:bg-neutral-900"}`}
    >
      <div className="relative">
        <Avatar className="w-10 h-10">
          <AvatarImage
            className="object-cover"
            src={contact?.image?.src || defaultHotelImage}
          />
          <AvatarFallback className="bg-blue-100 text-blue-600 capitalize">
            {contact?.name?.slice(0, 1)}
          </AvatarFallback>
        </Avatar>
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <h3 className="font-medium text-neutral-800 dark:text-neutral-200 truncate capitalize text-sm">
            {contact?.name}
          </h3>
          <span className="text-xs text-neutral-800 dark:text-neutral-400">
            {messageTime}
          </span>
        </div>
        <div className="text-sm">
          {contact?.lastMessage?.content?.isDeleted ? (
            <div
              className={`italic ${selectedId === contact?.id ? "text-neutral-800 dark:text-neutral-300" : "text-neutral-600 dark:text-neutral-400"}`}
            >
              Bu mesaj silindi
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 basis-11/12">
                  {contact?.lastMessage?.content?.fileType && (
                    <span
                      className={`dark:text-neutral-400 ${selectedId === contact?.id && "text-neutral-800 dark:text-white"}`}
                    >
                      {contact?.lastMessage?.content?.fileType?.includes(
                        "image"
                      ) && (
                        <div className="flex items-center gap-1">
                          <FileImage className="size-5" />
                          {!contact?.lastMessage?.content?.content && (
                            <span>Görsel</span>
                          )}
                        </div>
                      )}
                      {contact?.lastMessage?.content?.fileType?.includes(
                        "pdf"
                      ) && (
                        <div className="flex items-center gap-1">
                          <FileText className="size-5" />
                          {!contact?.lastMessage?.content?.content && (
                            <span>
                              {contact?.lastMessage?.content?.fileName ||
                                "Belge"}
                            </span>
                          )}
                        </div>
                      )}
                    </span>
                  )}
                  <span
                    className={`${isCenter ? "max-w-[calc(100%-20px)] line-clamp-2 [overflow-wrap:anywhere]" : "max-w-[150px] truncate"} ${selectedId === contact?.id ? "text-neutral-800 dark:text-neutral-300" : "text-neutral-600 dark:text-neutral-400"}`}
                  >
                    {contact?.lastMessage?.content?.content}
                  </span>
                </div>
                {isHotel && (
                  <CheckCheck
                    className={`size-4 ${checkIconColor} ${isCenter && "absolute right-2 bottom-3"}`}
                  />
                )}
                {!isHotel &&
                  !contact?.lastMessage?.content?.seen &&
                  selectedId !== contact?.id && (
                    <span className="w-3 h-3 rounded-full bg-green-500" />
                  )}
              </div>
            </>
          )}
        </div>
      </div>
    </Link>
  );
};

export default ChatList;
