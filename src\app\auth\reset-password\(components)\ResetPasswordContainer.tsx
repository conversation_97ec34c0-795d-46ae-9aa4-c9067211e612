"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import LoadingSpinner from "@/shared/icons/Spinner";
import Label from "@/components/Label";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { usePassword } from "@/hooks/auth/usePassword";
import { passwordRegex } from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";

interface ResetPasswordContainerProps {
  resetToken: string | string[] | undefined;
}

const ResetPasswordContainer: FC<ResetPasswordContainerProps> = ({
  resetToken,
}) => {
  const translate = useTranslations("ResetPasswordContainer");
  const { changePasswordHandler } = usePassword();
  const [loading, setLoading] = useState<boolean>(false);
  const [userNewPassword, setUserNewPassword] = useState<{
    newPassword: string;
    newPasswordCheck: string;
  }>({
    newPassword: "",
    newPasswordCheck: "",
  });
  const [enabled, setEnabled] = useState(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    setUserNewPassword((prevState: any) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const passwordCheck = passwordRegex.test(userNewPassword.newPassword);
  const newPasswordValidate =
    userNewPassword.newPassword.length > 0 &&
    userNewPassword.newPasswordCheck.length > 0 &&
    userNewPassword.newPassword === userNewPassword.newPasswordCheck &&
    passwordCheck;

  return (
    <>
      <h2 className="my-20 flex items-center justify-center text-xl font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-3xl md:leading-[115%]">
        {translate("changePassword")}
      </h2>
      <div className="mx-auto max-w-md space-y-6 sm:space-y-8">
        <form
          onSubmit={(event) =>
            changePasswordHandler(
              event,
              resetToken,
              userNewPassword.newPassword,
              setLoading
            )
          }
          className="max-w-xl space-y-6"
        >
          <div className="flex justify-end gap-2">
            <div className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              {enabled ? translate("hide") : translate("show")}
            </div>
            <Switch
              name="show-hide-password"
              onClick={() => setEnabled((prev) => !prev)}
              checked={enabled}
            />
          </div>

          <div>
            <Label>{translate("newPassword")}</Label>
            <Input
              type={`${enabled === true ? "text" : "password"}`}
              className="mt-1.5"
              name="newPassword"
              onChange={handleChange}
              value={userNewPassword.newPassword}
            />
            {!passwordCheck && userNewPassword.newPassword.length > 0 && (
              <span className="mt-3 block text-xs text-red-500">
                {translate("newPasswordValid")}
              </span>
            )}
          </div>
          <div>
            <Label>{translate("newPasswordConfirm")}</Label>
            <Input
              type={`${enabled === true ? "text" : "password"}`}
              className="mt-1.5"
              name="newPasswordCheck"
              onChange={handleChange}
              value={userNewPassword.newPasswordCheck}
            />
            {userNewPassword.newPassword !== userNewPassword.newPasswordCheck &&
              userNewPassword.newPasswordCheck.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("newValidPasswordConfirm")}
                </span>
              )}
          </div>
          <div className="pt-2">
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
              disabled={!newPasswordValidate}
              type="submit"
            >
              {loading ? <LoadingSpinner /> : translate("save")}
            </Button>
          </div>
        </form>
      </div>
    </>
  );
};

export default ResetPasswordContainer;
