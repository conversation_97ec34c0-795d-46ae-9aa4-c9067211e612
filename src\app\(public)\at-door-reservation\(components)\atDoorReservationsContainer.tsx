"use client";
import React from "react";
import AtDoorReservationsStepper from "./atDoorReservationsStepper";
import AddPetContainer from "@/app/hotel/account/add-pet/(components)/AddPetContainer";
import MultipleSelectPetContainer from "./(steps)/(petSelectStep)/MultipleSelectPetContainer";
import SelectPetCheckout from "./(steps)/(petSelectStep)/SelectPetCheckout";
import MultipleSelectPetServiceContainer from "./(steps)/(petSelectStep)/MultipleSelectPetServiceContainer";
import SelectPetServiceCheckout from "./(steps)/(petSelectStep)/SelectPetServiceCheckout";
import SummaryStep from "./(steps)/(summaryStep)/summaryStep";
import PaymentStep from "./(steps)/(paymentStep)/paymentStep";

interface AtDoorReservationContainerProps {
  hotelId: string;
  orderData: any;
  hotelCustomerId: string | null;
  hotelCustomerPetData: any;
  addPetParam: string;
  selectedPetTypes: string[];
  stepParams: {
    summary: string;
    payment: string;
  };
}

export const getSelectedPetIds = (servicesOrReservations: any[]) => {
  return servicesOrReservations
    ?.map((item: any) => item?.pet?._id)
    .filter(Boolean);
};

export const stepHandler = (stepParams: {
  summary: string;
  payment: string;
}) => {
  const hasSummaryStep = stepParams.summary;
  const hasPaymentStep = stepParams.payment;

  if (hasSummaryStep) return 2;
  if (hasPaymentStep) return 3;

  return 1;
};

const AtDoorReservationContainer: React.FC<AtDoorReservationContainerProps> = ({
  hotelId,
  orderData,
  hotelCustomerId,
  hotelCustomerPetData,
  addPetParam,
  selectedPetTypes,
  stepParams,
}) => {
  // Creates an array of selected pet IDs from services, filtering out any null or undefined values
  const serviceSelectedPetArray = getSelectedPetIds(orderData?.services);

  // Creates an array of selected pet IDs from reservations, filtering out any null or undefined values
  const reservationSelectedPetArray = getSelectedPetIds(
    orderData?.reservations
  );

  const stepValue = stepHandler(stepParams);

  return (
    <div className="flex w-full flex-col space-y-8 border-neutral-200 px-0 dark:border-neutral-700 sm:rounded-2xl sm:border sm:p-6 xl:p-8">
      <div className="mx-auto w-full rounded-2x p-2 dark:bg-neutral-900">
        <div className="mx-auto w-full">
          <AtDoorReservationsStepper stepParams={stepParams} />
        </div>
        <div className="shrink-0 bg-border h-[1px] w-full my-10"></div>
        {stepValue === 1 && addPetParam && (
          <AddPetContainer
            hotelCustomerId={hotelCustomerId}
            hotelId={hotelId}
            isPublic
            selectedPetTypes={selectedPetTypes}
          />
        )}
        {/* Reservations */}
        {stepValue === 1 &&
          !addPetParam &&
          orderData?.reservations?.length > 0 && (
            <>
              {orderData?.reservations?.length > 1 ? (
                <div className="pt-4 xl:pt-8">
                  <MultipleSelectPetContainer
                    orderData={orderData}
                    hotelCustomerPetData={hotelCustomerPetData}
                    hotelCustomerId={hotelCustomerId}
                  />
                </div>
              ) : (
                <div className="pt-4 xl:pt-8">
                  <SelectPetCheckout
                    hotelCustomerPetData={hotelCustomerPetData}
                    orderId={orderData?._id}
                    hotelCustomerId={hotelCustomerId}
                    petTypes={orderData?.reservations[0]?.room?.petType}
                    reservationSelectedPetArray={reservationSelectedPetArray}
                  />
                </div>
              )}
            </>
          )}
        {/* Services */}
        {stepValue === 1 && !addPetParam && orderData?.services?.length > 0 && (
          <>
            {orderData?.services?.length > 1 ? (
              <div className="pt-4 xl:pt-8">
                <MultipleSelectPetServiceContainer
                  orderData={orderData}
                  hotelCustomerPetData={hotelCustomerPetData}
                  hotelCustomerId={hotelCustomerId}
                />
              </div>
            ) : (
              <div className="pt-4 xl:pt-8">
                <SelectPetServiceCheckout
                  hotelCustomerPetData={hotelCustomerPetData}
                  orderId={orderData?._id}
                  hotelCustomerId={hotelCustomerId}
                  petTypes={orderData?.services[0]?.petType}
                  serviceSelectedPetArray={serviceSelectedPetArray}
                  serviceName={orderData?.services[0]?.serviceName}
                />
              </div>
            )}
          </>
        )}
        {stepValue === 2 && <SummaryStep orderData={orderData} />}
        {stepValue === 3 && <PaymentStep orderData={orderData} />}
      </div>
    </div>
  );
};

export default AtDoorReservationContainer;
