interface AddressType {
  country: {
    identificationCode: string;
    name: string;
  };
  streetName: string;
  buildingName: string;
  buildingNumber: string;
  cityName: string;
  postalZone: string;
  region: string;
  district: string;
  blockName: string;
  citySubdivisionName: string;
  postbox: string;
  room: string;
}

interface FileType {
  _id: string;
  src: string;
  size: number;
  mimeType: string;
  originalname: string;
  s3Path: string;
  docType: string;
  createdAt: string;
  updatedAt: string;
}

interface ImageSizeType {
  size: number;
  src: string;
  width: number;
  height: number;
}

export interface ImageType {
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimeType: string;
  fit: string;
  tags: string;
  createdAt: string;
  updatedAt: string;
  processed: boolean;
  img800: ImageSizeType;
  img400: ImageSizeType;
  img200: ImageSizeType;
  img100: ImageSizeType;
}

interface SpecialRuleType {
  title: string;
  rule: string;
  _id: string;
}

interface PetTaxiPolicyType {
  serviceHoursStart: string;
  serviceHoursEnd: string;
  cancellationPolicyType: string;
  description: string;
  dateRange: string;
  specialRules: SpecialRuleType[];
}

export interface PetTaxiDataApiTypes {
  acceptedPetTypes: string[];
  address: AddressType;
  createdAt: string;
  currency: string;
  files: FileType[];
  googleMapUrl: string;
  petTaxiDescription: string;
  petTaxiFeatures: string[];
  petTaxiName: string;
  petTaxiPhoneNumber: string;
  petTaxiSubMerchant: string;
  images: ImageType[];
  logo: ImageType;
  owner: string;
  passive: boolean;
  policy: PetTaxiPolicyType;
  status: string;
  taxNumber: string;
  taxOffice: string;
  updatedAt: string;
  _id: string;
}
