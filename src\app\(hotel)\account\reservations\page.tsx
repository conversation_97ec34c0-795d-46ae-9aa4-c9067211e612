import type { FC } from "react";
import React from "react";
import { cookies } from "next/headers";
import getHotelReservations from "@/actions/(protected)/reservations/getHotelReservations";
import ReservationDataTable from "./(components)/ReservationDataTable";
import ReservationPagination from "./(components)/ReservationPagination";
import ReservationCardMobile from "./(components)/(mobile)/ReservationCardMobile";
import ResevationFilterNavMobile from "./(components)/(mobile)/ResevationFilterNavMobile";
import EmptyState from "@/components/EmptyState";
import { ReservationListApiTypes } from "@/types/hotel/reservation/reservationListType";

interface ReservationPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

const ReservationsPage: FC<ReservationPageProps> = async ({ searchParams }) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const reservationPage = searchParams.page || 1;
  const tab = searchParams.tab || "all";
  const reservationList = await getHotelReservations(+reservationPage, tab);

  return (
    <div className="container">
      <div className="max-lg:hidden">
        <ReservationDataTable
          reservationList={reservationList?.data?.docs}
          hotelToken={hotelToken}
          tab={tab}
          reservationPage={+reservationPage}
        />
      </div>
      <div className="lg:hidden">
        <ResevationFilterNavMobile
          tab={tab}
          reservationPage={+reservationPage}
        />
      </div>
      {reservationList?.data?.docs?.length > 0 ? (
        <div className="lg:hidden">
          <div className="grid grid-cols-1 gap-3">
            {reservationList?.data?.docs?.map(
              (reservationData: ReservationListApiTypes) => {
                return (
                  <ReservationCardMobile
                    key={reservationData._id}
                    reservationData={reservationData}
                    hotelToken={hotelToken}
                  />
                );
              }
            )}
          </div>
        </div>
      ) : (
        <div className="lg:hidden">
          <EmptyState text="Rezervasyon bulunamadı" />
        </div>
      )}
      <div className="mt-5">
        <ReservationPagination
          tab={tab}
          reservationPage={+reservationPage}
          pageCount={reservationList?.data?.pageCount}
        />
      </div>
    </div>
  );
};

export default ReservationsPage;
