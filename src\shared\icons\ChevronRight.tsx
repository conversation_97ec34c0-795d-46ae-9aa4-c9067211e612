import type { FC } from "react";
import React from "react";

interface IconChevronRightProps {
  className?: string;
  strokeWidth?: number;
  onClick?: () => void;
}

const IconChevronRight: FC<IconChevronRightProps> = ({
  className = "size-6",
  strokeWidth = 2.8,
  onClick,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
      onClick={onClick}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m8.25 4.5 7.5 7.5-7.5 7.5"
      />
    </svg>
  );
};

export default IconChevronRight;
