import emojiRegex from "emoji-regex";

/**
 * Checks if the given text contains only emoji characters (ignoring whitespace).
 * Returns true if there is no non-emoji content left after removing all emojis.
 */
export const isOnlyEmoji = (text: string): boolean => {
  const regex = emojiRegex();

  const withoutEmojis = text.replace(regex, "").trim();

  return withoutEmojis === "";
};

/**
 * Splits a text into parts of plain text and emoji groups.
 * Consecutive emoji characters are grouped together into a single part.
 * Returns an array of objects with the text and a flag indicating if it's an emoji group.
 */
export const splitTextWithEmojis = (text: string) => {
  const regex = emojiRegex();
  const result: { text: string; isEmoji: boolean }[] = [];

  let lastIndex = 0;
  let emojiBuffer = ""; // Peş peşe emoji biriktirmek için

  const pushEmojiBuffer = () => {
    if (emojiBuffer.length > 0) {
      result.push({ text: emojiBuffer, isEmoji: true });
      emojiBuffer = "";
    }
  };

  for (const match of text.matchAll(regex)) {
    const start = match.index!;
    const end = start + match[0].length;

    // Önce emoji öncesi metni ekle (eğer varsa)
    if (start > lastIndex) {
      pushEmojiBuffer(); // Önce varsa birikmiş emojiyi ekle
      result.push({
        text: text.slice(lastIndex, start),
        isEmoji: false,
      });
    }

    // Emoji karakterini buffer'a ekle
    emojiBuffer += match[0];

    lastIndex = end;
  }

  // Son kalan kısmı işle
  pushEmojiBuffer();

  if (lastIndex < text.length) {
    result.push({
      text: text.slice(lastIndex),
      isEmoji: false,
    });
  }

  return result;
};
