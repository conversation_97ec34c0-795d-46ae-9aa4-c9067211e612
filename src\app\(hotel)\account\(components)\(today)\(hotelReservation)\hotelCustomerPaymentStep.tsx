import { useState } from "react";
import Label from "@/components/Label";
import Select from "@/shared/Select";

interface HotelCustomerPaymentStepProps {
  onSelectPaymentDetails: (channel: string, paymentType: string) => void;
  initialValues?: {
    channel: string;
    paymentType: string;
  };
}
const HotelCustomerPaymentStep: React.FC<HotelCustomerPaymentStepProps> = ({
  onSelectPaymentDetails,
  initialValues,
}) => {
  const [channel, setChannel] = useState(initialValues?.channel || "");
  const [paymentType, setPaymentType] = useState(
    initialValues?.paymentType || ""
  );

  const handleChannelChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newChannel = event.target.value;
    setChannel(newChannel);
    onSelectPaymentDetails(newChannel, paymentType);
  };

  const handlePaymentTypeChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newPaymentType = event.target.value;
    setPaymentType(newPaymentType);
    onSelectPaymentDetails(channel, newPaymentType);
  };

  return (
    <div>
      <div className="text-xl font-bold">Kanal ve Ödeme Tipi</div>
      <div className="flex flex-col mx-auto mt-20 space-y-5 min-w-[300px] p-4 w-64">
        <div>
          <Label>Kanal</Label>
          <Select value={channel} onChange={handleChannelChange}>
            <option value="" disabled>
              Kanal Seçiniz
            </option>
            <option value="atTheDoor">Kapıda</option>
            <option value="telephone">Telefon</option>
            <option value="website">Website</option>
          </Select>
        </div>
        <div>
          <Label>Ödeme Tipi</Label>
          <Select value={paymentType} onChange={handlePaymentTypeChange}>
            <option value="" disabled>
              Ödeme Tipi Seçiniz
            </option>
            <option value="cash">Nakit</option>
            <option value="creditCard">Kredi Kartı</option>
            <option value="transfer">Havale</option>
            <option value="mailOrder">Mail Order</option>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default HotelCustomerPaymentStep;
