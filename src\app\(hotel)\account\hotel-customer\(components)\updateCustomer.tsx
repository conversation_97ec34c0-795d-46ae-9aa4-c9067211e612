"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import Input from "@/shared/Input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import IconEdit from "@/shared/icons/Edit";
import { useHotelCustomers } from "@/hooks/hotel/useHotelCustomers";
import type { HotelCustomerApiTypes } from "@/types/hotel/hotelCustomerType";
import LoadingSpinner from "@/shared/icons/Spinner";
import { onlyLetterRegex, emailRegex } from "@/utils/regex/petOwnerRegex";
import { Button } from "@/components/ui/button";

interface UpdateHotelCustomerProps {
  rowOriginal: HotelCustomerApiTypes;
  hotelToken: string | undefined;
}

const updateCustomerModal: FC<UpdateHotelCustomerProps> = ({
  rowOriginal,
  hotelToken,
}) => {
  const { updateHotelCustomerHandler } = useHotelCustomers();
  const initialData = {
    fullName: rowOriginal.fullName,
    email: rowOriginal.email,
    phone: rowOriginal.phone,
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [updatedHotelCustomer, setUpdatedHotelCustomer] = useState(initialData);
  const [hotelCustomerModalIsOpen, setHotelCustomerModalIsOpen] =
    useState(false);

  function closeCustomerUpdateModal() {
    setHotelCustomerModalIsOpen(false);
    setLoading(false);
    setUpdatedHotelCustomer(initialData);
  }

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setUpdatedHotelCustomer((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const phoneRegex = /^\d{10}$/;
  const nameCheck = onlyLetterRegex.test(updatedHotelCustomer.fullName);
  const mailCheck = emailRegex.test(
    updatedHotelCustomer.email.toLowerCase().trim()
  );
  const phoneCheck = phoneRegex.test(updatedHotelCustomer.phone);

  const isAllValid = nameCheck && mailCheck && phoneCheck;

  return (
    <div>
      <div className="flex justify-end">
        <IconEdit
          className="size-5 duration-200 hover:text-secondary-6000"
          onClick={() => setHotelCustomerModalIsOpen(true)}
        />
      </div>
      <Dialog
        open={hotelCustomerModalIsOpen}
        onOpenChange={setHotelCustomerModalIsOpen}
      >
        <DialogContent
          onInteractOutside={closeCustomerUpdateModal}
          className="w-auto"
        >
          <DialogHeader>
            <DialogTitle>Müşteri Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) => {
              updateHotelCustomerHandler(
                event,
                hotelToken,
                updatedHotelCustomer,
                rowOriginal._id,
                setLoading,
                closeCustomerUpdateModal
              );
            }}
          >
            <div className="py-4 px-4 space-y-4">
              <label className="flex flex-col justify-start w-80">
                <span className="text-neutral-800 dark:text-neutral-200">
                  Ad-Soyad
                </span>
                <Input
                  type="text"
                  className="mt-1 rounded-lg"
                  name="fullName"
                  onChange={handleChange}
                  value={updatedHotelCustomer.fullName || ""}
                />
              </label>
              <label className="flex flex-col justify-start w-80">
                <span className="text-neutral-800 dark:text-neutral-200">
                  E-Posta
                </span>
                <Input
                  type="text"
                  className="mt-1 rounded-lg"
                  name="email"
                  onChange={handleChange}
                  value={updatedHotelCustomer.email || ""}
                />
              </label>
              <label className="flex flex-col justify-start">
                <span className="text-neutral-800 dark:text-neutral-200">
                  Telefon
                </span>
                <div className="flex rounded-lg shadow-sm shadow-black/5 w-80">
                  <span className="inline-flex items-center rounded-s-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="30"
                      viewBox="0 -30000 90000 60000"
                    >
                      <path fill="#e30a17" d="m0-30000h90000v60000H0z" />
                      <path
                        fill="#fff"
                        d="m41750 0 13568-4408-8386 11541V-7133l8386 11541zm925 8021a15000 15000 0 1 1 0-16042 12000 12000 0 1 0 0 16042z"
                      />
                    </svg>{" "}
                    <span className="ml-1">+90</span>
                  </span>
                  <Input
                    name="phone"
                    type="text"
                    placeholder="Telefon Numarası"
                    className="flex-1 block w-full min-w-0 rounded-none rounded-r-lg transition duration-150 ease-in-out sm:text-sm sm:leading-5"
                    onChange={handleChange}
                    maxLength={10}
                    value={updatedHotelCustomer.phone || ""}
                  />
                </div>
              </label>
              <div className="flex justify-end gap-5">
                <Button
                  variant="outline"
                  onClick={closeCustomerUpdateModal}
                  type="button"
                >
                  Vazgeç
                </Button>
                <Button
                  className="bg-secondary-6000 hover:bg-secondary-700"
                  disabled={!isAllValid}
                  type="submit"
                >
                  {loading ? <LoadingSpinner /> : "Güncelle"}
                </Button>
              </div>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default updateCustomerModal;
