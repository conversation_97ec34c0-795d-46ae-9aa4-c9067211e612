import React from "react";
import AddPetContainer from "./(components)/AddPetContainer";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

const AddNewPetPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const hotelCustomerId = searchParams.hotelCustomerId;

  if (!hotelToken || !hotelCustomerId) {
    redirect("/");
  }
  return (
    <div className="bg-neutral-50 dark:bg-neutral-900 min-h-screen">
      <AddPetContainer
        hotelToken={hotelToken}
        hotelCustomerId={hotelCustomerId}
        hotelData={hotelData?.data}
      />
    </div>
  );
};

export default AddNewPetPage;
