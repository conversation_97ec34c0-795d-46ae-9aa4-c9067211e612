import React from "react";
import ButtonPrimary from "@/shared/ButtonPrimary";
import NotFoundImage from "@/components/NotFoundImage";
import Link from "next/link";

const Page404 = () => (
  <div className="min-h-screen bg-[#fffaf7] lg:py-40 2xl:py-60">
    <div className="flex items-center justify-center gap-10 py-10 max-lg:min-h-screen max-lg:flex-col-reverse lg:gap-20">
      <div className="space-y-5">
        <h2 className="text-center text-6xl font-semibold lg:text-8xl">404</h2>
        <h3 className="text-center text-lg lg:text-2xl">
          Maalesef aradığınız sayfayı bulamadık.
        </h3>
        <div className="text-center text-lg font-medium">
          <Link href="/">
            <ButtonPrimary>Ana sayfaya dön</ButtonPrimary>
          </Link>
        </div>
      </div>
      <div>
        <NotFoundImage className="size-[200px] lg:size-[375px]" />
      </div>
    </div>
  </div>
);

export default Page404;
