"use client";
import React from "react";
import RoomSelectStep from "./(steps)/(roomSelectStep)/roomSelectStep";
import CustomerSelectStep from "./(steps)/(customerSelectStep)/customerSelectStep";
import AtDoorReservationsStepper from "./atDoorReservationsStepper";
import AddPetContainer from "@/app/hotel/account/add-pet/(components)/AddPetContainer";
import MultipleSelectPetContainer from "./(steps)/(petSelectStep)/MultipleSelectPetContainer";
import SelectPetCheckout from "./(steps)/(petSelectStep)/SelectPetCheckout";
import MultipleSelectPetServiceContainer from "./(steps)/(petSelectStep)/MultipleSelectPetServiceContainer";
import SelectPetServiceCheckout from "./(steps)/(petSelectStep)/SelectPetServiceCheckout";
import SendSmsContainer from "./(steps)/(petSelectStep)/SendSmsContainer";
import SummaryStep from "./(steps)/(summaryStep)/summaryStep";
import PaymentStep from "./(steps)/(paymentStep)/paymentStep";

interface AtDoorReservationContainerProps {
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  serviceData: any;
  allHotelCustomers: any;
  orderData: any;
  hotelCustomerId: string | null;
  hotelCustomerPetData: any;
  addPetParam: string;
  selectedPetTypes: string[];
  stepParams: {
    customer: string;
    pet: string;
    summary: string;
    payment: string;
  };
}

export const getSelectedPetIds = (servicesOrReservations: any[]) => {
  return servicesOrReservations
    ?.map((item: any) => item?.pet?._id)
    .filter(Boolean);
};

export const stepHandler = (stepParams: {
  customer: string;
  pet: string;
  summary: string;
  payment: string;
}) => {
  const hasCustomerStep = stepParams.customer;
  const hasPetStep = stepParams.pet;
  const hasSummaryStep = stepParams.summary;
  const hasPaymentStep = stepParams.payment;

  if (hasCustomerStep) return 2;
  if (hasPetStep) return 3;
  if (hasSummaryStep) return 4;
  if (hasPaymentStep) return 5;

  return 1;
};

const AtDoorReservationContainer: React.FC<AtDoorReservationContainerProps> = ({
  hotelToken,
  hotelData,
  subscriptionData,
  serviceData,
  allHotelCustomers,
  orderData,
  hotelCustomerId,
  hotelCustomerPetData,
  selectedPetTypes,
  addPetParam,
  stepParams,
}) => {
  // Creates an array of selected pet IDs from services, filtering out any null or undefined values
  const serviceSelectedPetArray = getSelectedPetIds(orderData?.services);

  // Creates an array of selected pet IDs from reservations, filtering out any null or undefined values
  const reservationSelectedPetArray = getSelectedPetIds(
    orderData?.reservations
  );

  const stepValue = stepHandler(stepParams);

  return (
    <div className="flex w-full flex-col space-y-8 border-neutral-200 px-0 dark:border-neutral-700 sm:rounded-2xl sm:border sm:p-6 xl:p-8">
      <div className="mx-auto w-full rounded-2x p-2 dark:bg-neutral-900">
        <div className="mx-auto w-full">
          <AtDoorReservationsStepper stepParams={stepParams} />
        </div>
        <div className="shrink-0 bg-border h-[1px] w-full my-10"></div>
        {stepValue === 1 && (
          <RoomSelectStep
            hotelToken={hotelToken}
            hotelData={hotelData}
            subscriptionData={subscriptionData}
            serviceData={serviceData}
          />
        )}
        {stepValue === 2 && (
          <CustomerSelectStep
            hotelToken={hotelToken}
            hotelData={hotelData}
            allHotelCustomers={allHotelCustomers}
          />
        )}
        {stepValue === 3 && addPetParam && (
          <AddPetContainer
            hotelToken={hotelToken}
            hotelCustomerId={hotelCustomerId}
            hotelId={hotelData?._id}
            isCheckout
            selectedPetTypes={selectedPetTypes}
          />
        )}
        {stepValue === 3 && !addPetParam && (
          <SendSmsContainer
            hotelCustomerId={hotelCustomerId}
            orderData={orderData}
            hotelToken={hotelToken}
          />
        )}
        {/* Reservations */}
        {stepValue === 3 &&
          !addPetParam &&
          orderData?.reservations?.length > 0 && (
            <>
              {orderData?.reservations?.length > 1 ? (
                <div className="pt-4 xl:pt-8">
                  <MultipleSelectPetContainer
                    orderData={orderData}
                    hotelCustomerPetData={hotelCustomerPetData}
                    hotelCustomerId={hotelCustomerId}
                  />
                </div>
              ) : (
                <div className="pt-4 xl:pt-8">
                  <SelectPetCheckout
                    hotelCustomerPetData={hotelCustomerPetData}
                    orderId={orderData?._id}
                    hotelCustomerId={hotelCustomerId}
                    petTypes={orderData?.reservations[0]?.room?.petType}
                    reservationSelectedPetArray={reservationSelectedPetArray}
                  />
                </div>
              )}
            </>
          )}
        {/* Services */}
        {stepValue === 3 && !addPetParam && orderData?.services?.length > 0 && (
          <>
            {orderData?.services?.length > 1 ? (
              <div className="pt-4 xl:pt-8">
                <MultipleSelectPetServiceContainer
                  orderData={orderData}
                  hotelCustomerPetData={hotelCustomerPetData}
                  hotelCustomerId={hotelCustomerId}
                />
              </div>
            ) : (
              <div className="pt-4 xl:pt-8">
                <SelectPetServiceCheckout
                  hotelCustomerPetData={hotelCustomerPetData}
                  orderId={orderData?._id}
                  hotelCustomerId={hotelCustomerId}
                  petTypes={orderData?.services[0]?.petType}
                  serviceSelectedPetArray={serviceSelectedPetArray}
                  serviceName={orderData?.services[0]?.serviceName}
                />
              </div>
            )}
          </>
        )}
        {stepValue === 4 && (
          <SummaryStep orderData={orderData} hotelToken={hotelToken} />
        )}
        {stepValue === 5 && <PaymentStep />}
      </div>
    </div>
  );
};

export default AtDoorReservationContainer;
