"use client";
import type { ChangeEvent } from "react";
import React, { useState } from "react";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import FormItem from "@/shared/FormItem";
import LoadingSpinner from "@/shared/icons/Spinner";
import IconCheck from "@/shared/icons/Check";
import Link from "next/link";
import { usePassword } from "@/hooks/auth/usePassword";
import { emailRegex } from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";

const ForgotPasswordContainer = ({
  role,
}: {
  role: string | string[] | undefined;
}) => {
  const translate = useTranslations("ForgotPasswordContainer");
  const { forgotPasswordHandler } = usePassword();
  const [email, setEmail] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const mailValue = e.target.value.toLowerCase().trim();
    setEmail(mailValue);
  };

  const mailCheck = emailRegex.test(email);

  return (
    <div className="container mb-20 lg:mb-40">
      {success ? (
        <div className="flex h-96 flex-col items-center justify-center gap-8">
          <IconCheck className="size-12 rounded-full bg-green-500 p-2 text-white md:size-16" />
          <div
            className="text-center font-medium md:text-lg"
            dangerouslySetInnerHTML={{ __html: translate("sendMessage") }}
          />
          <Link href="/">
            <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
              {translate("returnHomepage")}
            </Button>
          </Link>
        </div>
      ) : (
        <>
          <h2 className="my-20 flex items-center justify-center font-semibold leading-[115%] text-neutral-900 dark:text-neutral-100 md:text-3xl md:leading-[115%]">
            {translate("forgotPassword")}
          </h2>
          <form
            className="mx-auto grid max-w-md grid-cols-1 gap-6"
            onSubmit={(event) =>
              forgotPasswordHandler(event, email, role, setLoading, setSuccess)
            }
          >
            <FormItem
              label={translate("email")}
              desc={translate("emailDesc")}
              className="!text-lg"
            >
              <Input
                type="email"
                name="email"
                className="mt-1 text-lg"
                onChange={handleChange}
              />
              {!mailCheck && email.length > 0 && (
                <span className="mt-3 block text-sm text-red-500">
                  {translate("validEmail")}
                </span>
              )}
            </FormItem>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={!mailCheck}
              type="submit"
            >
              {loading ? <LoadingSpinner /> : translate("send")}
            </Button>
          </form>
        </>
      )}
    </div>
  );
};

export default ForgotPasswordContainer;
