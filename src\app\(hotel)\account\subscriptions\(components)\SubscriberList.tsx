"use client";
import React from "react";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Input from "@/shared/Input";
import { Search } from "lucide-react";
import EmptyState from "@/components/EmptyState";

export interface Subscribers {
  subscriptionName: string;
  petOwner: {
    fullName: string;
    email: string;
    phone: string;
  };
  startDate: string;
  subscriptionDuration: number;
  availableNights: number;
  status: string;
}

const SubscriberList = ({ subscriberData }: { subscriberData: any }) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [globalFilter, setGlobalFilter] = React.useState("");

  const columns: ColumnDef<Subscribers>[] = [
    {
      accessorFn: (row) => row.petOwner?.fullName || "-",
      header: "Ad-Soyad",
      cell: (info) => (
        <div className="capitalize">{String(info.getValue())}</div>
      ),
    },
    {
      accessorFn: (row) => row.petOwner?.phone || "-",
      header: "Telefon Numarası",
      cell: (info) => (
        <div className="capitalize">{String(info.getValue())}</div>
      ),
    },
    {
      accessorFn: (row) => row.subscriptionName || "-",
      header: "Üyelik Kartı İsmi",
      cell: (info) => (
        <div className="capitalize">{String(info.getValue())}</div>
      ),
    },
    {
      accessorFn: (row) => row.availableNights || "-",
      header: "Kalan Gece Sayısı",
      cell: (info) => (
        <div className="capitalize">
          {info.getValue() !== "-" ? `${info.getValue()} gece` : "-"}
        </div>
      ),
    },
    {
      accessorFn: (row) => {
        const date = new Date(row.startDate);
        date.setDate(date.getDate() + row.subscriptionDuration);
        return date.toLocaleDateString("tr-TR", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        });
      },
      header: "Son Geçerlilik Tarihi",
      cell: (info) => (
        <div className="capitalize">{String(info.getValue())}</div>
      ),
    },
    {
      accessorFn: (row) => {
        switch (row.status) {
          case "active":
            return "Aktif";
          case "expired":
            return "Süresi Dolmuş";
          default:
            return "-";
        }
      },
      header: "Durum",
      cell: (info) => (
        <div className="capitalize">{String(info.getValue())}</div>
      ),
    },
  ];

  const table = useReactTable({
    data: subscriberData ?? [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: (row, columnId, filterValue) => {
      const value = String(row.getValue(columnId)).toLowerCase();
      return value.includes(String(filterValue).toLowerCase());
    },
  });

  return (
    <div className="w-full">
      <div className="w-56 relative">
        <Input
          className="pe-9 ps-9"
          sizeClass="h-10"
          rounded="rounded-md"
          placeholder="Ara..."
          value={globalFilter}
          onChange={(e) => setGlobalFilter(e.target.value)}
        />
        <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
          <Search size={16} strokeWidth={2} />
        </div>
      </div>
      <div className="rounded-md border bg-white dark:bg-neutral-900 mt-5">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={`${
                    index % 2 === 0 ? "bg-gray-100 dark:bg-neutral-800" : ""
                  }`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyState text="Satılan üyelik kartı bulunamadı" />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default SubscriberList;
