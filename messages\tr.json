{"Page": {"description": "<p>We've unfortunately encountered an error.</p><p>You can try to <retry>reload the page</retry> you were visiting.</p>", "title": "Something went wrong!"}, "Layout": {"description": "This is a basic example that demonstrates the usage of <code>next-intl</code> with the Next.js App Router. Try changing the locale in the top right corner and see how the content changes.", "title": "next-intl example"}, "Component": {"title": "next-intl example"}, "LangDropdown": {"language": "Dil"}, "TodayContainer": {"reservation": "Rezervasyon", "revenue": "Kazanç", "guest": "<PERSON><PERSON><PERSON><PERSON>", "cancellation": "İptal", "yesterday": "<PERSON><PERSON><PERSON>"}, "TodayRoomCard": {"closeAll": "Tümünü <PERSON>", "openAll": "Tümünü Aç"}, "LoginPage": {"login": "<PERSON><PERSON><PERSON>", "hotelLogin": "Otel Giriş", "loginUsername": "Kullanıcı Adı / E-Posta", "loginPassword": "Şifre", "loginForgotPassword": "Şifrenizi mi unuttunuz?", "loginLogin": "<PERSON><PERSON><PERSON>", "loginNewuser": "Yeni kullanıcı?", "loginCreateaccount": "<PERSON><PERSON><PERSON>", "loginWelcome": "<PERSON><PERSON> Geldiniz"}, "CreateUserContainer": {"signup": "Üye ol", "login": "<PERSON><PERSON><PERSON> yap", "name": "Ad", "validName": "Lütfen geçerli ad giriniz", "surname": "Soyad", "validSurName": "Lütfen geçerli soyad giriniz", "email": "E-Posta", "validEmail": "Lütfen geçerli e-posta giriniz", "dateOfBirth": "<PERSON><PERSON><PERSON> tarihi", "username": "Kullanıcı adı", "validUsername": "Kullanıcı adınız en az 4 karakter uzunluğunda olmalı ve sadece küçük harfler (a-z) ve rakamlar (0-9) içermelidir. Özel karakterler veya boşluklar kullanılamaz.", "password": "Şifre", "validPassword": "Şifreniz en az 8 karakter olmalı, bir b<PERSON><PERSON><PERSON><PERSON> harf, bir küçük harf ve bir rakam içermelidir. Özel karakterler (!@#$%^&*()_+.-) kullanılabilir, ancak boşluk olmamalıdır.", "passwordConfirm": "<PERSON><PERSON><PERSON>", "validPasswordConfirm": "<PERSON><PERSON><PERSON> ve Şifre tekrar uyuşmuyor", "phoneNumber": "Cep telefonu", "phoneNumberInfo": "Başında 0 olacak şekilde telefon numaranızı giriniz.", "validPhoneNumber": "Telefon numaranız 0 ile başlamalı, boşluk, harf veya özel karakter kullanılmamalıdır.", "genderMale": "<PERSON><PERSON><PERSON>", "genderFemale": "Kadın", "genderOther": "Belirtmek istemiyorum", "account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z var mı?"}, "VerifyAccountContainer": {"accountVerification": "<PERSON><PERSON><PERSON>", "verificationCode": "Lütfen mailinize gelen doğrulama kodunu giriniz!", "confirm": "<PERSON><PERSON><PERSON>"}, "ForgotPasswordContainer": {"forgotPassword": "Şif<PERSON><PERSON>", "email": "E-Posta", "emailDesc": "Kayıtlı olduğunuz e-posta adresini yazınız", "validEmail": "Lütfen geçerli mail adresi giriniz", "send": "<PERSON><PERSON><PERSON>", "sendMessage": "Şifre sıfırlama bağlantısı, kayıtlı e-posta adresinize gönderilmiştir.<br /> Lütfen e-postanızı kontrol edin.", "returnHomepage": "<PERSON> <PERSON><PERSON> d<PERSON>n"}, "ResetPasswordContainer": {"changePassword": "<PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "newPasswordValid": "Şifreniz en az 8 karakter olmalı, bir b<PERSON><PERSON><PERSON><PERSON> harf, bir küçük harf ve bir rakam içermelidir. Özel karakterler (!@#$%^&*()_+.) kullanılabilir, ancak boşluk olmamalıdır.", "newPasswordConfirm": "<PERSON><PERSON>", "newValidPasswordConfirm": "<PERSON><PERSON> ve Şifre tekrar uyuşmuyor", "save": "<PERSON><PERSON>"}, "AuthHeader": {"back": "<PERSON>", "approve": "Geri dönerseniz bilgileriniz sıfırlanacaktır. Onaylıyor musunuz?"}, "HotelEmailValidation": {"emailVerification": "<PERSON><PERSON>", "emailAdress": "Lütfen email adresinizi yazınız", "validEmail": "Lütfen geçerli mail adresi giriniz", "send": "<PERSON><PERSON><PERSON>", "emailCode": "Mail adresinize gelen kodu giriniz", "confirm": "<PERSON><PERSON><PERSON>"}, "HotelPhoneValidation": {"phoneVerification": "Telefon Numarası Onaylama", "phoneNumber": "Lütfen telefon numaranızı yazınız", "phoneNumberInfo": "Başında 0 olmayacak şekilde telefon numaranızı giriniz.", "validPhoneNumber": "Telefon numaranız 0 ile başlamalı, boşluk, harf veya özel karakter kullanılmamalıdır.", "send": "<PERSON><PERSON><PERSON>", "phoneCode": "Telefonunuza gelen kodu giriniz", "confirm": "<PERSON><PERSON><PERSON>", "confirmSendCode": "Kod göndermeyi onaylıyor musunuz?", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "CreateHotelContainer": {"send": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>"}, "HotelFirstStep": {"hotelOwnerInfo": "İşletme Sahibi Bilgileri", "name": "Ad *", "validName": "Lütfen geçerli ad giriniz", "surname": "Soyad *", "validSurName": "Lütfen geçerli soyad giriniz", "dateOfBirth": "<PERSON><PERSON><PERSON> *", "username": "Kullanıcı adı *", "validUsername": "Kullanıcı adınız en az 4 karakter uzunluğunda olmalı ve sadece küçük harfler (a-z) ve rakamlar (0-9) içermelidir. Özel karakterler veya boşluklar kullanılamaz.", "password": "Şifre *", "validPassword": "Şifreniz en az 8 karakter olmalı, bir b<PERSON><PERSON><PERSON><PERSON> harf, bir küçük harf ve bir rakam içermelidir. Özel karakterler (!@#$%^&*()_+.-) kullanılabilir, ancak boşluk olmamalıdır.", "passwordConfirm": "<PERSON><PERSON><PERSON> *", "validPasswordConfirm": "<PERSON><PERSON><PERSON> ve Şifre tekrar uyuşmuyor", "gender": "Cinsiyet *", "genderMale": "<PERSON><PERSON><PERSON>", "genderFemale": "Kadın", "genderOther": "Belirtmek istemiyorum"}, "HotelSecondStep": {"hotelInfo": "İşletme Bilgileri", "propertyType": "İşletme türü *", "hotelName": "İşletme adı *", "hotelAddress": "<PERSON><PERSON>", "hotelCity": "Şehir *", "hotelDistrict": "İlçe *", "hotelNeighbourhood": "Mahalle *", "hotelNeighbourhoodDesc": "İşletmenizin bulunduğu mahalle", "hotelStreet": "Sokak *", "hotelStreetDesc": "İşletmenizin bulunduğu sokak", "hotelBuildingName": "<PERSON><PERSON> *", "hotelBuildingNameDesc": "İşletmenizin bulunduğu bina", "hotelBuildingNumber": "Bina No *", "hotelBuildingNumberDesc": "İşletmenizin bulunduğu bina no", "hotelPostalCode": "<PERSON>a kodu *", "hotelPostalCodeDesc": "İşletmenizin posta kodu", "hotelCompanyInfo": "Şirket Bilgileri", "hotelCompanyName": "<PERSON><PERSON> *", "hotelCompanyNameDesc": "<PERSON>rma adınız", "hotelCompanyTaxNumber": "<PERSON><PERSON><PERSON> *", "hotelCompanyTaxNumberDesc": "İşletmenizin vergi numarası", "hotelCompanyTaxOffice": "<PERSON><PERSON><PERSON> *", "hotelCompanyTaxOfficeDesc": "İşletmenizin kayıtlı olduğu vergi dairesi", "hotelPhoneNumber": "İşletme Telefon numarası", "hotelPhoneNumberDesc": "İşletmenize ait telefon numarasını giriniz", "hotelDescription": "İşletmenin <PERSON>", "hotelRequired": "Zorunlu alanların hepsi doldurulmalıdır.", "petHotel": "<PERSON><PERSON><PERSON><PERSON>", "petTaxi": "<PERSON><PERSON><PERSON><PERSON>", "petGroomer": "<PERSON><PERSON><PERSON><PERSON>", "veterinary": "Veteriner <PERSON>liniği", "petFriendlyHotel": "<PERSON><PERSON><PERSON><PERSON>", "trainingFarm": "Eğitim Çiftliği"}, "FileSection": {"hotelUploadHotelLicense": "İşletme ruhsatı yükle", "hotelUploadHotelPhoto": "İşletme profil fotoğrafı yükle"}, "HotelThirdStep": {"hotelFeatures": "Otel Özellikleri", "petTaxi": "Pet taksi", "petGrooming": "<PERSON>", "petNursery": "Kreş", "pool": "Havuz", "playground": "<PERSON><PERSON> alanı", "dogWalking": "Köpek gezdirme", "FeaturesCategories": {"healthAndSafety": "Sağlık ve Güvenlik", "comfortAndAccommodation": "Konfor ve <PERSON>ınma", "activitiesAndEntertainment": "Aktiviteler ve Eğlence", "extraServices": "Ekstra Hizmetler"}, "FeaturesLabels": {"veterinaryServices": "24/7 Veteriner hizmetleri", "cameraSurveillance": "<PERSON><PERSON><PERSON> siste<PERSON>", "securityCameras": "Güvenlik kameraları", "emergencyEquipment": "Acil durum ekipmanı", "hygienicToiletAreas": "Hijyenik tuvalet alanları", "climateControl": "<PERSON><PERSON><PERSON> ve ısıtma <PERSON>i", "individualRooms": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "comfortableBeds": "<PERSON><PERSON><PERSON><PERSON> ve rahat yataklar", "indoorShelters": "<PERSON><PERSON><PERSON> alan <PERSON>", "outdoorShelters": "Açık alan <PERSON>", "playAreas": "<PERSON><PERSON> al<PERSON>", "indoorPlayAreas": "<PERSON><PERSON><PERSON> oyun alanları", "gardenArea": "<PERSON><PERSON><PERSON><PERSON>", "trainingField": "Eğitim sa<PERSON>ı", "playPool": "<PERSON><PERSON> ha<PERSON>", "socializationActivities": "Sosyalleşme etkinlikleri", "liveCameraAccess": "Canlı kamera izleme", "photoUpdates": "Fotoğraflı bilgilendirme", "specialDietMeals": "Özel diyet yeme<PERSON>ri", "petSpa": "Pet Spa ve bakım hizmetleri", "pickupDropoff": "Alma ve bırakma hizmeti"}}, "SuccessPage": {"success": "Otel Kaydınız Başarıyla Gerçekleşti", "petTaxiSuccess": "Pet Taksi Kaydınız Başarıyla Gerçekleşti", "returnHomepage": "<PERSON><PERSON><PERSON> veya ana sayfaya dönebilirsiniz", "homepage": "<PERSON><PERSON><PERSON>", "hotelLogin": "Otel Giriş", "petTaxiLogin": "<PERSON>"}, "Step4ImagesUpload": {"hotelImages": "Otel görselleri"}, "Nav": {"navToday": "<PERSON><PERSON><PERSON><PERSON>", "navHotelİnformations": "Otel Bilgileri", "navRooms": "<PERSON><PERSON><PERSON>", "navServices": "<PERSON><PERSON><PERSON><PERSON>", "navCalendar": "Takvim", "navDashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navReservations": "Rezervasyon", "navPromoCode": "<PERSON><PERSON><PERSON>", "navBilling": "Ödeme Bilgileri", "navUser": "Kullanıcı Bilgileri", "navHotelLanding": "Otel Websitesi", "navPolicy": "Politika ve Sözleşmeler"}, "UnloggedHeader": {"headerPetOwner": "Pet sahibi misin?", "headerHotelOwner": "Otel sahibi misin?", "headerCreateHotel": "Otel Oluştur", "headerLogin": "<PERSON><PERSON><PERSON>", "headerCreateUser": "<PERSON><PERSON><PERSON>"}, "PetOwnerAvatarDropdown": {"myAccount": "He<PERSON>b<PERSON>m", "myFavorites": "<PERSON>av<PERSON><PERSON><PERSON>", "myPets": "<PERSON><PERSON><PERSON><PERSON>", "darkTheme": "<PERSON><PERSON> tema", "logout": "Çıkış"}, "HotelAvatarDropdown": {"myAccount": "He<PERSON>b<PERSON>m", "darkTheme": "<PERSON><PERSON> tema", "logout": "Çıkış"}, "NavMobile": {"myAccount": "He<PERSON>b<PERSON>m", "logout": "Çıkış yap", "createAccount": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>"}, "NavMobileHotel": {"myAccount": "He<PERSON>b<PERSON>m", "logout": "Çıkış"}, "HotelInformationsNav": {"hotelInfo": "Otel Bilgileri", "hotelAddress": "Otel Adresi", "hotelFeatures": "Otel Özellikleri", "hotelAcceptedPetTypes": "Kabul Edilen <PERSON>", "hotelAdditionalServices": "Ek Hizmetleri", "hotelPhotos": "Otel Fotoğrafları", "hotelDocuments": "<PERSON><PERSON>"}, "HotelInformations": {"hotelName": "Otel Adı", "hotelStory": "Otel Hikayesi", "hotelCompanyTaxNumber": "<PERSON><PERSON><PERSON>", "hotelCompanyTaxOffice": "<PERSON><PERSON><PERSON>", "hotelLogo": "Otel Logo", "save": "<PERSON><PERSON>", "hotelUploadPhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hotelTypeImage": "PNG, JPG, GIF Türü Resim"}, "HotelAddress": {"cityName": "Şehir", "region": "İlçe", "district": "Mahalle", "streetName": "Sokak", "buildingName": "<PERSON><PERSON>", "buildingNumber": "Bina No", "postalZone": "Posta kodu", "save": "<PERSON><PERSON>"}, "HotelFeatures": {"hotelFeatures": "Otel Özellikleri", "petTaxi": "Pet taksi", "petCoiffeur": "<PERSON>", "petGrooming": "<PERSON>", "petNursery": "Kreş", "pool": "Havuz", "playground": "<PERSON><PERSON> alanı", "dogWalking": "Köpek gezdirme", "save": "<PERSON><PERSON>", "FeaturesCategories": {"healthAndSafety": "Sağlık ve Güvenlik", "comfortAndAccommodation": "Konfor ve <PERSON>ınma", "activitiesAndEntertainment": "Aktiviteler ve Eğlence", "extraServices": "Ekstra Hizmetler", "healthAndSafetyDescription": "Pet<PERSON>in güvenliğini ve sağlığını ön planda tutan hizmetler. <PERSON><PERSON><PERSON><PERSON>, veteriner des<PERSON>, d<PERSON><PERSON><PERSON> sağlık kontrolleri, yangın alarm sistemleri ve 24 saat gözetim.", "comfortAndAccommodationDescription": "Petlerin konforlu bir şekilde dinlenmesini sağlayan imkanlar. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ta<PERSON>lan<PERSON>ı<PERSON> yata<PERSON>, iklimlendirme sistemleri ve ferah barınma alanları.", "activitiesAndEntertainmentDescription": "Petlerin eğlenmesi ve fiziksel aktivitelerle vakit geçirmesi için sunulan hizmetler. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> al<PERSON>, günlük yürüyüşler ve eğitim programları.", "extraServicesDescription": "Petlerin konaklama deneyimini zenginleştiren ek hizmetler. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON>, <PERSON><PERSON> diyet yemekleri ve doğum günü partileri."}, "FeaturesLabels": {"veterinaryServices": "24/7 Veteriner hizmetleri", "cameraSurveillance": "<PERSON><PERSON><PERSON> siste<PERSON>", "securityCameras": "Güvenlik kameraları", "emergencyEquipment": "Acil durum ekipmanı", "hygienicToiletAreas": "Hijyenik tuvalet alanları", "climateControl": "<PERSON><PERSON><PERSON> ve ısıtma <PERSON>i", "individualRooms": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "comfortableBeds": "<PERSON><PERSON><PERSON><PERSON> ve rahat yataklar", "indoorShelters": "<PERSON><PERSON><PERSON> alan <PERSON>", "outdoorShelters": "Açık alan <PERSON>", "playAreas": "<PERSON><PERSON> al<PERSON>", "indoorPlayAreas": "<PERSON><PERSON><PERSON> oyun alanları", "gardenArea": "<PERSON><PERSON><PERSON><PERSON>", "trainingField": "Eğitim sa<PERSON>ı", "playPool": "<PERSON><PERSON> ha<PERSON>", "socializationActivities": "Sosyalleşme etkinlikleri", "liveCameraAccess": "Canlı kamera izleme", "photoUpdates": "Fotoğraflı bilgilendirme", "specialDietMeals": "Özel diyet yeme<PERSON>ri", "petSpa": "Pet Spa ve bakım hizmetleri", "pickupDropoff": "Alma ve bırakma hizmeti"}}, "HotelPetTypes": {"hotelAcceptedPetTypes": "Kabul Edilen <PERSON>", "cat": "<PERSON><PERSON>", "smallDogBreed": "Köpek (Küçük Irk)", "mediumDogBreed": "Köpek (Orta Irk)", "largeDogBreed": "Köpek (Büyük Irk)", "rabbit": "<PERSON><PERSON><PERSON><PERSON>", "hamster": "<PERSON><PERSON>", "guineaPig": "Ginepig", "ferret": "<PERSON><PERSON><PERSON><PERSON>", "hedgehog": "<PERSON><PERSON><PERSON>", "chinchilla": "Şinşila", "turtle": "Ka<PERSON>lumbağa", "snake": "<PERSON><PERSON><PERSON>", "iguana": "İguana", "parrot": "<PERSON><PERSON><PERSON>", "canary": "<PERSON><PERSON><PERSON>", "budgerigar": "<PERSON><PERSON><PERSON>", "cockatiel": "<PERSON>", "fish": "Balık (Akvaryum Türleri)", "finch": "İspinoz", "dove": "<PERSON><PERSON><PERSON><PERSON>", "lovebird": "<PERSON><PERSON><PERSON>", "freshwaterFish": "Tatlı Su Balığı", "saltwaterFish": "Tuzlu Su Balığı", "horse": "At", "pony": "<PERSON><PERSON><PERSON>", "donkey": "<PERSON><PERSON><PERSON>", "goat": "<PERSON><PERSON><PERSON>", "sheep": "<PERSON><PERSON>", "alpaca": "Alpaka", "llama": "<PERSON>", "exoticBird": "Egzotik Kuş", "monkey": "<PERSON><PERSON><PERSON>", "rooster": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "HotelAdditionalServices": {"hotelAdditionalServices": "<PERSON><PERSON>", "veterinary": "Veteriner", "specialNutrition": "<PERSON><PERSON> be<PERSON>", "grooming": "Bakım", "camera": "<PERSON><PERSON><PERSON>", "photoInfoServices": "Fotoğraflı bilgilendirme hizmeti", "specialCondition": "<PERSON><PERSON> du<PERSON> olan <PERSON>", "save": "<PERSON><PERSON>"}, "HotelPhotosContainer": {"hotelUploadPhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "HotelPhotoCard": {"hotelRemovePhoto": "Foto<PERSON><PERSON><PERSON>", "hotelDeletePhoto": "Seçili fotoğrafı silmek istiyor musunuz?", "hotelCancel": "Vazgeç", "hotelDelete": "Sil"}, "PostingContainer": {"PostingContainer": "Önceden oluşturduğunuz oda grubu yok. Lütfen oda grubu oluşturunuz."}, "HotelRoomCard": {"hotelRoomCount": "Oda Sayısı:", "roomCapacity": "<PERSON><PERSON>:", "petType": "<PERSON><PERSON><PERSON><PERSON>:"}, "UpdateAndCreateNewServiceModal": {"hotelAddNewRoomGroup": "<PERSON><PERSON>", "hotelAddMultipleRooms": "<PERSON><PERSON> fazla oda ekle", "hotelRoomName": "<PERSON><PERSON> ismi", "hotelRoomCapacity": "<PERSON><PERSON>", "hotelRoomGroupName": "<PERSON><PERSON> Grubu <PERSON>i", "hotelNumberOfRooms": "Oda Sayısı", "hotelRoomNameStartingNumber": "Oda Adı Başlangıç Numarası", "petType": "<PERSON> tipi", "roomFeatures": "Oda ö<PERSON>i", "roomFeaturesDesc": "Oda özelliklerinizi yazdıktan sonra enter'a veya ekle tuşuna basınız.", "add": "<PERSON><PERSON>", "roomInformation": "Oda bilgi alanı", "roomInformationDesc": "Oda hakkında kısaca bilgi veriniz", "hotelPhotos": "Otel Fotoğrafları", "hotelUploadPhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "Vazgeç", "roomUpdate": "<PERSON><PERSON>", "cat": "<PERSON><PERSON>", "dog": "Köpek", "roomType": "<PERSON><PERSON> tipi", "standard": "<PERSON><PERSON>", "private": "<PERSON><PERSON>", "shared": "Paylaşımlı"}, "UpdateAndCreateNewRoomModal": {"hotelAddNewRoomGroup": "<PERSON><PERSON>", "hotelAddMultipleRooms": "<PERSON><PERSON> fazla oda ekle", "hotelRoomName": "<PERSON><PERSON> ismi", "hotelRoomCapacity": "<PERSON><PERSON>", "hotelRoomGroupName": "<PERSON><PERSON> Grubu <PERSON>i", "hotelNumberOfRooms": "Oda Sayısı", "hotelRoomNameStartingNumber": "Oda Adı Başlangıç Numarası", "petType": "<PERSON> tipi", "roomFeatures": "Oda ö<PERSON>i", "roomFeaturesDesc": "Oda özelliklerinizi yazdıktan sonra enter'a veya ekle tuşuna basınız.", "add": "<PERSON><PERSON>", "roomInformation": "Oda bilgi alanı", "roomInformationDesc": "Oda hakkında kısaca bilgi veriniz", "roomPhotos": "Oda Fotoğrafları", "hotelUploadPhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "Vazgeç", "roomUpdate": "<PERSON><PERSON>", "cat": "<PERSON><PERSON>", "dog": "Köpek", "roomType": "<PERSON><PERSON> tipi", "standard": "<PERSON><PERSON>", "private": "<PERSON><PERSON>", "shared": "Paylaşımlı"}, "DeleteRoomModal": {"RemoveRoom": "<PERSON><PERSON>", "RemoveRoomConfirm": "odayı kaldırmak istiyor musunuz?", "cancel": "Vazgeç", "confirm": "<PERSON><PERSON><PERSON>"}, "HotelCalendarContainer": {"roomGroups": "Oda Grupları", "roomGroupName": "Grup Adı", "NumberOfRooms": "Oda Sayısı", "select": "Seç"}, "HotelCalendarRange": {"selectRoom": "<PERSON><PERSON>", "activeDays": "<PERSON><PERSON><PERSON>", "reservedDays": "Reserve günler", "passiveDays": "<PERSON><PERSON><PERSON>", "noPriceDays": "<PERSON><PERSON><PERSON>", "locale": "tr"}, "HotelCalendarSideBarRange": {"roomCapacity": "Günlük Oda Kapasitesi", "petType": "<PERSON>", "roomType": "<PERSON><PERSON>", "clearSelection": "<PERSON><PERSON><PERSON><PERSON> kaldı<PERSON>", "closetoReservations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opentoReservations": "Rezervasyona Aç", "locale": "tr-TR", "changePrice": "Fiyatı değiştirmek için tıklayın", "totalPrice": "<PERSON><PERSON><PERSON><PERSON>i toplam tutar", "clickForDetails": "Detay için tı<PERSON>n"}, "PriceSummary": {"priceBreakdown": "<PERSON>yat <PERSON>", "night": "gece", "nightlyRate": "Ortalama gecelik fiyatınız", "serviceFee": "<PERSON><PERSON><PERSON>", "totalPrice": "<PERSON><PERSON><PERSON><PERSON>i toplam tutar", "yourEarnings": "Kazancınız"}, "UpdateRoomPrice": {"priceUpdate": "<PERSON><PERSON><PERSON>", "newRoomRate": "Yeni oda fiyatını giriniz", "price": "Fiyat:", "serviceFee": "<PERSON><PERSON><PERSON>:", "totalPrice": "<PERSON><PERSON><PERSON><PERSON>ği toplam tutar:", "yourEarnings": "Kazancınız:", "cancel": "Vazgeç", "save": "<PERSON><PERSON>"}, "HotelCalendarDrawerRangeMobile": {"dateUpdate": "<PERSON><PERSON><PERSON>", "closetoReservations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opentoReservations": "Rezervasyona Aç", "locale": "tr-TR", "changePrice": "Fiyatı değiştirmek için tıklayın", "totalPrice": "<PERSON><PERSON><PERSON><PERSON>i toplam tutar", "clickForDetails": "Detay için tı<PERSON>n", "close": "Ka<PERSON><PERSON>", "back": "<PERSON><PERSON>"}, "ReservationList": {"roomName": "<PERSON><PERSON>", "petOwner": "<PERSON>", "petName": "<PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "Çıkış Tarihi", "channel": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "total": "Toplam", "status": "Durum", "action": "İşlem", "confirmed": "Onaylandı", "booked": "Rezerve", "checkedIn": "<PERSON><PERSON><PERSON>", "checkedOut": "Çıkış Yaptı", "declined": "Reddedildi", "waitingForCheckIn": "<PERSON><PERSON><PERSON>", "waitingForCheckOut": "Çıkış Bekleniyor", "waitingForApproval": "<PERSON><PERSON>", "waitingForBook": "<PERSON>ette", "cancelled": "İptal Edildi", "confirm": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "reservationNotFound": "Rezervasyon bulunamadı.", "showHideColumns": "<PERSON><PERSON>tun Gö<PERSON>/Gizle", "atTheDoor": "<PERSON><PERSON><PERSON><PERSON>", "telephone": "Telefon", "whatsapp": "Whatsapp", "pawbooking": "PawBooking"}, "BillingContainer": {"companyType": "Şirket Türü *", "soleProprietorship": "Şahıs <PERSON>", "stockCompany": "Limited & Anonim", "fullName": "Ad Soya<PERSON> *", "alias": "Ünvan *", "identityNumber": "<PERSON><PERSON> *", "taxNumber": "<PERSON><PERSON><PERSON> *", "birthDate": "<PERSON><PERSON><PERSON> *", "gsmNumber": "Telefon Numarası *", "address": "Adres *", "city": "Şehir *", "district": "İlçe *", "selectCity": "<PERSON><PERSON><PERSON>", "selectDistrict": "İlçe Seçin", "email": "E-Posta *", "authorizedPersonIdentityNumber": "Yetkili Kişi TC *", "authorizedPersonBirthDate": "Yet<PERSON>li <PERSON> Doğum Tarihi *", "taxOffice": "<PERSON><PERSON><PERSON> *", "save": "<PERSON><PERSON>", "ibanTitle": "IBA<PERSON> *"}, "ReservationActionButtons": {"reservationConfirmation": "<PERSON>zervas<PERSON>", "reservationConfirmationDesc": "<PERSON><PERSON><PERSON> rezer<PERSON>yonu onaylamak istiyor musunuz?", "confirm": "<PERSON><PERSON><PERSON>", "reservationCancellation": "Rezervasyon İptal", "reservationCancellationDesc": "Seçili rezervasyonu iptal etmek istiyor musunuz?", "cancel": "İptal Et"}, "ReservationDetail": {"details": "Detay", "reservationDetails": "Rezervasyon Detayı", "roomInformation": "<PERSON><PERSON>", "roomGroupName": "Oda Grup İsmi", "roomName": "<PERSON><PERSON>", "roomCapacity": "<PERSON><PERSON>", "petType": "<PERSON><PERSON>", "accommodationInformation": "Konaklama Bilgileri", "checkInDate": "<PERSON><PERSON><PERSON>", "checkOutDate": "Çıkış Tarihi", "stayDuration": "Konaklama Süresi", "petInformation": "<PERSON>", "petName": "<PERSON>", "petAge": "<PERSON>", "petKind": "<PERSON>", "petBreed": "<PERSON>", "petOwnerInformation": "<PERSON>", "petOwnerName": "<PERSON>", "phone": "Telefon", "email": "E-posta", "night": "gece", "vaccinationReport": "<PERSON>şı <PERSON>", "showPhoto": "Fotoğrafı Göster", "cat": "<PERSON><PERSON>", "smallDogBreed": "Köpek (Küçük Irk)", "mediumDogBreed": "Köpek (Orta Irk)", "largeDogBreed": "Köpek (Büyük Irk)", "horse": "At", "turtle": "Ka<PERSON>lumbağa", "rabbit": "<PERSON><PERSON><PERSON><PERSON>", "hamster": "<PERSON><PERSON>", "guineaPig": "Ginepig", "parrot": "<PERSON><PERSON><PERSON>", "hedgehog": "<PERSON><PERSON><PERSON>", "cockatiel": "<PERSON>"}, "HotelUserTable": {"noUsers": "Kullanıcı yok", "firstName": "Ad", "username": "Kullanıcı Adı", "email": "E-posta", "role": "Rol"}, "AddNewAndUpdateHotelUser": {"addUser": "Kullanıcı ekle", "addNewUser": "<PERSON><PERSON>", "updateUser": "Kullanıcı Güncelleme", "firstName": "Ad", "lastName": "Soyad", "gender": "Cinsiyet", "male": "<PERSON><PERSON><PERSON>", "female": "Kadın", "other": "<PERSON><PERSON><PERSON>", "username": "Kullanıcı Adı", "password": "Şifre", "email": "E-Posta", "dateOfBirth": "<PERSON><PERSON><PERSON>", "phone": "Cep Telefonu", "bio": "Hakkında", "validFirstName": "Lütfen geçerli ad giriniz", "validLastName": "Lütfen geçerli soyad giriniz", "validUsername": "Kullanıcı adınız en az 4 karakter uzunluğunda olmalı ve sadece küçük harfler (a-z) ve rakamlar (0-9) içermelidir. Özel karakterler veya boşluklar kullanılamaz.", "validPassword": "Şifreniz en az 8 karakter olmalı, bir b<PERSON><PERSON><PERSON><PERSON> harf, bir küçük harf ve bir rakam içermelidir. Özel karakterler (!@#$%^&*()_+.-) kullanılabilir, ancak boşluk olmamalıdır.", "validEmail": "Lütfen geçerli e-posta giriniz", "validDateOfBirth": "Lütfen geçerli doğum tarihi giriniz", "validPhone": "Lütfen geçerli telefon numarası giriniz", "cancel": "Vazgeç", "confirm": "<PERSON><PERSON><PERSON>"}, "DeleteHotelUser": {"removeUserTitle": "Kullanıcı Kaldırma", "removeUserConfirmation": "'i kaldırmak istiyor musunuz?", "cancel": "Vazgeç", "confirm": "<PERSON><PERSON><PERSON>"}, "Policy": {"cancellationPolicy": "İptal Politikası", "checkInStartTime": "Check-in Başlangıç Saati", "checkInEndTime": "Check-in Bitiş Saati", "checkOutTime": "Check-out <PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "cancellationOption": "İptal Seçeneği", "rules": "<PERSON><PERSON><PERSON>", "untilDateRange": "<PERSON><PERSON> bir tarihe kadar", "noCancellation": "İptal yok", "last24Hours": "Son 24 saat kala", "last48Hours": "Son 48 saat kala", "lastWeek": "Son 1 hafta kala", "last10Days": "Son 10 gün kala", "lastMonth": "Son 1 ay kala", "newRulesAdd": "<PERSON><PERSON>", "save": "<PERSON><PERSON>"}, "UpdatePolicy": {"updatePolicy": "Politikayı Güncelle", "updatePolicyTitle": "Politika Güncelleme", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "rules": "<PERSON><PERSON><PERSON>", "addNewRule": "<PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>"}, "CheckInOutTimePicker": {"checkInStartTimeLabel": "Check-in Başlangıç Saati", "checkInEndTimeLabel": "Check-in Bitiş Saati", "checkOutLabel": "Check-out <PERSON><PERSON>", "checkInError": "Check-in bit<PERSON><PERSON> saati, check-in ba<PERSON><PERSON>ıç saatinden önce olamaz!", "checkOutError": "Check-in saati, check-out saatinden önce olamaz!"}, "AddNewRule": {"newRuleLabel": "<PERSON><PERSON>", "ruleTitlePlaceholder": "Kural başlığı", "ruleDescriptionPlaceholder": "Kural açıklama", "updateButton": "<PERSON><PERSON><PERSON><PERSON>", "addButton": "<PERSON><PERSON>", "cancelButton": "Vazgeç"}, "CancellationOption": {"cancellationOption": "İptal Seçeneği", "select": "<PERSON><PERSON><PERSON>", "noCancellation": "İptal yok", "cancelUntilDateRange": "<PERSON><PERSON> bir tarihe kadar", "other1": "Açığa alma", "other2": "Sigortalı rezervasyon", "other3": "<PERSON><PERSON><PERSON><PERSON><PERSON> zaman iptal", "other4": "<PERSON><PERSON><PERSON><PERSON>", "dateRange": "<PERSON><PERSON><PERSON>", "last24Hours": "Son 24 saat kala", "last48Hours": "Son 48 saat kala", "lastWeek": "Son 1 hafta kala", "last10Days": "Son 10 gün kala", "lastMonth": "Son 1 ay kala"}, "UserActionButtons": {"editUser": "Kullanıcıyı düzenle", "deleteUser": "Kullanıcıyı sil"}, "AcceptDeclineModal": {"cancel": "Vazgeç"}, "PaginationDropdown": {"pageSelect": "<PERSON><PERSON>ç"}, "ReservationPagination": {"previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>"}, "DeleteFileModal": {"title": "<PERSON><PERSON><PERSON>", "confirmMessage": "Seçili dosyayı kaldırmak istiyor musunuz?", "confirmButton": "<PERSON><PERSON><PERSON>", "cancelButton": "Vazgeç"}, "HotelFiles": {"uploadButtonText": "<PERSON><PERSON><PERSON>", "saveButtonText": "<PERSON><PERSON>"}, "FileStep": {"hotelFiles": "Otel Dosyaları", "license": "<PERSON><PERSON><PERSON>", "veterinaryContract": "<PERSON><PERSON><PERSON>özleş<PERSON>i", "taxCertificate": "<PERSON><PERSON><PERSON>", "identificationDocumentFront": "Kimlik Belgesi <PERSON>", "identificationDocumentBack": "<PERSON>lik Belgesi Ark<PERSON>"}, "HotelAccountInformation": {"accountEditInfo": "<PERSON>sap bil<PERSON><PERSON><PERSON> d<PERSON>", "userInfo": "Kullanıcı Bilgileri", "username": "Kullanıcı Adı", "email": "Email", "phoneNumber": "Telefon Numarası", "fullName": "Tam Ad", "role": "Rol", "hotelInfo": "Otel Bilgileri", "hotelName": "Otel Adı", "hotelPhoneNumber": "Otel Telefon Numarası", "taxOffice": "<PERSON><PERSON><PERSON>", "taxNumber": "<PERSON><PERSON><PERSON>", "currency": "Para Birimi", "status": "Durum", "accountDeleteButton": "Oteli ve Hesabı Sil", "deleteTitle": "Otel ve <PERSON>", "deleteDescription": "Bu işlem geri alınamaz. Bu, hesabınızı kalıcı olarak silecek veverilerinizi sunucularımızdan kaldıracaktır.", "deleteLabel": "Otelinizi silmek için kullanıcı adınızı giriniz", "deleteCancel": "Vazgeç", "deleteButton": "Oteli ve Hesabı Sil"}, "LandingPricing": {"subscription": "Fiya<PERSON><PERSON>rma", "subscriptionSubtitle": "Size en uygun planı seçin", "free": "Ücretsiz", "monthly": "Aylık", "yearly": "Yıllık", "pawbookingOnly": "Sadece Pawbooking Rezervasyonları", "additionalServices": "Ek Hizmet Satışları", "additionalServicesSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bakım, veterinerlik ve özel beslenme gibi ek hizmetler sunabilirsiniz.", "commission": "Pawbooking Rezervasyon Komisyonu", "customerSupport": "PawBooking Müşteri Hizmetleri", "customerSupportDescription": "PawBooking destek ekibi tarafından sağlanır", "advancedContractManagement": "G<PERSON>ş<PERSON>ş Sözleşme Yönetimi", "customizablePolicies": "Özelleştirilebilir İptal ve İade Politikaları", "startNow": "<PERSON><PERSON>", "plus": "Plus", "multiChannelBookings": "PawBooking ve <PERSON><PERSON><PERSON>", "multiChannelBookingsDescription": "Telefon, <PERSON>, Referanslar <PERSON>ğ<PERSON>", "sellAdditionalServices": "Ek Hizmet Satışları", "additionalServicesExamples": "Pet Transfer, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>", "commissionAdvantage": "Rezervasyon Komisyon Avantajı", "commissionAdvantageDescription": "PawBooking müşterileri için %12, kendi müşterilerinizden komisyon alınmaz", "staffAccounts": "Personel Hesapları Oluşturma", "staffAccountsDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ve <PERSON> i<PERSON>", "discountCoupons": "İndirim <PERSON>ları ve Sadakat Programları", "customWebPages": "Özel Web Sayfası ve İçerik Sayfaları", "multiChannelSupport": "Çok Kanallı Müşteri Desteği", "privateCloudStorage": "<PERSON><PERSON>", "privateCloudStorageDescription": "Otel müşterilerinize özel güvenli depolama alanı", "dedicatedSupport": "<PERSON><PERSON><PERSON> Müşteri Desteği", "dedicatedSupportDescription": "İşletmenize özel atanmış müşteri destek ekibi", "features": "<PERSON><PERSON><PERSON><PERSON>", "standard": "<PERSON><PERSON>"}, "LandingGrid": {"reservationManagementName": "Rezervasyon Yönetimi", "reservationManagementDescription": "Rezervasyonlarını takip et ve yönet.", "createListingsName": "<PERSON><PERSON>", "createListingsDescription": "Odalarını zengin içeriklerle listele.", "guestRelationsName": "<PERSON><PERSON><PERSON><PERSON>", "guestRelationsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON> kur.", "calendarName": "Takvim", "calendarDescription": "Dosyalarını tarihe göre filtrelemek için takvimi kullan."}, "HotelMap": {}, "ServicesSoldList": {"petOwner": "<PERSON>", "petName": "<PERSON>", "date": "<PERSON><PERSON><PERSON>", "total": "Toplam", "status": "Durum", "action": "İşlem", "confirmed": "Onaylandı", "declined": "Reddedildi", "waitingForApproval": "<PERSON><PERSON>", "waitingForBook": "<PERSON>ette", "booked": "Rezerve", "confirm": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "reservationNotFound": "Rezervasyon bulunamadı.", "showHideColumns": "<PERSON><PERSON>tun Gö<PERSON>/Gizle", "veterinaryServices": "Veteriner Hizmeti", "transportationServices": "Ulaşım Hizmeti", "groomingServices": "<PERSON><PERSON><PERSON><PERSON>"}, "AddPet": {"selectPet": "<PERSON><PERSON><PERSON><PERSON>", "informations": "<PERSON><PERSON><PERSON><PERSON>", "healthInformations": "Sağlık Bilgileri", "habits": "Alışkanlıklar", "vet": "Veteriner", "vaccinationReport": "<PERSON>şı <PERSON>", "cat": "<PERSON><PERSON>", "dog": "Köpek", "smallDogBreed": "Köpek (Küçük Irk)", "mediumDogBreed": "Köpek (Orta Irk)", "largeDogBreed": "Köpek (Büyük Irk)", "horse": "At", "turtle": "Ka<PERSON>lumbağa", "rabbit": "<PERSON><PERSON><PERSON><PERSON>", "hamster": "<PERSON><PERSON>", "guineaPig": "Ginepig", "parrot": "<PERSON><PERSON><PERSON>", "hedgehog": "<PERSON><PERSON><PERSON>", "cockatiel": "<PERSON>", "alpaca": "Alpaka", "finch": "İspinoz", "canary": "<PERSON><PERSON><PERSON>"}, "PetInformation": {"cat": "<PERSON><PERSON>", "smallDogBreed": "Köpek (Küçük Irk)", "mediumDogBreed": "Köpek (Orta Irk)", "largeDogBreed": "Köpek (Büyük Irk)", "horse": "At", "turtle": "Ka<PERSON>lumbağa", "rabbit": "<PERSON><PERSON><PERSON><PERSON>", "hamster": "<PERSON><PERSON>", "guineaPig": "Ginepig", "parrot": "<PERSON><PERSON><PERSON>", "hedgehog": "<PERSON><PERSON><PERSON>", "cockatiel": "<PERSON>"}}