import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";

interface HandleSuccessItemParams {
  virtualPosTransactionId: string | undefined;
  hotelParamGuid: string | undefined;
  itemId: string | undefined;
  itemType: string | undefined;
  hotelToken: string | undefined;
  status: string;
}

export const useReservationTable = (
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const { toast } = useToast();

  const addItemDetail = async (
    itemData: any,
    hotelToken: string | undefined
  ) => {
    const { itemId, virtualPosTransactionId, hotelParamGuid, itemType } =
      itemData;

    if (hotelToken) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/hotel/account/payment/addOrderDetail`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              hotelToken: hotelToken,
            },
            body: JSON.stringify({
              itemId,
              itemType,
              virtualPosTransactionId,
              hotelParamGuid,
            }),
          }
        );

        const data = await response.json();

        if (!response.ok || !data.success) {
          toast({
            variant: "error",
            title: "Hata",
            description: data.error || "Beklenmedik bir hata oluştu.",
          });
          setLoading(false);
          return;
        }

        return data;
      } catch (error: any) {
        console.error("Error:", error);
        toast({
          variant: "error",
          title: "Hata",
          description: error.message,
        });
      }
    }
  };

  const confirmItem = async (itemData: any, hotelToken: string | undefined) => {
    const { pysOrderGuid, status, itemId, itemType } = itemData;

    if (hotelToken) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/hotel/account/payment/confirmItem`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              hotelToken: hotelToken,
            },
            body: JSON.stringify({
              pysOrderGuid,
              status,
              itemId,
              itemType,
            }),
          }
        );

        const data = await response.json();

        if (!response.ok || !data.success) {
          toast({
            variant: "error",
            title: "Hata",
            description: data.error || "Beklenmedik bir hata oluştu.",
          });
          setLoading(false);
          return;
        }

        toast({
          variant: "success",
          title: "Başarılı",
          description: "Rezervasyon başarılı bir şekilde onaylandı.",
        });

        const revalidatePath =
          itemType === "reservations"
            ? "/account/reservations"
            : itemType === "services"
              ? "/account/services"
              : "/account";

        revalidatePathHandler(revalidatePath);
        setLoading(false);
        return data;
      } catch (error: any) {
        console.error("Error:", error);
        toast({
          variant: "error",
          title: "Hata",
          description: error.message,
        });
      }
    }
  };

  const handleSuccessItem = async ({
    virtualPosTransactionId,
    hotelParamGuid,
    itemId,
    itemType,
    hotelToken,
    status,
  }: HandleSuccessItemParams) => {
    try {
      setLoading(true);
      const data = await addItemDetail(
        {
          itemId,
          itemType,
          virtualPosTransactionId,
          hotelParamGuid,
        },
        hotelToken
      );

      if (
        !(+data.data.result.Pazaryeri_TP_Siparis_Detay_EkleResult.Sonuc > 0)
      ) {
        toast({
          variant: "error",
          title: "Hata",
          description:
            data.data.result.Pazaryeri_TP_Siparis_Detay_EkleResult.Sonuc_Str,
        });
        return;
      }

      console.log("la bura calıstı");

      await confirmItem(
        {
          status: status,
          pysOrderGuid:
            data.data.result.Pazaryeri_TP_Siparis_Detay_EkleResult
              .PYSiparis_GUID,
          itemId,
          itemType,
        },
        hotelToken
      ).then((res) => {
        if (+res.data.result.Pazaryeri_TP_Siparis_OnayResult.Sonuc < 0) {
          toast({
            variant: "error",
            title: "Bildirim",
            description:
              res.data.result.Pazaryeri_TP_Siparis_OnayResult.Sonuc_Str,
          });
          return;
        }

        toast({
          variant: "success",
          title: "Rezervasyon durum güncelleme",
          duration: 6000,
          description:
            res.data.result.Pazaryeri_TP_Siparis_OnayResult.Durum_Str,
        });
      });
    } catch (e) {
      console.log(e);
    }
  };

  return { handleSuccessItem, confirmItem };
};
