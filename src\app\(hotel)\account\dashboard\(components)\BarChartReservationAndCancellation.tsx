"use client";
import type { FC } from "react";
import { TrendingUp } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  LabelList,
} from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartConfig = {
  rezervasyon: {
    label: "Rezervasyon",
    color: "hsl(var(--chart-1))",
  },
  iptal: {
    label: "İptal",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig;

interface BarChartReservationAndCancellationProps {
  data: any;
  startDate: string | string[];
  endDate: string | string[];
}
const BarChartReservationAndCancellation: FC<
  BarChartReservationAndCancellationProps
> = ({ data, startDate, endDate }) => {
  const chartData = [
    {
      //   month: "January",
      rezervasyon: data.totalReservations,
      iptal: data.cancelledReservations,
    },
  ];
  const formatIsoDate = (isoDate: string | string[]): string => {
    const dateStr = Array.isArray(isoDate) ? isoDate[0] : isoDate;

    const [year, month, day] = dateStr.split("-");
    return `${day}.${month}.${year}`;
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Rezervasyon-İptal Oranları</CardTitle>
        <CardDescription>
          {formatIsoDate(startDate) + " - " + formatIsoDate(endDate)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer className="h-[225px] lg:h-[250px]" config={chartConfig}>
          <BarChart layout="vertical" width={600} height={150} data={chartData}>
            <CartesianGrid horizontal={false} />
            <YAxis
              dataKey="months"
              type="category"
              tickLine={false}
              axisLine={false}
            />
            <XAxis type="number" tickLine={false} axisLine={false} />

            <ChartTooltip content={<ChartTooltipContent hideLabel />} />
            <ChartLegend content={<ChartLegendContent />} />

            <Bar
              dataKey="rezervasyon"
              stackId="a"
              fill="var(--color-rezervasyon)"
              radius={[0, 0, 0, 0]}
              barSize={30}
            >
              <LabelList
                dataKey="rezervasyon"
                position="insideLeft"
                offset={8}
                className="fill-[#ffffff]"
                fontSize={15}
              />
            </Bar>

            <Bar
              dataKey="iptal"
              stackId="a"
              fill="var(--color-iptal)"
              radius={[0, 4, 4, 0]}
              barSize={30}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm"></CardFooter>
    </Card>
  );
};
export default BarChartReservationAndCancellation;
