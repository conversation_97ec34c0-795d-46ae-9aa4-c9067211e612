"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  X,
  Star,
  Zap,
  Crown,
  Building2,
  Home,
  Car,
  GraduationCap,
} from "lucide-react";
import Link from "next/link";

export function PricingSection() {
  const [isYearly, setIsYearly] = useState(false);

  const serviceTypes = [
    { name: "Pet Otel", icon: Home, color: "text-blue-600" },
    { name: "<PERSON>", icon: Car, color: "text-green-600" },
    { name: "<PERSON> Eğiti<PERSON>", icon: GraduationCap, color: "text-amber-600" },
  ];

  const plans = [
    {
      name: "Ücretsiz",
      description:
        "PawBooking panelini tanımak ve otel profili oluşturmak isteyenler için",
      monthlyPrice: "0",
      yearlyPrice: "0",
      period: isYearly ? "yıllık" : "aylık",
      icon: Star,
      color: "border-[#FFB533]",
      buttonColor: "bg-[#FFDAB9] hover:bg-[#FFDAB9]",
      popular: false,
      features: [
        {
          name: "1 işletme tipi seçimi (otel / taksi / grooming)",
          included: true,
        },
        { name: "Sınırlı sayıda oda ve ek hizmet oluşturma", included: true },
        { name: "Online ödeme sistemi", included: true },
        { name: "E-posta desteği", included: true },
        { name: "Kampanya ve indirim kuponu", included: false },
        { name: "Kapıda rezervasyon alma", included: false },
        { name: "Otel müşterileri yönetimi", included: false },
        { name: "Personel yönetimi", included: false },
        { name: "Kasa yönetimi", included: false },
        { name: "Gelişmiş istatistik ve analiz", included: false },
      ],
      commission:
        "%18 (yalnızca PawBooking üzerinden gelen rezervasyonlar için)",
      setup: "Ücretsiz Kurulum",
      serviceLimit: "1 işletme",
      reservationNote: "PawBooking üzerinden ayda ortalama 18 rezervasyon",
    },
    {
      name: "Tek İşletme Pro",
      description: "Tek bir işletmesini profesyonelce yönetmek isteyenler için",
      monthlyPrice: "1600",
      yearlyPrice: "1250",
      period: isYearly ? "yıllık" : "aylık",
      icon: Zap,
      color: "border-[#FFB533]",
      buttonColor: "bg-[#FFB533] hover:bg-[#FFB533]",
      popular: false,
      features: [
        {
          name: "1 işletme tipi seçimi (otel / taksi / grooming)",
          included: true,
        },
        { name: "Kampanya ve indirim kuponu oluşturma", included: true },
        { name: "Kapıda rezervasyon alma", included: true },
        { name: "Otel müşterileri yönetimi", included: true },
        { name: "Personel yönetimi", included: true },
        { name: "Kasa yönetimi", included: true },
        { name: "Mobil uygulama erişimi", included: true },
        { name: "Temel istatistik ve analiz", included: true },
      ],
      commission:
        "%15 (yalnızca PawBooking üzerinden gelen rezervasyonlar için)",
      setup: "Ücretsiz Kurulum",
      serviceLimit: "1 işletme",
      reservationNote: "PawBooking üzerinden ayda ortalama 15 rezervasyon",
    },
    {
      name: "3 İşletmeli Pro",
      description:
        "Pet otel, taksi ve kuaför gibi birden fazla hizmet sunan işletmeler için",
      monthlyPrice: "3249",
      yearlyPrice: "2800",
      period: isYearly ? "yıllık" : "aylık",
      icon: Crown,
      color: "border-[#FF4500]",
      buttonColor: "bg-[#FF4500] hover:bg-[#FF4500]",
      popular: true,
      features: [
        {
          name: "3 farklı işletme tipi yönetimi (otel + taksi + grooming)",
          included: true,
        },
        { name: "Kampanya ve indirim kuponu", included: true },
        { name: "Kapıda rezervasyon alma", included: true },
        { name: "Otel müşterileri yönetimi", included: true },
        { name: "Personel ve kasa yönetimi", included: true },
        { name: "Mobil uygulama erişimi", included: true },
        { name: "Gelişmiş istatistik ve analiz", included: true },
        { name: "API erişimi ve özel entegrasyon", included: true },
        { name: "3 lokasyona kadar destek", included: true },
      ],
      commission:
        "%12 (yalnızca PawBooking üzerinden gelen rezervasyonlar için)",
      setup: "Ücretsiz Kurulum",
      serviceLimit: "3 işletme",
      reservationNote: "PawBooking üzerinden ayda ortalama 12 rezervasyon",
    },
    {
      name: "Enterprise",
      description:
        "Zincir oteller için özel mobil uygulama, API erişimi ve tüm özellikleri kapsayan üst düzey çözüm",
      monthlyPrice: "Fiyat için arayın",
      yearlyPrice: "Fiyat için arayın",
      period: isYearly ? "yıllık" : "aylık",
      icon: Building2,
      color: "border-red-800",
      buttonColor: "bg-red-800 hover:bg-red-900",
      popular: false,
      features: [
        { name: "Sınırsız işletme ve hizmet tipi oluşturma", included: true },
        { name: "Özel mobil uygulama (white-label)", included: true },
        { name: "Özel API erişimi ve entegrasyonlar", included: true },
        { name: "Franchise ve çoklu lokasyon yönetimi", included: true },
        { name: "Gelişmiş BI dashboard ve raporlama", included: true },
        { name: "Kurumsal takvim ve ödeme altyapısı", included: true },
        { name: "7/24 özel destek temsilcisi", included: true },
      ],
      commission:
        "Yalnızca PawBooking üzerinden gelen rezervasyonlar için komisyon alınır",
      setup: "Özel kurulum",
      serviceLimit: "Sınırsız",
    },
  ];

  return (
    <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            İşletmenize Uygun Paketi Seçin
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Pet Otel, Pet Taksi ve Pet Eğitim hizmetlerinizi tek platformda
            yönetin
          </p>

          {/* Service Types */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {serviceTypes.map((service, index) => (
              <div
                key={index}
                className="flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-sm"
              >
                <service.icon className={`w-5 h-5 ${service.color}`} />
                <span className="text-sm font-medium text-gray-700">
                  {service.name}
                </span>
              </div>
            ))}
          </div>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <span
              className={`text-sm font-medium ${!isYearly ? "text-gray-900" : "text-gray-500"}`}
            >
              Aylık
            </span>
            <button
              onClick={() => setIsYearly(!isYearly)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 ${
                isYearly ? "bg-orange-500" : "bg-gray-200"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isYearly ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
            <span
              className={`text-sm font-medium ${isYearly ? "text-gray-900" : "text-gray-500"}`}
            >
              Yıllık
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative ${plan.color} ${
                plan.popular
                  ? "ring-2 ring-orange-500 shadow-xl scale-105"
                  : "shadow-sm"
              } transition-all duration-300 hover:shadow-lg`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-[#C22436] text-white px-4 py-1">
                    En Popüler
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-6">
                <div className="flex justify-center mb-4">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      plan.popular
                        ? "bg-orange-100"
                        : plan.name === "Enterprise"
                          ? "bg-red-100"
                          : plan.name === "Tek İşletme"
                            ? "bg-blue-100"
                            : "bg-gray-100"
                    }`}
                  >
                    <plan.icon
                      className={`w-6 h-6 ${
                        plan.popular
                          ? "text-orange-600"
                          : plan.name === "Enterprise"
                            ? "text-red-800"
                            : plan.name === "Tek İşletme"
                              ? "text-blue-600"
                              : "text-gray-600"
                      }`}
                    />
                  </div>
                </div>
                <CardTitle className="text-lg font-bold">{plan.name}</CardTitle>
                <CardDescription className="text-gray-600 text-sm">
                  {plan.description}
                </CardDescription>

                {/* Service Limit Badge */}
                <div className="mt-2">
                  <Badge variant="secondary" className="text-xs">
                    {plan.serviceLimit}
                  </Badge>
                </div>

                <div className="mt-4">
                  {plan.name === "Ücretsiz" ? (
                    <span className="text-2xl font-bold text-gray-900">
                      Ücretsiz
                    </span>
                  ) : (
                    <>
                      {plan.name !== "Enterprise" && (
                        <>
                          <span className="text-2xl lg:text-3xl font-bold text-gray-900">
                            ₺{isYearly ? plan.yearlyPrice : plan.monthlyPrice}
                          </span>
                          <span className="text-gray-600 text-sm">
                            /{plan.period}
                          </span>
                        </>
                      )}
                      {isYearly &&
                        plan.name !== "Ücretsiz" &&
                        plan.name !== "Enterprise" && (
                          <div className="text-xs text-gray-500 line-through mt-1">
                            ₺{plan.monthlyPrice}/aylık
                          </div>
                        )}
                    </>
                  )}
                </div>
                <div className="flex flex-col justify-center space-x-2 text-xs text-gray-500 mt-2">
                  <span>Komisyon: {plan.commission}</span>
                  <span>•</span>
                  <span>{plan.setup}</span>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <Link
                  className="w-full max-md:hidden"
                  href="/auth/create-partner"
                  target="_blank"
                >
                  <Button
                    className={`w-full ${plan.buttonColor} text-white`}
                    size="lg"
                  >
                    {plan.name === "Ücretsiz"
                      ? "Hemen Başla"
                      : plan.name === "Enterprise"
                        ? "İletişime Geç"
                        : plan.popular
                          ? "Hemen Başla"
                          : "Hemen Başla"}
                  </Button>
                </Link>

                <div className="space-y-2">
                  {plan.features.map((feature, featureIndex) => (
                    <div
                      key={featureIndex}
                      className="flex items-start space-x-2"
                    >
                      {feature.included ? (
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                      ) : (
                        <X className="w-4 h-4 text-gray-300 flex-shrink-0 mt-0.5" />
                      )}
                      <span
                        className={`text-xs ${feature.included ? "text-gray-900" : "text-gray-400"} leading-relaxed`}
                      >
                        {feature.name}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            Hangi paketin işletmenize uygun olduğundan emin değil misiniz?
          </p>
          <Link
            className="w-full max-md:hidden"
            href="https://calendly.com/pawbooking-info/paw-tutorial"
            target="_blank"
          >
            <Button variant="outline" size="lg" className="bg-white">
              Ücretsiz İşletme Danışmanlığı
            </Button>
          </Link>
        </div>

        {/* Service Examples */}
        <div className="mt-12 bg-white rounded-2xl p-6 sm:p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
            İşletme Türü Örnekleri
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <Home className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">
                Pet Otel İşletmesi
              </h4>
              <p className="text-sm text-gray-600">
                Günlük/haftalık konaklama, oyun alanları, özel bakım hizmetleri
              </p>
            </div>
            <div className="text-center">
              <Car className="w-8 h-8 text-green-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">
                Pet Taksi İşletmesi
              </h4>
              <p className="text-sm text-gray-600">
                Veteriner transferi, havaalanı servisi, günlük gezinti
                hizmetleri
              </p>
            </div>
            <div className="text-center">
              <GraduationCap className="w-8 h-8 text-amber-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">Pet Eğitmeni</h4>
              <p className="text-sm text-gray-600">
                Temel itaat, sosyalleşme, özel davranış eğitimi hizmetleri
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
