import React from "react";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FooterReservationDetailprops {
  orderData: any;
}
const FooterReservationDetail: FC<FooterReservationDetailprops> = ({
  orderData,
}) => {
  return (
    <div className="flex max-md:flex-col max-md:gap-5 md:justify-between">
      <div className="md:basis-5/12 space-y-4">
        <div className="font-semibold capitalize">
          {orderData?.hotel?.hotelName}
        </div>
        <div className="md:flex justify-between max-md:grid grid-cols-2 max-md:gap-5">
          {orderData?.reservations?.length > 0 && (
            <div>
              <div>
                <p className="font-semibold text-sm">Konaklama</p>
                <div className="w-14 border-b border-white mt-1" />
              </div>
              {orderData?.reservations?.map((reservation: any) => {
                return (
                  <div key={reservation._id}>
                    <div className="mt-1 font-medium capitalize">
                      {reservation?.room?.roomName}
                    </div>
                    <div className="text-sm">{reservation?.hotelPet?.name}</div>
                    <div className="mt-2">
                      <div className="text-sm font-medium">
                        Konaklama süresi
                      </div>
                      <div className="text-sm">{reservation?.nights} gece</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
          {orderData?.services?.length > 0 && (
            <div>
              <div>
                <p className="font-semibold text-sm">Hizmet</p>
                <div className="w-14 border-b border-white mt-1" />
              </div>
              {orderData?.services?.map((service: any) => {
                return (
                  <div key={service?._id}>
                    <div className="flex items-center justify-between mt-1">
                      <p className="capitalize font-medium">
                        {service?.serviceName}
                      </p>
                    </div>
                    <p className="text-sm">{service?.hotelPet?.name}</p>
                  </div>
                );
              })}
            </div>
          )}
          {orderData?.subscriptions?.length > 0 && (
            <div>
              <div>
                <p className="font-semibold text-sm">Üyelik Kartı</p>
                <div className="w-14 border-b border-white mt-1" />
              </div>
              {orderData?.subscriptions?.map((subscription: any) => {
                return (
                  <div
                    key={subscription?._id}
                    className="flex items-center justify-between mt-1"
                  >
                    <p className="capitalize font-medium">
                      {subscription?.subscriptionName}
                    </p>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
      <div className="basis-2/12 flex justify-center">
        <Separator
          orientation="vertical"
          className="mt-2 h-40 max-md:hidden bg-white"
        />
      </div>
      <div className="md:basis-2/12 md:hidden w-full">
        <Separator />
      </div>
      <div className="md:basis-5/12">
        <p className="font-medium mb-2">Ücret Detayı</p>
        <Accordion type="single" collapsible>
          {orderData?.reservations?.length > 0 && (
            <AccordionItem className="border-none" value="item-1">
              <AccordionTrigger className="text-sm pb-2 pt-0">
                Konaklama
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2">
                  {orderData?.reservations?.map((reservation: any) => {
                    return (
                      <div key={reservation._id}>
                        <span className="mt-1 block font-medium capitalize">
                          {reservation?.room?.roomName}
                        </span>
                        <div className="flex items-center justify-between mt-1">
                          <span>
                            {new Intl.NumberFormat("tr-TR", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(reservation?.avgPrice)) + "₺"}{" "}
                            x {reservation?.nights} gece
                          </span>
                          <span>
                            {new Intl.NumberFormat("tr-TR", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(reservation?.total)) + "₺"}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </AccordionContent>
            </AccordionItem>
          )}
          {orderData?.services?.length > 0 && (
            <AccordionItem className="border-none" value="item-2">
              <AccordionTrigger className="text-sm pb-2 pt-0">
                Hizmetler
              </AccordionTrigger>
              <AccordionContent>
                {orderData?.services?.map((service: any) => {
                  return (
                    <div
                      key={service?._id}
                      className="flex items-center justify-between mt-1"
                    >
                      <p className="capitalize">{service?.serviceName}</p>

                      <div>
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(service?.total)) + "₺"}
                      </div>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          )}
          {orderData?.subscriptions?.length > 0 && (
            <AccordionItem className="border-none" value="item-3">
              <AccordionTrigger className="text-sm pb-2 pt-0">
                Üyelik Kartı
              </AccordionTrigger>
              <AccordionContent>
                {orderData?.subscriptions?.map((subscription: any) => {
                  return (
                    <div
                      key={subscription?._id}
                      className="flex items-center justify-between mt-1"
                    >
                      <p className="capitalize">
                        {subscription?.subscriptionName}
                      </p>

                      <div>
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(subscription?.total)) + "₺"}
                      </div>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          )}
          {orderData?.discounts?.length > 0 && (
            <AccordionItem className="border-none" value="item-3">
              <AccordionTrigger className="text-sm pb-2 pt-0">
                İndirim
              </AccordionTrigger>
              <AccordionContent>
                {orderData?.discounts?.map((discount: any) => {
                  const discountName =
                    discount?.discountReason === "firstReservationDiscount"
                      ? "İlk rezervasyon"
                      : discount?.discountReason === "promoCode"
                        ? `İndirim kuponu - ${discount?.appliedPromoCode}`
                        : "";
                  return (
                    <div key={discount?._id}>
                      <div className="flex items-center justify-between mt-1">
                        <div className="basis-1/3">{discountName}</div>
                        <p className="capitalize font-medium basis-1/3 text-center">
                          % {discount?.discountRate}
                        </p>

                        <div className="font-medium basis-1/3 text-end">
                          -{" "}
                          {new Intl.NumberFormat("tr-TR", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(Number(discount?.discountAmount)) + "₺"}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </AccordionContent>
            </AccordionItem>
          )}
        </Accordion>
      </div>
    </div>
  );
};

export default FooterReservationDetail;
