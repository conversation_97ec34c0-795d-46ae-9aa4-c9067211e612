import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";

export const useDiscount = () => {
  const { toast } = useToast();

  const addDiscount = async (
    hotelToken: string | undefined,
    requestBody: {
      code: string;
      orderId: string;
      hotel: string;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDiscountCode: React.Dispatch<React.SetStateAction<string>>
  ) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.checkoutDiscoutCode, {
        method: "POST",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });

        setLoading(false);
        setDiscountCode("");
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 3000,
        // title: "Hizmet Ekleme",
        description: "İndirim başarıyla uygulandı.",
      });
      setDiscountCode("");
      setTimeout(() => {
        setLoading(false);
      }, 2000);
      revalidatePathHandler("/hotel/checkout");
    } catch (error) {
      console.log(error);
    }
  };

  return { addDiscount };
};
