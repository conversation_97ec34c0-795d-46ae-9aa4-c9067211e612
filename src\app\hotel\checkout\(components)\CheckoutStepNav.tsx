import React from "react";
import type { FC } from "react";
import IconCheckCircle from "@/shared/icons/CheckCircle";
import IconInfo from "@/shared/icons/Info";
import IconCreditCard from "@/shared/icons/CreditCard";

interface CheckoutStepNavProps {
  step: number;
}

const CheckoutStepNav: FC<CheckoutStepNavProps> = ({ step }) => {
  const lineHandler = (firstCondition: boolean, secondCondition: boolean) => {
    const checkedCircle = "bg-[#bff2d4]";

    return firstCondition
      ? checkedCircle
      : secondCondition
        ? "bg-[#d5e4f2] dark:bg-[#7ba9cf]"
        : "bg-gray-200 dark:bg-gray-700";
  };

  const iconHandler = (value: boolean, IconComponent: React.ElementType) => {
    if (value) {
      return <IconCheckCircle className="size-5 text-[#3f894e]" />;
    } else {
      return <IconComponent className="size-5 text-gray-800 dark:text-white" />;
    }
  };

  // Step Icons
  const membershipInfoIconComponent = iconHandler(step > 1, IconInfo);
  const checkoutPaymentIconComponent = iconHandler(step > 2, IconCreditCard);

  // Step circle and line styles
  const membershipInfoLineStyle = lineHandler(step > 1, true);
  const checkoutPaymentLineStyle = lineHandler(step > 2, true);

  const checkoutSteps: any = [
    {
      id: 1,
      icon: membershipInfoIconComponent,
      label: "Paket Seçimi",
      style: membershipInfoLineStyle,
    },
    {
      id: 2,
      icon: checkoutPaymentIconComponent,
      label: "Ödeme",
      style: checkoutPaymentLineStyle,
    },
  ];
  return (
    <div className="pt-5">
      <div className="max-lg:hidden grid grid-cols-2 mb-5 gap-5">
        <div className={`w-full h-1 ${membershipInfoLineStyle}`}></div>
        <div className={`w-full h-1 ${checkoutPaymentLineStyle}`}></div>
      </div>
      <ol className="grid grid-cols-2 gap-4 lg:gap-2 items-center w-full">
        {checkoutSteps.map((item: any) => (
          <li key={item.id} className="flex w-full gap-2 items-center">
            <span
              className={`flex items-center justify-center w-8 h-8 rounded-full lg:h-10 lg:w-10 shrink-0 ${item.style}`}
            >
              {item.icon}
            </span>
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              {item.label}
            </p>
          </li>
        ))}
      </ol>
    </div>
  );
};

export default CheckoutStepNav;
