"use server";
import { redirect } from "next/navigation";
import { PUBLIC_API_PATHS } from "@/utils/apiUrls";
import { cookies } from "next/headers";

export async function getSelectedHotelAllocation(
  hotelId: string,
  petType: string,
  startDate: string,
  endDate: string
) {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const selectedPetType = petType ? `&petType=${petType}` : "";
  try {
    const response = await fetch(
      `${PUBLIC_API_PATHS.roomAllocationsGetList}?startDate=${startDate}&endDate=${endDate}&hotel=${hotelId}${selectedPetType}`,
      {
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const result = await response.json();

    if (result.status === 401) {
      redirect("/");
    } else if (result.status === 404) {
      redirect("/404");
    }

    if (!response.ok || !result.success) {
      console.error("Network response was not ok");
      return undefined;
    }
    return result;
  } catch (err: unknown) {
    console.error("Error fetching data:", err);
    return undefined;
  }
}
