"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import IconChevronDown from "@/shared/icons/ChevronDown";

interface ChatScrollToBottomProps {
  chatContainerRef: React.RefObject<HTMLDivElement | null>;
}

const ChatScrollToBottom: FC<ChatScrollToBottomProps> = ({
  chatContainerRef,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    const toggleVisibility = () => {
      const scrollTop = chatContainer.scrollTop;
      const scrollHeight = chatContainer.scrollHeight;
      const clientHeight = chatContainer.clientHeight;

      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

      if (distanceFromBottom > 600) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    chatContainer.addEventListener("scroll", toggleVisibility);

    // İlk mount olduğunda da kontrol et
    toggleVisibility();

    return () => {
      chatContainer.removeEventListener("scroll", toggleVisibility);
    };
  }, []);

  const scrollToBottom = () => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    chatContainer.scrollTo({
      top: chatContainer.scrollHeight,
      behavior: "smooth",
    });
  };

  return (
    <div
      onClick={scrollToBottom}
      className={`absolute bottom-40 md:bottom-32 lg:bottom-28 z-50 right-5 sm:right-10 cursor-pointer rounded-full bg-blue-600 p-2 text-white duration-300 ${isVisible ? "visible opacity-100" : "invisible opacity-0"}`}
    >
      <IconChevronDown />
    </div>
  );
};

export default ChatScrollToBottom;
