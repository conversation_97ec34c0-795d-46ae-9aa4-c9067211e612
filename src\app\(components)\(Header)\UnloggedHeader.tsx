import type { <PERSON> } from "react";
import React from "react";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import Link from "next/link";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";
import { useTranslations } from "next-intl";
import Notifications from "@/shared/Notifications";
import { Button } from "@/components/ui/button";

export interface MainNav1Props {
  className?: string;
}

const UnloggedHeader: FC<MainNav1Props> = ({ className = "" }) => {
  const translate = useTranslations("UnloggedHeader");
  const translate2 = useTranslations("LoginPage");
  //TODO: bu translate2 yi de translate'e al sonra UnloggedHeader altına
  return (
    <div className="nc-Header nc-header-bg sticky inset-x-0 top-0 z-40 w-full shadow-sm dark:border-b dark:border-neutral-700">
      <div className={`MainNav2 relative z-10 ${className}`}>
        <div className="relative flex h-20 justify-between px-4 lg:container">
          <div className="hidden justify-start space-x-4 max-lg:flex-1 sm:space-x-10 md:flex">
            <Link className="self-center" href="/">
              <PawBookingLogo className="size-16 self-center" />
            </Link>
          </div>
          <div className="hidden flex-1 shrink-0 justify-end text-neutral-700 dark:text-neutral-100 md:flex lg:flex-none">
            <div className="flex items-center space-x-4">
              <Link href={"/login"}>
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-16">
                  {translate("headerLogin")}
                </Button>
              </Link>
              <Link href={"/auth/create-hotel"}>
                <Button variant="outline" className="w-24">
                  Bize Katıl
                </Button>
              </Link>
            </div>
            {/* <Notifications className="mx-0.5" /> */}
            <SwitchDarkMode className="mx-0.5" />
            <div className="hidden space-x-0.5 xl:flex">
              <LangDropdownSingle className="hidden lg:flex" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnloggedHeader;
