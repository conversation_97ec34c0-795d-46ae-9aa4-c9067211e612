import type { S3Image } from "./s3Image";
import type { Room } from "./rooms";
export interface StatusLogType {
  logDate?: string;
  logMessage?: string;
}

export interface Hotel {
  _id?: string;
  owner?: any | string;
  hotelName?: string;
  propertyType?:
    | "petHotel"
    | "petTaxi"
    | "petGroomer"
    | "veterinary"
    | "farmStay"
    | "petFriendlyHotel"
    | "trainingFarm";
  legalName?: string;
  hotelPhoneNumber?: string;
  hotelDescription?: string;
  address: {
    room?: string;
    streetName?: string;
    blockName?: string;
    buildingName?: string;
    buildingNumber?: string;
    citySubdivisionName?: string;
    cityName?: string;
    postalZone?: string;
    postbox?: string;
    region?: string;
    district?: string;
    country: {
      identificationCode?: string;
      name?: string;
    };
  };
  taxOffice?: string;
  taxNumber?: string;
  hotelProperties?: [];
  acceptedPetTypes?: [];
  careServices: {
    veterinary?: boolean;
    specialNutrition?: boolean;
    grooming?: boolean;
    playground?: boolean;
    camera?: boolean;
    photoInfoServices?: boolean;
    specialCondition?: boolean;
  };
  images?: S3Image[] | any[];
  rooms?: Room[] | any[];
  status?: "pending" | "inReview" | "approved" | "warning" | "declined";
  reviewRequest: {
    requestDate?: string;
    requesting?: boolean;
  };
  statusLogs: StatusLogType[];
  passive?: boolean;
}
