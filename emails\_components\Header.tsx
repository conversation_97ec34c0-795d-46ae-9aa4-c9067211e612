import * as React from "react";
import { Img, Link, Section } from "@react-email/components";
import { BASE_URL } from "../_constants/constants";

const Header = () => {
  return (
    <Section style={header}>
      <Link href={BASE_URL} style={{ display: "inline-block" }}>
        <Img
          src="https://pawbooking.co/img/pawlogolight.png"
          width={128}
          height={128}
        />
      </Link>
    </Section>
  );
};

export default Header;

const header: any = {
  padding: "32px",
  backgroundColor: "#C53030",
  borderTopRightRadius: "8px",
  borderTopLeftRadius: "8px",
  textAlign: "center" as const,
};
