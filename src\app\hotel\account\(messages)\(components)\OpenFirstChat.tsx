"use client";
import { useEffect } from "react";
import type { FC } from "react";
import { useWindowSize } from "react-use";
import { useRouter } from "next/navigation";

interface OpenFirstChatProps {
  firstMessageId: string;
}

const OpenFirstChat: FC<OpenFirstChatProps> = ({ firstMessageId }) => {
  const router = useRouter();
  const windowWidth = useWindowSize().width;

  useEffect(() => {
    if (windowWidth > 767) {
      router.push(`/hotel/account/messages?id=${firstMessageId}`);
    }
  }, [windowWidth, firstMessageId, router]);

  return null;
};

export default OpenFirstChat;
