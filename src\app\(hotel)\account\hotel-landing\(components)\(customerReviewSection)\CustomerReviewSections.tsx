"use client";
import type { FC } from "react";
import React, { useState } from "react";
import CustomerReviewListing from "./CustomerReviewListing";
import type {
  CustomerReviewsSectionType,
  CustomerReviewType,
} from "@/types/hotel/hotelLandingType";
import AddNewCustomerReviewModal from "./AddNewCustomerReviewModal";
import CustomerReviews from "./CustomerReviews";

export interface CustomerReviewSectionProps {
  hotelToken: string | undefined;
  customerReviewsSectionData: CustomerReviewsSectionType;
}

const CustomerReviewsSections: FC<CustomerReviewSectionProps> = ({
  hotelToken,
  customerReviewsSectionData,
}) => {
  const [sectionData, setSectionData] = useState<CustomerReviewsSectionType>(
    customerReviewsSectionData
  );

  const handleUpdateReview = (
    updatedReview: CustomerReviewType,
    index: number
  ) => {
    setSectionData((prev) => {
      const updatedReviews = [...(prev.reviews || [])];
      updatedReviews[index] = updatedReview;
      return { ...prev, reviews: updatedReviews };
    });
  };

  const handleDeleteReview = (index: number) => {
    setSectionData((prev) => ({
      ...prev,
      reviews: (prev.reviews || []).filter((_, i) => i !== index),
    }));
  };

  return (
    <>
      <div className="listingSection__wrap">
        {/* HEADING */}

        <h2 className="text-2xl font-semibold">Yorumlar</h2>
        <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>

        {/* Content */}
        <AddNewCustomerReviewModal
          customerReviewsSectionData={customerReviewsSectionData}
          setSectionData={setSectionData}
          hotelToken={hotelToken}
        />
        {customerReviewsSectionData?.reviews &&
          customerReviewsSectionData?.reviews?.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {customerReviewsSectionData?.reviews?.map((reviews) => {
                return (
                  <CustomerReviews
                    key={reviews._id}
                    reviews={reviews}
                    customerReviewsSectionData={customerReviewsSectionData}
                    hotelToken={hotelToken}
                  />
                );
              })}
            </div>
          )}
        {/* comment */}
        {/* <div className="divide-y divide-neutral-100 dark:divide-neutral-800">
          <CustomerReviewListing
            className="py-8"
            reviews={customerReviewsSectionData?.reviews || []}
            onUpdateReview={handleUpdateReview}
            onDeleteReview={handleDeleteReview}
          />
        </div> */}
      </div>
    </>
  );
};

export default CustomerReviewsSections;
