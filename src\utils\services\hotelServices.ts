export const isServiceValid = (service: any) => {
  const validateValue = (value: any): boolean => {
    if (typeof value === "string") {
      return value.trim() !== "";
    }
    if (typeof value === "object" && value !== null) {
      return Object.values(value).every(validateValue);
    }
    return value !== null && value !== undefined;
  };

  return Object.values(service).every(validateValue);
};

export const timeData = Array.from({ length: 24 * 4 }, (_, index) => {
  const hours = Math.floor(index / 4);
  const minutes = (index % 4) * 15;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`;
});

export const timeMinuteData = Array.from(
  { length: 120 / 5 },
  (_, index) => (index + 1) * 5
);
