"use client";
import type { FC } from "react";
import React, { useState } from "react";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useTranslations } from "next-intl";
import ServicesSoldActionButtons from "./ServicesSoldActionButtons";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import ServicesSoldDetail from "./ServicesSoldDetail";
import EmptyState from "@/components/EmptyState";
import { statusHandler } from "./(mobile)/ServicesSoldMobile";
import { serviceSoldListApiTypes } from "@/types/hotel/services/serviceTypes";
import TablePagination from "@/components/TablePagination";

interface ServicesSoldTableProps {
  serviceList: serviceSoldListApiTypes[];
  hotelToken: string | undefined;
}

const ServicesSoldTable: FC<ServicesSoldTableProps> = ({
  serviceList,
  hotelToken,
}) => {
  const translate = useTranslations("ServicesSoldList");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const columns: ColumnDef<serviceSoldListApiTypes>[] = [
    {
      accessorKey: "Hizmet İsmi",
      header: "Hizmet İsmi",
      cell: ({ row }) => (
        <div className="font-medium capitalize">
          {row?.original?.serviceName}
        </div>
      ),
    },
    {
      accessorKey: translate("petOwner"),
      header: translate("petOwner"),
      cell: ({ row }) => {
        const fullName =
          row?.original?.petOwner?.fullName ||
          row?.original?.hotelCustomer?.fullName;

        return <div className="capitalize">{fullName}</div>;
      },
    },
    {
      accessorKey: translate("petName"),
      header: translate("petName"),
      cell: ({ row }) => {
        const petName =
          row?.original?.pet?.name || row?.original?.hotelPet?.name;

        return <div className="capitalize">{petName}</div>;
      },
    },
    {
      accessorKey: "Hizmet Tarihi",
      header: "Hizmet Tarihi",
      cell: ({ row }) => (
        <div> {formatDateToDayMonthYear(row?.original?.serviceDate)}</div>
      ),
    },
    {
      accessorKey: "Hizmet Türü",
      header: "Hizmet Türü",
      cell: ({ row }) => (
        <div className="capitalize">
          {translate(row?.original?.serviceType)}
        </div>
      ),
    },
    {
      accessorKey: translate("date"),
      header: translate("date"),
      cell: ({ row }) => {
        return <div>{formatDateToDayMonthYear(row?.original?.createdAt)}</div>;
      },
    },
    {
      accessorKey: translate("total"),
      header: translate("total"),
      cell: ({ row }) => {
        const price =
          new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(row.original?.totalHotel)) + "₺";
        return <div className="font-semibold">{price}</div>;
      },
    },
    {
      accessorKey: translate("status"),
      header: translate("status"),
      cell: ({ row }) => {
        const statusProps = statusHandler(row?.original?.status);
        return (
          <div className={`font-medium capitalize ${statusProps?.color}`}>
            {translate(row?.original?.status)}
          </div>
        );
      },
    },
    {
      accessorKey: translate("action"),
      header: translate("action"),
      cell: ({ row }) => {
        return (
          <ServicesSoldActionButtons
            rowOriginal={row.original}
            hotelToken={hotelToken}
          />
        );
      },
      enableHiding: false,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        return <ServicesSoldDetail servicesSoldData={row?.original} />;
      },
    },
  ];

  const table = useReactTable({
    data: serviceList ?? [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-4 justify-between">
        {/* <Input name="search" type="search" className="w-[240px]" /> */}
      </div>
      <div className="rounded-md border bg-white dark:bg-neutral-900 mb-5">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={`${index % 2 === 0 ? "bg-gray-100 dark:bg-neutral-800" : ""}`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyState text="Satılan hizmet bulunamadı" />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <TablePagination table={table} />
    </div>
  );
};

export default ServicesSoldTable;
