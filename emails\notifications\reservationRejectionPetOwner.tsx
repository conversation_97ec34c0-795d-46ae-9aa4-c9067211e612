import React, { ReactNode } from "react";
import mailImage from "../PetOwnerConfirmation.png";
import { Img, Link, Section } from "@react-email/components";

const reservationRejectionPetOwner = () => {
  return (
    <div
      style={{
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#fff7f3",
        padding: "30px",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          maxWidth: "600px",
          background: "#ffffff",
          padding: "20px",
          borderRadius: "10px",
          textAlign: "center",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div style={{ position: "relative" }}>
          <Img
            src="https://i.hizliresim.com/7cinscf.png"
            style={{ width: "100%", height: "auto" }}
          />
          <div
            style={{
              position: "absolute",
              top: "20%",
              left: "15px",
              transform: "translate(-50%,)",
            }}
          >
            <h2>
              {" "}
              <p><PERSON><PERSON><PERSON><PERSON> [<PERSON>], </p>
            </h2>
            <p>
              <br />
              <PERSON>zgünüz, rezervasyonunuz otel tarafından onaylanmamıştır. 🐾
            </p>
            <p>
              Dilerseniz, farklı otelleri inceleyerek yeni bir rezervasyon
              oluşturabilirsiniz. 🚀
            </p>
            <div
              style={{
                textAlign: "left",
                paddingLeft: "145px",
                marginBottom: "25px",
              }}
            >
              <p>
                Alternatif seçenekler için:{" "}
                <a
                  href="https://pawbooking.co/"
                  style={{
                    color: "#FF5722",
                    fontWeight: "bold",
                    textDecoration: "none",
                  }}
                >
                  pawbooking.co
                </a>{" "}
              </p>
            </div>{" "}
            <p>Anlayışınız için teşekkür ederiz.</p>
            <p>
              {" "}
              Sevgiler,
              <br /> <strong>PawBooking Ekibi 🧡</strong>
            </p>
            <p></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default reservationRejectionPetOwner;
