import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

export const formatCellValue = (value: any, columnId: string) => {
  if (columnId === "orderDate" && value) {
    return formatDateToDayMonthYear(value);
  }
  if (columnId === "totalAmount" && value) {
    const amount = parseFloat(value);
    if (!isNaN(amount)) {
      return new Intl.NumberFormat("tr-TR", {
        style: "currency",
        currency: "TRY",
      }).format(amount);
    }
  }
  if (columnId === "statusDetail" && value) {
    return translateStatus(value);
  }
  return String(value || "-");
};

export const globalFilterFn = (
  row: any,
  columnId: string,
  filterValue: string
) => {
  const value = row.getValue(columnId);
  if (value === undefined || value === null) return false;
  const filterText = String(filterValue).toLowerCase();

  if (columnId === "petOwner_fullName") {
    const fullName =
      row.original?.petOwner?.fullName ||
      row.original?.hotelCustomer?.fullName ||
      "";
    return fullName.toLowerCase().includes(filterText);
  }

  if (columnId === "orderDate") {
    const formattedDate = formatDateToDayMonthYear(String(value));
    return (formattedDate ?? "").toLowerCase().includes(filterText);
  }

  if (columnId === "statusDetail") {
    const status = String(value);
    const translatedStatus = translateStatus(status);
    return translatedStatus.toLowerCase().includes(filterText);
  }

  if (columnId === "totalAmount") {
    const amount = parseFloat(String(value));
    if (!isNaN(amount)) {
      const formatted = new Intl.NumberFormat("tr-TR", {
        style: "currency",
        currency: "TRY",
      }).format(amount);

      const cleanFilter = filterText.replace(/[.,₺\s]/g, "");
      const cleanValue = String(amount).replace(/[.,₺\s]/g, "");

      return (
        cleanValue.includes(cleanFilter) ||
        formatted.toLowerCase().includes(filterText)
      );
    }
  }

  return String(value).toLowerCase().includes(filterText);
};

const translateStatus = (status: string) => {
  switch (status) {
    case "notPerformed":
      return "Bekliyor";
    case "performing":
      return "Devam ediyor";
    case "performed":
      return "Tamamlandı";
    default:
      return status;
  }
};
