import * as React from "react";

import { Body, Container, Head, Html, Section } from "@react-email/components";

import Footer from "./Footer";
import Header from "./Header";

interface Props {
  children: React.ReactNode;
  newsletter?: boolean;
}

const Layout = ({ children }: Props) => {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Header />
          <Section style={content}>{children}</Section>
          <Footer />
        </Container>
      </Body>
    </Html>
  );
};

export default Layout;

const fontFamily = "HelveticaNeue,Helvetica,Arial,sans-serif";

const main = {
  backgroundColor: "#ffffff",
  fontFamily,
};

const container = {
  maxWidth: "580px",
  margin: "48px auto 0",
  backgroundColor: "#ffffff",
  borderTopRightRadius: "8px",
  borderTopLeftRadius: "8px",
};

const content = {
  padding: "32px 16px 16px 16px",
};
