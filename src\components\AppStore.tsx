import React from "react";

const IconAppStore = () => {
  return (
    <svg viewBox="0 0 180 52" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M179 46.003C179 48.7742 176.709 51.0189 173.874 51.0189H6.13243C3.2995 51.0189 1 48.7742 1 46.003V6.0035C1 3.23367 3.2995 0.981133 6.13243 0.981133H173.873C176.709 0.981133 178.999 3.23367 178.999 6.0035L179 46.003Z"
        fill="black"
      />
      <path
        d="M173.333 1.04163C176.42 1.04163 178.932 3.4905 178.932 6.5V45.5C178.932 48.5095 176.42 50.9584 173.333 50.9584H6.66667C3.58 50.9584 1.06833 48.5095 1.06833 45.5V6.5C1.06833 3.4905 3.58 1.04163 6.66667 1.04163H173.333ZM173.333 2.74817e-06H6.66667C3.00167 2.74817e-06 0 2.92663 0 6.5V45.5C0 49.0734 3.00167 52 6.66667 52H173.333C176.998 52 180 49.0734 180 45.5V6.5C180 2.92663 176.998 2.74817e-06 173.333 2.74817e-06Z"
        fill="#A6A6A6"
      />
      <path
        d="M40.1707 25.7192C40.132 21.5293 43.6893 19.4909 43.852 19.396C41.8373 16.5321 38.7147 16.1408 37.6173 16.1096C34.9947 15.8405 32.4507 17.6397 31.1147 17.6397C29.752 17.6397 27.6947 16.1356 25.4773 16.1798C22.624 16.2227 19.9547 17.8334 18.4907 20.3346C15.4693 25.4345 17.7227 32.929 20.6173 37.0513C22.0653 39.0702 23.7573 41.3244 25.972 41.2451C28.1387 41.158 28.948 39.8983 31.5627 39.8983C34.1533 39.8983 34.9133 41.2451 37.172 41.1944C39.4973 41.158 40.9613 39.1664 42.3587 37.1293C44.032 34.8153 44.704 32.5364 44.7307 32.4194C44.676 32.4012 40.2147 30.7411 40.1707 25.7192Z"
        fill="white"
      />
      <path
        d="M35.904 13.3978C37.0693 11.9769 37.8667 10.0438 37.6453 8.08208C35.9587 8.15488 33.8493 9.21958 32.6347 10.6093C31.56 11.8339 30.6 13.8411 30.848 15.7287C32.7427 15.8665 34.688 14.7966 35.904 13.3978Z"
        fill="white"
      />
      <path
        d="M71.5267 33.9552H68.4987L66.84 28.8735H61.0747L59.4947 33.9552H56.5467L62.2587 16.6548H65.7867L71.5267 33.9552ZM66.34 26.7415L64.84 22.224C64.6813 21.7625 64.384 20.6757 63.9453 18.9649H63.892C63.7173 19.7007 63.436 20.7875 63.0493 22.224L61.576 26.7415H66.34V26.7415Z"
        fill="white"
      />
      <path
        d="M86.216 27.5644C86.216 29.686 85.628 31.363 84.452 32.5941C83.3987 33.69 82.0907 34.2373 80.5293 34.2373C78.844 34.2373 77.6333 33.6471 76.896 32.4667H76.8427V39.0382H74V25.5871C74 24.2533 73.964 22.8844 73.8947 21.4804H76.3947L76.5533 23.4577H76.6067C77.5547 21.9679 78.9933 21.2243 80.924 21.2243C82.4333 21.2243 83.6933 21.8054 84.7013 22.9689C85.712 24.1337 86.216 25.6651 86.216 27.5644ZM83.32 27.6658C83.32 26.4516 83.04 25.4506 82.4773 24.6628C81.8627 23.8412 81.0373 23.4304 80.0027 23.4304C79.3013 23.4304 78.664 23.6592 78.0947 24.1103C77.524 24.5653 77.1506 25.1594 76.976 25.8952C76.888 26.2384 76.844 26.5192 76.844 26.7402V28.8202C76.844 29.7276 77.1293 30.4933 77.7 31.1186C78.2707 31.7439 79.012 32.0559 79.924 32.0559C80.9947 32.0559 81.828 31.6529 82.424 30.8495C83.0213 30.0448 83.32 28.984 83.32 27.6658Z"
        fill="white"
      />
      <path
        d="M100.932 27.5644C100.932 29.686 100.344 31.363 99.1667 32.5941C98.1147 33.69 96.8067 34.2373 95.2453 34.2373C93.56 34.2373 92.3493 33.6471 91.6133 32.4667H91.56V39.0382H88.7173V25.5871C88.7173 24.2533 88.6813 22.8844 88.612 21.4804H91.112L91.2707 23.4577H91.324C92.2707 21.9679 93.7093 21.2243 95.6413 21.2243C97.1493 21.2243 98.4093 21.8054 99.42 22.9689C100.427 24.1337 100.932 25.6651 100.932 27.5644ZM98.036 27.6658C98.036 26.4516 97.7547 25.4506 97.192 24.6628C96.5773 23.8412 95.7547 23.4304 94.7187 23.4304C94.016 23.4304 93.38 23.6592 92.8093 24.1103C92.2387 24.5653 91.8667 25.1594 91.692 25.8952C91.6053 26.2384 91.56 26.5192 91.56 26.7402V28.8202C91.56 29.7276 91.8453 30.4933 92.4133 31.1186C92.984 31.7426 93.7253 32.0559 94.64 32.0559C95.7107 32.0559 96.544 31.6529 97.14 30.8495C97.7373 30.0448 98.036 28.984 98.036 27.6658Z"
        fill="white"
      />
      <path
        d="M117.385 29.1036C117.385 30.5752 116.861 31.7725 115.809 32.6968C114.653 33.7069 113.044 34.2113 110.976 34.2113C109.067 34.2113 107.536 33.8525 106.377 33.1336L107.036 30.8235C108.284 31.5593 109.653 31.9285 111.145 31.9285C112.216 31.9285 113.049 31.6919 113.648 31.2213C114.244 30.7507 114.541 30.1189 114.541 29.3311C114.541 28.6291 114.296 28.0376 113.804 27.5579C113.315 27.0782 112.497 26.6323 111.356 26.2202C108.249 25.0905 106.697 23.4356 106.697 21.2594C106.697 19.8372 107.241 18.6711 108.331 17.7637C109.416 16.855 110.864 16.4013 112.675 16.4013C114.289 16.4013 115.631 16.6756 116.701 17.2229L115.991 19.4823C114.991 18.9519 113.86 18.6867 112.595 18.6867C111.595 18.6867 110.813 18.9272 110.253 19.4056C109.78 19.8333 109.543 20.3546 109.543 20.9721C109.543 21.6559 109.813 22.2214 110.357 22.666C110.831 23.0768 111.691 23.5214 112.939 24.0011C114.465 24.6004 115.587 25.3011 116.308 26.1045C117.027 26.9053 117.385 27.9076 117.385 29.1036Z"
        fill="white"
      />
      <path
        d="M126.784 23.5604H123.651V29.6171C123.651 31.1576 124.203 31.9272 125.309 31.9272C125.817 31.9272 126.239 31.8843 126.572 31.7985L126.651 33.9032C126.091 34.1073 125.353 34.21 124.44 34.21C123.317 34.21 122.44 33.8759 121.807 33.209C121.176 32.5408 120.859 31.4202 120.859 29.8459V23.5578H118.992V21.4778H120.859V19.1937L123.651 18.3721V21.4778H126.784V23.5604Z"
        fill="white"
      />
      <path
        d="M140.921 27.6151C140.921 29.5326 140.359 31.1069 139.236 32.338C138.059 33.6055 136.496 34.2373 134.548 34.2373C132.671 34.2373 131.176 33.6302 130.061 32.416C128.947 31.2018 128.389 29.6691 128.389 27.8218C128.389 25.8887 128.963 24.3053 130.113 23.0742C131.261 21.8418 132.811 21.2256 134.759 21.2256C136.636 21.2256 138.147 21.8327 139.287 23.0482C140.377 24.2273 140.921 25.7496 140.921 27.6151ZM137.972 27.7048C137.972 26.5543 137.72 25.5676 137.209 24.7447C136.613 23.7489 135.761 23.2523 134.657 23.2523C133.515 23.2523 132.647 23.7502 132.051 24.7447C131.54 25.5689 131.288 26.5712 131.288 27.7568C131.288 28.9073 131.54 29.894 132.051 30.7156C132.665 31.7114 133.524 32.208 134.632 32.208C135.717 32.208 136.569 31.701 137.184 30.6896C137.708 29.8511 137.972 28.854 137.972 27.7048Z"
        fill="white"
      />
      <path
        d="M150.161 23.9179C149.88 23.8672 149.58 23.8412 149.265 23.8412C148.265 23.8412 147.492 24.2091 146.948 24.9462C146.475 25.5962 146.237 26.4178 146.237 27.4097V33.9552H143.396L143.423 25.409C143.423 23.9712 143.387 22.6621 143.316 21.4817H145.792L145.896 23.8685H145.975C146.275 23.0482 146.748 22.3878 147.396 21.8925C148.029 21.4466 148.713 21.2243 149.451 21.2243C149.713 21.2243 149.951 21.2425 150.161 21.275V23.9179Z"
        fill="white"
      />
      <path
        d="M162.875 27.1276C162.875 27.6242 162.841 28.0428 162.771 28.3847H154.243C154.276 29.6171 154.688 30.5596 155.48 31.2096C156.199 31.7907 157.128 32.0819 158.269 32.0819C159.532 32.0819 160.684 31.8856 161.72 31.4917L162.165 33.4157C160.955 33.9305 159.525 34.1866 157.876 34.1866C155.892 34.1866 154.335 33.6172 153.201 32.4797C152.071 31.3422 151.504 29.8147 151.504 27.8985C151.504 26.0174 152.031 24.4509 153.085 23.2016C154.189 21.8678 155.681 21.2009 157.559 21.2009C159.403 21.2009 160.799 21.8678 161.747 23.2016C162.497 24.2611 162.875 25.5715 162.875 27.1276ZM160.164 26.4087C160.183 25.5871 159.997 24.8773 159.612 24.278C159.12 23.5071 158.364 23.1223 157.347 23.1223C156.417 23.1223 155.661 23.498 155.084 24.252C154.611 24.8513 154.329 25.5702 154.243 26.4074H160.164V26.4087Z"
        fill="white"
      />
    </svg>
  );
};

export default IconAppStore;
