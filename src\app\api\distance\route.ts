import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const start = searchParams.get("start");
  const end = searchParams.get("end");

  if (!start || !end) {
    return NextResponse.json({ error: "Eksik parametreler!" }, { status: 400 });
  }

  const apiKey = "AIzaSyCXi01Rpagu_wbKsoLUMW0UwWNhfn7xg6A";
  const googleUrl = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${encodeURIComponent(start)}&destinations=${encodeURIComponent(end)}&key=${apiKey}`;

  try {
    const res = await fetch(googleUrl);
    const data = await res.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: "API isteği başarısız!" },
      { status: 500 }
    );
  }
}
