import React from "react";
import AnimatedBeamHero from "@/app/(components)/(hotelHomePage)/Animated-beam";
import { TypewriterEffect } from "@/components/ui/typewriter-effect";
import Link from "next/link";
import { Button } from "@/components/ui/button";

const Hero = () => {
  const words = [
    {
      text: "Evcil",
    },
    {
      text: "Hayvan",
    },
    {
      text: "Rezervasyonlarınızı",
    },
    {
      text: "PawBooking",
      className: "text-primary-500 dark:text-primary-500",
    },
    {
      text: "ile",
    },
    {
      text: "Kolaylaştırın",
    },
  ];
  return (
    <div className="bg-gray-100/50 dark:bg-neutral-800 rounded-lg max-md:mt-5 mb-10 py-16 md:py-24">
      <div className="flex max-lg:flex-col items-center mx-auto container md:gap-20 lg:gap-5">
        <div className="basis-1/2 space-y-3 w-auto md:mt-0 md:mx-0">
          <div className="flex flex-col items-start justify-center">
            <p className="text-neutral-600 dark:text-neutral-200 text-base">
              Deneyiminizi Bir Adım Öne Taşıyın
            </p>
            <TypewriterEffect words={words} />
            <div className="flex space-y-0 space-x-4 mt-10 w-full max-w-sm">
              <Link className="w-full" href="/auth/create-hotel">
                <Button
                  variant="outline"
                  className="border-black dark:border-white w-full"
                >
                  Hemen Başla
                </Button>
              </Link>
              <Link className="w-full md:hidden" href="/login">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full">
                  Giriş Yap
                </Button>
              </Link>
              <Link
                className="w-full max-md:hidden"
                href="https://calendly.com/pawbooking-info/paw-tutorial"
                target="_blank"
              >
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full">
                  Randevu Al
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="basis-1/2 hidden md:block">
          <AnimatedBeamHero />
        </div>
      </div>
    </div>
  );
};

export default Hero;
