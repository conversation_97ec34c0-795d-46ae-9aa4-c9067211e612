"use client";

import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import IconFunnelX from "@/shared/icons/FunnelX";

interface FilterTabsProps {
  selected: string;
  onSelect: (key: string) => void;
  statusCounts: {
    waitingForApproval: number;
    waitingForCheckIn: number;
    waitingForCheckOut: number;
    checkedOut: number;
    checkedIn: number;
    empty: number;
  };
}

const tabs = [
  { key: "all", label: "Tüm Odalar", color: "bg-yellow-400" },
  { key: "checkedIn", label: "Dolu Odalar", color: "bg-green-400" },
  { key: "empty", label: "Boş Odalar", color: "bg-gray-400" },
  { key: "waitingForApproval", label: "Onay Bekliyor", color: "bg-orange-400" },
  { key: "waitingForCheckIn", label: "Bugün Check-in", color: "bg-blue-400" },
  { key: "waitingForCheckOut", label: "Bugün Check-out", color: "bg-red-400" },
  { key: "checkedOut", label: "Temizlik Bekliyor", color: "bg-[#FFDB58]" },
];

const FilterTabs: React.FC<FilterTabsProps> = ({
  selected,
  onSelect,
  statusCounts,
}) => {
  return (
    <>
      <div className="block md:hidden">
        <Select value={selected} onValueChange={onSelect}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Filtre seç" />
          </SelectTrigger>
          <SelectContent>
            {tabs.map((tab) => (
              <SelectItem key={tab.key} value={tab.key}>
                {tab.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="hidden md:flex gap-1 flex-wrap rounded-lg bg-gray-100 dark:bg-neutral-800 p-1 items-center">
        {tabs
          .filter((tab) => tab.key !== "all")
          .map((tab) => {
            const isSelected = selected === tab.key;

            let count = 0;
            if (tab.key === "checkedIn") count = statusCounts.checkedIn;
            if (tab.key === "empty") count = statusCounts.empty;
            if (tab.key === "waitingForApproval")
              count = statusCounts.waitingForApproval;
            if (tab.key === "waitingForCheckIn")
              count = statusCounts.waitingForCheckIn;
            if (tab.key === "waitingForCheckOut")
              count = statusCounts.waitingForCheckOut;
            if (tab.key === "checkedOut") count = statusCounts.checkedOut;

            return (
              <button
                key={tab.key}
                onClick={() => onSelect(tab.key)}
                className={`
                px-2 py-2 rounded-md text-sm font-medium transition-colors
                ${
                  isSelected
                    ? `${tab.color} text-white shadow`
                    : "bg-transparent text-gray-600 hover:bg-white hover:text-black dark:text-neutral-300 dark:hover:bg-neutral-700"
                }
                flex items-center justify-center
                min-w-[100px]
              `}
              >
                {tab.label}
                {count >= 0 && (
                  <span
                    className={`ml-2 rounded-full px-2 py-0.5 text-xs font-bold ${
                      isSelected
                        ? "bg-white text-black"
                        : "bg-gray-300 text-gray-700"
                    }`}
                  >
                    {count}
                  </span>
                )}
              </button>
            );
          })}
        {selected && selected !== "all" && (
          <button
            onClick={() => onSelect("all")}
            className="px-4 py-2 rounded-md bg-gray-300 text-gray-600 hover:bg-gray-400 hover:text-black"
            title="Seçimi Temizle"
            type="button"
          >
            <IconFunnelX className="size-5" />
          </button>
        )}
      </div>
    </>
  );
};

export default FilterTabs;
