import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";

export const usePassword = () => {
  const { toast } = useToast();
  const router = useRouter();

  // SENDS RESET PASSWORD LINK TO USER EMAIL ADDRESS
  const forgotPasswordHandler = async (
    event: FormEvent,
    email: string,
    role: string | string[] | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setSuccess: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/pub/auth/forgot`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: email,
            role: role,
          }),
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        setLoading(false);
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 6000,
        title: "Şifre Sıfırlama",
        description: data.data.tr,
      });
      setSuccess(true);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  // CHANGES THE USER PASSWORD
  const changePasswordHandler = async (
    event: FormEvent,
    resetToken: string | string[] | undefined,
    newPassword: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/pub/auth/reset`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            token: resetToken,
            password: newPassword,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        setLoading(false);
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        throw new Error("Network response was not ok");
      }

      setLoading(false);
      toast({
        variant: "success",
        duration: 6000,
        title: "Şifre Değiştirme",
        description:
          "Şifreniz başarıyla değiştirildi. Giriş ekranına yönlendiriliyorsunuz...",
      });
      setTimeout(() => {
        router.push("/");
      }, 2000);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  return { forgotPasswordHandler, changePasswordHandler };
};
