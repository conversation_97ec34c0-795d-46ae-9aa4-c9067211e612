"use client";
import type { FC } from "react";
import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useTranslations } from "next-intl";

interface PaginationDropdownProps {
  reservationPage: number;
  pageCount: number;
  tab: string | string[];
}

const PaginationDropdown: FC<PaginationDropdownProps> = ({
  reservationPage,
  pageCount,
  tab,
}) => {
  const translate = useTranslations("PaginationDropdown");
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          {reservationPage} / {pageCount}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        onCloseAutoFocus={(e) => e.preventDefault()}
        className="max-h-56 w-56 overflow-y-auto"
      >
        <DropdownMenuLabel>{translate("pageSelect")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup value={reservationPage.toString()}>
          {Array.from({ length: pageCount }, (_, index) => index + 1).map(
            (page: number) => {
              return (
                <Link
                  key={page}
                  href={`/account/reservations?page=${page}&tab=${tab}`}
                >
                  <DropdownMenuRadioItem value={page.toString()}>
                    {page}
                  </DropdownMenuRadioItem>
                  <DropdownMenuSeparator />
                </Link>
              );
            }
          )}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PaginationDropdown;
