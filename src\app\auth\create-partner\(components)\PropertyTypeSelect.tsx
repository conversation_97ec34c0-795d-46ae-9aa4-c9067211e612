"use client";
import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Label from "@/components/Label";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import {
  setPartner,
  setStepState,
} from "@/store/features/partnerAuth/partner-auth-slice";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import petHotel from "@/images/petHotel.png";
import petTaxi from "@/images/petTaxi.png";

const PropertyTypeSelect = () => {
  const dispatch = useDispatch();
  const partner = useSelector((state: RootState) => state.partnerAuth.partner);
  const selectedPropertyType = useSelector(
    (state: RootState) => state.partnerAuth.partner.propertyType
  );

  const handleChange = (value: string | null) => {
    dispatch(setPartner({ ...partner, propertyType: value }));
    dispatch(setStepState(2));
  };

  return (
    <div>
      <h2 className="font-semibold text-lg">İşletme Türü Seçimi</h2>
      <p className="text-sm mb-4">
        Lütfen ilerlemek istediğiniz işletme türünü seçiniz.
      </p>

      <RadioGroup
        value={selectedPropertyType ?? ""}
        onValueChange={handleChange}
        className="grid grid-cols-2 gap-4"
      >
        <div className="hover:scale-105 duration-200">
          <RadioGroupItem
            value="petHotel"
            id="property-type-hotel"
            className="peer sr-only"
          />
          <Label
            htmlFor="property-type-hotel"
            className="cursor-pointer capitalize flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary"
          >
            <span className="size-28 relative">
              <Image src={petHotel} className="object-cover" fill alt="" />
            </span>
            <span className="mt-2">Pet Otel</span>
          </Label>
        </div>

        <div className="hover:scale-105 duration-200">
          <RadioGroupItem
            value="petTaxi"
            id="property-type-petTaxi"
            className="peer sr-only"
          />
          <Label
            htmlFor="property-type-petTaxi"
            className="cursor-pointer capitalize flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent peer-data-[state=checked]:border-primary-6000 [&:has([data-state=checked])]:border-primary"
          >
            <span className="size-28 relative">
              <Image src={petTaxi} className="object-cover" fill alt="" />
            </span>
            <span className="mt-2">Pet Taksi</span>
          </Label>
        </div>
      </RadioGroup>

      <Separator className="mt-5" />
    </div>
  );
};

export default PropertyTypeSelect;
