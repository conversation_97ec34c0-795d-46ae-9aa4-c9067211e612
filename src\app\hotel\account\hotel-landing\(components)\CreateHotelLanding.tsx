"use client";
import React, { useState } from "react";
import type { FC } from "react";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

interface CreateHotelLandingProps {
  hotelToken: string | undefined;
}

const CreateHotelLanding: FC<CreateHotelLandingProps> = ({ hotelToken }) => {
  const { createHotelLanding } = useHotelLandingActions();
  const [loading, setLoading] = useState<boolean>(false);
  return (
    <form
      onSubmit={(event) => createHotelLanding(event, hotelToken, setLoading)}
      className="flex flex-col justify-center items-center gap-5 h-72"
    >
      <p className="font-semibold text-center">
        Şu anda bir otel landing page’iniz yok. Otelinizi ön plana çıkarmak için
        ilk adımı atın!
      </p>
      <Button
        type="submit"
        className="bg-secondary-6000 hover:bg-secondary-700 text-white"
      >
        {loading ? <LoadingSpinner /> : " Otel websitesi oluştur"}
      </Button>
    </form>
  );
};

export default CreateHotelLanding;
