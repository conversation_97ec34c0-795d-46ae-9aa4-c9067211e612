"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import { useHotelPromo } from "@/hooks/hotel/discounts/useHotelPromo";
import { Switch } from "@/components/ui/switch";
import Checkbox from "@/shared/Checkbox";

interface UpdatePromoCodeProps {
  promosData: any;
  hotelToken: string | undefined;
}

const UpdatePromoCode: FC<UpdatePromoCodeProps> = ({
  promosData,
  hotelToken,
}) => {
  const { updatePromo } = useHotelPromo();
  const initialData = {
    isActive: promosData.isActive,
    code: promosData.code,
    discount: promosData.discount,
    startDate: promosData.startDate
      ? new Date(promosData.startDate).toISOString().split("T")[0]
      : "",
    endDate: promosData.endDate
      ? new Date(promosData.endDate).toISOString().split("T")[0]
      : "",
    usageLimit: promosData.usageLimit,
    minimumConditionValue: promosData.minimumConditionValue,
    discountType: promosData.discountType,
    promoType: promosData.promoType,
    condition: promosData.condition,
  };
  const [promoCode, setPromoCode] = useState<any>(initialData);
  const [editPromoCodeIsOpen, setEditPromoCodeIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPromoCode((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleConditionChange = (selected: string) => {
    setPromoCode((prev: any) => ({
      ...prev,
      condition: selected,
      ...(selected === "none" && { minimumConditionValue: 0 }),
    }));
  };

  const handleTodayDateCheckbox = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;

    if (checked) {
      const today = new Date().toISOString().split("T")[0];
      setPromoCode((prev: any) => ({
        ...prev,
        startDate: today,
      }));
    } else {
      setPromoCode((prev: any) => ({
        ...prev,
        startDate: "",
      }));
    }
  };

  const isValid = () => {
    const {
      code,
      discount,
      startDate,
      endDate,
      usageLimit,
      condition,
      minimumConditionValue,
    } = promoCode;

    if (
      condition !== "none" &&
      (!minimumConditionValue || minimumConditionValue <= 0)
    ) {
      return false;
    }

    return (
      code.trim() !== "" &&
      discount > 0 &&
      startDate.trim() !== "" &&
      endDate.trim() !== "" &&
      usageLimit > 0 &&
      promoCode.discountType.trim() !== "" &&
      promoCode.promoType.trim() !== ""
    );
  };

  const buttonDisabled = isValid();

  const resetInputs = () => {
    setPromoCode(initialData);
    setEditPromoCodeIsOpen(false);
  };

  const closeModal = () => {
    setLoading(false);
    setEditPromoCodeIsOpen(false);
  };

  return (
    <div>
      <IconEdit
        onClick={() => setEditPromoCodeIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={editPromoCodeIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
          onInteractOutside={resetInputs}
        >
          <DialogHeader>
            <DialogTitle>Kampanya Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              updatePromo(
                event,
                hotelToken,
                promoCode,
                promosData._id,
                setLoading,
                closeModal,
                setDisabled
              )
            }
          >
            <h2 className="font-medium text-lg mb-3">Promosyon Kodu</h2>
            <div className="space-y-4">
              <FormItem label="Promosyon Kodu" className="w-full">
                <Input
                  value={promoCode.code}
                  name="code"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="İndirim Tipi">
                <Select
                  value={promoCode.discountType}
                  onValueChange={(selected) =>
                    setPromoCode((prev: any) => ({
                      ...prev,
                      discountType: selected,
                    }))
                  }
                >
                  <SelectTrigger className="w-full rounded-2xl">
                    <SelectValue placeholder="İndirim Tipi Seç" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Yüzde</SelectItem>
                    <SelectItem value="fixedAmount">Sabit Tutar</SelectItem>
                    <SelectItem value="freeNights">Ücretsiz Gece</SelectItem>
                    <SelectItem value="freeService">Ücretsiz Hizmet</SelectItem>
                    <SelectItem value="custom">Özel</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
              <FormItem label="Promosyon Tipi">
                <Select
                  value={promoCode.promoType}
                  onValueChange={(selected) =>
                    setPromoCode((prev: any) => ({
                      ...prev,
                      promoType: selected,
                    }))
                  }
                >
                  <SelectTrigger className="w-full rounded-2xl">
                    <SelectValue placeholder="Promosyon Tipi Seç" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="reservationDiscount">
                      Konaklama İndirimi
                    </SelectItem>
                    <SelectItem value="serviceDiscount">
                      Hizmet İndirimi
                    </SelectItem>
                    <SelectItem value="subscriptionDiscount">
                      Üyelik Kartı İndirimi
                    </SelectItem>
                    <SelectItem value="orderDiscount">
                      Rezervasyon İndirimi
                    </SelectItem>
                    <SelectItem value="firstReservation">
                      İlk Rezervasyon
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
              <FormItem label="İndirim Tutarı" className="w-full">
                <Input
                  min={0}
                  placeholder={
                    promoCode.discountType === "percentage"
                      ? "%"
                      : promoCode.discountType === "fixedAmount"
                        ? "₺"
                        : promoCode.discountType === "freeNights"
                          ? "Gece"
                          : promoCode.discountType === "freeService"
                            ? "Hizmet"
                            : ""
                  }
                  value={promoCode.discount}
                  name="discount"
                  type="number"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="Başlangıç Tarihi" className="w-full">
                <Input
                  name="startDate"
                  type="date"
                  onChange={handleChange}
                  value={promoCode.startDate}
                />
                <Checkbox
                  id="todayDate"
                  label="Bugünün Tarihini Seç"
                  name="todayDate"
                  checked={
                    promoCode.startDate ===
                    new Date().toISOString().split("T")[0]
                  }
                  onChange={handleTodayDateCheckbox}
                  className="mt-2 !text-sm"
                  inputClass="!size-5 !rounded-lg !-mr-2"
                />
              </FormItem>
              <FormItem label="Bitiş Tarihi" className="w-full">
                <Input
                  value={promoCode.endDate}
                  name="endDate"
                  type="date"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="Kullanım Limiti" className="w-full">
                <Input
                  min={1}
                  value={promoCode.usageLimit}
                  name="usageLimit"
                  type="number"
                  onChange={handleChange}
                />
              </FormItem>
              <FormItem label="Kullanım Koşulu">
                <Select
                  value={promoCode.condition}
                  onValueChange={(selected) => handleConditionChange(selected)}
                >
                  <SelectTrigger className="w-full rounded-2xl">
                    <SelectValue placeholder="Kullanım Koşulu Seç" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Yok</SelectItem>
                    <SelectItem value="totalReservationNights">
                      Konaklama gece sayısı (Bir rezervasyondaki minimum
                      konaklama sayısı)
                    </SelectItem>
                    <SelectItem value="totalOrderPrice">
                      Toplam Rezervasyon Fiyatı
                    </SelectItem>
                    <SelectItem value="reservationCount">
                      Rezervasyon sayısı (Tek seferde minimum rezervasyon
                      sayısı)
                    </SelectItem>
                    <SelectItem value="serviceCount">
                      Hizmet sayısı (Tek seferde minimum hizmet sayısı)
                    </SelectItem>
                    <SelectItem value="firstReservation">
                      İlk rezervasyon
                    </SelectItem>
                    <SelectItem value="betweenDates">
                      Belirli tarihler arası
                    </SelectItem>{" "}
                  </SelectContent>
                </Select>
              </FormItem>
              {promoCode.condition !== "none" && (
                <FormItem label="Minimum Kullanım Koşulu" className="w-full">
                  <Input
                    value={promoCode.minimumConditionValue}
                    name="minimumConditionValue"
                    type="number"
                    onChange={handleChange}
                  />
                </FormItem>
              )}
              <FormItem label="Aktiflik Durumu">
                <div className="flex gap-2">
                  <Switch
                    name="isActive"
                    checked={promoCode.isActive}
                    onCheckedChange={(checked) =>
                      setPromoCode((prev: any) => ({
                        ...prev,
                        isActive: checked,
                      }))
                    }
                    className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                  />
                  <p
                    className={
                      promoCode.isActive ? "text-green-500" : "text-red-500"
                    }
                  >
                    {promoCode.isActive ? "Aktif" : "Pasif"}
                  </p>
                </div>
              </FormItem>
            </div>
            {!buttonDisabled && (
              <span className="text-red-500 text-sm text-right block mt-2">
                Lütfen tüm alanları doldurunuz!
              </span>
            )}
            <div className="mt-7 flex justify-end gap-5">
              <Button onClick={resetInputs} variant="outline" type="button">
                İptal
              </Button>
              <Button
                disabled={
                  JSON.stringify(promoCode) === JSON.stringify(initialData) ||
                  !buttonDisabled ||
                  disabled
                }
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetInputs}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdatePromoCode;
