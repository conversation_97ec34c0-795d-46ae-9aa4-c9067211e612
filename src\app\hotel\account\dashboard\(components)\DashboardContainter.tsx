"use client";
import React, { useState } from "react";
import type { FC } from "react";
import DashboardInformationNav from "./DashboardInformationNav";
import ReportSummary from "./(tabs)/ReportSummary";
import ReportPayment from "./(tabs)/ReportPayment";

interface DashboardContainterProps {
  hotelAnalytics: any;
  startDate: string | string[];
  endDate: string | string[];
  membershipData: any;
  depositAmounts: any;
  ordersBetweenDates: any;
  revenueAnalytics: any;
  cancelledReservationStats: any;
  paymentTypes: any;
}

const DashboardContainter: FC<DashboardContainterProps> = ({
  hotelAnalytics,
  startDate,
  endDate,
  membershipData,
  depositAmounts,
  ordersBetweenDates,
  revenueAnalytics,
  cancelledReservationStats,
  paymentTypes,
}) => {
  const [navNumber, setNavNumber] = useState<number>(1);
  return (
    <div>
      <DashboardInformationNav
        navNumber={navNumber}
        setNavNumber={setNavNumber}
      />
      {navNumber === 1 && (
        <ReportSummary
          hotelAnalytics={hotelAnalytics}
          revenueAnalytics={revenueAnalytics}
          startDate={startDate}
          endDate={endDate}
          membershipData={membershipData}
        />
      )}
      {navNumber === 2 && (
        <ReportPayment
          depositAmounts={depositAmounts}
          ordersBetweenDates={ordersBetweenDates}
          revenueAnalytics={revenueAnalytics}
          cancelledReservationStats={cancelledReservationStats}
          paymentTypes={paymentTypes}
          startDate={startDate}
          endDate={endDate}
        />
      )}
    </div>
  );
};

export default DashboardContainter;
