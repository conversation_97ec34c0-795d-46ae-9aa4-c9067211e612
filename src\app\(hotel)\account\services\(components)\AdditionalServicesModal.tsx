"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import VeterinaryService from "./(services)/VeterinaryService";
import TransportationService from "./(services)/TransportationService";
import GroomingService from "./(services)/GroomingService";
import IconPlus from "@/shared/icons/Plus";
import PawPlus from "@/shared/PawPlus";
import PlusFeatureModal from "@/components/PlusFeatureModal";
interface AdditionalServicesModalProps {
  hotelToken: string | undefined;
  additionalServices: any;
  membershipData: any;
}

const AdditionalServicesModal: FC<AdditionalServicesModalProps> = ({
  hotelToken,
  additionalServices,
  membershipData,
}) => {
  const [addHotelServiceModal, setAddHotelServiceModal] =
    useState<boolean>(false);
  const [selectedService, setSelectedService] = useState<string>("");
  const [modalIsVisible, setModalIsVisible] = useState(false);

  const closeModal = () => {
    setAddHotelServiceModal(false);
    setSelectedService("");
  };

  const availableServicesCount = Object.values(
    additionalServices?.data?.availableServices || {}
  ).reduce<number>(
    (acc, serviceList) =>
      acc + (Array.isArray(serviceList) ? serviceList.length : 0),
    0
  );

  let maxServicesAllowed = 2;
  if (membershipData?.membershipType === "free") {
    maxServicesAllowed = 2;
  } else if (membershipData?.membershipType === "plus") {
    maxServicesAllowed = Infinity;
  }

  const serviceLimitReached = availableServicesCount >= maxServicesAllowed;

  return (
    <>
      <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
        <div className="mt-4 md:my-8">
          <h2 className="text-xl md:text-2xl font-semibold">
            Hizmet Oluşturma ve Düzenleme
          </h2>
          <span className="text-sm text-neutral-500 dark:text-neutral-300"></span>
        </div>
        {!serviceLimitReached ? (
          <Button
            onClick={() => setAddHotelServiceModal(true)}
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          >
            <IconPlus />
            Yeni Hizmet Ekle
          </Button>
        ) : (
          <Button
            type="button"
            className="bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
            onClick={() => setModalIsVisible(true)}
          >
            <PawPlus width="20" height="20" />
            Yeni Hizmet Ekle
          </Button>
        )}
        {modalIsVisible && (
          <PlusFeatureModal
            isOpen={modalIsVisible}
            setIsOpen={setModalIsVisible}
            message="Daha fazla oda grubu oluşturmak için Plus üye olmalısınız."
          />
        )}
      </div>
      <Dialog open={addHotelServiceModal}>
        <DialogContent
          onInteractOutside={closeModal}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>Yeni Hizmet Ekleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="listingSection__wrap_disable space-y-3">
            <Select
              value={selectedService}
              onValueChange={(selected) => setSelectedService(selected)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Hizmet Seç" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="veterinaryService">Veteriner</SelectItem>
                <SelectItem value="transportationService">Ulaşım</SelectItem>
                <SelectItem value="groomingService">Pet Kuaför</SelectItem>
              </SelectContent>
            </Select>
            {selectedService === "veterinaryService" && (
              <VeterinaryService
                hotelToken={hotelToken}
                closeModal={closeModal}
              />
            )}
            {selectedService === "transportationService" && (
              <TransportationService
                hotelToken={hotelToken}
                closeModal={closeModal}
              />
            )}
            {selectedService === "groomingService" && (
              <GroomingService
                hotelToken={hotelToken}
                closeModal={closeModal}
              />
            )}
          </div>
          {!selectedService && (
            <div className="mt-7 flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                İptal
              </Button>
            </div>
          )}
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AdditionalServicesModal;
