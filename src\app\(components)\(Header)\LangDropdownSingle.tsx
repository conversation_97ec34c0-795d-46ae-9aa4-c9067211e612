"use client";
import { ChevronDownIcon } from "@heroicons/react/24/solid";
import { GlobeAltIcon } from "@heroicons/react/24/outline";
import type { FC } from "react";
import { useTranslations, useLocale } from "next-intl";
import type { Locale } from "@/i18n/config";
import { setUserLocale } from "@/actions/locale";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverClose,
} from "@/components/ui/popover";

export const headerLanguage = [
  {
    id: "tr",
    name: "Türkçe",
  },
  {
    id: "en",
    name: "English",
  },
  /* {
    id: "ru",
    name: "Русский",
  },
  {
    id: "ar",
    name: "العربية",
  },
  {
    id: "de",
    name: "<PERSON><PERSON><PERSON>",
  }, */
];

interface LangDropdownProps {
  panelClassName?: string;
  className?: string;
  text?: boolean;
  isMobile?: boolean;
}

const LangDropdown: FC<LangDropdownProps> = ({
  panelClassName = "absolute min-w-[200px] right-0 z-10  px-4 mt-4 sm:px-0 !overflow-visible",
  className,
  text,
  isMobile = false,
}) => {
  const t = useTranslations("LangDropdown");
  const locale = useLocale();

  function onChange(value: string) {
    const locale = value as Locale;
    setUserLocale(locale);
  }
  return (
    <div className={`LangDropdown flex items-center ${className}`}>
      <Popover>
        <PopoverTrigger className="mr-2.5 flex items-center">
          <GlobeAltIcon className="size-[18px] opacity-80 hover:opacity-100" />
          {text && (
            <span className="ml-2 select-none text-sm font-medium text-gray-700 hover:text-opacity-100 dark:text-neutral-300">
              {t("language")}
            </span>
          )}
          {/* <ChevronDownIcon
            className={`ml-2 h-4 w-4  group-hover:text-opacity-80 transition ease-in-out duration-150`}
          /> */}
        </PopoverTrigger>
        <PopoverContent className="w-52" side="bottom" align="center">
          <div className="overflow-hidden">
            <div className="grid gap-8 lg:grid-cols-2">
              {headerLanguage.map((item, index) => (
                <PopoverClose
                  key={index}
                  onClick={() => {
                    onChange(item.id);
                  }}
                  asChild
                >
                  <div
                    className={`flex cursor-pointer items-center rounded-lg p-2 transition duration-150 ease-in-out hover:bg-neutral-100 dark:hover:bg-neutral-700 ${locale === item.id ? "bg-neutral-100 dark:bg-neutral-700" : ""}`}
                  >
                    <p className="text-sm font-medium ">{item.name}</p>
                  </div>
                </PopoverClose>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
export default LangDropdown;
