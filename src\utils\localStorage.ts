export const getKey = (key: string): string => `${key}`;

interface StorageObject<T> {
  data: T;
  expire?: number;
}

export const setLocalStorageItem = <T>(
  name: string,
  data: T,
  expire?: number
): void => {
  if (typeof window === "undefined") return;

  const obj: StorageObject<T> = { data };

  if (expire) {
    const time = new Date().getTime();
    obj.expire = time + expire * 1000;
  }

  window.localStorage.setItem(getKey(name), JSON.stringify(obj));
};

export const removeLocalStorageItem = (name: string): void => {
  if (typeof window === "undefined") return;

  window.localStorage.removeItem(getKey(name));
};

export const getLocalStorageItem = <T>(name: string): T | null => {
  if (typeof window === "undefined") return null;

  let obj: string | null;

  obj = window.localStorage.getItem(getKey(name));

  if (!obj) {
    return null;
  }

  let json: StorageObject<T>;

  json = JSON.parse(obj);

  const { data, expire } = json;

  if (expire) {
    const time = new Date().getTime();

    if (expire < time) {
      removeLocalStorageItem(name);
      return null;
    }
  }

  return data;
};

const localStorage = {
  setLocalStorageItem,
  getLocalStorageItem,
  removeLocalStorageItem,
};

export default localStorage;
