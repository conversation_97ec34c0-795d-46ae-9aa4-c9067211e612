"use client";
import React from "react";
import type { FC } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import IconChevronRight from "@/shared/icons/ChevronRight";
import Link from "next/link";
import { PaymentIcon } from "react-svg-credit-card-payment-icons";
import UnsubscribeModal from "./UnsubscribeModal";

interface MembershipProps {
  membershipData: any;
  hotelNextPaymentDate: any;
  hotelSubscriptionStatus: any;
  hotelToken: string | undefined;
  hotelCards: any;
}

const Membership: FC<MembershipProps> = ({
  membershipData,
  hotelNextPaymentDate,
  hotelSubscriptionStatus,
  hotelToken,
  hotelCards,
}) => {
  const formattedDate = new Date(hotelNextPaymentDate).toLocaleDateString(
    "tr-TR",
    {
      day: "numeric",
      month: "long",
      year: "numeric",
    }
  );

  return (
    <div>
      <div className="w-full md:px-36">
        <h1 className="text-2xl font-semibold text-black dark:text-white">
          Üyelik Bilgileri
        </h1>
        <p className="mt-2 text-neutral-700 dark:text-neutral-500 font-medium">
          Plan Ayrıntıları
        </p>
        <Card className="mt-5">
          <div className="w-full h-2 rounded-t-lg bg-gradient-to-r from-secondary-6000 to-[#FDE047]"></div>
          <CardHeader>
            <CardTitle className="text-xl">
              {membershipData?.membershipType === "plus"
                ? "Plus plan"
                : "Standart plan"}
              <span className="text-sm text-neutral-500 font-medium">
                {" "}
                (Mevcut üyelik){" "}
              </span>
            </CardTitle>
            <CardDescription>
              {membershipData?.membershipType === "plus"
                ? "Rezervasyon Komisyon Avantajı, Personel Hesapları Oluşturma, İndirim Kuponları ve Sadakat Programları ve çok daha fazlası"
                : "Daha yüksek komisyon, Sınırlı ek hizmet satışı"}
            </CardDescription>
          </CardHeader>
          {membershipData?.membershipType === "free" && (
            <CardContent className="space-y-3">
              <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
              <Link
                href="/checkout"
                className="flex justify-between items-center text-base font-semibold hover:bg-neutral-50 dark:hover:bg-slate-800 rounded-md px-1 py-2 cursor-pointer"
              >
                Plus üye ol <IconChevronRight className="ml-2 size-5" />
              </Link>
            </CardContent>
          )}
        </Card>
        <p className="my-5 text-neutral-700 dark:text-neutral-500 font-medium">
          Ödeme Bilgileri
        </p>
        <Card className="mt-5">
          <CardContent className="space-y-3 pt-4">
            {membershipData?.membershipType === "plus" && (
              <div>
                <div className="px-1 py-2">
                  <div className="flex justify-between items-center text-xl font-semibold">
                    {hotelSubscriptionStatus === "active"
                      ? "Bir sonraki ödeme"
                      : "Abonelik bitiş tarihi"}
                  </div>
                  <div className="text-base text-muted-foreground mt-1">
                    {formattedDate}
                  </div>
                  {hotelCards?.[0] && hotelCards[0]?.last4 && (
                    <div className="flex gap-2 items-center text-base font-semibold text-gray-700 dark:text-neutral-400 mt-2">
                      <PaymentIcon
                        className="border"
                        type={
                          hotelCards[0].cardTYpe === "MASTER"
                            ? "MasterCard"
                            : hotelCards[0].cardTYpe === "VISA"
                              ? "Visa"
                              : hotelCards[0].cardTYpe
                        }
                        format="logo"
                      />
                      {`•••• •••• •••• ${hotelCards[0].last4}`}
                    </div>
                  )}
                </div>
                <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>{" "}
              </div>
            )}
            <Link
              href="/account/my-user/manage-payment"
              className="flex justify-between items-center text-base font-semibold hover:bg-neutral-50 dark:hover:bg-slate-800 rounded-md px-1 py-2 cursor-pointer"
            >
              Ödeme yöntemini yönetin{" "}
              <IconChevronRight className="ml-2 size-5" />
            </Link>
            {/* <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
            <div className="flex justify-between items-center text-base font-semibold hover:bg-neutral-50 dark:hover:bg-slate-800 rounded-md px-1 py-2 cursor-pointer">
              Ödeme geçmişini görüntüleyin{" "}
              <IconChevronRight className="ml-2 size-5" />
            </div> */}
          </CardContent>
        </Card>
        {membershipData?.membershipType === "plus" &&
          hotelSubscriptionStatus === "active" && (
            <UnsubscribeModal hotelToken={hotelToken} />
          )}
      </div>
    </div>
  );
};

export default Membership;
