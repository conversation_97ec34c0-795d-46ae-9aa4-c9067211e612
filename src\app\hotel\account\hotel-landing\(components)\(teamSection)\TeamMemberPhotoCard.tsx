"use client";
import type { FC } from "react";
import React, { useState } from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";
import IconDelete from "@/shared/icons/Delete";

export interface TeamMemberPhotoCardProps {
  className?: string;
  item: any;
  hotelToken: string | undefined;
  index: number;
}

const TeamMemberPhotoCard: FC<TeamMemberPhotoCardProps> = ({
  className = "",
  item,
  hotelToken,
  index,
}) => {
  const [deletePhotoIsOpen, setDeletePhotoIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { deleteTeamMemberPhoto } = useHotelLandingActions();
  return (
    <div
      className={`nc-CardCategory5 flex flex-col ${className}`}
      data-nc-id="CardCategory5"
    >
      <div
        className={`group aspect-h-3 aspect-w-4 relative h-0 w-full shrink-0 overflow-hidden rounded-2xl`}
      >
        <Image
          fill
          alt=""
          src={item.memberImage || ""}
          className="size-full rounded-2xl object-cover"
          sizes="(max-width: 400px) 100vw, 400px"
        />
        <span className="absolute inset-0 bg-black bg-opacity-10 opacity-0 transition-opacity group-hover:opacity-100"></span>
      </div>
      <div className="mt-4 text-wrap px-3">
        <h2
          className={`text-base font-medium capitalize text-neutral-900 dark:text-neutral-100 sm:text-lg`}
        >
          {item.memberName}
        </h2>
        <span
          className={`mt-1 block text-sm capitalize text-neutral-6000 dark:text-neutral-400`}
        >
          {item.memberTitle}
        </span>
      </div>
      <div className="flex justify-end">
        <IconDelete
          className="size-5 cursor-pointer duration-200 hover:text-secondary-6000"
          onClick={() => {
            setDeletePhotoIsOpen(true);
          }}
        />
      </div>
      <div>
        <Dialog open={deletePhotoIsOpen}>
          <DialogContent
            onInteractOutside={() => {
              setDeletePhotoIsOpen(false);
            }}
          >
            <DialogHeader>
              <DialogTitle>Üye Kaldırma</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form
              onSubmit={(event) => {
                deleteTeamMemberPhoto(
                  event,
                  hotelToken,
                  item,
                  setLoading,
                  setDeletePhotoIsOpen
                );
              }}
            >
              <div className="mb-4 mt-2">
                <p className="text-gray-500 dark:text-neutral-200">
                  Seçili üyeyi kaldırmak istiyor musunuz?
                </p>
              </div>
              <div className="flex justify-end gap-5">
                <ButtonSecondary
                  type="button"
                  onClick={() => {
                    setDeletePhotoIsOpen(false);
                  }}
                >
                  Vazgeç
                </ButtonSecondary>
                <ButtonPrimary type="submit">
                  {loading ? <LoadingSpinner /> : "Kaldır"}
                </ButtonPrimary>
              </div>
            </form>
            <DialogClose
              onClick={() => {
                setDeletePhotoIsOpen(false);
              }}
              className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="size-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default TeamMemberPhotoCard;
