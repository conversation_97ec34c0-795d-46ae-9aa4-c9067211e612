"use client";
import type { FC } from "react";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { Button } from "@/components/ui/button";
import IconEdit from "@/shared/icons/Edit";
import IconInfo from "@/shared/icons/Info";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import { Switch } from "@/components/ui/switch";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import PriceSummaryMobile from "./PriceSummaryMobile";
import UpdateRoomPriceMobile from "./UpdateRoomPriceMobile";
import { useTranslations } from "next-intl";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";

interface HotelCalendarDrawerRangeMobileProps {
  startDate: Date | undefined;
  endDate: Date | undefined;
  hotelToken: string | undefined;
  setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>;
  minPrice: number | undefined;
  maxPrice: number | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  dateStatues: boolean[];
  hasUndefinedPrice: boolean;
  drawerOpen: boolean;
  setDrawerOpen: React.Dispatch<React.SetStateAction<boolean>>;
  inRangePrices: number[] | [];
}

const HotelCalendarDrawerRangeMobile: FC<
  HotelCalendarDrawerRangeMobileProps
> = ({
  startDate,
  endDate,
  hotelToken,
  setFetchAgain,
  minPrice,
  maxPrice,
  setEndDate,
  setStartDate,
  dateStatues,
  hasUndefinedPrice,
  drawerOpen,
  setDrawerOpen,
  inRangePrices,
}) => {
  const translate = useTranslations("HotelCalendarDrawerRangeMobile");
  const falseClass = "translate-x-[700px] invisible";
  const trueClass = "translate-x-0 visible";
  const { selectedRoomId } = useSelector(
    (state: RootState) => state.hotelCalendar
  );
  const [newRoomPrice, setNewRoomPrice] = useState<string | undefined>("");
  const [selectedRoomData, setSelectedRoomData] = useState<any>(null);
  const [checked, setChecked] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState<number>(0);
  const { fetchSelectedRoomData, calendarDateStatusHandler } =
    useHotelCalendarActions();

  const closeDrawerHandler = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setSelectedNumber(0);
    setNewRoomPrice("");
  };

  // Adjusted start and end date
  const adjustedStartDate = adjustDateToTimezone(startDate);
  const adjustedEndDate = adjustDateToTimezone(endDate);
  const startDateToString = adjustedStartDate
    ? adjustedStartDate.toISOString()
    : undefined;
  const endDateToString = adjustedEndDate
    ? adjustedEndDate.toISOString()
    : undefined;

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchSelectedRoomData(hotelToken, selectedRoomId);
      setSelectedRoomData(data.data);
    };
    fetchData();
  }, [selectedRoomId, hotelToken, fetchSelectedRoomData]);

  //if one of the dates is passive sets switch off
  useEffect(() => {
    if (dateStatues.includes(true)) {
      setChecked(false);
    } else {
      setChecked(true);
    }
  }, [dateStatues]);

  const displayCustomerTotalPrice = () => {
    if (minPrice && maxPrice) {
      const sumArray = inRangePrices.reduce(
        (accumulator, currentValue) => accumulator + currentValue,
        0
      );
      return sumArray;
    }
  };

  return (
    <>
      {selectedRoomData && (
        <div className="relative max-w-md overflow-hidden px-3 text-sm">
          <Drawer
            open={drawerOpen}
            onOpenChange={(isOpen) => {
              setDrawerOpen(isOpen);
              if (!isOpen) closeDrawerHandler();
            }}
          >
            <DrawerContent onInteractOutside={closeDrawerHandler}>
              <DrawerHeader>
                <DrawerTitle>{translate("dateUpdate")}</DrawerTitle>
                <DrawerDescription className="hidden"></DrawerDescription>
              </DrawerHeader>
              <div
                className={`top-0 size-full space-y-5 overflow-x-hidden pl-3 pr-10 duration-500       
            `}
              >
                <div
                  className={`overflow-x-hidden duration-500 ${selectedNumber === 0 ? trueClass : falseClass}`}
                >
                  {!hasUndefinedPrice && (
                    <div className="my-5 flex items-center gap-5">
                      <Switch
                        className="data-[state=checked]:bg-[#58d68d] data-[state=unchecked]:bg-[#6c757d]"
                        checked={checked}
                        onClick={() => {
                          setChecked(!checked);
                          calendarDateStatusHandler(
                            hotelToken,
                            startDateToString,
                            endDateToString,
                            selectedRoomId,
                            setFetchAgain,
                            checked
                          );
                        }}
                      />
                      <div>
                        {checked
                          ? translate("closetoReservations")
                          : translate("opentoReservations")}
                      </div>
                    </div>
                  )}
                  <div className="mb-5 flex items-center text-lg font-medium underline">
                    <div>
                      {adjustedStartDate &&
                        adjustedStartDate.toLocaleDateString(
                          translate("locale"),
                          {
                            month: "short",
                            day: "2-digit",
                          }
                        )}
                    </div>
                    <div>-</div>
                    <div>
                      {adjustedEndDate &&
                        adjustedEndDate.toLocaleDateString(
                          translate("locale"),
                          {
                            month: "short",
                            day: "2-digit",
                          }
                        )}
                    </div>
                  </div>
                  <div
                    onClick={() => setSelectedNumber(1)}
                    className="rounded-lg border border-gray-300 p-3"
                  >
                    {minPrice ? (
                      <div className="text-2xl font-semibold">
                        {minPrice === maxPrice
                          ? new Intl.NumberFormat("tr-TR", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(maxPrice))
                          : new Intl.NumberFormat("tr-TR", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(minPrice)) +
                            "-" +
                            new Intl.NumberFormat("tr-TR", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            }).format(Number(maxPrice))}
                        ₺
                      </div>
                    ) : (
                      <div className="h-8" />
                    )}
                  </div>
                  <div
                    onClick={() => {
                      setSelectedNumber(1);
                    }}
                    className="mt-2 flex cursor-pointer items-center justify-center gap-0.5 text-center text-sm underline"
                  >
                    {translate("changePrice")}
                    <IconEdit className="size-4" />
                  </div>
                </div>
              </div>
              <div
                className={`absolute top-20 size-full overflow-auto bg-transparent px-5 duration-500 ${selectedNumber === 1 ? trueClass : falseClass}`}
              >
                <UpdateRoomPriceMobile
                  hotelToken={hotelToken}
                  newRoomPrice={newRoomPrice}
                  setNewRoomPrice={setNewRoomPrice}
                  setFetchAgain={setFetchAgain}
                  startDateToString={startDateToString}
                  endDateToString={endDateToString}
                  selectedRoomId={selectedRoomId}
                  setSelectedNumber={setSelectedNumber}
                />
              </div>
              <div
                className={`absolute top-28 size-full overflow-auto bg-transparent px-5 duration-500 ${selectedNumber === 2 ? trueClass : falseClass}`}
              >
                <PriceSummaryMobile
                  displayCustomerTotalPrice={displayCustomerTotalPrice}
                  nightCount={inRangePrices.length}
                />
                <Button
                  variant="outline"
                  className="mt-8 w-full"
                  onClick={() => {
                    setSelectedNumber(0);
                  }}
                >
                  {translate("back")}
                </Button>
              </div>
              <div
                onClick={() => setSelectedNumber(2)}
                className={`ml-3 mr-10 mt-5 cursor-pointer rounded-lg border border-gray-300 p-3 text-sm duration-500 ${selectedNumber === 0 ? trueClass : falseClass}`}
              >
                <div className="text-center font-medium text-neutral-700 dark:text-neutral-400">
                  {translate("totalPrice")}
                </div>
                <div className="mt-2 text-center text-2xl font-semibold">
                  {displayCustomerTotalPrice() &&
                    new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(
                      Number(
                        (Number(displayCustomerTotalPrice()) * 1.18).toFixed(2)
                      )
                    ) + "₺"}
                </div>
              </div>
              <div
                onClick={() => {
                  setSelectedNumber(2);
                }}
                className={`ml-3 mr-10 mt-2 flex cursor-pointer items-center justify-center gap-0.5 text-center text-sm underline duration-500 hover:text-secondary-6000 ${selectedNumber === 0 ? trueClass : falseClass}`}
              >
                {translate("clickForDetails")}
                <IconInfo className="size-4" />
              </div>

              <DrawerFooter>
                <Button
                  variant="outline"
                  className="mt-8 relative"
                  onClick={closeDrawerHandler}
                >
                  {translate("close")}
                </Button>
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
        </div>
      )}
    </>
  );
};

export default HotelCalendarDrawerRangeMobile;
