"use client";
import type { FC } from "react";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import type {
  CustomerReviewsSectionType,
  CustomerReviewType,
} from "@/types/hotel/hotelLandingType";
import turkeyCityDistrict from "@/data/jsons/mernis_city_district.json";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";
import LoadingSpinner from "@/shared/icons/Spinner";

interface AddNewCustomerReviewModalProps {
  customerReviewsSectionData: CustomerReviewsSectionType;
  setSectionData: React.Dispatch<
    React.SetStateAction<CustomerReviewsSectionType>
  >;
  hotelToken: string | undefined;
}

const AddNewCustomerReviewModal: FC<AddNewCustomerReviewModalProps> = ({
  customerReviewsSectionData,
  setSectionData,
  hotelToken,
}) => {
  const { addCustomerReviewSectionHandler } = useHotelLandingActions();
  const [addNewCommentIsOpen, setAddNewCommentIsOpen] =
    useState<boolean>(false);
  const [newReview, setNewReview] = useState<CustomerReviewType>({
    customerName: "",
    review: "",
    city: "",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setSectionData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNewReviewChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewReview((prev) => ({ ...prev, [name]: value }));
  };

  const closeModal = () => {
    setNewReview({ customerName: "", review: "", city: "" });
    setAddNewCommentIsOpen(false);
  };

  const isAddButtonDisabled =
    !newReview.customerName?.trim() ||
    !newReview.review?.trim() ||
    !newReview.city;

  return (
    <div className="flex justify-end">
      <Button
        className="bg-secondary-6000 hover:bg-secondary-700 text-white"
        onClick={() => setAddNewCommentIsOpen(true)}
      >
        Yorum Ekle
      </Button>
      <Dialog open={addNewCommentIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)]"
          onInteractOutside={closeModal}
        >
          <DialogHeader>
            <DialogTitle>Yorum Ekleme</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              addCustomerReviewSectionHandler(
                event,
                customerReviewsSectionData,
                hotelToken,
                setLoading,
                newReview,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2"></div>
            <div className="space-y-5">
              <div className="relative">
                <FormItem label="Yorum Başlığı">
                  <Input
                    type="text"
                    name="title"
                    className="mt-1.5"
                    value={customerReviewsSectionData.title || ""}
                    onChange={handleChange}
                  />
                </FormItem>
                <FormItem label="Açıklama" className="mt-3">
                  <Textarea
                    name="description"
                    className="mt-1.5"
                    value={customerReviewsSectionData.description || ""}
                    onChange={handleChange}
                  />
                </FormItem>
              </div>
              <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
              <div>
                <FormItem label="Müşteri Adı">
                  <Input
                    type="text"
                    name="customerName"
                    className="mt-1.5"
                    value={newReview.customerName}
                    onChange={handleNewReviewChange}
                  />
                </FormItem>
                <FormItem label="Müşteri Yorumu" className="mt-3">
                  <Textarea
                    name="review"
                    className="mt-1.5"
                    value={newReview.review}
                    onChange={handleNewReviewChange}
                  />
                </FormItem>
                <FormItem label="Şehir" className="mt-3">
                  <Select
                    onValueChange={(selected) =>
                      setNewReview({ ...newReview, city: selected })
                    }
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Şehir Seç" />
                    </SelectTrigger>
                    <SelectContent>
                      {turkeyCityDistrict.map((city) => {
                        return (
                          <SelectItem key={city.ilkodu} value={city.iladi}>
                            {city.iladi}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </FormItem>
              </div>
              <div className="flex justify-end items-center mt-3 gap-5">
                <Button type="button" onClick={closeModal} variant="outline">
                  Vazgeç
                </Button>
                <Button
                  type="submit"
                  className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                  disabled={isAddButtonDisabled}
                >
                  {loading ? <LoadingSpinner /> : "Kaydet"}
                </Button>
              </div>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AddNewCustomerReviewModal;
