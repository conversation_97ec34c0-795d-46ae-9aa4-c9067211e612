"use client";

import * as React from "react";
import { <PERSON>ge<PERSON><PERSON><PERSON>, ArrowRight, XCircle } from "lucide-react";
import NumberFlow from "@number-flow/react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export interface PricingTier {
  id: string;
  name: string;
  price: Record<string, number | string>;
  description: string;
  features: { name: string; included: boolean; description?: string }[];
  cta: string;
  highlighted?: boolean;
  popular?: boolean;
  membershipData?: any;
}

interface PricingCardProps {
  tier: PricingTier;
  paymentFrequency: string;
  membershipData?: any;
  onSelectTier?: (
    tierId: string,
    price: number | string,
    frequency: string
  ) => void;
}

export function PricingCard({
  tier,
  paymentFrequency,
  membershipData,
  onSelectTier,
}: PricingCardProps) {
  const price = tier.price[paymentFrequency];
  const isHighlighted = tier.highlighted;
  const isPopular = tier.popular;

  const handleSelectTier = () => {
    if (onSelectTier && !isCurrentMembership) {
      onSelectTier(tier.id, price, paymentFrequency);
    }
  };

  const membershipType = (
    membershipData?.membershipType?.trim() || ""
  ).toLowerCase();
  const tierId = (tier.id || "").toLowerCase();
  const isCurrentMembership = membershipType === tierId;
  return (
    <Card
      className={cn(
        "relative flex flex-col gap-8 overflow-hidden p-6",
        isHighlighted
          ? "bg-foreground text-background"
          : "bg-background text-foreground",
        isPopular && "ring-2 ring-primary"
      )}
    >
      <h2 className="flex items-center gap-3 text-xl font-medium capitalize">
        {tier.name}
        {isPopular && (
          <Badge variant="secondary" className="mt-1 z-10">
            🔥 Popüler
          </Badge>
        )}
      </h2>

      <div className="relative h-12">
        {typeof price === "number" ? (
          <>
            <NumberFlow
              format={{
                style: "currency",
                currency: "TRY",
                trailingZeroDisplay: "stripIfInteger",
              }}
              value={price}
              className="text-4xl font-medium"
            />
          </>
        ) : (
          <h1 className="text-4xl font-medium">{price}</h1>
        )}
      </div>

      <div className="flex-1 space-y-2">
        <h3 className="text-sm font-medium">{tier.description}</h3>
        <ul className="space-y-2">
          {tier.features.map((feature, index) => (
            <li
              key={index}
              className={cn(
                "flex items-center gap-2 text-sm font-medium",
                isHighlighted ? "text-background" : "text-muted-foreground"
              )}
            >
              {feature.included ? (
                <BadgeCheck className="h-4 w-4 text-emerald-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <div>
                <span>{feature.name}</span>
                {feature.description && (
                  <p className="text-xs text-muted-foreground">
                    ({feature.description})
                  </p>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>

      <Button
        className="w-full bg-secondary-6000 hover:bg-secondary-700 text-white"
        onClick={handleSelectTier}
        disabled={isCurrentMembership}
      >
        {isCurrentMembership ? "Seçili" : tier.cta}
        {!isCurrentMembership && <ArrowRight className="ml-2 h-4 w-4" />}
      </Button>
    </Card>
  );
}
