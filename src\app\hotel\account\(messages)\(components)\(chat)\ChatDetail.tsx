"use client";
import React, { useEffect, useMemo, useState, useRef } from "react";
import type { FC } from "react";
import ChatBottomRef from "./ChatBottomRef";
import { useDispatch, useSelector } from "react-redux";
import {
  setChatData,
  setSelectedIdValue,
} from "@/store/features/liveChat/live-chat-slice";
import type { RootState } from "@/store";
import Chat from "./Chat";
import { useSocket } from "@/app/Socket";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import ChatScrollToBottom from "./ChatScrollToBottom";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import HotelAvatar from "@/shared/HotelAvatar";
import defaultHotelImage from "@/images/default-hotel-photo.webp";
import TypingStatus from "./TypingStatus";
import DetailDrawer from "../(contactDetail)/DetailDrawer";
import { LoaderOne } from "@/components/ui/loader";

interface ChatDetailProps {
  chatHistory: any;
  contactDetail: any;
  selectedId: string | string[] | undefined;
  pageParam: string | string[] | undefined;
  hotelData: any;
}

const ChatDetail: FC<ChatDetailProps> = ({
  chatHistory,
  contactDetail,
  selectedId,
  pageParam,
  hotelData,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { sendSeen } = useSocket();
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [seenSentIds, setSeenSentIds] = useState<string[]>([]);
  const pageNumber = pageParam || 5;
  const chatData = useSelector(
    (state: RootState) => state.liveChatText.chatData
  );

  // Detect unseen messages
  const selectedMessages = useMemo(() => {
    return chatData?.messages
      ?.filter(
        (message: any) =>
          message?.to === hotelData?._id &&
          !message?.isDeleted &&
          !message?.seen &&
          !seenSentIds.includes(message?._id)
      )
      ?.map((message: any) => message?._id);
  }, [chatData, hotelData?._id, seenSentIds]);

  // Set chatData in Redux
  useEffect(() => {
    dispatch(setChatData(chatHistory));
    dispatch(setSelectedIdValue(selectedId));
  }, [chatHistory, dispatch, selectedId]);

  // Send seen event
  useEffect(() => {
    if (selectedMessages?.length > 0) {
      sendSeen({
        from: hotelData?._id,
        to: selectedId,
        messageId: selectedMessages,
      });
      setSeenSentIds((prev) => [...prev, ...selectedMessages]);
    }
  }, [selectedMessages, sendSeen, hotelData?._id, selectedId]);

  const loadMoreMessageButtonHandler = () => {
    if (chatHistory?.messages?.length >= +pageNumber * 50) {
      return true;
    } else {
      return false;
    }
  };

  const isMoreMessageButtonTrue = loadMoreMessageButtonHandler();

  // Normalize date: sadece yıl, ay, gün kısmını al
  const normalizeDate = (timestamp?: string) => {
    if (!timestamp) return undefined;

    const date = new Date(timestamp); // ISO string'i Date yap
    if (isNaN(date.getTime())) return undefined;

    // Yalnızca yıl, ay, gün kısmını al
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  };

  // Returns "Today", "Yesterday" or formatted date "dd.MM.yyyy" based on the local timezone
  const formatDateHeader = (timestamp?: string) => {
    const messageDay = normalizeDate(timestamp);
    if (!messageDay) return "";

    const today = normalizeDate(new Date().toISOString());
    const yesterday = today && new Date(today.getTime() - 86400000);

    if (messageDay.getTime() === today?.getTime()) return "Bugün";
    if (messageDay.getTime() === yesterday?.getTime()) return "Dün";

    const day = messageDay.getDate().toString().padStart(2, "0");
    const month = (messageDay.getMonth() + 1).toString().padStart(2, "0");
    const year = messageDay.getFullYear();
    return ` ${day}.${month}.${year}`;
  };

  return (
    <>
      <div className="sticky top-0 left-0 bg-white dark:bg-background z-10 px-4 py-3 border-b">
        <div className="flex items-center justify-between gap-1">
          {selectedId && (
            <>
              <div className="flex items-center gap-1">
                <Link className="md:hidden" href="/hotel/account/messages">
                  <ChevronLeft />
                </Link>
                <HotelAvatar
                  sizeClass="w-8 h-8"
                  imgUrl={chatHistory?.toInfo?.image?.src || defaultHotelImage}
                  logo={chatHistory?.toInfo?.image?.src || defaultHotelImage}
                />
                <p className="relative text-xs sm:text-sm font-semibold capitalize">
                  {chatHistory?.toInfo?.name}
                  <TypingStatus />
                </p>
              </div>
              {contactDetail && (
                <div className="lg:hidden">
                  <DetailDrawer
                    key={selectedId as string}
                    contactDetail={contactDetail}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
      {chatData?.success ? (
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto overscroll-contain hiddenScrollbar py-5 px-3 md:px-10 bg-gray-50 dark:bg-background"
        >
          <ChatScrollToBottom chatContainerRef={chatContainerRef} />
          <div className="2xl:max-w-7xl w-full 2xl:mx-auto">
            <div className="space-y-5 sm:space-y-3 text-sm">
              {isMoreMessageButtonTrue && (
                <div className="flex justify-center">
                  <Button
                    onClick={() =>
                      router.replace(
                        `/hotel/account/messages?id=${selectedId}&page=${+pageNumber + 10}`
                      )
                    }
                    className="text-xs bg-blue-600 hover:bg-blue-700"
                  >
                    Önceki mesajları yükle
                  </Button>
                </div>
              )}

              {chatData?.messages?.map((message: any, index: number) => {
                const prevMessage = chatData?.messages[index - 1];
                const prevDate = prevMessage
                  ? normalizeDate(prevMessage.timestamp)
                  : undefined;
                const currentDate = normalizeDate(message.timestamp);

                const showDateHeader =
                  !prevDate || currentDate?.getTime() !== prevDate.getTime();

                return (
                  <React.Fragment key={message._id}>
                    {showDateHeader && (
                      <div className="text-center text-neutral-600 dark:text-neutral-400 text-sm my-2">
                        {formatDateHeader(message.timestamp)}
                      </div>
                    )}
                    <Chat
                      message={message}
                      hotelData={hotelData}
                      selectedId={selectedId}
                      toImage={chatData?.toInfo?.image?.src}
                    />
                  </React.Fragment>
                );
              })}
            </div>
          </div>
          <ChatBottomRef />
        </div>
      ) : (
        <div className="flex-1 flex justify-center items-center bg-gray-50 dark:bg-background">
          <LoaderOne />
        </div>
      )}
    </>
  );
};

export default ChatDetail;
