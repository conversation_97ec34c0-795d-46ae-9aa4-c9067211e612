"use client";
import React from "react";
import type { FC } from "react";
// import { TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  // <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface roomGroupDataTypes {
  roomGroupName: string;
  reservationCount: number;
}

interface ChartConfig {
  label: string;
}

interface DynamicChartConfig {
  [key: string]: ChartConfig;
  reservationCount: ChartConfig;
}

interface BarChartActiveSelectProps {
  title?: string;
  preferredRoomData: roomGroupDataTypes[];
  startDate: string | string[];
  endDate: string | string[];
}

const BarChartActiveSelect: FC<BarChartActiveSelectProps> = ({
  title = "Bar Chart - Active",
  preferredRoomData,
  startDate,
  endDate,
}) => {
  const colorArray = [
    "#e76f51",
    "#264653",
    "#f4a261",
    "#2a9d8f",
    "#e9c46a",
    "#669bbc",
    "#003049",
    "#fdf0d5",
    "#c1121f",
    "#780000",
  ];

  const sortedRoomData = preferredRoomData.sort(
    (a, b) => b.reservationCount - a.reservationCount
  );

  const roomDataWithColor = sortedRoomData.map((room, index: number) => ({
    ...room,
    fill: colorArray[index % colorArray.length],
  }));

  const dynamicChartConfig = roomDataWithColor.reduce(
    (acc: DynamicChartConfig, item) => {
      acc[item.roomGroupName] = {
        label: item.roomGroupName,
      };
      return acc;
    },
    {
      reservationCount: { label: "Rezervasyon" },
    }
  );

  const formatIsoDate = (isoDate: string | string[]): string => {
    const dateStr = Array.isArray(isoDate) ? isoDate[0] : isoDate;

    const [year, month, day] = dateStr.split("-");
    return `${day}.${month}.${year}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">{title}</CardTitle>
        <CardDescription className="text-center">
          {formatIsoDate(startDate) + " - " + formatIsoDate(endDate)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer className="h-[273px]" config={dynamicChartConfig}>
          <BarChart accessibilityLayer data={roomDataWithColor}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="roomGroupName"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => {
                const label =
                  dynamicChartConfig[value as keyof typeof dynamicChartConfig]
                    ?.label;
                return label ? label.slice(0, 4) : "";
              }}
              tick={{ fontSize: 12 }}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar
              dataKey="reservationCount"
              barSize={20}
              fill="url(#colorUv)"
              strokeWidth={2}
              radius={8}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      {/* <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="size-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter> */}
    </Card>
  );
};

export default BarChartActiveSelect;
