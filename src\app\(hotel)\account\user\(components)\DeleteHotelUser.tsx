"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { useHotelUserInformations } from "@/hooks/hotel/useHotelUserInformations";
import type { UserInformationApiTypes } from "@/types/hotel/hotelUserType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconDelete from "@/shared/icons/Delete";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
export interface DeleteHotelUserProps {
  rowOriginal: UserInformationApiTypes;
  hotelToken: string | undefined;
}

const DeleteHotelUser: FC<DeleteHotelUserProps> = ({
  hotelToken,
  rowOriginal,
}) => {
  const translate = useTranslations("DeleteHotelUser");
  const { deleteHotelUserHandler } = useHotelUserInformations();
  const [loading, setLoading] = useState<boolean>(false);
  const [deleteHotelUserIsOpen, setDeleteHotelUserIsOpen] = useState(false);

  function closeModal() {
    setDeleteHotelUserIsOpen(false);
    setLoading(false);
  }

  return (
    <>
      <IconDelete
        onClick={() => setDeleteHotelUserIsOpen(true)}
        className="size-5 duration-200 hover:text-secondary-6000"
      />
      <Dialog open={deleteHotelUserIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>{translate("removeUserTitle")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteHotelUserHandler(
                event,
                hotelToken,
                rowOriginal._id,
                setLoading,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                {rowOriginal.fullName} {translate("removeUserConfirmation")}
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                {translate("cancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : translate("confirm")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeleteHotelUser;
