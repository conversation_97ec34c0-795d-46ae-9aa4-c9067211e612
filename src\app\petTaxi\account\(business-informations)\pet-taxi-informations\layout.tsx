import type { FC } from "react";
import React from "react";
import PetTaxiInformationsNav from "./(components)/PetTaxiInformationsNav";

export interface PetTaxiInformationsLayoutProps {
  children?: React.ReactNode;
}

const PetTaxiInformationsLayout: FC<PetTaxiInformationsLayoutProps> = ({
  children,
}) => {
  return (
    <div className="container">
      <PetTaxiInformationsNav />
      {children}
    </div>
  );
};

export default PetTaxiInformationsLayout;
