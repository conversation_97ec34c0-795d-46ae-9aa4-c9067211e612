"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import IconUpload from "@/shared/icons/Upload";
import { usePetTaxiInformationsActions } from "@/hooks/taxi/useTaxiInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import DeleteFileModal from "./DeleteFileModal";
import { useTranslations } from "next-intl";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
interface MultiplePetTaxiFilesProps {
  petTaxiToken: string | undefined;
  filePath: string;
  docType: string;
  name: string;
  taxiData: PetTaxiDataApiTypes;
}

const MultiplePetTaxiFiles: FC<MultiplePetTaxiFilesProps> = ({
  petTaxiToken,
  filePath,
  docType,
  name,
  taxiData,
}) => {
  const translate = useTranslations("HotelFiles");
  const [fileObjects, setFileObjects] = useState<File[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const { uploadFileHandler } = usePetTaxiInformationsActions();

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const newFiles = Array.from(files);
      setFileObjects((prevFiles) => [...prevFiles, ...newFiles]);

      setLoading(true);

      try {
        const formEvent = {
          preventDefault: () => {},
          target: event.target.form,
        } as any;

        await uploadFileHandler(
          formEvent,
          [...fileObjects, ...newFiles],
          petTaxiToken,
          setLoading,
          filePath,
          docType,
          () => {}
        );

        setFileObjects([]);
        event.target.value = "";
      } catch (error) {
        console.error("File upload error:", error);
        setLoading(false);
      }
    } else {
      console.warn("No files selected");
    }
  };

  // filter files based on the selected docType
  const filteredFiles = taxiData?.files?.filter(
    (file: any) => file?.doctype === docType
  );

  // sort files from the most recently uploaded to the earliest uploaded
  const sortedData = filteredFiles?.sort(
    (a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const renderFileNames = () => {
    return fileObjects?.map((file, index) => (
      <div key={index} className="mt-2 flex items-center">
        <span className="text-sm max-md:w-60 max-md:text-xs">{file?.name}</span>
      </div>
    ));
  };

  const renderSortedFileNames = () => {
    return sortedData?.map((file: any, index: number) => (
      <div
        key={index}
        className="relative flex items-center border-b border-neutral-300 pb-1"
      >
        <a
          className="hover:underline max-md:w-60"
          href={file?.src}
          target="_blank"
        >
          <span className="text-sm max-md:text-xs">{file?.originalname}</span>
        </a>
        {file?._id && (
          <DeleteFileModal fileId={file?._id} petTaxiToken={petTaxiToken} />
        )}
      </div>
    ));
  };

  return (
    <div className="flex items-center justify-between max-md:flex-col max-md:items-start max-md:gap-5">
      <div className="w-full md:basis-1/2">
        <div className="my-2 text-sm font-medium capitalize text-neutral-700 dark:text-neutral-300">
          {name}
        </div>
        <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
          <div className="flex flex-col items-center space-y-1 text-center">
            <div className="text-sm text-neutral-6000 dark:text-neutral-300">
              <label
                htmlFor={`file-upload-${docType}`}
                className="group relative cursor-pointer rounded-md font-medium text-secondary-6000"
              >
                <div className="mb-1 flex justify-center">
                  <IconUpload className="size-6 text-secondary-6000 group-hover:text-primary-700" />
                </div>
                <span className="group-hover:text-primary-700 group-hover:underline">
                  {name} {translate("uploadButtonText")}
                </span>
                <input
                  id={`file-upload-${docType}`}
                  name={`file-upload-${docType}`}
                  type="file"
                  className="sr-only"
                  accept="image/jpeg, image/png, application/pdf"
                  multiple={true}
                  onChange={handleFileChange}
                />
              </label>
              {/* <p className="pl-1">or drag and drop</p> */}
            </div>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              PDF or Image
            </p>
            <div className="mt-4 font-semibold">{renderFileNames()}</div>
            <div className="mt-4 space-y-3">{renderSortedFileNames()}</div>
            {loading && (
              <div className="mt-4 flex justify-center">
                <LoadingSpinner />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiplePetTaxiFiles;
