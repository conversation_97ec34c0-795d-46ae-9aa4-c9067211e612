"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Smile } from "lucide-react";
import EmojiPicker from "emoji-picker-react";
import { Button } from "@/components/ui/button";
import { useSelector, useDispatch } from "react-redux";
import type { RootState } from "@/store";
import { setText } from "@/store/features/liveChat/live-chat-slice";
import { useWindowSize } from "react-use";

interface EmojiContainerProps {
  emojiOpen: boolean;
  setEmojiOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const EmojiContainer: FC<EmojiContainerProps> = ({
  emojiOpen,
  setEmojiOpen,
}) => {
  const dispatch = useDispatch();
  const [mobile, setMobile] = useState<boolean>(false);
  const textMessage = useSelector(
    (state: RootState) => state.liveChatText.text
  );
  const windowWidth = useWindowSize().width;

  useEffect(() => {
    if (windowWidth > 767) {
      setMobile(false);
    } else {
      setMobile(true);
    }
  }, [windowWidth]);
  return (
    <>
      {mobile ? (
        <Drawer open={emojiOpen} onOpenChange={setEmojiOpen}>
          <DrawerTrigger className="absolute right-2 top-5 text-neutral-800 dark:text-neutral-200">
            <Smile className="size-5 duration-200" />
          </DrawerTrigger>
          <DrawerContent className="px-2">
            <DrawerHeader>
              <DrawerTitle className="sr-only"></DrawerTitle>
              <DrawerDescription className="sr-only"></DrawerDescription>
            </DrawerHeader>
            <EmojiPicker
              autoFocusSearch={false}
              onEmojiClick={(emojiData) => {
                dispatch(setText(textMessage + emojiData.emoji));
                setEmojiOpen(false);
              }}
              className="!w-full"
            />
            <DrawerFooter>
              <DrawerClose asChild>
                <Button variant="outline">Kapat</Button>
              </DrawerClose>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      ) : (
        <Popover open={emojiOpen} onOpenChange={setEmojiOpen}>
          <PopoverTrigger className="absolute right-2 top-5 text-neutral-800 dark:text-neutral-200">
            <Smile className="size-5 hover:text-blue-700 duration-200" />
          </PopoverTrigger>
          <PopoverContent
            side="top"
            align="start"
            alignOffset={-330}
            className="w-[350px] p-0 border-none"
          >
            <EmojiPicker
              autoFocusSearch={false}
              onEmojiClick={(emojiData) => {
                dispatch(setText(textMessage + emojiData.emoji));
                setEmojiOpen(false);
              }}
            />
          </PopoverContent>
        </Popover>
      )}
    </>
  );
};

export default EmojiContainer;
