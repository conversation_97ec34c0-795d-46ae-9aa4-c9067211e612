"use client";
import React from "react";
import type { <PERSON> } from "react";
import MobileCheckoutDrawer from "./MobileCheckoutDrawer";

interface MobileFooterCheckoutProps {
  selectedTier: any;
}

const MobileFooterCheckout: FC<MobileFooterCheckoutProps> = ({
  selectedTier,
}) => {
  return (
    <div className="fixed inset-x-0 bottom-0 z-10 block border-t border-neutral-200 bg-white dark:border-neutral-6000 dark:bg-neutral-800 pb-3 lg:hidden">
      <div className="block text-sm font-medium container">
        {selectedTier && (
          <>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-1">
                <span className="text-sm font-medium">Toplam:</span>
                <span className="text-lg font-semibold">
                  {new Intl.NumberFormat("tr-TR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(Number(selectedTier?.price)) + "₺"}
                </span>
              </div>
              <MobileCheckoutDrawer selectedTier={selectedTier} />
            </div>
            <div className="flex flex-col font-semibold xl:text-lg underline capitalize">
              PawBooking {selectedTier?.tierId} Üyelik -{" "}
              {selectedTier?.frequency} abonelik
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MobileFooterCheckout;
