"use client";
import type { FC } from "react";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSelector, useDispatch } from "react-redux";
import type { RootState } from "@/store";
import { setSelectedRoomId } from "@/store/features/hotelCalendar/calendar-slice";
import { useRouter } from "next/navigation";

interface RoomSelectProps {
  roomGroupData: any;
}

const RoomSelect: FC<RoomSelectProps> = ({ roomGroupData }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const selectedRoomGroupId = useSelector(
    (state: RootState) => state.hotelCalendar.selectedRoomGroupId
  );
  const selectedRoomId = useSelector(
    (state: RootState) => state.hotelCalendar.selectedRoomId
  );
  const filteredRoomGroup = roomGroupData.find(
    (room: any) => room._id === selectedRoomGroupId
  );

  return (
    <Select
      onValueChange={(selectedRoom) => {
        dispatch(setSelectedRoomId(selectedRoom));
        router.push(
          `/account/calendar?selectedRoom=${selectedRoom}&selectedRoomGroup=${selectedRoomGroupId}`
        );
      }}
      defaultValue={selectedRoomId || filteredRoomGroup.rooms[0].roomId}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Oda Seç" />
      </SelectTrigger>
      <SelectContent>
        {filteredRoomGroup.rooms.map((room: any) => {
          return (
            <SelectItem key={room._id} value={room._id}>
              {room.roomName}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};

export default RoomSelect;
