import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { PET_TAXI_API_PATHS } from "@/utils/apiUrls";
import { usePetTaxiInformationsActions } from "../taxi/useTaxiInformations";

export const useCancellationPolicyTaxi = () => {
  const { toast } = useToast();
  const { handlePetTaxiStatusUpdate } = usePetTaxiInformationsActions();

  const addCancellationPolicyInformationHandler = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    cancellationPolicyInformations: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.policy, {
          method: "POST",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(cancellationPolicyInformations),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 4000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 4000,
          title: "İptal Politikası Ekleme",
          description: "İptal Politikası başarıyla eklendi.",
        });
        await handlePetTaxiStatusUpdate(
          event,
          petTaxiToken,
          "inReview",
          setLoading
        );
      } catch (error) {
        console.log(error);
        setLoading(false);
      }
    }
  };

  const updateCancellationPolicyInformationHandler = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    updatedCancellationPolicyInfo: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(PET_TAXI_API_PATHS.policy, {
          method: "PUT",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedCancellationPolicyInfo),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 4000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 4000,
          title: "İptal Politikasi Güncelleme",
          description: "İptal Politikasi başarıyla güncellendi.",
        });
        revalidatePathHandler("/petTaxi/account/policy");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const deleteCancellationPolicyInformationHandler = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    cancellationPolicyID: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeCancellationPolicyModal: () => void
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      try {
        const response = await fetch(`${PET_TAXI_API_PATHS.policy}`, {
          method: "DELETE",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 4000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 4000,
          title: "İptal Politikası Silme",
          description: "İptal Politikası başarıyla silindi.",
        });
        revalidatePathHandler("/petTaxi/account/policy");
        closeCancellationPolicyModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    addCancellationPolicyInformationHandler,
    deleteCancellationPolicyInformationHandler,
    updateCancellationPolicyInformationHandler,
  };
};
