"use client";
import type { FC } from "react";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Image, { StaticImageData } from "next/image";
import Axess from "@/images/creditCardLogo/Axess.png";
import CardFinans from "@/images/creditCardLogo/CardFinans.png";
import Maximum from "@/images/creditCardLogo/Maximum.png";
import Paraf from "@/images/creditCardLogo/Paraf.png";
import World from "@/images/creditCardLogo/World.png";
import SaglamKart from "@/images/creditCardLogo/SaglamKart.png";
import Param from "@/images/creditCardLogo/Param.png";
import Combo from "@/images/creditCardLogo/Combo.png";
import ISTANBULKART from "@/images/creditCardLogo/ISTANBULKART.png";

interface InstallmentRatesProps {
  installmentRatesData: any;
  className?: string;
}

const cardInfo: Record<
  string,
  { logo: StaticImageData; color: string; width: string }
> = {
  Axess: {
    logo: Axess,
    color: "bg-yellow-100 border-yellow-400",
    width: "w-16",
  },
  CardFinans: {
    logo: CardFinans,
    color: "bg-blue-100 border-blue-400",
    width: "w-24",
  },
  Combo: {
    logo: Combo,
    color: "bg-green-100 border-green-400",
    width: "w-20",
  },
  ISTANBULKART: {
    logo: ISTANBULKART,
    color: "bg-orange-100 border-orange-400",
    width: "w-20",
  },
  Maximum: { logo: Maximum, color: "bg-red-100 border-red-400", width: "w-20" },
  Paraf: { logo: Paraf, color: "bg-cyan-100 border-cyan-400", width: "w-16" },
  Param: { logo: Param, color: "bg-gray-100 border-gray-400", width: "w-20" },
  SaglamKart: {
    logo: SaglamKart,
    color: "bg-purple-100 border-purple-400",
    width: "w-16",
  },
  World: {
    logo: World,
    color: "bg-indigo-100 border-indigo-400",
    width: "w-20",
  },
};

const InstallmentRates: FC<InstallmentRatesProps> = ({
  installmentRatesData,
  className = "",
}) => {
  const [isCustomerOpen, setIsCustomerOpen] = useState(false);

  const monthLabels = [
    "01",
    "02",
    "03",
    "04",
    "05",
    "06",
    "07",
    "08",
    "09",
    "10",
    "11",
    "12",
  ];

  return (
    <div>
      <div className="flex justify-start">
        <div
          onClick={() => setIsCustomerOpen(true)}
          className={`text-blue-400 normal-case underline cursor-pointer ${className}`}
        >
          Taksit oranlarını görüntüle
        </div>
      </div>
      <Dialog open={isCustomerOpen} onOpenChange={setIsCustomerOpen}>
        <DialogContent
          onInteractOutside={() => setIsCustomerOpen(false)}
          className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto"
        >
          <DialogHeader>
            <DialogTitle>Taksit Oranları</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {installmentRatesData
              ?.filter(
                ({ key }: any) =>
                  !["Unknown", "ParamAcquirer"].includes(key.cardName)
              )
              .map(({ key, value }: any) => {
                const cardName = key.cardName;
                if (!value || Object.keys(value).length === 0) return null;

                const card = cardInfo[cardName] || {
                  logo: "",
                  color: "bg-white border-gray-300",
                };

                return (
                  <div
                    key={cardName}
                    className={`rounded-xl border p-4 ${card.color} shadow-sm`}
                  >
                    <div className="flex items-center mb-5">
                      <Image
                        src={card.logo}
                        alt={cardName}
                        width={80}
                        height={80}
                        className={`${card.width} h-auto mr-3 object-contain`}
                      />
                    </div>
                    <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-12 gap-2 text-sm">
                      {monthLabels.map((month) => (
                        <div
                          key={month}
                          className="bg-white rounded p-2 border text-center"
                        >
                          <div className="font-bold">{parseInt(month)} Ay</div>
                          <div className="text-sm text-gray-700">
                            {value[month]?.toFixed(2) ?? "-"}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InstallmentRates;
