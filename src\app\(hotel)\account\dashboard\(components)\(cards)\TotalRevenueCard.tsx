import type { FC } from "react";
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import IconChart from "@/shared/icons/Chart";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TotalRevenueCardProps {
  title?: string;
  data: {
    totalRevenue: number;
  };
}

const TotalRevenueCard: FC<TotalRevenueCardProps> = ({
  title = "Title",
  data,
}) => {
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger className="cursor-default max-lg:shrink-0 md:basis-1/4">
            <Card className="px-3 pb-1 border-secondary-6000 dark:border-neutral-400">
              <CardHeader className="px-2 pt-2 pb-0">
                <CardTitle className="mb-3 flex items-center justify-between gap-1.5 text-xs sm:text-sm font-medium text-neutral-500 dark:text-neutral-400 lg:text-base">
                  {title}
                  <div className="rounded-md bg-secondary-6000 dark:bg-neutral-400 p-1">
                    <IconChart className="size-5 text-white" />
                  </div>
                </CardTitle>
                {/* <CardDescription>Card Description</CardDescription> */}
              </CardHeader>
              <CardContent className="text-start font-medium lg:text-xl pt-0 pb-1 px-2">
                <p>
                  {new Intl.NumberFormat("tr-TR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(Number(data?.totalRevenue)) + " ₺"}
                </p>
              </CardContent>
              {/* <CardFooter>
        <p>Card Footer</p>
      </CardFooter> */}
            </Card>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">
              Seçili tarihler arasındaki toplam gelir
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

export default TotalRevenueCard;
