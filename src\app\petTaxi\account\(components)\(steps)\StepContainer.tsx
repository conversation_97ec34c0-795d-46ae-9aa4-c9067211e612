import React from "react";
import type { FC } from "react";
import FileStep from "./(file)/FileStep";
import BillingStep from "./(billing)/BillingStep";
import PolicyStep from "./(policy)/PolicyStep";
import PetTaxiPetTypes from "../../(business-informations)/pet-taxi-informations/(components)/(petTaxiPetTypes)/PetTaxiPetTypes";
import PetTaxiFeatures from "../../(business-informations)/pet-taxi-informations/(components)/(petTaxiFeatures)/PetTaxiFeatures";
import IconCheck from "@/shared/icons/Check";
import HotelSuccessImage from "@/components/HotelSuccessImage";
import PetTaxiPhotosContainer from "../../(business-informations)/pet-taxi-informations/(components)/(petTaxiPhotos)/PetTaxiPhotosContainer";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
interface StepContainerProps {
  petTaxiToken: string | undefined;
  taxiData: PetTaxiDataApiTypes;
  checkSubMerchant: boolean;
}

const StepContainer: FC<StepContainerProps> = ({
  petTaxiToken,
  taxiData,
  checkSubMerchant,
}) => {
  const stepHandler = (taxiData: PetTaxiDataApiTypes) => {
    if (taxiData?.files?.length < 5) return 1;

    if (!checkSubMerchant) return 2;

    if (taxiData?.acceptedPetTypes?.length === 0) return 3;

    if (taxiData?.petTaxiFeatures?.length === 0) return 4;

    if (taxiData?.images?.length === 0) return 5;

    if (!taxiData?.policy?.serviceHoursEnd) return 6;

    return 8;
  };

  const stepValue = stepHandler(taxiData);

  return (
    <div className="md:mt-10">
      {stepValue === 1 && (
        <FileStep taxiData={taxiData} petTaxiToken={petTaxiToken} />
      )}
      {stepValue === 2 && <BillingStep petTaxiToken={petTaxiToken} />}
      {stepValue === 3 && (
        <PetTaxiPetTypes taxiData={taxiData} petTaxiToken={petTaxiToken} />
      )}
      {stepValue === 4 && (
        <PetTaxiFeatures taxiData={taxiData} petTaxiToken={petTaxiToken} />
      )}
      {stepValue === 5 && (
        <PetTaxiPhotosContainer
          taxiData={taxiData}
          petTaxiToken={petTaxiToken}
        />
      )}
      {stepValue === 6 && <PolicyStep petTaxiToken={petTaxiToken} />}
      {stepValue === 8 && (
        <div className="flex flex-col justify-center items-center gap-5 text-center font-medium text-lg">
          <div className="flex items-center relative">
            <HotelSuccessImage className="size-60" />
            <IconCheck className="size-12 rounded-full bg-green-500 p-2 text-white absolute -right-7 bottom-5" />
          </div>
          <p>
            Tebrikler! Pet Taksi kaydınızı başarıyla oluşturdunuz. Kayıt
            bilgileriniz şu anda onay sürecindedir. Danışmanımız en kısa sürede
            sizinle iletişime geçerek süreci tamamlamanız için gerekli
            yönlendirmeleri yapacaktır. İlginiz için teşekkür ederiz.
          </p>
        </div>
      )}
    </div>
  );
};

export default StepContainer;
