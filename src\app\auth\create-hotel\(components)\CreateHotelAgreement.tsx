import React from "react";

const CreateHotelAgreement = ({ hotel }: { hotel: any }) => {
  return (
    <div>
      <p className="text-sm">
        Bu sözleşme, bir tarafta merkezi BAŞPINAR(ORGANİZE)OSB MAH. O.S.B
        2.BÖLGE 83235 NOLU CAD. NO: 8 İÇ KAPI NO: 1 ŞEHİTKAMİL / GAZİANTEP
        adresinde bulunan Pawbooking Teknoloji A.Ş. (bundan sonra “Pawbooking”
        olarak anılacaktır) ile diğer tarafta merkezi{" "}
        {hotel?.district && hotel?.cityName && hotel?.citySubdivisionName
          ? hotel?.district +
            " " +
            hotel?.streetName +
            " " +
            hotel?.buildingName +
            " " +
            hotel?.buildingNumber +
            " " +
            hotel?.citySubdivisionName +
            "/" +
            hotel?.cityName
          : "[pet hotel adresi]"}{" "}
        adresinde bulunan{" "}
        {hotel?.hotelName ? hotel?.hotelName : "[pet hotel adı]"} (bundan sonra
        “Pet Hotel” olarak anılacaktır) arasında aşağıda belirtilen şartlar
        çerçevesinde yapılmıştır.
      </p>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          1. Taraflar
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Bu sözleşme, Pawbooking ile Pet Hotel arasında pet sahiplerine
            yönelik hizmetlerin sunulması ve rezervasyon yönetimi süreçlerini
            düzenlemek amacıyla akdedilmiştir.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          2. Sözleşmenin Konusu
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Bu sözleşme, Pet Hotel’in Pawbooking platformu aracılığıyla sunmuş
            olduğu hizmetleri, rezervasyon süreçlerini ve tarafların hak ve
            yükümlülüklerini belirler. Pawbooking, pet sahipleri için en uygun
            otel seçeneklerini sunar ve bu hizmetlerin rezervasyonunu sağlar.
            Pet Hotel, platformda listelenen hizmetlerini uygun koşullarda
            eksiksiz bir şekilde sunmayı taahhüt eder.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          3. Pawbooking'in Yükümlülükleri
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Pawbooking, platform aracılığıyla Pet Hotel’in sunduğu hizmetlerin
            görünürlüğünü artırmak ve pet sahiplerine ulaşılabilir kılmak için
            gerekli yazılım, teknik altyapı ve pazarlama desteğini sağlar.
          </li>
          <li className="text-sm">
            Pawbooking, pet sahipleri ile Pet Hotel arasındaki rezervasyon
            sürecinin yönetilmesi, iptallerin işlenmesi ve ödemenin güvenli bir
            şekilde alınmasını sağlar.
          </li>
          <li className="text-sm">
            Pawbooking, pet sahiplerinin talep ve şikayetlerini göz önünde
            bulundurarak Pet Hotel ile iletişim sağlar ve gerektiğinde çözüm
            odaklı destek sunar.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          4. Pet Hotel'in Yükümlülükleri
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Pet Hotel, Pawbooking platformunda sunmuş olduğu hizmetlerin
            doğruluğunu, hizmet kalitesini ve hijyen standartlarını sağlamayı
            kabul eder.
          </li>
          <li className="text-sm">
            Pet Hotel, Tarım ve Orman Bakanlığı tarafından belirlenen barınma
            şartlarına ve hayvan refahına dair yasal düzenlemelere uygun olarak
            hizmet verir. Petlerin beslenme değerleri ve sağlıklı koşullarda
            bakımları için gerekli önlemleri alır.
          </li>
          <li className="text-sm">
            Pet Hotel, petlerin beslenmesinde kaliteli ve dengeli mamalar
            kullanmayı taahhüt eder. Beslenme planları Tarım ve Orman
            Bakanlığı’nın belirlediği standartlara uygun olmalıdır.
          </li>
          <li className="text-sm">
            Pet Hotel, yükümlülüklerini eksiksiz olarak yerine getireceğini
            kabul, beyan ve taahhüt eder.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          5. Pet Çiftleştirilmesi ve Sağlık Önlemleri
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Pet Hotel, petlerin rızası olmaksızın çiftleştirilmesine izin
            veremez ve bu konuda gerekli önlemleri almakla yükümlüdür.
            Çiftleştirme yalnızca pet sahiplerinin onayı ve yasal çerçeve
            içerisinde yapılabilir. Pet hotel bu hususu kabul, beyan ve taahhüt
            eder.
          </li>
          <li className="text-sm">
            Pet Hotel, konaklama süresince petlerin sağlığı ile ilgili tüm
            önlemleri almak zorundadır. Gerekli aşılar ve veteriner
            kontrollerinin yapılmış olması sağlanmalıdır. Pet hotel bu hususu
            kabul, beyan ve taahhüt eder.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          6. Ödemeler ve Gelir Dağılımı
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Pet sahipleri tarafından Pawbooking platformu aracılığıyla yapılan
            rezervasyonların ödemeleri, Pawbooking tarafından tahsil edilecek ve
            ilgili komisyonlar düşüldükten sonra Pet Hotel’e aktarılacaktır.
          </li>
          <li className="text-sm">
            Taraflar, Pawbooking’in hizmet bedeli olarak toplam rezervasyon
            bedelinin %18’ini komisyon olarak alacağını kabul ederler. Kalan
            tutar, hizmetin tamamlanmasını takiben Pet Hotel’in belirttiği banka
            hesabına 14 gün içinde aktarılacaktır.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          7. İptaller ve Geri Ödemeler
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Pet Hotel, pet sahiplerinin yapmış olduğu rezervasyon iptalleri için
            belirlenen iptal politikasına uymak zorundadır. Rezervasyon iptali
            durumunda pet sahibine geri ödeme yapılması hususunda Pawbooking’in
            belirlediği şartlar geçerli olacaktır.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          8. Sözleşmenin Feshi
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Taraflardan herhangi biri, diğer tarafın sözleşmeye aykırı hareket
            etmesi durumunda yazılı olarak bildirimde bulunarak sözleşmeyi
            feshedebilir. Yazılı başvurular için taraflar yukarıda verilen
            adreslerin doğruluğundan sorumludur. Adresin değişmesi durumunda bu
            husus karşı tarafa ivedi olarak bildirilecektir.
          </li>
          <li className="text-sm">
            Fesih durumunda tarafların fesih tarihine kadar olan tüm
            yükümlülükleri devam eder.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          9. Yetkili Mahkeme ve Hukuk
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Bu sözleşmeden doğan uyuşmazlıklarda Türkiye Cumhuriyeti kanunları
            geçerli olacak olup, Gaziantep Mahkemeleri ve İcra Daireleri
            yetkilidir.
          </li>
        </ul>
      </div>
      <div>
        <h3 className="mb-3 mt-5 text-lg font-semibold underline md:text-xl">
          10. Gizlilik
        </h3>
        <ul className="list-disc space-y-2 pl-2.5">
          <li className="text-sm">
            Taraflar, sözleşme süresi boyunca elde edilen her türlü ticari
            bilgi, müşteri bilgileri ve iş modellerini gizli tutmayı kabul eder.
          </li>
          <li className="text-sm">
            Pet hotel, edinmiş olduğu her türlü müşteri bilgilerini 6698 Sayılı
            Kişisel Verilerin Korunması Kanununa uygun şekilde hareket
            edeceğini, bu bilgileri 3. Kişiler ile paylaşmayacağını, yurt dışına
            aktarmayacağını kabul, beyan ve taahhüt eder.
          </li>
        </ul>
      </div>
      <div className="mt-5">
        <p className="text-sm mb-3">
          İşbu sözleşme 10 madde ve 4 nüshadan oluşmuş olup, taraflarca imza
          altına alınarak bu tarih itibari ile geçerlilik kazanacaktır.
        </p>
        <div className="mb-3 text-sm space-y-2">
          <p>PAWBOOKING TEKNOLOJİ A.Ş.</p>
          <p>İsim:</p>
          <p>Tarih:</p>
          <p>İmza:</p>
        </div>
        <div className="mb-3 text-sm space-y-2">
          <p className="capitalize">
            {hotel?.hotelName ? hotel?.hotelName : "[PET HOTEL ADI]"}
          </p>
          <p>İsim:</p>
          <p>Tarih:</p>
          <p>İmza:</p>
        </div>
      </div>
    </div>
  );
};

export default CreateHotelAgreement;
