import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setCalculatedRoomData } from "@/store/features/calculatedRoomData/calculated-room-data-slice";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import type { Route } from "@/routers/types";

export const useHotelAtDoorReservation = () => {
  const { toast } = useToast();
  const router = useRouter();
  const dispatch = useDispatch();

  const addItemToCart = async (
    hotelToken: string | undefined,
    requestBody: any,
    setLoading?: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal?: () => void,
    setDisabled?: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading?.(true);
    setDisabled?.(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.addItemToCart, {
        method: "POST",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        closeModal?.();
        setDisabled?.(false);
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 3000,
        // title: "Ürün Ekleme",
        description: "Ürün başarıyla eklendi.",
      });
      closeModal?.();
      dispatch(setCalculatedRoomData(data.data));
      return data;
    } catch (error) {
      console.log(error);
    }
  };

  const removeItemFromCart = async (
    removedItemId: string,
    selectedItems: string[],
    totalOrderPrice: number,
    itemType: string,
    filteredId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.removeItemFromCart, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          removedItemId: removedItemId,
          selectedItems: selectedItems,
          totalOrderPrice: totalOrderPrice,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        setIsModalOpen(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      setIsModalOpen(false);

      dispatch(setCalculatedRoomData(data.data));
      return data;
    } catch (error) {
      console.log(error);
    }
  };

  const userFirstReservationDiscount = async (
    hotelToken: string | undefined,
    reservationId: string,
    orderId: string
  ) => {
    if (!hotelToken || !reservationId || !orderId) return;

    try {
      const response = await fetch(HOTEL_API_PATHS.firstReservationDiscount, {
        method: "POST",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reservationId: reservationId,
          orderId: orderId,
        }),
      });
      const data = await response.json();

      // if (!response.ok || !data.success) {
      //   const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
      //   toast({
      //     variant: "error",
      //     duration: 3000,
      //     // title: "Hata",
      //     description: `${errorMessage}`,
      //   });

      //   throw new Error("Network response was not ok");
      // }

      return data;
    } catch (error) {
      console.log(error);
    }
  };

  const createOrder = async (
    hotelToken: string | undefined,
    selectedItems: string[],
    totalOrderPrice: number,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.createOrder, {
        method: "POST",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          selectedItems: selectedItems,
          totalOrderPrice: totalOrderPrice,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }
      await userFirstReservationDiscount(
        hotelToken,
        data?.data?.order?.reservations[0],
        data?.data?.order?._id
      );
      router.push(`/checkout?orderId=${data.data.order._id}`);
      return data;
    } catch (error) {
      console.log(error);
    }
  };

  const addItemToOrder = async (
    hotelToken: string | undefined,
    requestBody: any,
    orderItemType: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setLoading(true);
    setDisabled(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.addItemToOrder, {
        method: "POST",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        closeModal();
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      toast({
        variant: "success",
        duration: 3000,
        // title: "Hizmet Ekleme",
        description: "Hizmet başarıyla eklendi.",
      });
      if (orderItemType === "service") {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
      revalidatePathHandler("/checkout");
      closeModal();
    } catch (error) {
      console.log(error);
    }
  };

  const removeItemFromOrder = async (
    event: FormEvent,
    hotelToken: string | undefined,
    orderId: string,
    itemType: string,
    itemId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    petId?: string
  ) => {
    event.preventDefault();

    setLoading(true);
    setDisabled(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.removeItemFromOrder, {
        method: "DELETE",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId: orderId,
          itemType: itemType,
          itemId: itemId,
        }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        closeModal();
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      closeModal();
      setTimeout(() => {
        setDisabled(false);
      }, 2000);
      if (itemType === "service" && petId) {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
      toast({
        variant: "success",
        duration: 3000,
        // title: "Hizmet Kaldırma",
        description: "Hizmet başarıyla kaldırıldı.",
      });
      revalidatePathHandler("/checkout");
    } catch (error) {
      console.log(error);
    }
  };

  const updateOrder = async ({
    hotelToken,
    requestBody,
    setLoading,
  }: {
    hotelToken?: string | undefined;
    requestBody: any;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  }) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.updateOrder, {
        method: "PUT",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      revalidatePathHandler("/checkout");
    } catch (error) {
      console.log(error);
    }
  };

  const updateReservation = async ({
    updates,
    setLoading,
    hotelToken,
    route,
  }: {
    updates: any;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
    hotelToken?: string | undefined;
    route: string;
  }) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.updateReservation, {
        method: "PUT",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      router.push(route as Route);
    } catch (error) {
      console.log(error);
    }
  };
  const updateService = async ({
    updates,
    setLoading,
    hotelToken,
    route,
  }: {
    updates: any;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
    hotelToken?: string | undefined;
    route: string;
  }) => {
    setLoading(true);
    try {
      const response = await fetch(HOTEL_API_PATHS.updateService, {
        method: "PUT",
        headers: {
          ...(hotelToken && { hotelToken: hotelToken }),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      router.push(route as Route);
    } catch (error) {
      console.log(error);
    }
  };

  return {
    addItemToCart,
    removeItemFromCart,
    createOrder,
    addItemToOrder,
    removeItemFromOrder,
    updateOrder,
    updateReservation,
    updateService,
  };
};
