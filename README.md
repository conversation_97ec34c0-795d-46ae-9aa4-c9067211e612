# :paw_prints:

## PawHoof

### pet hotel management software

This project creating for pet hotel organization app to research and book

## Features

- List all otels on home page.
- create user / otel / room / review
- filter rooms and hotels
- login acording to role user/otel_manager
- notification and reservation for otel manager
- add favorite room for user
- purchasing and booking for user
- review from user
- create animal for user

## Tech

used thoose techs for server :

- Node.js
- Express.js
- MongoDB
- Docker

for client :

- Next.js 14
- Redux Toolkit
- TailwindCss

## Installation

Clone project your local

```sh
git clone ...
```

Install the dependencies and devDependencies and start the yarn.

```sh
yarn install
yarn dev
```

## View

to view project last version click this
url : <a target="_blank" href="">for ... follow this</a>

### Commits Explanation

git commit -m "feature detail. :tada: @ardaninsaturnu #master"

- :rainbow: for style
- :hammer: for bug fixies
- :dna: for merging
- :tada: for new features
