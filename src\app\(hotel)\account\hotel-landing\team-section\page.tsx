import React from "react";
import { cookies } from "next/headers";
import getHotelLanding from "@/actions/(protected)/hotel/hotelLanding/getHotelLanding";
import TeamSections from "../(components)/(teamSection)/TeamSections";
import { redirect } from "next/navigation";

const TeamSectionPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelLandingData = await getHotelLanding();

  if (!hotelLandingData) {
    redirect("/account/hotel-landing");
  }

  return (
    <TeamSections
      hotelToken={hotelToken}
      teamSectionData={hotelLandingData?.data?.data?.teamSection}
    />
  );
};

export default TeamSectionPage;
