// Checks if the given `pathname` includes any of the paths specified in the `paths` array.

// @param {string | null} pathname - The current URL pathname. Can be null if unavailable.
// @param {string[]} paths - An array of string paths to check against the `pathname`.
// @returns {boolean} - Returns `true` if any path in the `paths` array is found in the `pathname`, otherwise `false`.

// Example:
// const result = isMatchingPaths('/checkout', ['/checkout', '/success']);
// console.log(result); // true

export const isMatchingPaths = (pathname: string | null, paths: string[]) => {
  return paths.some((path) => pathname?.includes(path));
};
