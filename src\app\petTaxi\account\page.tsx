import React from "react";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import StepContainer from "./(components)/(steps)/StepContainer";
import StepNav from "./(components)/StepNav";
import getCheckTaxiSubMerchant from "@/actions/(protected)/taxi/getCheckTaxiSubMerchant";

const TaxiTodayPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();
  const checkSubMerchant = await getCheckTaxiSubMerchant();

  return (
    <div className="container">
      {taxiData?.data?.status === "approved" ? (
        <div></div>
      ) : (
        <>
          {taxiData?.data?.status !== "inReview" && (
            <StepNav
              taxiData={taxiData?.data}
              checkSubMerchant={checkSubMerchant?.data?.exists}
            />
          )}
          <StepContainer
            taxiData={taxiData?.data}
            petTaxiToken={petTaxiToken}
            checkSubMerchant={checkSubMerchant?.data?.exists}
          />
        </>
      )}
    </div>
  );
};

export default TaxiTodayPage;
