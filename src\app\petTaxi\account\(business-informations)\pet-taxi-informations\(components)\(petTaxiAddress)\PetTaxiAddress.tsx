"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { usePetTaxiInformationsActions } from "@/hooks/taxi/useTaxiInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import type { PetTaxiAddressApiTypes } from "@/types/taxi/taxiInformationType";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";

interface PetTaxiAddressProps {
  taxiData: PetTaxiDataApiTypes;
  petTaxiToken: string | undefined;
}

const PetTaxiAddress: FC<PetTaxiAddressProps> = ({
  taxiData,
  petTaxiToken,
}) => {
  const translate = useTranslations("HotelAddress");
  const initialData = {
    cityName: taxiData.address.cityName,
    region: taxiData.address.region,
    district: taxiData.address.district,
    streetName: taxiData.address.streetName,
    buildingName: taxiData.address.buildingName,
    buildingNumber: taxiData.address.buildingNumber,
    postalZone: taxiData.address.postalZone,
  };
  const [petTaxiAddress, setPetTaxiAddress] =
    useState<PetTaxiAddressApiTypes>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handlePetTaxiAddress } = usePetTaxiInformationsActions();

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    setPetTaxiAddress((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  return (
    <form
      onSubmit={(event) => {
        handlePetTaxiAddress(
          event,
          petTaxiToken,
          petTaxiAddress,
          setLoading,
          setDisabled
        );
      }}
    >
      <div className="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-5">
        {Object.entries(petTaxiAddress).map(([key, value]) => {
          return (
            <FormItem key={key} label={translate(key)}>
              <Input name={key} onChange={handleChange} value={value} />
            </FormItem>
          );
        })}
      </div>
      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            disabled ||
            JSON.stringify(initialData) === JSON.stringify(petTaxiAddress)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </form>
  );
};

export default PetTaxiAddress;
