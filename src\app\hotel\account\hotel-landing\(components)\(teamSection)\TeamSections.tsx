"use client";
import type { FC } from "react";
import React, { useState } from "react";
import Input from "@/shared/Input";
import LoadingSpinner from "@/shared/icons/Spinner";
import FormItem from "@/shared/FormItem";
import AddTeamMemberModal from "./AddTeamMemberModal";
import type { TeamSectionType } from "@/types/hotel/hotelLandingType";
import Textarea from "@/shared/Textarea";
import TeamMemberPhotos from "./TeamMemberPhotos";
import { Button } from "@/components/ui/button";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

export interface TeamSectionProps {
  hotelToken: string | undefined;
  teamSectionData: TeamSectionType;
}

const TeamSections: FC<TeamSectionProps> = ({
  hotelToken,
  teamSectionData,
}) => {
  const initialData = {
    title: teamSectionData?.title || "",
    description: teamSectionData?.description || "",
    teamMembers: teamSectionData?.teamMembers || [],
  };
  const { uploadTeamSectionHandler } = useHotelLandingActions();
  const [sectionData, setSectionData] = useState<TeamSectionType>(initialData);
  const [loading, setLoading] = useState<boolean>(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setSectionData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="listingSection__wrap">
      <h2 className="text-2xl font-semibold">Takımlar</h2>
      <div className="w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      {!teamSectionData?.title && !teamSectionData?.description && (
        <form
          onSubmit={(event) => {
            uploadTeamSectionHandler(
              event,
              sectionData,
              hotelToken,
              setLoading
            );
          }}
        >
          <FormItem label="Takım Adı" className="mt-5">
            <Input
              type="text"
              name="title"
              className="mt-1.5"
              value={sectionData.title || ""}
              onChange={handleChange}
            />
          </FormItem>
          <FormItem label="Açıklama" className="mt-5">
            <Textarea
              name="description"
              className="mt-1.5"
              value={sectionData.description || ""}
              onChange={handleChange}
            />
          </FormItem>
          <div className="mt-5 flex justify-end pt-2">
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700"
              disabled={
                JSON.stringify(initialData) === JSON.stringify(sectionData)
              }
              type="submit"
            >
              {loading ? <LoadingSpinner /> : "Kaydet"}
            </Button>
          </div>
        </form>
      )}

      <div className="flex justify-end">
        <AddTeamMemberModal
          teamSectionData={sectionData}
          hotelToken={hotelToken}
        />
      </div>
      <div>
        <TeamMemberPhotos
          teamMemberData={teamSectionData?.teamMembers || []}
          hotelToken={hotelToken}
        />
      </div>
    </div>
  );
};

export default TeamSections;
