"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import convertOneDateToString from "@/utils/convertOneDateToString";
import { datePickerLanguageHandler } from "@/utils/datePickerLanguageHandler";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { stepHandler } from "../atDoorReservationsContainer";
import FooterLoggedInButtons from "./FooterButtons";
import { getSelectedPetIds } from "../atDoorReservationsContainer";
import IconChevronUp from "@/shared/icons/ChevronUp";
import FooterReservationDetail from "./FooterReservationDetail";
import { PawPrint } from "lucide-react";

interface FooterReservationProps {
  orderData: any;
  addPetParam: string;
  hotelToken: string | undefined;
  stepParams: {
    customer: string;
    pet: string;
    summary: string;
    payment: string;
  };
  hotelCustomerId: string;
}

const FooterReservation: FC<FooterReservationProps> = ({
  orderData,
  addPetParam,
  hotelToken,
  stepParams,
  hotelCustomerId,
}) => {
  const dispatch = useDispatch();
  const datePickerLanguage = datePickerLanguageHandler("tr");
  const [checkoutDetail, setCheckoutDetail] = useState<boolean>(false);

  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const formattedStartDate = convertOneDateToString(
    orderData?.reservations[0]?.startDate,
    false,
    datePickerLanguage.firstValue
  );
  const formattedEndDate = convertOneDateToString(
    orderData?.reservations[0]?.endDate,
    false,
    datePickerLanguage.firstValue
  );

  const buttonValue = stepHandler(stepParams);

  //maps through the checkoutData.pet array and creates a new array reservationPetData,
  //where each pet item is paired with the corresponding reservation ID from orderData, along with its owner data
  const reservationPetData = checkoutData?.pet?.map((petItem, index) => ({
    reservationId: orderData?.reservations[index]?._id,
    data: {
      hotelCustomer: checkoutData.hotelCustomer,
      hotelPet: petItem,
    },
  }));

  const servicePetData = checkoutData?.servicePet?.map((petItem, index) => ({
    serviceId: orderData?.services[index]?._id,
    data: {
      hotelCustomer: checkoutData.hotelCustomer,
      hotelPet: petItem,
    },
  }));

  // Sets checkout data on page reload if order has service and reservation pet data
  useEffect(() => {
    // Creates an array of selected pet IDs from services, filtering out any null or undefined values
    const serviceSelectedPetArray = getSelectedPetIds(orderData?.services);

    // Creates an array of selected pet IDs from reservations, filtering out any null or undefined values
    const reservationSelectedPetArray = getSelectedPetIds(
      orderData?.reservations
    );

    dispatch(
      setCheckoutData({
        ...checkoutData,
        hotelCustomer: hotelCustomerId,
        servicePet: serviceSelectedPetArray,
        pet: reservationSelectedPetArray,
      })
    );
  }, []);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  if (addPetParam) return null;

  return (
    <div className={`fixed inset-x-0 bottom-0 z-10 block bg-[#3A3F51] py-3`}>
      <div className="container">
        <div className="block relative h-full">
          <PawPrint
            className={`max-xl:hidden ${checkoutDetail ? "size-60 2xl:size-80 -left-60 2xl:-left-80" : "size-24 -left-28"} duration-200 pointer-events-none text-white -rotate-[70deg] absolute top-0 opacity-5 -z-10`}
          />
          {orderData && (
            <>
              <div
                className={`${checkoutDetail ? " max-h-96 md:max-h-[600px] opacity-100 py-2" : "max-h-0 opacity-0 py-0"} transition-all duration-300 text-white overflow-y-auto overflow-x-hidden overscroll-contain`}
              >
                <FooterReservationDetail orderData={orderData} />
              </div>
              <div className="flex justify-between items-end h-full">
                <div>
                  {orderData?.reservations[0] && (
                    <span className="block font-semibold xl:text-lg underline text-white">
                      {formattedStartDate && formattedStartDate}
                      {formattedEndDate ? " - " + formattedEndDate : ""}
                    </span>
                  )}
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1 text-white">
                      <span className="text-sm font-medium">Toplam:</span>
                      <span className="text-lg font-semibold">
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(orderData?.totalAmount)) + "₺"}
                      </span>
                    </div>
                    <div className="text-sm font-medium w-40 h-10 text-white bg-[#3A3F51] [clip-path:polygon(20%_0%,_80%_0%,_100%_100%,_100%_100%,_0_100%)] absolute right-0 left-0 -top-10 mx-auto">
                      <span
                        onClick={() => setCheckoutDetail((prev) => !prev)}
                        className="flex justify-center items-center gap-1 cursor-pointer pt-2"
                      >
                        Detayı gör{" "}
                        <IconChevronUp
                          className={`size-5 ${checkoutDetail && "rotate-180"} transition-all duration-300`}
                          strokeWidth={2}
                        />
                      </span>
                    </div>
                  </div>
                </div>
                <FooterLoggedInButtons
                  orderData={orderData}
                  buttonValue={buttonValue}
                  hotelToken={hotelToken}
                  reservationPetData={reservationPetData}
                  servicePetData={servicePetData}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FooterReservation;
