"use client";
import React, { useState } from "react";
import type { FC } from "react";
import Image from "next/image";
import Security from "@/images/security.png";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Card, CardContent } from "@/components/ui/card";
import { PaymentIcon } from "react-svg-credit-card-payment-icons";
import { EllipsisVertical, Trash2, CircleCheck } from "lucide-react";
import AddPaymentModal from "./AddPayment";
import { useSubscriptionPayment } from "@/hooks/hotel/useSubscriptionPayment";
import LoadingSpinner from "@/shared/icons/Spinner";

interface ManagePaymentProps {
  hotelCards: any;
  hotelToken: string | undefined;
}

const ManagePayment: FC<ManagePaymentProps> = ({ hotelCards, hotelToken }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { setPrimaryCard, removeSavedCard } = useSubscriptionPayment();

  const handleSetPrimaryCard = async (targetGUID: string) => {
    try {
      await setPrimaryCard(hotelToken, targetGUID, setLoading);
    } catch (error) {
      console.log(error);
    }
  };

  const handleRemoveSavedCard = async (ksGuid: string) => {
    try {
      await removeSavedCard(hotelToken, ksGuid, setLoading);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div>
      <div className="space-y-8">
        <div className="flex flex-col items-center gap-5">
          <Image src={Security} alt="security" width={70} height={70} />
          <div className="text-4xl font-bold">Ödeme yöntemini yönetin</div>
          <p className="text-lg">
            Üyelik ücretinizi nasıl ödeyeceğinizi kontrol edin.
          </p>
        </div>
        <div className="flex flex-col items-center justify-center space-y-4">
          {hotelCards?.map((card: any, index: number) => (
            <Card
              key={index}
              className="shadow-md w-full md:w-[600px] relative"
            >
              {index === 0 && (
                <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-2 py-0.5 rounded-tr-md rounded-bl-md">
                  Varsayılan
                </div>
              )}
              <CardContent className="flex items-center justify-between p-4">
                <div className="flex gap-2 items-center text-base font-semibold text-gray-700 dark:text-neutral-400">
                  <PaymentIcon
                    className="border"
                    type={
                      card?.cardTYpe === "MASTER"
                        ? "MasterCard"
                        : card?.cardTYpe === "VISA"
                          ? "Visa"
                          : card?.cardTYpe
                    }
                    format="logo"
                    width={55}
                  />
                  {`•••• •••• •••• ${card?.last4}`}
                </div>
                <div className="hover:bg-neutral-50 dark:hover:bg-neutral-900 rounded-md cursor-pointer">
                  <Popover>
                    <PopoverTrigger asChild>
                      <EllipsisVertical />
                    </PopoverTrigger>
                    <PopoverContent className="w-44 py-2 space-y-1" align="end">
                      {loading ? (
                        <div className="flex justify-center">
                          <LoadingSpinner />
                        </div>
                      ) : (
                        <>
                          {index !== 0 && hotelCards.length > 1 && (
                            <div className="space-y-1">
                              <div
                                onClick={() => handleSetPrimaryCard(card?.GUID)}
                                className="flex text-sm text-gray-600 dark:text-gray-400 font-medium items-center gap-1 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                              >
                                <CircleCheck size={16} />
                                Varsayılan Yap
                              </div>
                              <div className="w-full border-b border-neutral-200 dark:border-neutral-700"></div>
                            </div>
                          )}
                          <div
                            onClick={() => handleRemoveSavedCard(card?.GUID)}
                            className={`flex text-sm font-medium items-center gap-1 rounded-md ${
                              hotelCards.length > 1
                                ? "text-gray-600 dark:text-gray-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer"
                                : "text-gray-400 cursor-not-allowed"
                            }`}
                          >
                            <Trash2 size={16} />
                            Kaldır
                          </div>
                          {hotelCards.length === 1 && (
                            <div className="text-xs text-gray-500 mt-2 cursor-not-allowed">
                              Bu ödeme yöntemini kaldırmak istiyorsanız önce
                              başka bir ödeme yöntemi ekleyin.
                            </div>
                          )}{" "}
                        </>
                      )}
                    </PopoverContent>
                  </Popover>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="flex justify-center">
          <div
            onClick={() => setIsModalOpen(true)}
            className="font-semibold py-2 px-4 rounded-md hover:bg-secondary-6000 hover:text-white cursor-pointer"
          >
            Ödeme Yöntemi Ekle
          </div>
        </div>
      </div>
      <AddPaymentModal
        hotelToken={hotelToken}
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      />
    </div>
  );
};

export default ManagePayment;
