"use server";
import { redirect } from "next/navigation";
import { PUBLIC_API_PATHS } from "@/utils/apiUrls";

export default async function getAvailableServices(hotelId: string) {
  try {
    const response = await fetch(
      `${PUBLIC_API_PATHS.availableServices}/${hotelId}`,
      {
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const result = await response.json();

    if (result.status === 401) {
      redirect("/");
    } else if (result.status === 404) {
      redirect("/404");
    }

    if (!response.ok || !result.success) {
      console.error("Network response was not ok");
      return undefined;
    }
    return result;
  } catch (err: unknown) {
    console.error("Error fetching data:", err);
    return undefined;
  }
}
