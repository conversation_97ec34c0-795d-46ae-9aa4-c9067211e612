"use client";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React, { useRef, useEffect, useState, useMemo } from "react";
import IconChevronRight from "@/shared/icons/ChevronRight";
import IconChevronLeft from "@/shared/icons/ChevronLeft";
import { useTranslations } from "next-intl";

interface NavItem {
  route: string;
  title: string;
  slug: string;
  role: string;
  passive: boolean;
}

const LimitedNav = () => {
  const translate = useTranslations("Nav");
  const pathname = usePathname();
  const router = useRouter();
  const navContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const limitedNav: NavItem[] = useMemo(
    () => [
      {
        route: `/account/billing`,
        title: translate("navBilling"),
        slug: "billing",
        role: "otel",
        passive: false,
      },
    ],
    [translate]
  );

  useEffect(() => {
    router.push("/account/billing");
  }, [router, pathname]);

  //Determines the enabled and disabled states of the right and left buttons.
  const updateScrollState = () => {
    if (navContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = navContainerRef.current;

      const tolerance = 2;
      setCanScrollLeft(scrollLeft > tolerance);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - tolerance);
    }
  };

  const scrollLeft = () => {
    if (navContainerRef.current) {
      navContainerRef.current.scrollBy({ left: -200, behavior: "smooth" });
      setTimeout(updateScrollState, 300);
    }
  };

  const scrollRight = () => {
    if (navContainerRef.current) {
      navContainerRef.current.scrollBy({ left: 200, behavior: "smooth" });
      setTimeout(updateScrollState, 300);
    }
  };

  //Determines the scroll position based on the selected nav item when the page first loads.
  useEffect(() => {
    const navContainer = navContainerRef.current;
    const handleScroll = () => updateScrollState();
    if (navContainer) {
      navContainer.addEventListener("scroll", handleScroll);
      updateScrollState(); // Initial check
    }
    return () => {
      if (navContainer) {
        navContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = limitedNav.findIndex((item: any) => item.route === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname, limitedNav]);

  return (
    <div className="container">
      <div className="flex items-center justify-center">
        <button
          className="hidden cursor-pointer disabled:cursor-default md:block"
          disabled={!canScrollLeft}
        >
          <IconChevronLeft
            onClick={scrollLeft}
            className={`mr-2 size-5 text-secondary-6000 ${
              canScrollLeft ? "opacity-100" : "opacity-40"
            }`}
          />
        </button>
        <div
          ref={navContainerRef}
          className="hiddenScrollbar flex space-x-8 overflow-x-auto md:space-x-14"
        >
          {limitedNav.map((item: any, index: number) => {
            const isActive =
              index === 0
                ? pathname === item.route
                : pathname.includes(item.route);

            return item.passive ? (
              <div
                key={index}
                className={`block shrink-0 border-b-2 py-5 capitalize text-gray-400 md:py-8 ${
                  isActive
                    ? "border-secondary-6000 font-medium"
                    : "border-transparent"
                }`}
              >
                {item.title}
              </div>
            ) : (
              <Link
                key={index}
                href={item.route}
                className={`block shrink-0 border-b-2 py-5 capitalize md:py-8 ${
                  isActive
                    ? "border-secondary-6000 font-medium"
                    : "border-transparent"
                }`}
              >
                {item.title}
              </Link>
            );
          })}
        </div>
        <button
          className="hidden cursor-pointer bg-transparent disabled:cursor-default md:block"
          disabled={!canScrollRight}
        >
          <IconChevronRight
            onClick={scrollRight}
            className={`ml-2 size-5 bg-transparent text-secondary-6000 ${
              canScrollRight ? "opacity-100" : "opacity-40"
            }`}
          />
        </button>
      </div>
    </div>
  );
};

export default LimitedNav;
