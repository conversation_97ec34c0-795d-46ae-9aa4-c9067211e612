"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

import FileStepHeader from "./FileStepHeader";
import ProgressBar from "./ProgressBar";
import FileStepContent from "./FileStepContent";
import StatusMessage from "./StatusMessage";
import NavigationButtons from "./NavigationButtons";
import SuccessMessage from "./SuccessMessage";

interface FileStepProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

interface FileStepData {
  docType: string;
  name: string;
  required: boolean;
  condition?: (hotelData: HotelDataApiTypes) => boolean;
}

const FileStep: FC<FileStepProps> = ({ hotelToken, hotelData }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const fileSteps: FileStepData[] = [
    {
      docType: "license",
      name: "<PERSON>şlet<PERSON> Ruhsatı",
      required: true,
    },
    {
      docType: "taxCertificate",
      name: "Vergi Levhası",
      required: true,
    },
    {
      docType: "identificationDocumentFront",
      name: "Kimlik Ön Yüz",
      required: true,
    },
    {
      docType: "identificationDocumentBack",
      name: "Kimlik Arka Yüz",
      required: true,
    },
    {
      docType: "veterinaryContract",
      name: "Veteriner Sözleşmesi",
      required: true,
      condition: (hotelData) => hotelData.propertyType === "petHotel",
    },
  ];

  const filteredSteps = fileSteps.filter(
    (step) => !step.condition || step.condition(hotelData)
  );

  const completedSteps = React.useMemo(() => {
    if (!hotelData?.files) return new Set<number>();

    const newCompletedSteps = new Set<number>();
    filteredSteps.forEach((step, index) => {
      const isUploaded = hotelData.files.some(
        (file: any) => file?.doctype === step.docType
      );
      if (isUploaded) {
        newCompletedSteps.add(index);
      }
    });
    return newCompletedSteps;
  }, [hotelData?.files, filteredSteps]);

  useEffect(() => {
    if (!hasInitialized && filteredSteps.length > 0) {
      for (let i = 0; i < filteredSteps.length; i++) {
        const step = filteredSteps[i];
        const isUploaded = hotelData?.files?.some(
          (file: any) => file?.doctype === step.docType
        );
        if (!isUploaded) {
          setCurrentStep(i);
          setHasInitialized(true);
          return;
        }
      }
      setCurrentStep(0);
      setHasInitialized(true);
    }
  }, [filteredSteps.length, hasInitialized]);

  const nextStep = () => {
    if (currentStep < filteredSteps.length - 1 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const prevStep = () => {
    if (currentStep > 0 && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(currentStep - 1);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex !== currentStep && !isTransitioning) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentStep(stepIndex);
        setIsTransitioning(false);
      }, 200);
    }
  };

  const currentStepData = filteredSteps[currentStep];

  return (
    <>
      <FileStepHeader
        currentStep={currentStep}
        filteredStepsLength={filteredSteps.length}
        currentStepName={currentStepData?.name}
      />

      <ProgressBar
        filteredSteps={filteredSteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        isTransitioning={isTransitioning}
        onGoToStep={goToStep}
      />

      <FileStepContent
        hotelData={hotelData}
        hotelToken={hotelToken}
        currentStep={currentStep}
        currentStepData={currentStepData}
        isTransitioning={isTransitioning}
      />

      <StatusMessage
        currentStep={currentStep}
        completedSteps={completedSteps}
        isTransitioning={isTransitioning}
      />

      <NavigationButtons
        currentStep={currentStep}
        filteredStepsLength={filteredSteps.length}
        completedSteps={completedSteps}
        isTransitioning={isTransitioning}
        onPrevStep={prevStep}
        onNextStep={nextStep}
      />

      <SuccessMessage
        completedStepsSize={completedSteps.size}
        filteredStepsLength={filteredSteps.length}
      />
    </>
  );
};

export default FileStep;
