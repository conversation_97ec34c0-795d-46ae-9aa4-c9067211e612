"use client";
import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import RoomCalculatedDetail from "./(calculatedDetail)/RoomCalculatedDetail";
import ServiceCalculatedDetail from "./(calculatedDetail)/ServiceCalculatedDetail";
import SubscriptionCalculatedDetail from "./(calculatedDetail)/SubscriptionCalculatedDetail";

const CalculatedDetailContainer = ({
  hotelToken,
}: {
  hotelToken: string | undefined;
}) => {
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const reservations = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "reservation"
  );
  const subscriptions = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "subscription"
  );
  const services = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "service"
  );

  return (
    <>
      <div className="flex flex-col space-y-4 max-h-72 overflow-y-auto">
        <RoomCalculatedDetail hotelToken={hotelToken} />
        <ServiceCalculatedDetail hotelToken={hotelToken} />
        <SubscriptionCalculatedDetail hotelToken={hotelToken} />
      </div>
      {calculatedRoomData &&
        (subscriptions.length > 0 ||
          reservations.length > 0 ||
          services.length > 0) && (
          <div className="flex justify-between font-semibold">
            <span>Toplam</span>
            <span>
              {new Intl.NumberFormat("tr-TR", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(Number(calculatedRoomData?.totalOrderPrice)) + "₺"}
            </span>
          </div>
        )}
    </>
  );
};

export default CalculatedDetailContainer;
