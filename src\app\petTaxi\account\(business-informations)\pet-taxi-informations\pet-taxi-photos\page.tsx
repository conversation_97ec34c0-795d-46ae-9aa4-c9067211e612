import React from "react";
import PetTaxiPhotosContainer from "../(components)/(petTaxiPhotos)/PetTaxiPhotosContainer";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";

const PetTaxiPhotosPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();
  return (
    <>
      {taxiData && (
        <PetTaxiPhotosContainer
          taxiData={taxiData?.data}
          petTaxiToken={petTaxiToken}
        />
      )}
    </>
  );
};

export default PetTaxiPhotosPage;
