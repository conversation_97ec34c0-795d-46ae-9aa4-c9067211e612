"use client";
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

interface SubscriptionFormProps {
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  onClose?: () => void;
}

const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
  hotelToken,
  hotelData,
  subscriptionData = [],
  onClose,
}) => {
  const [selectedSubscription, setSelectedSubscription] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const { addItemToCart } = useHotelAtDoorReservation();
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );

  const addToCartHandler = (subscription: any) => {
    if (!subscription) {
      console.error("No subscription selected.");
      return;
    }
    const requestBody = {
      itemType: "subscription",
      itemData: {
        ...subscription,
        hotel: hotelData?._id,
        subscriptionHotelId: subscription?._id,
      },
      selectedItems: calculatedRoomData?.selectedItems || [],
      totalOrderPrice: calculatedRoomData?.totalOrderPrice || 0,
    };

    addItemToCart(hotelToken, requestBody);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        <div>
          <Label>Üyelik Kartı</Label>
          <Select
            onValueChange={(value) => {
              const selected = subscriptionData.find(
                (sub: any) => sub._id === value
              );
              setSelectedSubscription(selected || null);
            }}
            disabled={loading}
          >
            <SelectTrigger className="rounded-md border border-neutral-200 dark:border-neutral-700 min-h-10 w-full mt-1">
              <SelectValue placeholder="Üyelik Kartı Seç" />
            </SelectTrigger>
            <SelectContent>
              {subscriptionData && subscriptionData.length > 0 ? (
                subscriptionData.map((subscription: any) => (
                  <SelectItem key={subscription._id} value={subscription._id}>
                    {subscription.subscriptionName} -{" "}
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(subscription.total)) + "₺"}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="no-subscription" disabled>
                  Üyelik Kartı Bulunamadı
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end gap-5 pb-1">
          <Button variant="ghost" onClick={onClose}>
            İptal
          </Button>
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={() => addToCartHandler(selectedSubscription)}
            disabled={!selectedSubscription || loading}
          >
            Ekle
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionForm;
