import type { FC } from "react";
import React from "react";
import type { AllocationType } from "@/store/features/hotelCalendar/calendar-types";
import { createLocalDate } from "@/utils/createLocalDate";

interface Props {
  dayOfMonth: number;
  date?: Date | undefined;
  selectedRoomCalendarData: AllocationType[];
}

const HotelCalendarDatePickerRangeMobile: FC<Props> = ({
  dayOfMonth,
  date,
  selectedRoomCalendarData,
}) => {
  const matchingItem = selectedRoomCalendarData?.find((item: any) => {
    const itemDate = createLocalDate(item.allocationDate);
    const today = new Date();
    if (itemDate) {
      today.setHours(12, 0, 0, 0);
      itemDate.setHours(12, 0, 0, 0);
      return (
        itemDate >= today &&
        itemDate.getFullYear() === date?.getFullYear() &&
        itemDate.getMonth() === date?.getMonth() &&
        itemDate.getDate() === date?.getDate()
      );
    }
  });

  const dateStatus =
    matchingItem?.passive === false
      ? "Aktif"
      : matchingItem?.passive === true
        ? "Pasif"
        : "F.yok";

  const colorStatus = () => {
    if (matchingItem?.passive === true) {
      return "bg-[#6c757d]";
    } else if (
      matchingItem?.passive === false &&
      matchingItem?.avail === false
    ) {
      return "bg-[#FFB3B3]";
    } else if (
      matchingItem?.passive === false &&
      matchingItem?.avail === true
    ) {
      return "bg-[#58d68d]";
    } else {
      return "bg-[#fd7e14]";
    }
  };

  // resets hours to compare without hour
  const resetDate = date && date.setHours(0, 0, 0, 0);
  const today = new Date().setHours(0, 0, 0, 0);

  return (
    <button
      id={`${date && date.toISOString().split("T")[0]}`}
      className={`react-datepicker__day_span relative !h-20 !w-60 !rounded-none border border-gray-200 ${
        dateStatus === "Pasif" || dateStatus === "F.yok"
          ? "line-through"
          : "active-date"
      }`}
    >
      <span className="text-xs md:text-base">{dayOfMonth}</span>
      <span className="absolute bottom-2 right-1 text-xs font-medium md:right-3 md:text-base">
        {matchingItem?.avail === false && matchingItem.passive === false ? (
          <span className="text-[#FFB3B3]">Reserved</span>
        ) : (
          matchingItem &&
          (matchingItem?.roomPrice.toFixed(2) ??
            matchingItem?.price.toFixed(2)) + "₺"
        )}
      </span>
      <span
        className={`absolute right-3 top-2 size-1.5 text-xs font-medium ${colorStatus()} ${resetDate && resetDate < today && "!bg-transparent"}`}
      ></span>
    </button>
  );
};

export default HotelCalendarDatePickerRangeMobile;
