interface FormatCurrencyStringProps {
  value: number;
  currency: string;
  language?: string;
}
export const formatAmount = ({
  value,
  currency,
  language = "tr-TR",
}: FormatCurrencyStringProps): string => {
  const numberFormat = new Intl.NumberFormat(language, {
    style: "currency",
    currency,
    currencyDisplay: "symbol",
  });

  const parts = numberFormat.formatToParts(value);
  let zeroDecimalCurrency = true;

  for (const part of parts) {
    if (part.type === "decimal") {
      zeroDecimalCurrency = false;
      break;
    }
  }

  value = zeroDecimalCurrency ? value : parseFloat((value / 100).toFixed(2));
  return numberFormat.format(value);
};
