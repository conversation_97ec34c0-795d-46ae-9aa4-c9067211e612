import type { FC } from "react";
import React from "react";
import type { Route } from "@/routers/types";
import GallerySlider from "@/components/GallerySlider";
import DeleteRoomModal from "./(deleteRoom)/DeleteRoomModal";
import UpdateRoomModal from "./(updateRoom)/UpdateRoomModal";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import { useTranslations } from "next-intl";
import type { RoomGroupDataApiTypes } from "@/types/hotel/roomGroupType";

export interface HotelRoomCardProps {
  hotel: any;
  className?: string;
  roomGroup: RoomGroupDataApiTypes;
  size?: "default" | "small";
  href?: Route<string>;
  hotelToken: string | undefined;
  acceptedPetTypes: string[] | [];
}

const HotelRoomCard: FC<HotelRoomCardProps> = ({
  size = "default",
  className = "",
  hotel,
  roomGroup,
  hotelToken,
  acceptedPetTypes,
}) => {
  const translate = useTranslations("HotelRoomCard");
  const { _id, roomGroupName, roomCount, images, petType, rooms } = roomGroup;

  return (
    <div className={`nc-StayCard2 group relative ${className}`}>
      <div className="relative w-full">
        <GallerySlider
          uniqueID={`StayCard2_${_id}`}
          ratioClass="aspect-w-12 aspect-h-11"
          galleryImgs={images?.length > 0 ? images : [defaultHotelImage]}
          imageClass="rounded-lg"
        />
      </div>

      <div className={size === "default" ? "mt-3 space-y-3" : "mt-2 space-y-2"}>
        <div className="mb-4 mt-3 space-y-2">
          <div className="flex items-center space-x-2">
            <h2
              className={`font-semibold capitalize text-neutral-900 dark:text-white ${
                size === "default" ? "text-base" : "text-base"
              }`}
            >
              <span className="line-clamp-1">{roomGroupName}</span>
            </h2>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>{translate("hotelRoomCount")}</div>
            <div> {roomCount}</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>{translate("roomCapacity")}</div>
            <div> {rooms[0].roomCapacity}</div>
          </div>
          <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
            <div>{translate("petType")}</div>
            <div className="capitalize">{petType}</div>
          </div>
        </div>
        <div className="w-14 border-b border-neutral-100 dark:border-neutral-800"></div>
        <div className="flex items-center justify-between"></div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400">
        <UpdateRoomModal
          hotel={hotel}
          roomGroup={roomGroup}
          hotelToken={hotelToken}
          acceptedPetTypes={acceptedPetTypes}
        />
        <DeleteRoomModal roomGroup={roomGroup} hotelToken={hotelToken} />
      </div>
    </div>
  );
};

export default HotelRoomCard;
