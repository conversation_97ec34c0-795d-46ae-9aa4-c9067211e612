import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { getTranslations } from "next-intl/server";
import IconCheck from "@/shared/icons/Check";

type SuccessPageProps = {
  searchParams: {
    propertyType?: string;
  };
};

const SuccessPage = async ({ searchParams }: SuccessPageProps) => {
  const translate = await getTranslations("SuccessPage");
  const propertyType = searchParams.propertyType;

  return (
    <div className="container flex flex-col items-center py-52">
      <IconCheck
        strokeWidth={3.5}
        className="size-20 bg-green-600 text-white rounded-full p-2 mb-7"
      />
      <h2 className="mb-2 text-center text-2xl">
        {propertyType === "petHotel"
          ? translate("success")
          : translate("petTaxiSuccess")}
      </h2>
      <div className="text-center">{translate("returnHomepage")}</div>
      <div className="flex gap-5">
        <Link className="mt-5" href="/">
          <Button variant="outline">{translate("homepage")}</Button>
        </Link>
        <Link className="mt-5" href="/login">
          <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
            {propertyType === "petHotel"
              ? translate("hotelLogin")
              : translate("petTaxiLogin")}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default SuccessPage;
