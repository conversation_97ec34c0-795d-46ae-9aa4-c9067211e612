import React from "react";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import PetTaxiPetTypes from "../(components)/(petTaxiPetTypes)/PetTaxiPetTypes";

const PetTaxiPetTypesPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();
  return (
    <>
      {taxiData && (
        <PetTaxiPetTypes
          taxiData={taxiData?.data}
          petTaxiToken={petTaxiToken}
        />
      )}
    </>
  );
};

export default PetTaxiPetTypesPage;
