import React from "react";
import type { FC } from "react";
import { format } from "date-fns";
import DeleteInstallment from "../deleteDiscount/DeleteInstallment";
import UpdateInstallment from "../updateDiscount/UpdateInstallment";

export interface InstallmentCardProps {
  installmentData: any;
  hotelToken: string | undefined;
}
const InstallmentCard: FC<InstallmentCardProps> = ({
  installmentData,
  hotelToken,
}) => {
  const formatDate = (date: string) => {
    if (!date) return "";
    return format(new Date(date), "dd.MM.yyyy");
  };

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex justify-between items-center">
        <div className="flex">
          <span className="relative flex h-2.5 w-2.5 mt-[7px] mr-2">
            <span
              className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                installmentData?.isActive ? "bg-green-700" : "bg-red-700"
              } opacity-75`}
            ></span>
            <span
              className={`relative inline-flex rounded-full h-2.5 w-2.5 ${
                installmentData?.isActive ? "bg-green-600" : "bg-red-600"
              }`}
            ></span>
          </span>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <h2 className="font-semibold capitalize text-neutral-900 dark:text-white">
                <span className="line-clamp-1">
                  {installmentData?.cardBrands.join(", ")}
                </span>
              </h2>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Taksit Sayısı:</div>
              <div className="font-semibold">
                {" "}
                {installmentData?.maxInstallmentCount}
              </div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Kampanya Tarih Aralığı:</div>
              <div className="font-semibold">
                {formatDate(installmentData?.startDate)} /{" "}
                {formatDate(installmentData?.endDate)}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdateInstallment
          installmentData={installmentData}
          hotelToken={hotelToken}
        />
        <DeleteInstallment
          hotelToken={hotelToken}
          installmentId={installmentData?._id}
        />
      </div>
    </div>
  );
};

export default InstallmentCard;
