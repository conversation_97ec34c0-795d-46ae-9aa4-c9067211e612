import React from "react";
import {
  Paw<PERSON>rint,
  <PERSON>issors,
  Info,
  MessageCircle,
  ChartLine,
  IdCard,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast, ToastOptions, Zoom } from "react-toastify";
import { useRouter } from "next/navigation";
import { Route } from "next";

interface ToastNotificationProps {
  message: {
    title: string;
    body: string;
    data?: any;
  };
}

export const ToastNotification: React.FC<ToastNotificationProps> = ({
  message,
}) => {
  const router = useRouter();

  const handleButtonClick = (link: any) => {
    if (link) {
      router.push(`/account/${link}` as Route);
    }
  };

  const typeStyles: Record<
    string,
    {
      icon: React.ReactNode;
      buttonClass: string;
      buttonText?: string;
      buttonLink?: string;
    }
  > = {
    reservation: {
      icon: <PawPrint className="text-green-600" />,
      buttonClass:
        "bg-green-600 hover:bg-transparent border text-white hover:text-green-600 border-green-600",
      buttonText: "Rezervasyonlara Git",
      buttonLink: "reservations",
    },
    service: {
      icon: <Scissors className="text-orange-600" />,
      buttonClass:
        "bg-orange-600  hover:bg-transparent border text-white hover:text-orange-600 border-orange-600",
      buttonText: "Hizmetlere Git",
      buttonLink: "services/services-sold",
    },
    subscription: {
      icon: <IdCard className="text-purple-600" />,
      buttonClass:
        "bg-purple-600 hover:bg-transparent border text-white hover:text-purple-600 border-purple-600",
      buttonText: "Üyeliklere Git",
      buttonLink: "subscriptions/subscriber-list",
    },
    statistic: {
      icon: <ChartLine className="text-red-600" />,
      buttonClass:
        "bg-red-600 hover:bg-transparent border text-white hover:text-red-600 border-red-600",
      buttonText: "İstatistiklere Git",
      buttonLink: "dashboard",
    },
    newMessage: {
      icon: <MessageCircle className="text-blue-600" />,
      buttonClass:
        "bg-blue-600 hover:bg-transparent border text-white hover:text-blue-600 border-blue-600",
      buttonText: "Mesajlara Git",
      buttonLink: "",
    },
    default: {
      icon: <Info className="text-gray-500" />,
      buttonClass:
        "bg-gray-500 hover:bg-transparent border text-white hover:text-gray-500 border-gray-500",
    },
  };

  const { icon, buttonClass, buttonText, buttonLink } =
    typeStyles[message.data?.pushType ?? "default"] || typeStyles.default;

  return (
    <div className="flex flex-col">
      <div className="flex items-start">
        <div className="mr-3">{icon}</div>
        <div>
          <div className="font-semibold">{message.title}</div>
          <div>{message.body}</div>
        </div>
      </div>
      {buttonText && (
        <div className="flex justify-start mt-3 mx-8">
          <Button
            onClick={() => handleButtonClick(buttonLink)}
            className={`${buttonClass} py-0 w-[228px]`}
          >
            {buttonText}
          </Button>
        </div>
      )}
    </div>
  );
};

export const showToastNotification = (message: {
  title: string;
  body: string;
  data?: any;
}) => {
  const progressClasses: Record<string, string> = {
    reservation: "bg-green-600",
    service: "bg-orange-600",
    subscription: "bg-purple-600",
    statistic: "bg-red-600",
    newMessage: "bg-blue-600",
    default: "bg-gray-500",
  };

  const toastType = message.data?.pushType ?? "default";
  const progressClass = progressClasses[toastType] || progressClasses.default;

  const options: ToastOptions = {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "light",
    transition: Zoom,
    icon: false,
    progressClassName: progressClass,
  };

  toast.success(<ToastNotification message={message} />, options);
};
