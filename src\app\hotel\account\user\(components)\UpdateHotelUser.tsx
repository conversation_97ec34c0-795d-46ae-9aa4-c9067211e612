"use client";
import type { ChangeE<PERSON>, FC } from "react";
import React, { useState } from "react";
import Label from "@/components/Label";
import { Button } from "@/components/ui/button";
import Input from "@/shared/Input";
import Select from "@/shared/Select";
import Textarea from "@/shared/Textarea";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelUserInformations } from "@/hooks/hotel/useHotelUserInformations";
import type { UserInformationApiTypes } from "@/types/hotel/hotelUserType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconEdit from "@/shared/icons/Edit";
import {
  onlyLetterRegex,
  emailRegex,
  usernameRegex,
} from "@/utils/regex/petOwnerRegex";
import { useTranslations } from "next-intl";

export interface UpdateHotelUserProps {
  rowOriginal: UserInformationApiTypes;
  hotelToken: string | undefined;
}

const UpdateHotelUser: FC<UpdateHotelUserProps> = ({
  hotelToken,
  rowOriginal,
}) => {
  const translate = useTranslations("AddNewAndUpdateHotelUser");
  const { updateHotelUserHandler } = useHotelUserInformations();
  const initialData = {
    firstName: rowOriginal.firstName,
    lastName: rowOriginal.lastName,
    gender: rowOriginal.gender,
    username: rowOriginal.username,
    email: rowOriginal.email,
    dateOfBirth: rowOriginal.dateOfBirth,
    phone: rowOriginal.phone,
    bio: rowOriginal.bio,
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [hotelUserUpdateModalIsOpen, setHotelUserModalIsOpen] = useState(false);
  const [updatedHotelUserInfo, setUpdatedHotelUserInfo] = useState(initialData);

  const hotelUserInfoHandler = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setUpdatedHotelUserInfo((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  function closeHotelUserUpdateModal() {
    setHotelUserModalIsOpen(false);
    setLoading(false);
    setUpdatedHotelUserInfo(initialData);
  }

  const phoneRegex = /^(0|90)(\d{10})$/;
  const nameCheck = onlyLetterRegex.test(updatedHotelUserInfo.firstName);
  const lastNameCheck = onlyLetterRegex.test(updatedHotelUserInfo.lastName);
  const mailCheck = emailRegex.test(
    updatedHotelUserInfo.email.toLowerCase().trim()
  );
  const usernameCheck = usernameRegex.test(updatedHotelUserInfo.username);
  const phoneCheck = phoneRegex.test(updatedHotelUserInfo.phone);
  const dateOfBirthCheck = updatedHotelUserInfo.dateOfBirth.length > 0;

  const isAllValid =
    nameCheck &&
    lastNameCheck &&
    mailCheck &&
    usernameCheck &&
    phoneCheck &&
    dateOfBirthCheck;

  return (
    <>
      <IconEdit
        onClick={() => setHotelUserModalIsOpen(true)}
        className="size-5 duration-200 hover:text-secondary-6000"
      />
      <Dialog open={hotelUserUpdateModalIsOpen}>
        <DialogContent
          className="overflow-y-auto max-h-[calc(100vh-50px)]"
          onInteractOutside={closeHotelUserUpdateModal}
        >
          <DialogHeader>
            <DialogTitle> {translate("updateUser")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            className="mt-10 max-w-3xl grow space-y-6 md:mt-0"
            onSubmit={(event) => {
              updateHotelUserHandler(
                event,
                hotelToken,
                updatedHotelUserInfo,
                rowOriginal._id,
                setLoading,
                closeHotelUserUpdateModal
              );
            }}
          >
            <div>
              <Label>{translate("firstName")}</Label>
              <Input
                className="mt-1.5"
                name="firstName"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.firstName || ""}
              />
              {!nameCheck && updatedHotelUserInfo.firstName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validFirstName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("lastName")}</Label>
              <Input
                className="mt-1.5"
                name="lastName"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.lastName || ""}
              />
              {!lastNameCheck && updatedHotelUserInfo.lastName.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validLastName")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("gender")}</Label>
              <Select
                className="mt-1.5"
                name="gender"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.gender || ""}
              >
                <option value="Male">{translate("male")}</option>
                <option value="Female">{translate("female")}</option>
                <option value="Other">{translate("other")}</option>
              </Select>
            </div>
            <div>
              <Label>{translate("username")}</Label>
              <Input
                className="mt-1.5"
                name="username"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.username || ""}
              />
              {!usernameCheck && updatedHotelUserInfo.username.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validUsername")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("email")}</Label>
              <Input
                className="mt-1.5"
                name="email"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.email || ""}
              />
              {!mailCheck && updatedHotelUserInfo.email.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validEmail")}
                </span>
              )}
            </div>
            <div className="max-w-lg">
              <Label>{translate("dateOfBirth")}</Label>
              <Input
                className="mt-1.5"
                type="date"
                name="dateOfBirth"
                max="2007-01-01"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.dateOfBirth || ""}
              />
              {!dateOfBirthCheck && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validDateOfBirth")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("phone")}</Label>
              <Input
                className="mt-1.5"
                name="phone"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.phone || ""}
              />
              {!phoneCheck && updatedHotelUserInfo.phone.length > 0 && (
                <span className="mt-3 block text-xs text-red-500">
                  {translate("validPhone")}
                </span>
              )}
            </div>
            <div>
              <Label>{translate("bio")}</Label>
              <Textarea
                className="mt-1.5"
                name="bio"
                onChange={hotelUserInfoHandler}
                value={updatedHotelUserInfo.bio || ""}
              />
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                onClick={closeHotelUserUpdateModal}
                type="button"
              >
                {translate("cancel")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                disabled={
                  !(
                    isAllValid &&
                    JSON.stringify(initialData) !==
                      JSON.stringify(updatedHotelUserInfo)
                  )
                }
                type="submit"
              >
                {loading ? <LoadingSpinner /> : translate("confirm")}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeHotelUserUpdateModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateHotelUser;
