import React from "react";
import { cookies } from "next/headers";
import getAllServices from "@/actions/(protected)/hotel/services/getAllServices";
import ServicesSoldTable from "../(components)/servicesSold/ServicesSoldTable";
import ServiceSoldMobile from "../(components)/servicesSold/(mobile)/ServicesSoldMobile";
import EmptyState from "@/components/EmptyState";
import { serviceSoldListApiTypes } from "@/types/hotel/services/serviceTypes";

const ServicesSoldPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const servicesAllData = await getAllServices();

  return (
    <div>
      <div className="max-lg:hidden">
        <ServicesSoldTable
          serviceList={servicesAllData?.data}
          hotelToken={hotelToken}
        />
      </div>
      {servicesAllData?.data?.length > 0 ? (
        <div className="lg:hidden">
          <div className="grid grid-cols-1 gap-3">
            {servicesAllData?.data?.map(
              (servicesData: serviceSoldListApiTypes) => {
                return (
                  <ServiceSoldMobile
                    key={servicesData._id}
                    servicesData={servicesData}
                    hotelToken={hotelToken}
                  />
                );
              }
            )}
          </div>
        </div>
      ) : (
        <div className="lg:hidden">
          <EmptyState text="Satılan hizmet bulunamadı" />
        </div>
      )}
    </div>
  );
};

export default ServicesSoldPage;
