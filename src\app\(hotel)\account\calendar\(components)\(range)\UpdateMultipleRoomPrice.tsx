"use client";
import React, { useState } from "react";
import type { FC, ChangeEvent } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import MultipleSelector from "@/components/ui/multiple-selector";
import type { Option } from "@/components/ui/multiple-selector";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth";
import DatePickerCustomDay from "@/components/DatePickerCustomDay";
import DatePicker from "react-datepicker";
import { adjustDateToTimezone } from "@/utils/adjustDateToTimezone";
import { createLocalDate } from "@/utils/createLocalDate";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { useTranslations } from "next-intl";
import { Checkbox } from "@/components/ui/checkbox";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import LoadingSpinner from "@/shared/icons/Spinner";

interface UpdateMultipleRoomPriceProps {
  roomGroupData: any;
  hotelToken: string | undefined;
  setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>;
}

const UpdateMultipleRoomPrice: FC<UpdateMultipleRoomPriceProps> = ({
  roomGroupData,
  hotelToken,
  setFetchAgain,
}) => {
  const translate = useTranslations("UpdateRoomPrice");
  const { setRoomPrice } = useHotelCalendarActions();
  const [startDate, setStartDate] = useState<Date | string | undefined>(
    new Date().toISOString().split("T")[0]
  );
  const [endDate, setEndDate] = useState<Date | string | undefined>(
    new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0]
  );
  const [newRoomPrice, setNewRoomPrice] = useState<string | undefined>(
    undefined
  );
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [selectedRoomsArray, setSelectedRoomsArray] = useState<any>([]);
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [priceValue, setPriceValue] = useState<string | undefined>("");

  const selectedRoomGroupId = useSelector(
    (state: RootState) => state.hotelCalendar.selectedRoomGroupId
  );
  const filteredRoomGroup = roomGroupData.find(
    (room: any) => room._id === selectedRoomGroupId
  );
  const OPTIONS: Option[] = filteredRoomGroup.rooms.map((room: any) => ({
    label: room.roomName,
    value: room._id,
  }));

  const closeModal = () => {
    setOpen(false);
    setIsChecked(false);
    setLoading(false);
    setSelectedRoomsArray([]);
    setNewRoomPrice(undefined);
    setPriceValue("");
    setStartDate(new Date().toISOString().split("T")[0]);
    setEndDate(
      new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0]
    );
  };

  const onChangeDate = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start ?? undefined);
    setEndDate(end ?? undefined);
  };

  const adjustedStartDate = adjustDateToTimezone(startDate);
  const adjustedEndDate = adjustDateToTimezone(endDate);
  const startDateToString =
    adjustedStartDate && adjustedStartDate.toISOString().split("T")[0];
  const endDateToString =
    adjustedEndDate && adjustedEndDate.toISOString().split("T")[0];

  const selectAllRoomsHandler = () => {
    const allRooms = filteredRoomGroup.rooms.map((room: any) => ({
      label: room.roomName,
      value: room._id,
    }));
    setSelectedRoomsArray(allRooms);
  };

  const removeAllRoomsHandler = () => {
    setSelectedRoomsArray([]);
  };

  const buttonDisabled =
    selectedRoomsArray.length > 0 &&
    newRoomPrice &&
    startDateToString &&
    endDateToString;

  const selectedRoomIds = selectedRoomsArray.map((room: any) => room.value);

  const isAllRoomsSelected =
    filteredRoomGroup.rooms.map((room: any) => ({
      label: room.roomName,
      value: room._id,
    })).length === selectedRoomsArray.length;

  const newRoomPriceHandler = (event: ChangeEvent<HTMLInputElement>) => {
    const price = event.target.value;

    if (+price < 0) {
      return;
    }

    setPriceValue(price);
    if (isChecked) {
      const adjustedPrice = +price / (1 + 18 / 100);
      setNewRoomPrice(adjustedPrice.toString());
    } else {
      setNewRoomPrice(price);
    }
  };

  return (
    <div className="text-center">
      <Button
        onClick={() => setOpen(true)}
        className="text-xs hover:text-white hover:bg-secondary-700"
        variant="outline"
      >
        Çoklu Oda Fiyatı Güncelleme
      </Button>
      <Dialog open={open}>
        <DialogContent
          className="max-w-5xl overflow-y-auto max-h-[calc(100vh-50px)]"
          onOpenAutoFocus={(event) => event.preventDefault()}
          onInteractOutside={closeModal}
        >
          <DialogHeader>
            <DialogTitle>Çoklu Oda Fiyatı Güncelleme</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div>
            <div className="nc-Label text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Fiyat değişikliği yapmak istediğiniz odaları seçiniz
            </div>
            <div className="flex items-center gap-5">
              <div className="basis-5/6">
                <MultipleSelector
                  defaultOptions={OPTIONS}
                  value={selectedRoomsArray}
                  onChange={setSelectedRoomsArray}
                  placeholder="oda seçiniz"
                  emptyIndicator={
                    <p className="text-center text-sm leading-10 text-gray-600 dark:text-gray-400">
                      Oda bulunamadı.
                    </p>
                  }
                />
              </div>
              <div className="flex items-center text-sm gap-1 basis-1/6">
                {isAllRoomsSelected ? (
                  <Checkbox checked onClick={removeAllRoomsHandler} />
                ) : (
                  <Checkbox checked={false} onClick={selectAllRoomsHandler} />
                )}
                <p>Tüm odaları seç</p>
              </div>
            </div>
          </div>
          <div className="overflow-hidden rounded-3xl bg-white p-3 ring-opacity-5 dark:bg-neutral-800 h-[430px]">
            <DatePicker
              selected={startDate ? createLocalDate(startDate) : undefined}
              minDate={new Date()}
              onChange={onChangeDate}
              startDate={startDate ? createLocalDate(startDate) : undefined}
              endDate={endDate ? createLocalDate(endDate) : undefined}
              selectsRange
              monthsShown={2}
              showPopperArrow={false}
              locale={"tr"}
              inline
              renderCustomHeader={(p) => (
                <DatePickerCustomHeaderTwoMonth {...p} />
              )}
              renderDayContents={(day, date) => (
                <DatePickerCustomDay dayOfMonth={day} date={date} />
              )}
            />
          </div>
          <div className="flex items-center justify-center gap-5">
            <div className="basis-1/2">
              {!isChecked ? (
                <FormItem
                  className="!text-neutral-700 dark:!text-neutral-400"
                  label={translate("newRoomRate")}
                >
                  <Input
                    onChange={newRoomPriceHandler}
                    type="number"
                    value={priceValue}
                  />
                </FormItem>
              ) : (
                <FormItem
                  className="!text-neutral-700 dark:!text-neutral-400"
                  label={"Misafirin ödeyeceği fiyatı giriniz"}
                >
                  <Input
                    onChange={newRoomPriceHandler}
                    type="number"
                    value={priceValue}
                  />
                </FormItem>
              )}
              <div className="flex items-center gap-2 mt-2">
                <Checkbox
                  onClick={() => {
                    setIsChecked((prev) => !prev);
                    setNewRoomPrice("");
                    setPriceValue("");
                  }}
                  checked={isChecked}
                />
                <span className="text-sm text-neutral-700 dark:text-neutral-400">
                  veya misafirin ödeyeceği fiyatı gir
                </span>
              </div>
            </div>
            <div className="space-y-4 basis-1/2">
              <div className="mt-2 flex gap-1 text-sm">
                <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                  {translate("price")}
                </div>
                <div className="font-medium">
                  {newRoomPrice
                    ? new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(newRoomPrice)) + "₺"
                    : "0₺"}
                </div>
              </div>
              <div className="mt-2 flex gap-1 text-sm">
                <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                  {translate("serviceFee")}
                </div>
                <div className="font-medium">
                  {newRoomPrice
                    ? new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(newRoomPrice) * 0.18) + "₺"
                    : "0₺"}
                </div>
              </div>
              {!isChecked ? (
                <div className="mt-2 flex gap-1 text-sm">
                  <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                    {translate("totalPrice")}
                  </div>
                  <div className="font-medium">
                    {newRoomPrice
                      ? new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(
                          Number(newRoomPrice) +
                            Number(Number(newRoomPrice) * 0.18)
                        ) + "₺"
                      : "0₺"}
                  </div>
                </div>
              ) : (
                <div className="mt-2 flex gap-1 text-sm">
                  <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                    Yeni oda fiyatı:
                  </div>
                  <div className="font-medium">
                    {newRoomPrice
                      ? new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(priceValue) / 1.18) + "₺"
                      : "0₺"}
                  </div>
                </div>
              )}
              <div className="mt-2 flex gap-1 text-sm">
                <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                  {translate("yourEarnings")}
                </div>
                <div className="font-medium">
                  {newRoomPrice
                    ? new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(newRoomPrice)) + "₺"
                    : "0₺"}
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-center gap-5">
            <Button onClick={closeModal} className="w-1/3" variant="outline">
              {translate("cancel")}
            </Button>
            <Button
              onClick={() => {
                setRoomPrice(
                  hotelToken,
                  startDateToString,
                  endDateToString,
                  selectedRoomIds,
                  newRoomPrice,
                  setFetchAgain,
                  closeModal,
                  setLoading,
                  setDisabled
                );
              }}
              type="button"
              disabled={disabled || !buttonDisabled}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/3"
            >
              {loading ? <LoadingSpinner /> : translate("save")}
            </Button>
          </div>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UpdateMultipleRoomPrice;
