import React from "react";
import HotelPhotosContainer from "../(components)/(hotelPhotos)/HotelPhotosContainer";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";

const HotelPhotosPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  return (
    <>
      {hotelData && (
        <HotelPhotosContainer
          hotelData={hotelData?.data}
          hotelToken={hotelToken}
        />
      )}
    </>
  );
};

export default HotelPhotosPage;
