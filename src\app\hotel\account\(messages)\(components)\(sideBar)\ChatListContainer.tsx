"use client";
import React, { useState, useEffect } from "react";
import type { FC } from "react";
import ChatList from "./ChatList";
import { useDispatch, useSelector } from "react-redux";
import { setChatList } from "@/store/features/liveChat/live-chat-slice";
import type { RootState } from "@/store";

interface ChatListContainerProps {
  selectedId: string | string[] | undefined;
  chatContacts: any;
  hotelId: string;
  isCenter?: boolean;
}

const ChatListContainer: FC<ChatListContainerProps> = ({
  selectedId,
  chatContacts,
  hotelId,
  isCenter = false,
}) => {
  const dispatch = useDispatch();

  const chatList = useSelector(
    (state: RootState) => state.liveChatText.chatList
  );

  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    dispatch(setChatList(chatContacts));
    setLoading(false);
  }, [chatContacts, dispatch]);

  const sortedChatList = chatList?.contacts?.slice()?.sort((a: any, b: any) => {
    const dateA = new Date(a?.lastMessage?.content?.createdAt || 0).getTime();
    const dateB = new Date(b?.lastMessage?.content?.createdAt || 0).getTime();
    return dateB - dateA;
  });

  if (loading) {
    return (
      <>
        {Array.from({ length: 8 }).map((_, index) => (
          <div
            key={index}
            className="animate-pulse flex items-center gap-2 mb-4 p-3"
          >
            <div className="basis-1/10">
              <div className="w-10 h-10 bg-gray-200 dark:bg-neutral-800 rounded-full" />
            </div>
            <div className="space-y-2 w-full basis-9/10">
              <div className="w-full h-3 bg-gray-200 dark:bg-neutral-800 rounded-full"></div>
              <div className="w-full h-3 bg-gray-200 dark:bg-neutral-800 rounded-full"></div>
            </div>
          </div>
        ))}
      </>
    );
  }
  if (chatList?.contacts?.length === 0) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto">
      {sortedChatList?.map((contact: any) => {
        return (
          <ChatList
            key={contact?.id}
            contact={contact}
            selectedId={selectedId}
            hotelId={hotelId}
            isCenter={isCenter}
          />
        );
      })}
    </div>
  );
};

export default ChatListContainer;
