"use client";
import React from "react";
import SinglePetTaxiFile from "../../../(business-informations)/pet-taxi-informations/(components)/(petTaxiFiles)/SinglePetTaxiFile";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";

interface FileStepData {
  docType: string;
  name: string;
  condition?: (taxiData: PetTaxiDataApiTypes) => boolean;
}

interface FileStepContentProps {
  taxiData: PetTaxiDataApiTypes;
  petTaxiToken: string | undefined;
  currentStep: number;
  currentStepData?: FileStepData;
  isTransitioning: boolean;
}

const FileStepContent: React.FC<FileStepContentProps> = ({
  taxiData,
  petTaxiToken,
  currentStep,
  currentStepData,
  isTransitioning,
}) => {
  if (!taxiData || !currentStepData) {
    return null;
  }

  return (
    <div className="relative overflow-hidden">
      <div
        key={currentStep}
        className={`transform transition-all duration-700 ease-out ${
          isTransitioning
            ? "opacity-50 scale-95"
            : "opacity-100 scale-100 animate-in slide-in-from-bottom-8 fade-in duration-700"
        }`}
      >
        <SinglePetTaxiFile
          petTaxiToken={petTaxiToken}
          filePath={`petTaxi/${taxiData._id}/files/`}
          docType={currentStepData.docType}
          name={currentStepData.name}
          taxiData={taxiData}
          fullWidth={true}
        />
      </div>
    </div>
  );
};

export default FileStepContent;
