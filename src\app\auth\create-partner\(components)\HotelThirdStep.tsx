"use client";
import type { FC, ChangeEvent } from "react";
import React from "react";
import Checkbox from "@/shared/Checkbox";
import { setHotelFeatures } from "@/store/features/partnerAuth/partner-auth-slice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useTranslations } from "next-intl";

export interface PageAddListing3Props {}

const HotelThirdStep: FC<PageAddListing3Props> = () => {
  const translate = useTranslations("HotelThirdStep");
  const dispatch = useDispatch();
  const hotelFeatures = useSelector(
    (state: RootState) => state.partnerAuth.hotelFeatures
  );

  // check and uncheck checkboxes
  const handleCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    dispatch(setHotelFeatures({ ...hotelFeatures, [name]: checked }));
  };

  // adds hotel features to an array and removes
  const handleHotelFeatures = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          hotelFeaturesArray: [...hotelFeatures.hotelFeaturesArray, name],
        })
      );
    } else {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          hotelFeaturesArray: hotelFeatures.hotelFeaturesArray.filter(
            (feature) => feature !== name
          ),
        })
      );
    }
  };

  // adds accepted pet types to an array and removes
  const handleAcceptedPetTypes = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          acceptedPetTypes: [...hotelFeatures.acceptedPetTypes, name],
        })
      );
    } else {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          acceptedPetTypes: hotelFeatures.acceptedPetTypes.filter(
            (feature) => feature !== name
          ),
        })
      );
    }
  };

  // adds hotel care services to an array and removes
  const handleCareServices = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          hotelCareServices: [...hotelFeatures.hotelCareServices, name],
        })
      );
    } else {
      dispatch(
        setHotelFeatures({
          ...hotelFeatures,
          hotelCareServices: hotelFeatures.hotelCareServices.filter(
            (feature) => feature !== name
          ),
        })
      );
    }
  };

  return (
    <>
      <h2 className="pb-5 text-2xl font-semibold">
        {translate("hotelFeatures")}
      </h2>
      <div className="mb-5 w-14 border-b border-neutral-200 dark:border-neutral-700"></div>
      {/* FORM */}
      <div className="space-y-8">
        {/* ITEM */}

        <div className="space-y-3">
          <div>
            <label className="text-lg font-semibold" htmlFor="">
              {translate("hotelFeatures")}
            </label>
            <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <Checkbox
                label={translate("petTaxi")}
                name="petTaxi"
                id="pet-taxi"
                onChange={(e) => handleHotelFeatures(e)}
              />
              <Checkbox
                label={translate("petCoiffeur")}
                name="petCoiffeur"
                id="pet-coiffeur"
                onChange={(e) => handleHotelFeatures(e)}
              />

              <Checkbox
                label={translate("petNursery")}
                name="petCare"
                id="pet-care"
                onChange={(e) => handleHotelFeatures(e)}
              />
              <Checkbox
                label={translate("pool")}
                name="pool"
                id="pool-id"
                onChange={(e) => handleHotelFeatures(e)}
              />
              <Checkbox
                label={translate("playground")}
                name="playground"
                id="playground-id"
                onChange={(e) => handleHotelFeatures(e)}
              />
              <Checkbox
                label={translate("dogWalking")}
                name="dogWalk"
                id="dog-Walk"
                onChange={(e) => handleHotelFeatures(e)}
              />
            </div>
          </div>
        </div>
        <div className="space-y-3">
          <div>
            <label className="text-lg font-semibold" htmlFor="">
              {translate("hotelAcceptedPetTypes")}
            </label>
            <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <Checkbox
                label={translate("cat")}
                name="cat"
                id="cat-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              />
              <Checkbox
                label={translate("dog")}
                name="dog"
                id="dog-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              />
              <Checkbox
                label={translate("horse")}
                name="horse"
                id="horse-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              />

              <Checkbox
                label={translate("turtle")}
                name="turtle"
                id="turtle-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              />

              <Checkbox
                label={translate("rabbit")}
                name="rabbit"
                id="rabbit-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              />
              {/* <Checkbox
                label="Kuş"
                name="bird"
                id="bird-id"
                onChange={(e) => handleAcceptedPetTypes(e)}
              /> */}
            </div>
          </div>
        </div>
        <div className="space-y-3">
          <div>
            <label className="text-lg font-semibold">
              {translate("hotelCareServices")}
            </label>
            <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
              <Checkbox
                label={translate("veterinary")}
                name="veterinary"
                id="veterinary-id"
                onChange={(e) => handleCareServices(e)}
              />
              <Checkbox
                label={translate("specialNutrition")}
                name="specialNutrition"
                id="special-nutrition"
                onChange={(e) => handleCareServices(e)}
              />
              <Checkbox
                label={translate("grooming")}
                name="grooming"
                id="grooming-id"
                onChange={(e) => handleCareServices(e)}
              />
              <Checkbox
                label={translate("camera")}
                name="camera"
                id="camera-id"
                onChange={(e) => handleCareServices(e)}
              />
              <Checkbox
                label={translate("photoInfoServices")}
                name="photoInfoServices"
                id="photo-info-services"
                onChange={(e) => handleCareServices(e)}
              />
              <Checkbox
                label={translate("specialCondition")}
                name="specialCondition"
                id="special-condition"
                onChange={(e) => handleCareServices(e)}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HotelThirdStep;
