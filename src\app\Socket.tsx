"use client";
import type { ReactNode } from "react";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { showToastNotification } from "@/components/ToastNotification";
import { useSelector, useDispatch } from "react-redux";
import type { Socket } from "socket.io-client";
import { io } from "socket.io-client";
import type { RootState } from "@/store";
import {
  setIsSockedConnected,
  appendChatMessage,
  editChatMessage,
  deleteChatMessage,
  setIsTyping,
  seenChatMessages,
  setChatListMessageContent,
  setChatListEditMessageContent,
} from "@/store/features/liveChat/live-chat-slice";

export interface IncomingMessage {
  type: string;
  [key: string]: any;
}

interface SocketContextType {
  send: (event: string, data: any) => void;
  sendChat: (data: any) => void;
  sendTyping: (data: any) => void;
  sendSeen: (data: any) => void;
  sendPush: (data: any) => void;
  sendEdited: (data: any) => void;
  sendDeleted: (data: any) => void;
  setOnNewNotification: React.Dispatch<
    React.SetStateAction<(() => void) | null>
  >;
  connected: boolean;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  token: string | undefined;
  children: ReactNode;
}

export const SocketProvider = ({ token, children }: SocketProviderProps) => {
  const socketRef = useRef<Socket | null>(null);
  const [onNewNotification, setOnNewNotification] = useState<
    (() => void) | null
  >(null);
  const dispatch = useDispatch();

  const connected = useSelector(
    (state: RootState) => state.liveChatText.isSockedConnected
  );
  const selectedIdValue = useSelector(
    (state: RootState) => state.liveChatText.selectedIdValue
  );
  const selectedIdValueRef = useRef(selectedIdValue);
  selectedIdValueRef.current = selectedIdValue;

  useEffect(() => {
    if (!token) return;
    const socket = io(process.env.NEXT_PUBLIC_SOCKET_URI!, {
      path: "/socket",
      transports: ["websocket"],
      reconnection: true,
    });

    socketRef.current = socket;

    socket.on("connect", () => {
      console.log("[Socket.IO] Connected");
      dispatch(setIsSockedConnected(true));
      socket.emit("init", { token });
    });

    socket.on("disconnect", () => {
      console.log("[Socket.IO] Disconnected");
      dispatch(setIsSockedConnected(false));
    });

    socket.on("chat", (message) => {
      if (
        selectedIdValueRef?.current === message?.from ||
        selectedIdValueRef?.current === message?.to
      ) {
        dispatch(appendChatMessage(message));
      }

      dispatch(
        setChatListMessageContent({
          id1: message?.from,
          id2: message?.to,
          content: message?.text,
          messageId: message?._id,
          seen: false,
          timestamp: message?.timestamp,
          isDeleted: false,
          fileType: message?.fileType,
          fileName: message?.fileName,
        })
      );
    });

    socket.on("edited", (message) => {
      dispatch(
        editChatMessage({
          messageId: message?.messageId,
          text: message?.text,
        })
      );
      dispatch(
        setChatListEditMessageContent({
          id: message?.messageId,
          content: message?.text,
        })
      );
    });

    socket.on("deleted", (message) => {
      dispatch(
        setChatListEditMessageContent({
          id: message?.messageId,
          content: "Bu mesaj silindi",
        })
      );
      dispatch(deleteChatMessage(message?.messageId));
    });

    socket.on("typing", (message) => {
      dispatch(setIsTyping(!!message?.isTyping));
    });

    socket.on("seen", (message) => {
      dispatch(seenChatMessages(message?.messageId));
      dispatch(
        setChatListEditMessageContent({
          id: message?.messageId[message?.messageId?.length - 1],
          seen: true,
        })
      );
    });

    socket.on("push", (message) => {
      const audio = new Audio("/sounds/dog.mp3");
      audio.play();
      showToastNotification({
        title: message.title,
        body: message.body,
        data: message.data,
      });
    });

    return () => {
      socket.disconnect();
    };
  }, [token, dispatch]);

  const send = useCallback((event: string, data: any) => {
    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn("Socket.IO not connected.");
    }
  }, []);

  const sendChat = useCallback(
    (params: {
      from: string;
      to: string;
      text?: string;
      fileUrl?: string;
      fileType?: string;
    }) => {
      send("chat", params);
    },
    [send]
  );

  // API: sendTyping
  const sendTyping = useCallback(
    (params: { from: string; to: string; isTyping: boolean }) => {
      send("typing", params);
    },
    [send]
  );

  // API: sendSeen
  const sendSeen = useCallback(
    (params: { from: string; to: string; messageId: string[] }) => {
      send("seen", params);
    },
    [send]
  );

  // API: sendEdited
  const sendEdited = useCallback(
    (params: {
      from: string;
      to: string;
      messageId: string;
      newText: string;
    }) => {
      send("edited", params);
    },
    [send]
  );

  // API: sendDeleted
  const sendDeleted = useCallback(
    (params: { from: string; to: string; messageId: string }) => {
      send("deleted", params);
    },
    [send]
  );

  // API: sendPush
  const sendPush = useCallback(
    (params: { to: string; title: string; data: string; body: string }) => {
      send("push", params);
    },
    [send]
  );

  const contextValue = useMemo(
    () => ({
      sendChat,
      sendTyping,
      sendSeen,
      sendEdited,
      sendDeleted,
      sendPush,
      send,
      setOnNewNotification,
      connected,
    }),
    [
      sendChat,
      sendTyping,
      sendSeen,
      sendEdited,
      sendDeleted,
      sendPush,
      send,
      setOnNewNotification,
      connected,
    ]
  );

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};
