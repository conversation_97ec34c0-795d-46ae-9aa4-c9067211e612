"use client";
import React, { useState } from "react";
import type { FC } from "react";
import { Paperclip, Images, FileText } from "lucide-react";
import Input from "@/shared/Input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";

interface UploadChatFileProps {
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  setImage: React.Dispatch<React.SetStateAction<string[] | []>>;
  liveChatInputRef: React.RefObject<HTMLInputElement>;
  setPdfFileNames: React.Dispatch<React.SetStateAction<string[] | []>>;
}

const UploadChatFile: FC<UploadChatFileProps> = ({
  setPhotoFileObject,
  setImage,
  liveChatInputRef,
  setPdfFileNames,
}) => {
  const [popoverOpen, setPopverOpen] = useState<boolean>(false);
  return (
    <>
      <Popover open={popoverOpen} onOpenChange={setPopverOpen}>
        <PopoverTrigger onClick={() => setPopverOpen(true)}>
          <Paperclip className="size-5 hover:text-blue-700 duration-200" />
        </PopoverTrigger>
        <PopoverContent
          className="w-32"
          side="top"
          align="start"
          sideOffset={10}
          alignOffset={-5}
        >
          <label className="cursor-pointer">
            <p className="text-sm font-medium flex gap-1 items-center cursor-pointer hover:text-blue-700 duration-200">
              <Images className="size-4" />
              Görsel
            </p>
            <Input
              id="live-chat-image-upload"
              name="live-chat-image-upload"
              type="file"
              className="sr-only"
              accept=".jpg, .jpeg, .png, .webp, .heic, .heif"
              multiple={true}
              ref={liveChatInputRef}
              onChange={(e) => {
                const newFiles = e.target.files;
                //FileList;
                if (newFiles) {
                  setPhotoFileObject((prevFileObject) => {
                    const dataTransfer = new DataTransfer();
                    // Add previous files
                    if (prevFileObject) {
                      Array.from(prevFileObject).forEach((file) =>
                        dataTransfer.items.add(file)
                      );
                    }
                    // Add new files
                    Array.from(newFiles).forEach((file) =>
                      dataTransfer.items.add(file)
                    );
                    return dataTransfer.files; // Updated FileList
                  });
                  const newImageUrls = Array.from(newFiles).map((file) =>
                    URL.createObjectURL(file)
                  );
                  setImage((prevImages: any) => [
                    ...prevImages,
                    ...newImageUrls,
                  ]);
                }
                setPopverOpen(false);
              }}
            />
          </label>
          <Separator className="my-2" />
          <label className="cursor-pointer">
            <p className="text-sm font-medium flex gap-1 items-center cursor-pointer hover:text-blue-700 duration-200">
              <FileText className="size-4" />
              Belge
            </p>
            <Input
              id="live-chat-file-upload"
              name="live-chat-file-upload"
              type="file"
              className="sr-only"
              accept=".pdf"
              multiple={true}
              ref={liveChatInputRef}
              onChange={(e) => {
                const newFiles = e.target.files;
                if (newFiles) {
                  setPhotoFileObject((prevFileObject) => {
                    const dataTransfer = new DataTransfer();
                    if (prevFileObject) {
                      Array.from(prevFileObject).forEach((file) =>
                        dataTransfer.items.add(file)
                      );
                    }
                    Array.from(newFiles).forEach((file) =>
                      dataTransfer.items.add(file)
                    );
                    return dataTransfer.files;
                  });

                  // ✅ PDF dosya adlarını ekle
                  const newPdfNames = Array.from(newFiles).map(
                    (file) => file.name
                  );
                  setPdfFileNames((prev) => [...prev, ...newPdfNames]);
                }
                setPopverOpen(false);
              }}
            />
          </label>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default UploadChatFile;
