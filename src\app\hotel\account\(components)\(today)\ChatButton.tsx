import React from "react";
import type { <PERSON> } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquareMore, SendHorizontal } from "lucide-react";

interface ChatButtonProps {
  petOwnerId: string;
}

const ChatButton: FC<ChatButtonProps> = ({ petOwnerId }) => {
  return (
    <Link href={`/hotel/account/messages?id=${petOwnerId}`}>
      <Button className="group overflow-hidden bg-[#CA3839] hover:bg-[#A52A2B] text-xs lg:w-full text-white rounded-md">
        Pet sahibine mesaj gönder
        <MessageSquareMore className="transition-transform duration-700 translate-x-[1000%] group-hover:translate-x-0 text-white" />
        <SendHorizontal className="transition-transform duration-700 group-hover:translate-x-[1000%] -ml-6 text-white" />
      </Button>
    </Link>
  );
};

export default ChatButton;
