import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { useLogin } from "../useLogin";

export const useDeleteHotel = () => {
  const { toast } = useToast();
  const { logoutUser } = useLogin();

  const deleteHotel = async (
    event: FormEvent,
    hotelToken: string | undefined,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.myHotel, {
          method: "DELETE",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata olu<PERSON>tu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Otel ve Hesap Silme",
          description: "Otel ve hesap başarıyla silindi.",
        });
        logoutUser();
        setLoading(false);
      } catch (error) {
        console.log(error);
      }
    }
  };
  return { deleteHotel };
};
