import { PUBLIC_API_PATHS } from "@/utils/apiUrls";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import type { Route } from "@/routers/types";

export const useHotelAtDoorReservationPublic = () => {
  const { toast } = useToast();
  const router = useRouter();

  const updateReservation = async ({
    updates,
    setLoading,
    route,
  }: {
    updates: any;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
    route: string;
  }) => {
    setLoading(true);
    try {
      const response = await fetch(PUBLIC_API_PATHS.updateReservation, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata olu<PERSON>.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      router.push(route as Route);
    } catch (error) {
      console.log(error);
    }
  };

  const updateService = async ({
    updates,
    setLoading,
    route,
  }: {
    updates: any;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
    route: string;
  }) => {
    setLoading(true);
    try {
      const response = await fetch(PUBLIC_API_PATHS.updateService, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ updates }),
      });
      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 3000,
          // title: "Hata",
          description: `${errorMessage}`,
        });
        setLoading(false);
        throw new Error("Network response was not ok");
      }

      setTimeout(() => {
        setLoading(false);
      }, 2000);
      router.push(route as Route);
    } catch (error) {
      console.log(error);
    }
  };

  return {
    updateReservation,
    updateService,
  };
};
