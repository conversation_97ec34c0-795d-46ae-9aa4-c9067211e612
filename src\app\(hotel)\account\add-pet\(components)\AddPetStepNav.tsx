import React from "react";
import type { FC } from "react";
import IconPawGuest from "@/shared/icons/PawGuest";
import IconCheckCircle from "@/shared/icons/CheckCircle";
import IconInfo from "@/shared/icons/Info";
import IconPlus from "@/shared/icons/Plus";
import IconDocumentEmpty from "@/shared/icons/DocumentEmpty";
import IconPhoto from "@/shared/icons/Photo";

interface AddPetStepNavProps {
  step: number;
}

const AddPetStepNav: FC<AddPetStepNavProps> = ({ step }) => {
  const lineHandler = (firstCondition: boolean, secondCondition: boolean) => {
    const checkedCircle = "bg-[#bff2d4]";

    return firstCondition
      ? checkedCircle
      : secondCondition
        ? "bg-[#d5e4f2] dark:bg-[#7ba9cf]"
        : "bg-gray-200 dark:bg-gray-700";
  };

  const iconHandler = (value: boolean, IconComponent: React.ElementType) => {
    if (value) {
      return <IconCheckCircle className="size-5 text-[#3f894e]" />;
    } else {
      return <IconComponent className="size-5 text-gray-800 dark:text-white" />;
    }
  };

  // Step Icons
  const petIconComponent = iconHandler(step > 1, IconPawGuest);
  const petInformationIconComponent = iconHandler(step > 2, IconInfo);
  const petHealthIconComponent = iconHandler(step > 3, IconPlus);
  const petHabitIconComponent = iconHandler(step > 4, IconDocumentEmpty);
  const petPhotoIconComponent = iconHandler(step > 5, IconPhoto);

  // Step circle and line styles
  const petLineStyle = lineHandler(step > 1, true);
  const petInformationLineStyle = lineHandler(step > 2, true);
  const petHealthLineStyle = lineHandler(step > 3, true);
  const petHabitLineStyle = lineHandler(step > 4, true);
  const petPhotoLineStyle = lineHandler(step > 5, true);

  const addPetSteps: any = [
    {
      id: 1,
      icon: petIconComponent,
      label: "Evcil Hayvan Seçimi",
      style: petLineStyle,
    },
    {
      id: 2,
      icon: petInformationIconComponent,
      label: "Bilgiler",
      style: petInformationLineStyle,
    },
    {
      id: 3,
      icon: petHealthIconComponent,
      label: "Sağlık Bilgileri",
      style: petHealthLineStyle,
    },
    {
      id: 4,
      icon: petHabitIconComponent,
      label: "Alışkanlıklar",
      style: petHabitLineStyle,
    },
    {
      id: 5,
      icon: petPhotoIconComponent,
      label: "Aşı Karnesi",
      style: petPhotoLineStyle,
    },
  ];
  return (
    <div className="pt-5">
      <div className="max-lg:hidden grid grid-cols-5 mb-5 gap-5">
        <div className={`w-full h-1 ${petLineStyle}`}></div>
        <div className={`w-full h-1 ${petInformationLineStyle}`}></div>
        <div className={`w-full h-1 ${petHealthLineStyle}`}></div>
        <div className={`w-full h-1 ${petHabitLineStyle}`}></div>
        <div className={`w-full h-1 ${petPhotoLineStyle}`}></div>
      </div>
      <ol className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-2 items-center w-full">
        {addPetSteps.map((item: any) => (
          <li key={item.id} className="flex w-full gap-2 items-center">
            <span
              className={`flex items-center justify-center w-8 h-8 rounded-full lg:h-10 lg:w-10 shrink-0 ${item.style}`}
            >
              {item.icon}
            </span>
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              {item.label}
            </p>
          </li>
        ))}
      </ol>
    </div>
  );
};

export default AddPetStepNav;
