import React from "react";
import HeroSections from "./(components)/(heroSection)/HeroSections";
import { cookies } from "next/headers";
import getHotelLanding from "@/actions/(protected)/hotel/hotelLanding/getHotelLanding";
import CreateHotelLanding from "./(components)/CreateHotelLanding";

const HotelLandingPages = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelLandingData = await getHotelLanding();

  return (
    <>
      {hotelLandingData ? (
        <HeroSections
          hotelToken={hotelToken}
          heroSectionData={hotelLandingData.data?.data?.heroSection}
        />
      ) : (
        <CreateHotelLanding hotelToken={hotelToken} />
      )}
    </>
  );
};

export default HotelLandingPages;
