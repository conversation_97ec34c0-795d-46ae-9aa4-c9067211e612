import type { <PERSON> } from "react";
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import IconCalendar from "@/shared/icons/Calendar";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface TotalReservationCardProps {
  title?: string;
  data: {
    totalReservations: number;
    canceledReservations: number;
  };
}

const TotalReservationCard: FC<TotalReservationCardProps> = ({
  title = "Title",
  data,
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger className="cursor-default max-lg:shrink-0 md:basis-1/4">
          <Card className="px-3 pb-1 border-secondary-6000 dark:border-neutral-400">
            <CardHeader className="px-2 pt-2 pb-0">
              <CardTitle className="mb-3 flex items-center justify-between gap-1.5 text-xs sm:text-sm font-medium text-neutral-500 dark:text-neutral-400 lg:text-base">
                {title}
                <div className="rounded-md bg-secondary-6000 dark:bg-neutral-400 p-1">
                  <IconCalendar className="size-5 text-white" />
                </div>
              </CardTitle>
              {/* <CardDescription>Card Description</CardDescription> */}
            </CardHeader>
            <CardContent className="text-start font-medium lg:text-xl pt-0 pb-1 px-2">
              <p>{data?.totalReservations}</p>
            </CardContent>
            {/* <CardFooter>
        <p>Card Footer</p>
      </CardFooter> */}
          </Card>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs font-medium">
            Seçili tarihler arasındaki toplam rezervasyon sayısı
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default TotalReservationCard;
