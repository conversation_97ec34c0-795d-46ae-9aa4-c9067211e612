import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface TextState {
  text: string;
  oldText: string;
  messageId: string;
  isEdit: boolean;
  chatData: any;
  isLoading: boolean;
  isNewChatOpen: boolean;
  isSockedConnected: boolean;
  isTyping: boolean;
  chatList: any;
  selectedIdValue: string | string[] | undefined;
}

const initialState: TextState = {
  text: "",
  oldText: "",
  messageId: "",
  isEdit: false,
  chatData: { messages: [] },
  isLoading: false,
  isNewChatOpen: false,
  isSockedConnected: true,
  isTyping: false,
  chatList: { contacts: [] },
  selectedIdValue: undefined,
};

const textSlice = createSlice({
  name: "liveChatText",
  initialState,
  reducers: {
    setText: (state, action: PayloadAction<string>) => {
      state.text = action.payload;
    },
    setOldText: (state, action: PayloadAction<string>) => {
      state.oldText = action.payload;
    },
    setMessageId: (state, action: PayloadAction<string>) => {
      state.messageId = action.payload;
    },
    setIsEdit: (state, action: PayloadAction<boolean>) => {
      state.isEdit = action.payload;
    },
    setIsTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    setSelectedIdValue: (
      state,
      action: PayloadAction<string | string[] | undefined>
    ) => {
      state.selectedIdValue = action.payload;
    },
    setChatData: (state, action: PayloadAction<any>) => {
      state.chatData = action.payload;
    },
    setChatList: (state, action: PayloadAction<any>) => {
      state.chatList = action.payload;
    },
    appendChatMessage: (state, action: PayloadAction<any>) => {
      state.chatData.messages.push(action.payload);
    },
    editChatMessage: (
      state,
      action: PayloadAction<{ messageId: string; text: string }>
    ) => {
      state.chatData.messages = state.chatData.messages.map((msg: any) =>
        msg._id === action.payload.messageId
          ? { ...msg, isEdited: true, text: action.payload.text }
          : msg
      );
    },
    deleteChatMessage: (state, action: PayloadAction<string>) => {
      state.chatData.messages = state.chatData.messages.map((msg: any) =>
        msg._id === action.payload ? { ...msg, isDeleted: true } : msg
      );
    },
    seenChatMessages: (state, action: PayloadAction<string[]>) => {
      state.chatData.messages = state.chatData.messages.map((msg: any) =>
        action.payload.includes(msg._id) ? { ...msg, seen: true } : msg
      );
    },
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setIsNewChatOpen: (state, action: PayloadAction<boolean>) => {
      state.isNewChatOpen = action.payload;
    },
    setIsSockedConnected: (state, action: PayloadAction<boolean>) => {
      state.isSockedConnected = action.payload;
    },
    setChatListMessageContent: (
      state,
      action: PayloadAction<{
        id1: string;
        id2: string;
        content: string;
        messageId: string;
        seen: boolean;
        timestamp: string;
        isDeleted: boolean;
        fileType: string | undefined;
        fileName: string | undefined;
      }>
    ) => {
      state.chatList.contacts = state.chatList.contacts.map((contact: any) =>
        contact.id === action.payload.id1 || contact.id === action.payload.id2
          ? {
              ...contact,
              lastMessage: {
                ...contact.lastMessage,
                content: {
                  ...contact.lastMessage.content,
                  content: action.payload.content,
                  _id: action.payload.messageId,
                  from: action.payload.id1,
                  seen: action.payload.seen,
                  createdAt: action.payload.timestamp,
                  isDeleted: action.payload.isDeleted,
                  fileType: action.payload.fileType,
                  fileName: action.payload.fileName,
                },
              },
            }
          : contact
      );
    },
    setChatListEditMessageContent: (
      state,
      action: PayloadAction<{ id: string; content?: string; seen?: boolean }>
    ) => {
      state.chatList.contacts = state.chatList.contacts.map((contact: any) => {
        if (contact.lastMessage.content._id === action.payload.id) {
          return {
            ...contact,
            lastMessage: {
              ...contact.lastMessage,
              content: {
                ...contact.lastMessage.content,
                ...(action.payload.content !== undefined && {
                  content: action.payload.content,
                }),
                ...(action.payload.seen !== undefined && {
                  seen: action.payload.seen,
                }),
              },
            },
          };
        }
        return contact;
      });
    },
  },
});

export const {
  setText,
  setOldText,
  setMessageId,
  setIsEdit,
  setChatData,
  setIsLoading,
  setIsNewChatOpen,
  setIsSockedConnected,
  appendChatMessage,
  editChatMessage,
  deleteChatMessage,
  setIsTyping,
  seenChatMessages,
  setChatList,
  setChatListMessageContent,
  setChatListEditMessageContent,
  setSelectedIdValue,
} = textSlice.actions;
export default textSlice.reducer;
