import React from "react";
import type { FC } from "react";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { formatPhoneNumber } from "./TodayRoomCard";

interface VetInformationProps {
  selectedRoom: any;
}

const VetInformation: FC<VetInformationProps> = ({ selectedRoom }) => {
  return (
    <div className="">
      <Separator className="my-2" />
      <div className="flex max-md:flex-col md:items-center gap-4 md:gap-6 p-4 md:p-6 mt-6 rounded-xl border border-gray-200 shadow-sm bg-white">
        {/* Avatar */}
        <div className="flex justify-center">
          <div className="w-24 h-24 rounded-full border-2 border-gray-400 overflow-hidden">
            <Image
              src="https://api.dicebear.com/9.x/personas/svg?seed=Liam&eyes=glasses,happy,sunglasses,wink,open"
              width={112}
              height={112}
              alt="vetPhoto"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
        {/* Bilgiler */}
        <div className="flex flex-col gap-2 text-sm text-gray-800">
          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">Veteriner Klinik Adı:</span>
            <span className="font-semibold text-gray-600 capitalize">
              {selectedRoom.reservation?.pet?.vet?.clinicalName || "-"}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">Veteriner:</span>
            <span className="font-semibold text-gray-600 capitalize">
              {`${selectedRoom.reservation?.pet?.vet?.vetName || "-"} ${selectedRoom.reservation?.pet?.vet?.vetLastName || ""}`}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">E-posta:</span>
            <span className="font-semibold text-gray-600">
              {selectedRoom.reservation?.pet?.vet?.vetEmail || "-"}
            </span>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center gap-1">
            <span className="text-gray-600">Telefon:</span>
            <span className="font-semibold text-gray-600">
              {formatPhoneNumber(
                selectedRoom.reservation?.pet?.vet?.vetPhoneNumber || "-"
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VetInformation;
