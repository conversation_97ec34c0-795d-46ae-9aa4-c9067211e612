import React from "react";
import type { FC } from "react";

interface TurkishFlagProps {
  className?: string;
}

const TurkishFlag: FC<TurkishFlagProps> = ({ className = "" }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="28"
      height="28"
      viewBox="0 0 512 513"
      fill="none"
    >
      <g clip-path="url(#clip0_4_7314)">
        <path
          d="M256 512.581C397.385 512.581 512 397.966 512 256.581C512 115.196 397.385 0.581055 256 0.581055C114.615 0.581055 0 115.196 0 256.581C0 397.966 114.615 512.581 256 512.581Z"
          fill="#D80027"
        />
        <path
          d="M252.869 328.929C212.913 328.929 180.521 296.537 180.521 256.581C180.521 216.625 212.913 184.233 252.869 184.233C265.327 184.233 277.049 187.384 287.283 192.929C271.228 177.227 249.271 167.537 225.043 167.537C175.865 167.537 136 207.403 136 256.58C136 305.757 175.866 345.623 225.043 345.623C249.273 345.623 271.229 335.932 287.283 320.231C277.049 325.778 265.327 328.929 252.869 328.929V328.929Z"
          fill="#F0F0F0"
        />
        <path
          d="M321.323 209.767L342.328 238.712L376.345 227.682L355.307 256.602L376.309 285.546L342.304 274.474L321.267 303.394L321.289 267.633L287.283 256.561L321.301 245.531L321.323 209.767Z"
          fill="#F0F0F0"
        />
      </g>
      <defs>
        <clipPath id="clip0_4_7314">
          <rect
            width="512"
            height="512"
            fill="white"
            transform="translate(0 0.581055)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TurkishFlag;
