# See https://help.github.com/articles/ignoring-files/ for more otel-landing ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# JetBrains IDE files
.idea/

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.env
.env.development

*!!__*
* copy*

# Auto Generated PWA files
/public/sw.js
/public/workbox-*.js
/public/worker-*.js
/public/sw.js.map
/public/workbox-*.js.map
/public/worker-*.js.map 


yarn.lock


# Sentry Config File
.env.sentry-build-plugin

.idea