"use client";
import React from "react";
import { Check } from "lucide-react";

interface ProgressBarProps {
  filteredSteps: any[];
  currentStep: number;
  completedSteps: Set<number>;
  isTransitioning: boolean;
  onGoToStep: (stepIndex: number) => void;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  filteredSteps,
  currentStep,
  completedSteps,
  isTransitioning,
  onGoToStep,
}) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        {filteredSteps.map((step, index) => (
          <div key={step.docType} className="flex items-center">
            <button
              onClick={() => onGoToStep(index)}
              disabled={isTransitioning}
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                index === currentStep
                  ? "bg-[#d5e4f2] text-black scale-110"
                  : completedSteps.has(index)
                    ? "bg-green-500 text-white"
                    : "bg-neutral-200 text-neutral-600 hover:bg-neutral-300"
              } ${isTransitioning ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
            >
              {completedSteps.has(index) && index !== currentStep ? (
                <Check className="w-4 h-4" />
              ) : (
                index + 1
              )}
            </button>
            {index < filteredSteps.length && (
              <div
                className={`hidden md:flex w-12 h-1 mx-2 transition-all duration-700 ${
                  index === currentStep
                    ? "bg-[#d5e4f2]"
                    : completedSteps.has(index)
                      ? "bg-green-500"
                      : "bg-neutral-200"
                }`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProgressBar;
