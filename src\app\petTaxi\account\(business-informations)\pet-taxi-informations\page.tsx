import React from "react";
import PetTaxiInformations from "./(components)/(petTaxiInfo)/PetTaxiInformations";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";

const PetTaxiInformationsPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();

  return (
    <>
      <div className="mt-4 md:mt-8">
        <h2 className="text-xl md:text-2xl font-semibold">
          Pet Taksi Bilgileri
        </h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Bu alanda pet taksisine ait logo, isim ve açıklama kısımlarını
          düzenleyerek pet sahiplerinin dikkatini çekebilirsiniz.
        </span>
      </div>
      {taxiData && (
        <PetTaxiInformations
          taxiData={taxiData?.data}
          petTaxiToken={petTaxiToken}
        />
      )}
    </>
  );
};

export default PetTaxiInformationsPage;
