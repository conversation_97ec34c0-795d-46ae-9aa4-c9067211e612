"use client";
import React, { useRef, useEffect } from "react";
import type { FC } from "react";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";

interface DashboardContainterProps {
  navNumber: number;
  setNavNumber: React.Dispatch<React.SetStateAction<number>>;
}

const DashboardInformationNav: FC<DashboardContainterProps> = ({
  navNumber,
  setNavNumber,
}) => {
  const translate = useTranslations("HotelInformationsNav");
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  interface NavItem {
    name: string;
    id: number;
  }
  const navList: NavItem[] = [
    {
      name: "Özet Raporlar",
      id: 1,
    },
    {
      name: "Ödeme Raporları",
      id: 2,
    },
    // {
    //   name: "Rezervasyon Raporları",
    //   id: 3,
    // },
    // {
    //   name: "<PERSON><PERSON><PERSON><PERSON> Raporları",
    //   id: 4,
    // },
    // {
    //   name: "Hizmet Raporları",
    //   id: 5,
    // },
    // {
    //   name: "Rekabet Raporları",
    //   id: 6,
    // },
  ];

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = navList.findIndex((item) => item.id === navNumber);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname]);

  return (
    <div
      ref={navContainerRef}
      className="hiddenScrollbar flex gap-3 overflow-y-hidden bg-transparent px-1 pb-5 text-sm md:overflow-x-auto md:pb-1.5 md:text-base mt-10 mb-4"
    >
      {navList.map((navItem) => (
        <div
          key={navItem.id}
          onClick={() => setNavNumber(navItem.id)}
          className={`${navNumber === navItem.id ? "bg-secondary-6000 text-white font-medium" : "bg-transparent"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 text-neutral-700 dark:text-neutral-200`}
        >
          {navItem.name}
        </div>
      ))}
    </div>
  );
};

export default DashboardInformationNav;
