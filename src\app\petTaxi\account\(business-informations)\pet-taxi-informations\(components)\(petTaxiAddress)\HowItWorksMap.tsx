import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import IconQuestion from "@/shared/icons/Question";

const HowItWorksMap = () => {
  return (
    <Dialog>
      <DialogTrigger className="text-sm hover:text-secondary-6000 duration-200 flex items-center font-medium text-neutral-500 dark:text-neutral-300 leading-8">
        <span className="relative flex h-2.5 w-2.5 mr-1">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-700 opacity-75"></span>
          <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-secondary-6000"></span>
        </span>
        Nasıl Çalışır <IconQuestion className="size-4 ml-0.5" strokeWidth={2} />
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle><PERSON><PERSON></DialogTitle>
          <DialogDescription className="sr-only"></DialogDescription>
        </DialogHeader>
        <ul className="list-decimal space-y-2 text-sm">
          <li>
            <span className="font-semibold">https://www.google.com/maps</span>{" "}
            adresine gidin ve taksinizin konumunu haritadan seçin.
          </li>
          <li>
            Seçimi yaptıktan sonra paylaş tuşuna basın ve açılan pencereden{" "}
            <span className="font-semibold">harita yerleştirme </span>
            sekmesine tıklayın ve ardından{" "}
            <span className="font-semibold">HTML'Yİ KOPYALA</span>'ya basın.
          </li>
          <li>
            Panelde bulunan{" "}
            <span className="font-semibold">
              Google Harita Yerleştirme HTML Alanı'
            </span>
            na kopyaladığınız konumu yapıştırın ve{" "}
            <span className="font-semibold">Haritada göster</span> tuşuna basın.
          </li>
          <li>
            Aşağıdaki haritada konumunuzu göreceksiniz. Konumunuz doğruysa{" "}
            <span className="font-semibold">Kaydet</span> tuşuna basarak
            taksinizin harita konumunu kaydedebilirsiniz.{" "}
          </li>
        </ul>
      </DialogContent>
    </Dialog>
  );
};

export default HowItWorksMap;
