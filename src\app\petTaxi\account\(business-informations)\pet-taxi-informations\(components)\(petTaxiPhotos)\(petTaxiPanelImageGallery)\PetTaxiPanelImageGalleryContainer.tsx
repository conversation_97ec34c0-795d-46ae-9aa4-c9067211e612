"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import PetTaxiPanelImageGallery from "./PetTaxiPanelImageGallery";
import type { ImageType } from "@/types/taxi/taxiDataType";

const PetTaxiPanelImageGalleryContainer = ({
  taxiPhotos,
}: {
  taxiPhotos: ImageType[];
}) => {
  const searchParams = useSearchParams();
  const modal = searchParams?.get("modal");

  return (
    <>
      <PetTaxiPanelImageGallery
        isShowModal={modal === "PET_TAXI_PHOTOS_SCROLLABLE"}
        taxiPhotos={taxiPhotos}
      />
    </>
  );
};

export default PetTaxiPanelImageGalleryContainer;
