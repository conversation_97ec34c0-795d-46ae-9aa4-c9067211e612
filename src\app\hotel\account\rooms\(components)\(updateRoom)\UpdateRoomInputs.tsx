"use client";
import type { FC } from "react";
import React from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import IconCancelSolid from "@/shared/icons/CancelSolid";
import type { UpdateRoomInputsProps } from "@/types/hotel/rooms/updateRoomTypes";
import { PET_TYPES } from "@/app/(enums)/enums";
import Checkbox from "@/shared/Checkbox";

const UpdateRoomInputs: FC<UpdateRoomInputsProps> = ({
  updateRoomInputs,
  roomFeatureNames,
  handlePetTypeCheckboxChange,
  handleChange,
  hotelFeaturesRemoveHandler,
  roomFeaturesHandler,
  roomFeaturesNameHandler,
  textAreaHandleChange,
  handleRoomFeatures,
  acceptedPetTypes,
}) => {
  const translate = useTranslations("UpdateAndCreateNewRoomModal");
  const petTypes = acceptedPetTypes.map((petType) => ({
    id: petType,
    label: PET_TYPES[petType as keyof typeof PET_TYPES],
  }));

  return (
    <div className="space-y-8">
      <div className="grid gap-3 max-md:space-y-2 md:grid-cols-2">
        <FormItem label={translate("hotelRoomGroupName")}>
          <Input
            name="roomGroupName"
            value={updateRoomInputs.roomGroupName}
            onChange={handleChange}
          />
        </FormItem>
        <FormItem label={translate("hotelRoomCapacity")}>
          <Input
            className="invalid:border-red-500"
            name="roomCapacity"
            type="number"
            min={1}
            value={updateRoomInputs.roomCapacity}
            onChange={handleChange}
          />
        </FormItem>
      </div>
      <div>
        <div className="nc-Label text-sm font-medium text-neutral-700 dark:text-neutral-300">
          {translate("petType")}
        </div>
        <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          {petTypes.map((pet, index) => {
            return (
              <div key={index}>
                <Checkbox
                  key={pet.id}
                  id={pet.id}
                  label={pet.label}
                  name={pet.label}
                  checked={updateRoomInputs.petType.includes(pet.id)}
                  onChange={handlePetTypeCheckboxChange}
                />
              </div>
            );
          })}
        </div>
      </div>
      <div>
        <div className="nc-Label mb-2 text-sm font-medium text-neutral-700 dark:text-neutral-300">
          {translate("roomFeatures")}
        </div>
        <FormItem className="rounded-2xl border">
          {updateRoomInputs.roomFeatures &&
            updateRoomInputs.roomFeatures.length > 0 && (
              <div className="flex flex-wrap items-center gap-3 px-3 pt-1">
                {updateRoomInputs.roomFeatures.map(
                  (items: string, index: number) => {
                    return (
                      <div
                        className="flex items-center gap-1 rounded-full bg-secondary-500 px-3 py-0.5 text-sm capitalize text-white"
                        key={index}
                      >
                        {items}
                        <IconCancelSolid
                          onClick={() => hotelFeaturesRemoveHandler(index)}
                          className="size-[17px] cursor-pointer"
                        />
                      </div>
                    );
                  }
                )}
              </div>
            )}
          <Input
            className="!focus:outline-none border-none placeholder:text-xs focus:border-none focus:ring-0"
            placeholder=""
            onKeyDown={roomFeaturesHandler}
            onChange={(e) => roomFeaturesNameHandler(e)}
            value={roomFeatureNames}
          />
        </FormItem>
        <span className="mt-3 block text-xs text-neutral-500 dark:text-neutral-400 ">
          {translate("roomFeaturesDesc")}
        </span>
        <div className="pr-1 pt-5 text-right">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleRoomFeatures}
          >
            {translate("add")}
          </Button>
        </div>
        <div className="mb-5 grid gap-3 max-md:space-y-2 md:grid-cols-1">
          <FormItem
            label={translate("roomInformation")}
            desc={translate("roomInformationDesc")}
          >
            <Textarea
              className="mt-1.5"
              name="roomDescription"
              value={updateRoomInputs.roomDescription}
              onChange={textAreaHandleChange}
            />
          </FormItem>
        </div>
      </div>
    </div>
  );
};

export default UpdateRoomInputs;
