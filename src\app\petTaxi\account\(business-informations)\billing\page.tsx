import React from "react";
import BillingContainer from "./BillingContainer";
import { cookies } from "next/headers";
import getTaxiSubMerchant from "@/actions/(protected)/taxi/getTaxiSubMerchant";

const AccountBilling = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const petTaxiSubMerchantData = await getTaxiSubMerchant();

  return (
    <BillingContainer
      petTaxiToken={petTaxiToken}
      petTaxiSubMerchantData={petTaxiSubMerchantData?.data}
    />
  );
};

export default AccountBilling;
