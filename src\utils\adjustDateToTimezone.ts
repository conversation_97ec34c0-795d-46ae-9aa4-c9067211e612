/**
 * This function adjusts a given date to a specific timezone by:
 * 1. Parsing the input if it's a string or directly using it if it's already a Date object.
 * 2. Compensating for the system's timezone offset to align the date with UTC.
 * 3. Setting the UTC time to 6:00 AM while preserving the adjusted date.
 *
 * Example:
 * const date = "2024-11-20T15:00:00Z"; // A UTC date
 * const adjustedDate = adjustDateToTimezone(date);
 * console.log(adjustedDate);
 * // Output: 2024-11-20T06:00:00.000Z (UTC time set to 6 AM)
 *
 * If the input is undefined, the function returns undefined.
 */

export const adjustDateToTimezone = (date: Date | string | undefined) => {
  if (!date) return undefined;
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const adjustedDate = new Date(
    dateObj.getTime() - dateObj.getTimezoneOffset() * 60000
  );
  adjustedDate.setUTCHours(12, 0, 0, 0);
  return adjustedDate;
};
