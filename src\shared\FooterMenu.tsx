import React from "react";
import type { CustomLink } from "@/data/types";
import IconAppStore from "@/components/AppStore";
import IconGooglePlay from "@/components/GooglePlay";
import ShimmerButtonComponent from "./ShimmerButton";
import Link from "next/link";
import type { Route } from "@/routers/types";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import LangDropdown from "@/app/(components)/(Header)/LangDropdownSingle";

export interface WidgetFooter {
  id: string;
  title: string;
  menus: CustomLink[];
}

export interface FooterProps {
  onClickClose?: () => void;
}

const FooterMenu: React.FC<FooterProps> = () => {
  const widgetMenus: WidgetFooter[] = [
    {
      id: "1",
      title: "Destek",
      menus: [
        { href: "/", label: "Yardım Merkezi" },
        { href: "/", label: "Pawbooker AI" },
        { href: "/cancellation-refund-policy", label: "İptal Seçenekleri" },
        { href: "/FAQ", label: "S.S.S" },
      ],
    },
    {
      id: "2",
      title: "Pet Hotel sahipliği",
      menus: [
        {
          href: "/",
          label: "Pet Otelinizi PawBooking'e Taşıyın",
        },
        {
          href: "/",
          label: "PawBooking Partnerliği Hakkında",
        },
      ],
    },
    {
      id: "3",
      title: "Faydalı linkler",
      menus: [
        {
          href: "/about-us",
          label: "Hakkımızda",
        },
        {
          href: "/",
          label: "Kariyer",
        },
        {
          href: "/",
          label: "Blog",
        },
        {
          href: "/privacy-policy",
          label: "Kişisel Veriler",
        },
      ],
    },
  ];
  const renderWidgetMenuItem = (menu: WidgetFooter, index: number) => {
    return (
      <div key={index} className="text-sm max-md:border-b max-md:pb-5">
        <h2 className="mt-5 font-semibold text-neutral-700 dark:text-neutral-200">
          {menu.title}
        </h2>
        <ul className="mt-2 space-y-2">
          {menu.menus.map((item, index) => (
            <li key={index}>
              <Link
                className={`group inline-flex items-center gap-1.5 text-2xl text-neutral-6000 hover:text-black dark:text-neutral-300 dark:hover:text-white ${
                  item.label === "Pawbooker AI"
                    ? "pointer-events-none opacity-70"
                    : ""
                }`}
                href={item.href as Route}
              >
                {item.icon && <i className={` ${item.icon}`}></i>}
                <span className="text-sm"> {item.label}</span>
              </Link>
            </li>
          ))}
        </ul>
        {index === 1 && (
          <div className="mt-5 text-start">
            <Link href="/auth/create-partner">
              <ShimmerButtonComponent>
                Hemen Otelini Kaydet
              </ShimmerButtonComponent>
            </Link>
          </div>
        )}
        {index === 2 && (
          <div className="mt-5 flex space-x-4 text-start">
            <a
              href="https://instagram.com/pawbooking"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center rounded-full bg-[#f63c07] p-2 text-xl text-white duration-200 hover:bg-secondary-700"
            >
              <i className="lab la-instagram"></i>
            </a>
            <a
              href="https://www.linkedin.com/company/pawbooking"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center rounded-full bg-[#f63c07] p-2 text-xl text-white duration-200 hover:bg-secondary-700"
            >
              <i className="lab la-linkedin-in"></i>
            </a>
            <a
              href="https://www.facebook.com/profile.php?id=61567866060813"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center rounded-full bg-[#f63c07] p-2 text-xl text-white duration-200 hover:bg-secondary-700"
            >
              <i className="lab la-facebook-f"></i>
            </a>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="relative w-full pb-12 md:pb-24 md:pt-10">
        <div className="grid grid-cols-1 gap-x-6 sm:grid-cols-2 md:grid-cols-3 md:justify-items-center lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4">
          {widgetMenus.map(renderWidgetMenuItem)}
          <div className="flex flex-col items-start space-y-1 max-md:mt-5 md:items-center md:justify-center">
            <div className="w-36 cursor-pointer">
              <IconAppStore />
            </div>
            <div className="w-36 cursor-pointer">
              <IconGooglePlay />
            </div>
            <div className="flex">
              <LangDropdown text className="" />
              <SwitchDarkMode />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FooterMenu;
