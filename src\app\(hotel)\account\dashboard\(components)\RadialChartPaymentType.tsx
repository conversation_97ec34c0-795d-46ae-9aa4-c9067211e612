"use client";
import React from "react";
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "@/components/ui/chart";

const LABELS: Record<string, string> = {
  creditCard: "Kredi <PERSON>",
  cash: "Nakit",
  mailOrder: "Mail Order",
  transfer: "Havale",
  online: "Online",
  unknown: "Bilinmeyen",
};

const COLORS: Record<string, string> = {
  creditCard: "#6A4FFF",
  cash: "#00B8FF",
  mailOrder: "#DDDD2D",
  transfer: "#37B855",
  online: "#FF4DB8",
  unknown: "#A0A0A0",
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-md shadow-md p-2 text-sm space-y-2">
        {payload.map((item: any) => (
          <div key={item.name}>
            <p className="font-medium">{item.name}</p>
            <p className="text-muted-foreground">
              {item.value.toLocaleString()} işlem
            </p>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export function PaymentTypeComponent({
  paymentTypes,
  startDate,
  endDate,
}: {
  paymentTypes: { type: string; count: number }[];
  startDate: string | string[];
  endDate: string | string[];
}) {
  const chartData = paymentTypes.reduce(
    (acc, item) => ({ ...acc, [item.type]: item.count }),
    { month: "2025" }
  );

  const totalPayments = paymentTypes.reduce((sum, item) => sum + item.count, 0);

  const chartConfig = paymentTypes.reduce((acc, item) => {
    acc[item.type] = {
      label: LABELS[item.type] || item.type,
      color: COLORS[item.type] || "#CCCCCC",
    };
    return acc;
  }, {} as ChartConfig);

  const percentages = paymentTypes.reduce(
    (acc, item) => {
      acc[item.type] = Math.round((item.count / totalPayments) * 100);
      return acc;
    },
    {} as Record<string, number>
  );

  const formatIsoDate = (isoDate: string | string[]): string => {
    const dateStr = Array.isArray(isoDate) ? isoDate[0] : isoDate;

    const [year, month, day] = dateStr.split("-");
    return `${day}.${month}.${year}`;
  };

  return (
    <Card className="flex flex-col min-h-[515px]">
      <CardHeader className="items-center pb-0">
        <CardTitle>Ödeme Tipleri</CardTitle>
        <CardDescription className="font-medium">
          {formatIsoDate(startDate) + " - " + formatIsoDate(endDate)}
        </CardDescription>
      </CardHeader>

      <CardContent className="flex flex-1 items-center justify-center pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square w-full h-[250px] max-w-[250px]"
        >
          <RadialBarChart
            width={250}
            height={250}
            data={[chartData]}
            endAngle={360}
            startAngle={0}
            innerRadius={80}
            outerRadius={130}
            barSize={15}
          >
            <ChartTooltip
              cursor={false}
              content={<CustomTooltip total={totalPayments} />}
              wrapperStyle={{ zIndex: 100 }}
            />
            <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle">
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) - 16}
                          className="fill-foreground text-2xl font-bold"
                        >
                          {totalPayments.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 4}
                          className="fill-muted-foreground"
                        >
                          İşlem
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </PolarRadiusAxis>
            {paymentTypes.map(({ type }) => (
              <RadialBar
                key={type}
                dataKey={type}
                stackId="a"
                cornerRadius={8}
                fill={COLORS[type] || "#CCCCCC"}
                className="stroke-background stroke-[5px]"
                name={LABELS[type] || type}
              />
            ))}
          </RadialBarChart>
        </ChartContainer>
      </CardContent>

      <div className="px-6 py-4 space-y-2">
        <div className="flex flex-col">
          <div className="grid grid-cols-[auto_1fr_auto] gap-x-2 w-full">
            {paymentTypes.map(({ type }) => (
              <React.Fragment key={type}>
                <div
                  className="w-3 h-3 rounded-full mt-1"
                  style={{ backgroundColor: COLORS[type] || "#CCCCCC" }}
                ></div>
                <span className="text-sm">{LABELS[type] || type}</span>
                <span className="font-medium text-right w-12">
                  {percentages[type]}%
                </span>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      {/* <CardFooter className="flex-col gap-2 text-sm pt-0">
        <div className="flex items-center gap-2 font-medium leading-none">
          Geçen aya göre %5.2 artış <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          2025 yılı toplam ödeme dağılımı
        </div>
      </CardFooter> */}
    </Card>
  );
}
