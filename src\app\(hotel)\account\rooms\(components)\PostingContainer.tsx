"use client";
import type { <PERSON> } from "react";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import type {
  RoomGroupDataApiTypes,
  RoomGroupListType,
} from "@/types/hotel/roomGroupType";
import dynamic from "next/dynamic";
import HotelRoomCardx from "./HotelRoomCardx";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
import { Button } from "@/components/ui/button";
import PlusFeatureModal from "@/components/PlusFeatureModal";
import PawPlus from "@/shared/PawPlus";

interface PostingContainerProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
  roomGroupData: RoomGroupListType;
  membershipData: any;
}

const CreateRoomModal = dynamic(() => import("./(createRoom)/CreateRoomModal"));

const PostingContainer: FC<PostingContainerProps> = ({
  hotelData,
  hotelToken,
  roomGroupData,
  membershipData,
}) => {
  const translate = useTranslations("PostingContainer");
  const [modalIsVisible, setModalIsVisible] = useState(false);

  const maxRoomLimit = membershipData?.membershipType === "plus" ? Infinity : 3;

  return (
    <>
      {roomGroupData?.length === 0 ? (
        <>
          <div className="mb-5 text-center text-lg font-semibold">
            {translate("PostingContainer")}
          </div>
          <div className="flex items-center justify-center w-full">
            <CreateRoomModal hotel={hotelData} hotelToken={hotelToken} />
          </div>
        </>
      ) : roomGroupData?.length < maxRoomLimit ? (
        <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
          <div className="mt-4 md:my-8">
            <h2 className="text-xl md:text-2xl font-semibold">
              Oda Grubu Oluşturma ve Düzenleme
            </h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300"></span>
          </div>
          <CreateRoomModal hotel={hotelData} hotelToken={hotelToken} />
        </div>
      ) : (
        <div className="flex max-md:flex-col max-md:items-start max-md:space-y-3 justify-between items-center">
          <div className="mt-4 md:my-8">
            <h2 className="text-xl md:text-2xl font-semibold">
              Oda Grubu Oluşturma ve Düzenleme
            </h2>
            <span className="text-sm text-neutral-500 dark:text-neutral-300"></span>
          </div>
          <Button
            type="button"
            className="bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
            onClick={() => setModalIsVisible(true)}
          >
            <PawPlus width="20" height="20" />
            Yeni Oda Grubu Ekle
          </Button>
        </div>
      )}
      <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
        {roomGroupData &&
          roomGroupData?.length > 0 &&
          roomGroupData?.map((roomGroup: RoomGroupDataApiTypes) => (
            <HotelRoomCardx
              hotel={hotelData}
              roomGroup={roomGroup}
              key={roomGroup._id}
              hotelToken={hotelToken}
              acceptedPetTypes={hotelData.acceptedPetTypes}
            />
          ))}
      </div>
      {modalIsVisible && (
        <PlusFeatureModal
          isOpen={modalIsVisible}
          setIsOpen={setModalIsVisible}
          message="Daha fazla oda grubu oluşturmak için Plus üye olmalısınız."
        />
      )}
    </>
  );
};

export default PostingContainer;
