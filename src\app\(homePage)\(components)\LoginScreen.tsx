import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, Lock } from "lucide-react";

export function LoginScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-orange-500 rounded-2xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">🐾</span>
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            PawBooking
          </CardTitle>
          <Badge variant="secondary" className="mx-auto">
            Partner Girişi
          </Badge>
          <CardDescription className="text-gray-600 mt-2">
            Partner panelinize giriş yapın
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">E-posta</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className="pl-10"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Şifre</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex items-center justify-between text-sm">
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span className="text-gray-600">Beni hatırla</span>
            </label>
            <a href="#" className="text-orange-500 hover:text-orange-600">
              Şifremi unuttum
            </a>
          </div>
          <Button className="w-full bg-orange-500 hover:bg-orange-600">
            Giriş Yap
          </Button>
          <div className="text-center text-sm text-gray-600">
            Henüz partner değil misiniz?{" "}
            <a
              href="#"
              className="text-orange-500 hover:text-orange-600 font-medium"
            >
              Ücretsiz kayıt olun
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
