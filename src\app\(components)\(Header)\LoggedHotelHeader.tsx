import type { FC } from "react";
import React from "react";
import { cookies } from "next/headers";
import HotelAvatarDropdown from "./HotelAvatarDropdown";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";
import Link from "next/link";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import HotelMenuBar from "@/shared/HotelMenuBar";
import { Button } from "@/components/ui/button";
import NotificationDropdownWrapper from "./NotificationDropdownWrapper";

export interface LoggedHotelHeaderProps {
  className?: string;
}

const LoggedHotelHeader: FC<LoggedHotelHeaderProps> = async ({
  className = "",
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div className={`MainNav2 relative z-10 ${className}`}>
      <div className={`flex justify-between px-4 lg:container md:h-20`}>
        <div className="hidden flex-1 justify-start space-x-3 sm:space-x-8 md:flex lg:space-x-10">
          <Link href="/account" className="self-center">
            <PawBookingLogo className="size-16 self-center" />
          </Link>
          <div className="hidden h-10 self-center border-l border-neutral-300 dark:border-neutral-500 lg:block"></div>
        </div>
        <div className="hidden flex-1 shrink-0 justify-end text-neutral-700 dark:text-neutral-100 md:flex lg:flex-none">
          <div className="flex space-x-2 lg:space-x-1">
            {membershipData?.data?.membershipType === "free" && (
              <div className="flex items-center justify-center">
                <Link href="/checkout">
                  <Button className="bg-[#FFD700] hover:bg-yellow-500 text-white">
                    Plus üye ol
                  </Button>
                </Link>
              </div>
            )}
            <SwitchDarkMode className="mx-0.5" />
            <LangDropdownSingle className="hidden lg:flex" />
            <NotificationDropdownWrapper hotelToken={hotelToken} />
            {hotelData?.data && (
              <HotelAvatarDropdown
                logo={hotelData?.data?.logo}
                legalName={hotelData?.data?.hotelName}
              />
            )}
            {/* <HotelMenuBar /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoggedHotelHeader;
