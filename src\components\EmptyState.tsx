import React from "react";
import IconCalendar from "@/shared/icons/Calendar";

interface EmptyStateProps {
  text: string;
  subtext?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ text, subtext }) => {
  return (
    <div className="flex flex-col items-center justify-center rounded-lg border-2 border-gray-200 px-4 py-12">
      <IconCalendar className="mb-4 size-20 text-gray-400" />
      <p className="text-center text-xl font-semibold text-gray-600">{text}</p>
      {subtext && (
        <p className="mt-2 text-center text-sm text-gray-500">{subtext}</p>
      )}
    </div>
  );
};

export default EmptyState;
