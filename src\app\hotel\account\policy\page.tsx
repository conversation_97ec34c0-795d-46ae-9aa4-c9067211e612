import React from "react";
import { cookies } from "next/headers";
import getCancellationPolicyInformations from "@/actions/(protected)/policy/getCancellationPolicyInformation";
import Policy from "./(components)/Policy";

const CancellationPoliciesPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const cancellationPolicyInformationsData =
    await getCancellationPolicyInformations();

  return (
    <div className="container pt-8">
      <Policy
        cancellationPolicyInformationsData={
          cancellationPolicyInformationsData?.data
        }
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default CancellationPoliciesPage;
