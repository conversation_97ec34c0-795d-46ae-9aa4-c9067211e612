import type { FC } from "react";
import React from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import Link from "next/link";
import PaginationDropdown from "./PaginationDropdown";
import { useTranslations } from "next-intl";
interface ReservationPaginationProps {
  reservationPage: number;
  pageCount: number;
  tab: string | string[];
}

const ReservationPagination: FC<ReservationPaginationProps> = ({
  reservationPage,
  pageCount,
  tab,
}) => {
  const translate = useTranslations("ReservationPagination");

  return (
    <Pagination>
      <PaginationContent>
        {+reservationPage > 1 && (
          <PaginationItem>
            <Link
              href={`/hotel/account/reservations?page=${+reservationPage - 1}&tab=${tab}`}
            >
              <PaginationPrevious>{translate("previous")}</PaginationPrevious>
            </Link>
          </PaginationItem>
        )}
        <PaginationDropdown
          reservationPage={reservationPage}
          pageCount={pageCount}
          tab={tab}
        />
        {reservationPage < pageCount && (
          <PaginationItem>
            <Link
              href={`/hotel/account/reservations?page=${+reservationPage + 1}&tab=${tab}`}
            >
              <PaginationNext>{translate("next")}</PaginationNext>
            </Link>
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  );
};

export default ReservationPagination;
