import { useEffect, useRef } from "react";

type Dependency = unknown;
type DependencyName = string | number;
type ChangedDeps = Record<
  DependencyName,
  { before: Dependency; after: Dependency }
>;

export const useEffectDebug = (
  effectHook: () => void,
  dependencies: Dependency[],
  dependencyNames: DependencyName[] = []
): void => {
  const previousDeps = usePrevious(dependencies, []);

  const changedDeps = dependencies.reduce<ChangedDeps>(
    (accum, dependency, index) => {
      if (dependency !== previousDeps[index]) {
        const keyName = dependencyNames[index] || index;
        return {
          ...accum,
          [keyName]: {
            before: previousDeps[index],
            after: dependency,
          },
        };
      }

      return accum;
    },
    {}
  );

  if (Object.keys(changedDeps).length) {
    console.info("[use-effect-debugger] ", changedDeps);
  }

  useEffect(effectHook, dependencies);
};

const usePrevious = <T>(value: T, initialValue: T): T => {
  const ref = useRef<T>(initialValue);
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
};
