"use client";
import React, { useMemo, useRef, useEffect } from "react";
import type { FC } from "react";

interface NavItem {
  id: number;
  title: string;
  slug: string;
  passive: boolean;
}

interface UpdatePetNavProps {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
}

const UpdatePetNav: FC<UpdatePetNavProps> = ({ step, setStep }) => {
  const navContainerRef = useRef<HTMLDivElement>(null);
  const listNav: NavItem[] = useMemo(
    () => [
      {
        id: 1,
        title: "Bilgi<PERSON>",
        slug: "information",
        passive: false,
      },
      {
        id: 2,
        title: "Sağlık Bilgileri",
        slug: "",
        passive: false,
      },
      {
        id: 3,
        title: "Alışkanlıklar",
        slug: "habits",
        passive: false,
      },
      {
        id: 4,
        title: "<PERSON><PERSON><PERSON>",
        slug: "service",
        role: "otel",
        passive: false,
      },
    ],
    []
  );

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = listNav.findIndex((item: any) => item.id === step);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [step, listNav]);

  return (
    <div className="container mt-3">
      <div
        ref={navContainerRef}
        className="hiddenScrollbar flex justify-between md:justify-center gap-3 overflow-y-hidden bg-transparent px-1 pb-5 text-sm md:overflow-x-auto md:pb-1.5 md:text-base"
      >
        {listNav.map((navItem) => (
          <div
            key={navItem.id}
            onClick={() => setStep(navItem.id)}
            className={`${navItem.id === step ? "bg-secondary-6000 text-white font-medium" : "bg-transparent"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 text-neutral-700 dark:text-neutral-200`}
          >
            {navItem.title}
          </div>
        ))}
      </div>
    </div>
  );
};

export default UpdatePetNav;
