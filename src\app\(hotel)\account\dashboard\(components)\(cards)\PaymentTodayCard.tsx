import React from "react";
import type { FC } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import Icon<PERSON>hart from "@/shared/icons/Chart";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import IconMinus from "@/shared/icons/Minus";
import IconChevronUp from "@/shared/icons/ChevronUp";
import IconChevronDown from "@/shared/icons/ChevronDown";

interface PaymentTodayCardProps {
  todayDepositAmount: number;
  todayChangePercent: number;
}

const PaymentTodayCard: FC<PaymentTodayCardProps> = ({
  todayDepositAmount,
  todayChangePercent,
}) => {
  const isNegativeChange = todayChangePercent < 0;
  const changePercentAbs = Math.abs(todayChangePercent);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger className="cursor-default max-lg:shrink-0 md:basis-1/4">
            <Card className="px-3 pb-1 border-secondary-6000">
              <CardHeader className="px-2 pt-2 pb-0">
                <CardTitle className="flex items-center justify-between gap-1.5 text-xs sm:text-sm font-medium text-neutral-500 dark:text-neutral-400 lg:text-base">
                  Bugün Gelecek Para
                  <div className="rounded-md bg-secondary-6000 p-1">
                    <IconChart className="size-5 text-white" />
                  </div>
                </CardTitle>
                {/* <CardDescription>Card Description</CardDescription> */}
              </CardHeader>
              <CardContent className="text-start font-medium lg:text-xl pt-0 pb-1 px-2">
                <p>{todayDepositAmount.toLocaleString("tr-TR")}₺</p>
                <div className="pt-2">
                  {todayChangePercent === 0 ? (
                    <IconMinus className="size-6 text-gray-400" />
                  ) : (
                    <div
                      className={`${
                        isNegativeChange
                          ? "text-white bg-[#ee3939]"
                          : "text-[#14a23e] bg-[#e6f5eb]"
                      } font-medium px-1.5 py-0.5 text-xs md:text-sm rounded-xl w-16`}
                    >
                      <div className="flex items-center justify-center gap-0.5">
                        <span className="text-xs md:text-sm">%</span>
                        {changePercentAbs.toFixed(2)}
                        {isNegativeChange ? (
                          <IconChevronDown
                            className="size-4"
                            strokeWidth={3.5}
                          />
                        ) : (
                          <IconChevronUp className="size-4" strokeWidth={3.5} />
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              {/* <CardFooter>
        <p>Card Footer</p>
      </CardFooter> */}
            </Card>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">
              Bugün hesabınıza yatacak olan para
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

export default PaymentTodayCard;
