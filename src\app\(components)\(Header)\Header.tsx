"use client";
import React, { useEffect, useRef, useState } from "react";
import type { PathName } from "@/routers/types";
import { usePathname } from "next/navigation";
import { useThemeMode } from "@/hooks/useThemeMode";

export type SiteHeaders = "Header 1" | "Header 2" | "Header 3";

interface HomePageItem {
  name: string;
  slug: PathName;
}

const OPTIONS = {
  root: null,
  rootMargin: "0px",
  threshold: 1.0,
};
let OBSERVER: IntersectionObserver | null = null;
const PAGES_HIDE_HEADER_BORDER = [
  "/listing-car-detail",
  "/listing-experiences-detail",
  "/listing-room-detail",
];

const Header = () => {
  const anchorRef = useRef<HTMLDivElement>(null);

  const [headers] = useState<SiteHeaders[]>(["Header 1"]);

  const [homePages] = useState<HomePageItem[]>([
    { name: "Home Main", slug: "/" },
  ]);
  const [headerSelected, setHeaderSelected] = useState<SiteHeaders>("Header 1");

  const [isTopOfPage, setIsTopOfPage] = useState(true);

  useEffect(() => {
    setIsTopOfPage(window.pageYOffset < 5);
  }, []);
  //
  useThemeMode();
  //
  const pathname = usePathname();

  const intersectionCallback = (entries: IntersectionObserverEntry[]) => {
    entries.forEach((entry) => {
      setIsTopOfPage(entry.isIntersecting);
    });
  };

  useEffect(() => {
    // disconnect the observer
    // observer for show the LINE bellow header
    if (!PAGES_HIDE_HEADER_BORDER.includes(pathname as PathName)) {
      OBSERVER && OBSERVER.disconnect();
      OBSERVER = null;
      return;
    }
    if (!OBSERVER) {
      OBSERVER = new IntersectionObserver(intersectionCallback, OPTIONS);
      anchorRef.current && OBSERVER.observe(anchorRef.current);
    }
  }, [pathname]);

  const renderHeader = () => {
    let headerClassName = "shadow-sm dark:border-b dark:border-neutral-700";
    if (PAGES_HIDE_HEADER_BORDER.includes(pathname as PathName)) {
      headerClassName = isTopOfPage
        ? ""
        : "shadow-sm dark:border-b dark:border-neutral-700";
    }
  };

  return (
    <>
      {/* <MainNav1 /> */}
      <div ref={anchorRef} className="invisible absolute h-1"></div>
    </>
  );
};

export default Header;
