"use client";
import React, { useState } from "react";
import type { FC } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import Input from "@/shared/Input";
import { useDeleteHotel } from "@/hooks/hotel/useDeleteHotel";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";

interface DeleteHotelProps {
  username: string;
  hotelToken: string | undefined;
}

const DeleteHotel: FC<DeleteHotelProps> = ({ username, hotelToken }) => {
  const { deleteHotel } = useDeleteHotel();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [hotelUsername, setHotelUsername] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const buttonDisabled = hotelUsername !== username;
  const translate = useTranslations("HotelAccountInformation");

  const closeModal = () => {
    setIsOpen(false);
    setHotelUsername("");
  };

  return (
    <div>
      <div className="p-6 bg-gray-50 space-y-4 text-center dark:bg-neutral-700">
        <Button
          onClick={() => setIsOpen(true)}
          className="w-full py-3 bg-[#d00000] hover:bg-secondary-700 text-white font-semibold rounded-lg shadow-md transition-transform transform hover:scale-105"
        >
          {translate("accountDeleteButton")}
        </Button>
        <Dialog open={isOpen}>
          <DialogContent onInteractOutside={closeModal}>
            <DialogHeader className="mb-5">
              <DialogTitle>{translate("deleteTitle")}</DialogTitle>
              <DialogDescription>
                {translate("deleteDescription")}
              </DialogDescription>
            </DialogHeader>
            <form
              onSubmit={(event) => deleteHotel(event, hotelToken, setLoading)}
            >
              <p className="text-center text-sm font-medium mb-2">
                {translate("deleteLabel")}
              </p>
              <Input
                onChange={(event) =>
                  setHotelUsername(event.target.value.trim())
                }
                name="username"
                value={hotelUsername}
              />
              <div className="flex gap-5 items-center mt-7">
                <Button
                  type="button"
                  onClick={closeModal}
                  variant="outline"
                  className="w-full"
                >
                  {translate("deleteCancel")}
                </Button>
                <Button
                  disabled={buttonDisabled}
                  type="submit"
                  className="w-full bg-secondary-6000 hover:bg-secondary-700 text-white"
                >
                  {loading ? <LoadingSpinner /> : translate("deleteButton")}
                </Button>
              </div>
            </form>
            <DialogClose
              onClick={closeModal}
              className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="size-4" />
              <span className="sr-only">{translate("deleteClose")}</span>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default DeleteHotel;
