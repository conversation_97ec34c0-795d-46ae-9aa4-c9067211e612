"use client";
import { useEffect, useState } from "react";
import { getBalanceByOrder } from "@/actions/(protected)/hotel/getBalanceByOrder";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import BalanceTableTransaction from "./BalanceTableTransaction";

interface BalanceTableExpandedRowProps {
  row: any;
}

const BalanceTableExpandedRow = ({ row }: BalanceTableExpandedRowProps) => {
  const [balanceData, setBalanceData] = useState<any[]>([]);

  useEffect(() => {
    const fetchBalanceData = async () => {
      const orderId = row.original?._id;
      if (!orderId) return;

      try {
        const data = await getBalanceByOrder(orderId);
        setBalanceData(data?.data?.balances || []);
      } catch (error) {
        console.error("Error fetching balance data:", error);
        setBalanceData([]);
      }
    };

    fetchBalanceData();
  }, [row.original?._id]);

  return (
    <div className="px-2 w-full">
      {balanceData.length > 0 ? (
        balanceData.map((balance, index) => (
          <Accordion key={index} type="single" collapsible>
            <AccordionItem value={`item-${index}`}>
              <AccordionTrigger className="grid grid-cols-[max-content,minmax(0,1fr),min-content] gap-4 py-2 hover:no-underline px-16">
                <div className="justify-self-start text-gray-700 dark:text-neutral-50 font-semibold">
                  {balance?.balanceType === "reservations"
                    ? "Konaklama"
                    : balance?.balanceType === "services"
                      ? "Hizmet"
                      : balance?.balanceType === "subscriptions"
                        ? "Üyelik Kartı"
                        : balance?.balanceType || "-"}
                  {balance?.referenceId && ` (${balance.referenceId})`}
                </div>
                <div className="justify-self-end text-gray-700 dark:text-neutral-50 font-semibold">
                  {new Intl.NumberFormat("tr-TR", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  }).format(Number(balance?.balanceAmount) || 0)}
                  {" ₺"}
                </div>
              </AccordionTrigger>
              <AccordionContent className="w-full bg-white dark:bg-neutral-900 px-4 rounded-lg py-2">
                <BalanceTableTransaction
                  transactionsData={balance?.transactions || []}
                />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))
      ) : (
        <div className="text-center py-4">Balance bulunamadı</div>
      )}
    </div>
  );
};

export default BalanceTableExpandedRow;
