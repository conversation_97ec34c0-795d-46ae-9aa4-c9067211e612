import type { FC } from "react";
import React from "react";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import RedirectComponent from "./(components)/(steps)/RedirectComponent";

export interface CommonLayoutProps {
  children?: React.ReactNode;
}

const AccountLayout: FC<CommonLayoutProps> = async ({ children }) => {
  const taxiData = await getMyTaxi();

  return (
    <div className="nc-CommonLayoutAccount min-h-screen bg-neutral-50 dark:bg-neutral-900">
      <div className="pb-24 pt-8 lg:pb-32">{children}</div>
      {taxiData?.data?.status !== "approved" && <RedirectComponent />}
    </div>
  );
};

export default AccountLayout;
