"use client";
import React from "react";
import Lightbox from "yet-another-react-lightbox";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import Download from "yet-another-react-lightbox/plugins/download";
import "yet-another-react-lightbox/styles.css";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import "yet-another-react-lightbox/plugins/thumbnails.css";

interface ImageLightboxProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  imageUrl: string | string[];
  fileName: string;
  setModalIsOpen?: (open: boolean) => void;
}

const ImageLightbox: React.FC<ImageLightboxProps> = ({
  isOpen,
  setIsOpen,
  imageUrl,
  fileName,
  setModalIsOpen,
}) => {
  const imageList = Array.isArray(imageUrl) ? imageUrl : [imageUrl];

  const showControls = imageList.length > 1;

  return (
    <Lightbox
      open={isOpen}
      close={() => {
        setIsOpen(false);
        if (setModalIsOpen) {
          setModalIsOpen(true);
        }
      }}
      slides={imageList.map((img, index) => ({
        src: img,
        downloadUrl: `/api/download?url=${encodeURIComponent(img)}&filename=${
          imageList.length > 1
            ? `${fileName}-${index + 1}.jpg`
            : `${fileName}.jpg`
        }`,
      }))}
      plugins={[Zoom, Download, ...(showControls ? [Thumbnails] : [])]}
      thumbnails={{
        border: 1,
        borderRadius: 4,
        padding: 4,
        height: 80,
        gap: 10,
      }}
      carousel={{ finite: true }}
      render={
        showControls
          ? undefined
          : {
              buttonPrev: () => null,
              buttonNext: () => null,
            }
      }
    />
  );
};

export default ImageLightbox;
