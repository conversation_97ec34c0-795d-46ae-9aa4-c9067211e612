"use client";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { usePhoneStepActions } from "@/hooks/createHotelSteps/usePhoneStep";
import { phoneRegex } from "../create-hotel-regex";
import { useTranslations } from "next-intl";

export default function HotelPhoneValidation() {
  const translate = useTranslations("HotelPhoneValidation");
  const { hotelSecondPhoneStep<PERSON>and<PERSON>, handleCheckPhone } =
    usePhoneStepActions();
  const [hotelPhone, setHotelPhone] = useState<string | null>(null);
  const [authCode, setAuthCode] = useState<string | null>(null);
  const [disabled, setDisabled] = useState<boolean>(false);
  const phoneAuthStep = useSelector(
    (state: RootState) => state.hotelAuth.phoneAuthStep
  );
  const [comfirmPhoneNumberIsOpen, setComfirmPhoneNumberIsOpen] =
    useState(false);

  function closeModal() {
    setComfirmPhoneNumberIsOpen(false);
  }

  function openModal() {
    setComfirmPhoneNumberIsOpen(true);
  }

  const validPhone = hotelPhone && phoneRegex.test(hotelPhone);

  return (
    <div>
      <h2 className="pb-5 text-2xl font-semibold">
        {translate("phoneVerification")}
      </h2>
      <div className="space-y-5">
        {phoneAuthStep ? (
          <div>
            <div className="nc-Label mb-1 text-sm font-medium text-neutral-700 dark:text-neutral-300">
              {translate("phoneCode")}
            </div>
            <InputOTP
              onChange={(e) => setAuthCode(e)}
              maxLength={6}
              pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
              value={authCode || ""}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>
        ) : (
          <FormItem
            label={translate("phoneNumber")}
            desc={translate("phoneNumberInfo")}
          >
            <div className="flex rounded-2xl shadow-sm shadow-black/5 w-64">
              <span className="inline-flex items-center rounded-s-2xl border border-input bg-background px-3 text-sm text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.0"
                  width="12.000000pt"
                  height="12.000000pt"
                  viewBox="0 0 512.000000 512.000000"
                  preserveAspectRatio="xMidYMid meet"
                >
                  <g
                    transform="translate(0.000000,512.000000) scale(0.100000,-0.100000)"
                    fill="#E9141B"
                    stroke="none"
                  >
                    <path d="M2332 5109 c-201 -18 -463 -76 -648 -144 -151 -55 -406 -184 -534 -270 -164 -110 -276 -204 -425 -356 -204 -210 -340 -404 -465 -663 -152 -314 -227 -597 -252 -954 -24 -335 30 -719 147 -1038 55 -151 184 -406 270 -534 203 -304 476 -570 781 -761 116 -73 342 -184 478 -234 317 -117 703 -171 1038 -147 357 25 640 100 954 252 259 125 453 261 663 465 229 223 378 427 512 700 160 328 235 606 261 972 13 180 2 409 -28 583 -70 410 -235 793 -486 1128 -89 119 -302 339 -423 437 -203 165 -493 330 -739 420 -337 124 -741 176 -1104 144z m98 -1293 c305 -65 583 -237 761 -471 69 -91 69 -95 -1 -32 -208 188 -456 277 -735 264 -148 -7 -260 -34 -381 -91 -124 -59 -204 -115 -295 -205 -399 -399 -399 -1043 0 -1442 386 -385 1004 -399 1411 -32 69 63 70 59 3 -29 -179 -235 -457 -408 -763 -474 -114 -25 -391 -25 -499 0 -266 60 -467 169 -652 355 -154 153 -247 303 -314 504 -124 372 -65 783 158 1117 64 96 241 273 337 337 173 116 373 191 580 217 70 9 317 -3 390 -18z m1344 -836 c72 -99 135 -180 141 -180 6 0 102 30 214 66 112 36 205 63 208 61 2 -3 -55 -87 -127 -186 l-132 -181 132 -181 c73 -99 130 -183 127 -185 -3 -3 -95 24 -205 60 -110 36 -206 66 -214 66 -8 0 -73 -81 -144 -180 -71 -99 -132 -180 -136 -180 -5 0 -8 103 -8 228 l0 228 -203 67 c-112 37 -209 67 -215 67 -42 0 15 24 203 84 l215 70 0 228 c0 125 3 228 8 228 4 0 65 -81 136 -180z" />
                  </g>
                </svg>{" "}
                <span className="ml-1">+90</span>
              </span>
              <Input
                type="tel"
                name="hotelPhone"
                value={
                  hotelPhone?.startsWith("0")
                    ? hotelPhone.slice(1)
                    : hotelPhone || ""
                }
                onChange={(e) => {
                  let sanitizedValue = e.target.value.replace(/\D+/g, "");
                  if (sanitizedValue.length > 10) {
                    sanitizedValue = sanitizedValue.slice(0, 10);
                  }
                  if (sanitizedValue && !sanitizedValue.startsWith("0")) {
                    sanitizedValue = `0${sanitizedValue}`;
                  }
                  setHotelPhone(sanitizedValue);
                }}
                maxLength={10}
                className="flex-1 block w-full min-w-0 rounded-none rounded-r-2xl transition duration-150 ease-in-out sm:text-sm sm:leading-5"
              />
            </div>
            {!validPhone && hotelPhone && (
              <span className="mt-3 block text-xs text-red-500">
                {translate("validPhoneNumber")}
              </span>
            )}
          </FormItem>
        )}
        <div className="flex items-center gap-2">
          {!phoneAuthStep ? (
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={openModal}
              disabled={!validPhone}
            >
              {translate("send")}
            </Button>
          ) : (
            <Button
              onClick={() => {
                hotelSecondPhoneStepHandler(
                  hotelPhone,
                  authCode,
                  setAuthCode,
                  setDisabled
                );
              }}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              disabled={disabled || !(authCode && authCode.length === 6)}
            >
              {translate("confirm")}
            </Button>
          )}
        </div>
        <Dialog open={comfirmPhoneNumberIsOpen}>
          <DialogContent onInteractOutside={closeModal}>
            <DialogHeader>
              <DialogTitle> {translate("phoneVerification")}</DialogTitle>
              <DialogDescription className="sr-only"></DialogDescription>
            </DialogHeader>
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                <span className="font-semibold tracking-wide text-black dark:text-neutral-200">
                  {hotelPhone}
                </span>
                <br />
                <span className="mt-3">{translate("confirmSendCode")}</span>
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                type="button"
                className="w-full"
                onClick={closeModal}
              >
                {translate("change")}
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white w-full"
                type="button"
                onClick={() => {
                  handleCheckPhone(
                    hotelPhone,
                    closeModal,
                    setDisabled,
                    setHotelPhone
                  );
                }}
                disabled={disabled}
              >
                {translate("send")}
              </Button>
            </div>
            <DialogClose
              onClick={closeModal}
              className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="size-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
