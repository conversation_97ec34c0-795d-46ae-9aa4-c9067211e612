"use client";
import React, { useState } from "react";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import type { ChangeEvent, FC } from "react";
import RuleCard from "./RuleCard";
import CheckInOutTimePicker from "./CheckInOutTimePicker";
import AddNewRule from "./AddNewRule";
import CancellationOption from "./CancellationOption";
import { useCancellationPolicy } from "@/hooks/policy/useCancellationPolicy";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
interface PolicyStepProps {
  hotelToken: string | undefined;
}
export interface PolicyDataTypes {
  checkInStartTime: string | undefined;
  checkInEndTime: string | undefined;
  checkOutTime: string | undefined;
  specialRules: { title: string; rule: string }[];
  cancellationPolicyType: string;
  dateRange: string;
  description: string;
}

const PolicyStep: FC<PolicyStepProps> = ({ hotelToken }) => {
  const translate = useTranslations("Policy");
  const { addCancellationPolicyInformationHandler } = useCancellationPolicy();
  const [policyData, setPolicyData] = useState<PolicyDataTypes>({
    checkInStartTime: undefined,
    checkInEndTime: undefined,
    checkOutTime: undefined,
    specialRules: [],
    cancellationPolicyType: "",
    dateRange: "",
    description: "",
  });
  const [addNewRule, setAddNewRule] = useState<boolean>(false);
  const [newRule, setNewRule] = useState<{ title: string; rule: string }>({
    title: "",
    rule: "",
  });
  const [editRule, setEditRule] = useState<boolean>(false);
  const [selectedRuleId, setSelectedRuleId] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // checks if all inputs are true
  const isPolicyDataValid = (): boolean => {
    const {
      checkInStartTime,
      checkInEndTime,
      checkOutTime,
      cancellationPolicyType,
      description,
      dateRange,
    } = policyData;

    if (!checkInStartTime || !checkInEndTime || !checkOutTime) return false;

    // const checkInDateValidate = new Date(`1970-01-01T${checkInStartTime}:00`);
    // const checkOutDateValidate = new Date(`1970-01-01T${checkOutTime}:00`);
    // const isValid = checkInDateValidate > checkOutDateValidate;

    const isDateRangeValid =
      cancellationPolicyType !== "CANCEL_UNTIL_DATE_RANGE" ||
      Boolean(dateRange);

    return (
      Boolean(checkInStartTime) &&
      Boolean(checkInEndTime) &&
      Boolean(checkOutTime) &&
      Boolean(cancellationPolicyType) &&
      Boolean(description) &&
      isDateRangeValid
    );
  };

  const isAllValid = isPolicyDataValid();

  const resetNewRule = () => {
    setNewRule({
      title: "",
      rule: "",
    });
    setAddNewRule(false);
    setEditRule(false);
  };

  // adds check-in and check-out time
  const timeHandler = (date: string, name: string) => {
    if (name === "checkInStartTime") {
      setPolicyData({ ...policyData, checkInStartTime: date });
    } else if (name === "checkInEndTime") {
      setPolicyData({ ...policyData, checkInEndTime: date });
    } else {
      setPolicyData({ ...policyData, checkOutTime: date });
    }
  };

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setNewRule({ ...newRule, [name]: value });
  };

  // adds new rule
  const addRuleHandler = () => {
    setPolicyData({
      ...policyData,
      specialRules: [
        ...policyData.specialRules,
        { title: newRule.title.trim(), rule: newRule.rule.trim() },
      ],
    });
    resetNewRule();
  };

  // deletes selected rule
  const deleteRuleHandler = (ruleId: number) => {
    const filteredRules = policyData.specialRules.filter(
      (_, index) => index !== ruleId
    );
    setPolicyData({ ...policyData, specialRules: filteredRules });
  };

  // finds the selected rule and opens edit inputs
  const editRuleHandler = (ruleId: number) => {
    setSelectedRuleId(ruleId);
    setEditRule(true);
    setAddNewRule(true);
    const selectedRule = policyData.specialRules.find(
      (_, index) => index === ruleId
    );
    if (selectedRule) {
      setNewRule(selectedRule);
    }
  };

  // updates selected rule
  const updateRuleHandler = () => {
    setPolicyData({
      ...policyData,
      specialRules: policyData.specialRules.map((rule, index) =>
        index === selectedRuleId
          ? { title: newRule.title, rule: newRule.rule }
          : rule
      ),
    });
    resetNewRule();
  };

  return (
    <>
      <p className="text-xl font-semibold mb-5">
        {translate("cancellationPolicy")}
      </p>
      <form
        onSubmit={(event) => {
          addCancellationPolicyInformationHandler(
            event,
            hotelToken,
            policyData,
            setLoading
          );
        }}
        className="space-y-5"
      >
        <CheckInOutTimePicker
          timeHandler={timeHandler}
          checks={{
            checkInStartTime: policyData.checkInStartTime,
            checkInEndTime: policyData.checkInEndTime,
            checkOutTime: policyData.checkOutTime,
          }}
        />
        <div className="max-w-md">
          <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold mb-3 max-md:text-sm">
            {translate("description")}
          </p>
          <Textarea
            onChange={(event) => {
              setPolicyData({ ...policyData, description: event.target.value });
            }}
            placeholder="İptal politikasıyla ilgili açıklamalarınızı buraya yazabilirsiniz..."
          />
        </div>
        <div>
          {addNewRule && (
            <AddNewRule
              newRule={newRule}
              handleChange={handleChange}
              editRule={editRule}
              updateRuleHandler={updateRuleHandler}
              addRuleHandler={addRuleHandler}
              resetNewRule={resetNewRule}
            />
          )}
          <div className="mt-5">
            <p className="text-neutral-700 dark:text-neutral-300 text-md font-semibold mb-3 max-md:text-sm">
              {translate("rules")}
            </p>
            <Button
              type="button"
              onClick={() => setAddNewRule(true)}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white mb-5 max-md:text-xs"
            >
              {translate("newRulesAdd")}
            </Button>
            {policyData.specialRules.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {policyData.specialRules.map((rule, index) => {
                  return (
                    <RuleCard
                      key={index}
                      rules={rule}
                      index={index}
                      deleteRuleHandler={deleteRuleHandler}
                      editRuleHandler={editRuleHandler}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </div>
        <CancellationOption
          policyData={policyData}
          setPolicyData={setPolicyData}
        />
        <Button
          type="submit"
          disabled={!isAllValid}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white max-md:text-xs"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </form>
    </>
  );
};

export default PolicyStep;
