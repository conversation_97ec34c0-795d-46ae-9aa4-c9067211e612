"use client";
import React, { useState } from "react";
import type { FC } from "react";
import type { ChangeEvent } from "react";
import Input from "@/shared/Input";
import FormItem from "@/shared/FormItem";
import ButtonPrimary from "@/shared/ButtonPrimary";
import { useDiscountPublic } from "@/hooks/public/useDiscountPublic";
import LoadingSpinner from "@/shared/icons/Spinner";

interface CheckoutDiscountInputProps {
  orderId: string;
  hotel: string;
}

const CheckoutDiscountInput: FC<CheckoutDiscountInputProps> = ({
  orderId,
  hotel,
}) => {
  const { addDiscount } = useDiscountPublic();
  const [discountCode, setDiscountCode] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);

  const handleDiscountCode = (event: ChangeEvent<HTMLInputElement>) => {
    setDiscountCode(event.target.value.trim().toUpperCase());
  };

  return (
    <div className="flex justify-end">
      <FormItem className="w-80" htmlFor="discountCode" label="İndirim kodu">
        <span className="flex items-center gap-1">
          <Input
            onChange={handleDiscountCode}
            id="discountCode"
            name="discountCode"
            value={discountCode}
          />
          <ButtonPrimary
            onClick={() =>
              addDiscount(
                { code: discountCode, orderId: orderId, hotel: hotel },
                setLoading,
                setDiscountCode
              )
            }
            disabled={loading || !discountCode}
          >
            {loading ? <LoadingSpinner /> : "Kullan"}
          </ButtonPrimary>
        </span>
      </FormItem>
    </div>
  );
};

export default CheckoutDiscountInput;
