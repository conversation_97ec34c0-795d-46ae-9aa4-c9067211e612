"use client";
import React from "react";
import type { FC } from "react";
import Image from "next/image";
import { CircleX } from "lucide-react";

interface PreviewUploadImageContainerProps {
  image: string[] | [];
  setImage: React.Dispatch<React.SetStateAction<string[] | []>>;
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  liveChatInputRef: React.RefObject<HTMLInputElement | null>;
}

const PreviewUploadImageContainer: FC<PreviewUploadImageContainerProps> = ({
  image,
  setImage,
  setPhotoFileObject,
  liveChatInputRef,
}) => {
  return (
    <>
      {image.length > 0 && (
        <div className="mt-2 flex gap-5 flex-wrap">
          {image.map((image: any, index: number) => {
            return (
              <div key={index} className="relative">
                <Image
                  src={image}
                  width={100}
                  height={100}
                  className="rounded-lg"
                  alt="vaccination photo"
                />
                <button
                  className="absolute top-0 right-0"
                  type="button"
                  onClick={() => {
                    setImage((prevImages: any) =>
                      prevImages.filter((_: any, i: any) => i !== index)
                    );
                    setPhotoFileObject((prevFileObject) => {
                      if (prevFileObject) {
                        // Convert FileList to Array
                        const prevFilesArray = Array.from(prevFileObject);
                        // Perform the delete operation
                        const updatedFiles = prevFilesArray.filter(
                          (_, i) => i !== index
                        );
                        // Convert File[] back to FileList
                        const dataTransfer = new DataTransfer();
                        updatedFiles.forEach((file) =>
                          dataTransfer.items.add(file)
                        );
                        return dataTransfer.files; // Updated FileList
                      }
                      return undefined; // Return undefined instead of null
                    });
                    if (liveChatInputRef.current) {
                      liveChatInputRef.current.value = ""; // Clear the input
                    }
                  }}
                >
                  <CircleX className="size-6 rounded-full bg-white" />
                </button>
              </div>
            );
          })}
        </div>
      )}
    </>
  );
};

export default PreviewUploadImageContainer;
