import type { FC } from "react";
import React from "react";
interface IconCheckProps {
  className?: string;
  strokeWidth?: number;
}

const IconCheck: FC<IconCheckProps> = ({
  className = "size-6",
  strokeWidth = 1.5,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m4.5 12.75 6 6 9-13.5"
      />
    </svg>
  );
};

export default IconCheck;
