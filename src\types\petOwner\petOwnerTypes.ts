interface CountryTypes {
  identificationCode: string;
  name: string;
}

interface AddressTypes {
  streetName: string;
  cityName: string;
  region: string;
  country: CountryTypes;
  room: string;
  blockName: string;
  buildingName: string;
  buildingNumber: string;
  citySubdivisionName: string;
  postalZone: string;
  district: string;
}

interface BillingInfoTypes {
  individual: boolean;
  companyName: string;
  firstName: string;
  lastName: string;
  taxOffice: string;
  taxNumber: string;
}

export interface PetOwnerTypes {
  username: string;
  phone: string;
  email: string;
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
}

export interface PetOwnerApiTypes {
  _id: string;
  username: string;
  email: string;
  phone: string;
  fullName: string;
  firstName: string;
  lastName: string;
  role: string;
  gender: string;
  dateOfBirth: string;
  image: Image;
  bio: string;
  passive: boolean;
  createdAt: string;
  updatedAt: string;
  session: {
    language: string;
  };
}

export interface Image {
  img800: Img800;
  img400: Img400;
  img200: Img200;
  img100: Img100;
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  tags: string;
  createdDate: string;
}

export interface Img800 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img400 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img200 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img100 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface PetOwnerAddressTypes {
  alias: string;
  address: AddressTypes;
  billingInfo: BillingInfoTypes;
}

export interface PetOwnerApiAddressTypes {
  _id: string;
  petOwner: string;
  alias: string;
  isBilling: boolean;
  isDefault: boolean;
  address: AddressTypes;
  billingInfo: BillingInfoTypes;
  passive: boolean;
  createdAt: string;
  updatedAt: string;
}
