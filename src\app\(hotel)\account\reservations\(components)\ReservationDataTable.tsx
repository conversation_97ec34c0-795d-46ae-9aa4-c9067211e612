"use client";
import type { FC } from "react";
import React, { useState } from "react";
import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { Reservation } from "@/dbModels/reservations";
import { useTranslations } from "next-intl";
import ReservationActionButtons from "./ReservationActionButtons";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import ReservationDetail from "./ReservationDetail";
import ReservationFilterNav from "../(components)/ReservationFilterNav";
import Input from "@/shared/Input";
import EmptyState from "@/components/EmptyState";
import { statusHandler } from "./(mobile)/ReservationCardMobile";
import { ReservationListApiTypes } from "@/types/hotel/reservation/reservationListType";

interface ReservationDataTableProps {
  reservationList: ReservationListApiTypes[];
  hotelToken: string | undefined;
  tab: string | string[];
  reservationPage: number;
}

const ReservationDataTable: FC<ReservationDataTableProps> = ({
  reservationList,
  hotelToken,
  tab,
  reservationPage,
}) => {
  const translate = useTranslations("ReservationList");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const columns: ColumnDef<ReservationListApiTypes>[] = [
    {
      accessorKey: translate("roomName"),
      header: translate("roomName"),
      cell: ({ row }) => (
        <div className="font-medium capitalize">
          {row?.original?.room?.roomName}
        </div>
      ),
    },
    {
      accessorKey: translate("petOwner"),
      header: translate("petOwner"),
      cell: ({ row }) => (
        <div className="capitalize"> {row?.original?.petOwner?.fullName}</div>
      ),
    },
    {
      accessorKey: translate("petName"),
      header: translate("petName"),
      cell: ({ row }) => (
        <div className="capitalize"> {row?.original?.pet?.name}</div>
      ),
    },
    {
      accessorKey: translate("startDate"),
      header: translate("startDate"),
      cell: ({ row }) => (
        <div> {formatDateToDayMonthYear(row?.original?.startDate)}</div>
      ),
    },
    {
      accessorKey: translate("endDate"),
      header: translate("endDate"),
      cell: ({ row }) => (
        <div> {formatDateToDayMonthYear(row?.original?.endDate)}</div>
      ),
    },
    {
      accessorKey: translate("channel"),
      header: translate("channel"),
      cell: ({ row }) => {
        return (
          <div className="font-medium capitalize">{row?.original?.channel}</div>
        );
      },
    },
    {
      accessorKey: translate("date"),
      header: translate("date"),
      cell: ({ row }) => {
        return <div>{formatDateToDayMonthYear(row?.original?.createdAt)}</div>;
      },
    },
    {
      accessorKey: translate("total"),
      header: translate("total"),
      cell: ({ row }) => {
        const price =
          new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(row.original.totalHotel)) + "₺";
        return <div className="font-semibold">{price}</div>;
      },
    },
    {
      accessorKey: translate("status"),
      header: translate("status"),
      cell: ({ row }) => {
        const statusProps = statusHandler(row?.original?.status);
        return (
          <div className={`font-medium capitalize ${statusProps?.color}`}>
            {translate(row?.original?.status)}
          </div>
        );
      },
    },
    {
      accessorKey: translate("action"),
      header: translate("action"),
      cell: ({ row }) => {
        return (
          <ReservationActionButtons
            rowOriginal={row.original}
            hotelToken={hotelToken}
          />
        );
      },
      enableHiding: false,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        return <ReservationDetail reservationData={row?.original} />;
      },
    },
  ];

  const table = useReactTable({
    data: reservationList,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-4 justify-between">
        <ReservationFilterNav tab={tab} reservationPage={+reservationPage} />
        {/* <Input name="search" type="search" className="w-[240px]" /> */}
      </div>
      <div className="rounded-md border bg-white dark:bg-neutral-900">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={`${index % 2 === 0 ? "bg-gray-100 dark:bg-neutral-800" : ""}`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <EmptyState text="Rezervasyon bulunamadı" />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ReservationDataTable;
