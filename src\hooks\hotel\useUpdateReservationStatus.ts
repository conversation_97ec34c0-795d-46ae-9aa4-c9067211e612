import { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useUpdateReservationStatus = () => {
  const { toast } = useToast();

  // ADDS NEW HOTEL USER
  const updateReservationStatusHandler = async (
    hotelToken: string | undefined,
    status: string,
    reservationId: string | undefined
  ) => {
    if (hotelToken) {
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updateReservationStatus}/${reservationId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              status,
            }),
          }
        );

        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata o<PERSON>.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 6000,
          title: "Rezervasyon Güncelleme",
          description: "Rezervasyon durum güncellemesi başarıyla yapıldı.",
        });
        revalidatePathHandler("/hotel/account");
      } catch (error) {
        console.log(error);
      }
    }
  };

  return {
    updateReservationStatusHandler,
  };
};
