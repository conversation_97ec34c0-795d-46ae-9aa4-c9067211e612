"use client";
import { useEffect } from "react";
import ButtonPrimary from "@/shared/ButtonPrimary";
import Link from "next/link";
import ErrorImage from "@/components/ErrorImage";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen bg-[#fffaf7] lg:py-56 2xl:py-72">
      <div className="flex items-center justify-center gap-10 py-10 max-lg:min-h-screen max-lg:flex-col-reverse lg:gap-20">
        <div className="space-y-5">
          <h2 className="text-center text-5xl font-semibold lg:text-6xl">
            Hay aksi!
          </h2>
          <h3 className="text-center text-lg lg:text-2xl">
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> göre bir şeyler ters gitti.
          </h3>
          <div className="text-center text-lg font-medium">
            <Link href="/">
              <ButtonPrimary>Ana sayfaya dön</ButtonPrimary>
            </Link>
            {/* <button
              onClick={
                // Attempt to recover by trying to re-render the segment
                () => reset()
              }
            >
              Tekrar dene
            </button> */}
          </div>
        </div>
        <div>
          <ErrorImage className="h-[125px] w-[250px] lg:h-[250px] lg:w-[500px]" />
        </div>
      </div>
    </div>
  );
}
