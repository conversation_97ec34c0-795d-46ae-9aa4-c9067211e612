"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ListFilter, Columns3 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import IconDocumentEmpty from "@/shared/icons/DocumentEmpty";
import { Table } from "@tanstack/react-table";
import { BalanceTableProps } from "@/types/hotel/balanceTableType";

interface BalanceTableHeaderProps {
  table: Table<BalanceTableProps>;
  globalFilter: string;
  setGlobalFilter: (value: string) => void;
  handleOpenPDF: () => void;
}

const BalanceTableHeader = ({
  table,
  globalFilter,
  setGlobalFilter,
  handleOpenPDF,
}: BalanceTableHeaderProps) => {
  return (
    <div className="flex flex-wrap items-center justify-between gap-3">
      <div className="flex items-center gap-3">
        {/* Filter by all columns */}
        <div className="w-56 relative">
          <Input
            className="peer min-w-60 ps-9"
            placeholder="Filtrele"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
          />
          <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
            <ListFilter size={16} strokeWidth={2} aria-hidden="true" />
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" onClick={handleOpenPDF}>
          <IconDocumentEmpty aria-hidden="true" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              <Columns3
                className="-ms-1 me-2 opacity-60"
                size={16}
                strokeWidth={2}
                aria-hidden="true"
              />
              Görünüm
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Sütun Gizle</DropdownMenuLabel>
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                    onSelect={(event) => event.preventDefault()}
                  >
                    {typeof column.columnDef.header === "string"
                      ? column.columnDef.header
                      : column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default BalanceTableHeader;
