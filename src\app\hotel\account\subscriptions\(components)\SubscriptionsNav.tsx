"use client";
import React, { useRef, useEffect } from "react";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";

const SubscriptionsNav = () => {
  const translate = useTranslations("HotelInformationsNav");
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  interface NavItem {
    name: string;
    path: string;
    id: number;
  }
  const navList: NavItem[] = [
    {
      name: "<PERSON><PERSON>lik Kartları",
      path: "/hotel/account/subscriptions",
      id: 1,
    },
    {
      name: "Üye Listesi",
      path: "/hotel/account/subscriptions/subscriber-list",
      id: 2,
    },
  ];

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = navList.findIndex((item) => item.path === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname]);

  return (
    <div
      ref={navContainerRef}
      className="hiddenScrollbar flex gap-3 overflow-y-hidden bg-transparent px-1 pb-5 text-sm md:overflow-x-auto md:pb-1.5 md:text-base"
    >
      {navList.map((navItem) => (
        <Link
          href={navItem.path as Route}
          key={navItem.id}
          className={`${pathname === navItem.path ? "bg-secondary-6000 text-white font-medium" : "bg-transparent"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 text-neutral-700 dark:text-neutral-200`}
        >
          {navItem.name}
        </Link>
      ))}
    </div>
  );
};

export default SubscriptionsNav;
