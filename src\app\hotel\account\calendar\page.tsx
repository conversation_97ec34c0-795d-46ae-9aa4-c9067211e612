import React from "react";
import HotelCalendarContainer from "./(components)/HotelCalendarContainer";
import { cookies } from "next/headers";
import getRoomGroups from "@/actions/(protected)/hotel/getRoomGroups";

const CalendarPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const roomGroupData = await getRoomGroups();

  return (
    <div className="overflow-y-auto overflow-x-hidden pt-8">
      <HotelCalendarContainer
        hotelToken={hotelToken}
        roomGroupData={roomGroupData?.data?.data}
        roomParams={searchParams}
      />
    </div>
  );
};

export default CalendarPage;
