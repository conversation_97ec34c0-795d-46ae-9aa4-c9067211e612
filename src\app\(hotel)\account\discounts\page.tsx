import React from "react";
import { cookies } from "next/headers";
import getPromos from "@/actions/(protected)/hotel/discounts/getPromos";
import getInstallment from "@/actions/(protected)/hotel/discounts/getInstallment";
import PromoCodeCard from "./(components)/(discountCard)/PromoCodeCard";
import InstallmentCard from "./(components)/(discountCard)/InstallmentCard";
import DiscountsModal from "./(components)/DiscountsModal";

const DiscountsPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const promosData = await getPromos();
  const installmentData = await getInstallment();

  return (
    <div className="container">
      <DiscountsModal hotelToken={hotelToken} />
      {promosData?.data?.length > 0 && (
        <>
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Promosyon Kodu
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {promosData?.data &&
              promosData?.data.map((promo: any) => (
                <PromoCodeCard
                  promosData={promo}
                  key={promo._id}
                  hotelToken={hotelToken}
                />
              ))}
          </div>
        </>
      )}
      {installmentData?.data?.length > 0 && (
        <>
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Taksit
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {installmentData?.data &&
              installmentData?.data.map((installment: any) => (
                <InstallmentCard
                  installmentData={installment}
                  key={installment._id}
                  hotelToken={hotelToken}
                />
              ))}
          </div>
        </>
      )}
    </div>
  );
};

export default DiscountsPage;
