"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import { BalanceTableProps } from "@/types/hotel/balanceTableType";

export const createColumns = (
  expandedRows: Record<string, boolean>,
  toggleRowExpanded: (rowId: string) => void
): ColumnDef<BalanceTableProps>[] => [
  {
    accessorKey: "petOwner.fullName",
    header: "Ad-Soyad",
    cell: ({ row }) => (
      <div className="capitalize">
        {row.original?.petOwner?.fullName || "-"}
      </div>
    ),
  },
  {
    accessorKey: "_id",
    header: "Rezervasyon Numarası",
    cell: ({ row }) => (
      <div className="lowercase">{row.getValue("_id") || "-"}</div>
    ),
    size: 250,
  },
  {
    accessorKey: "orderDate",
    header: "Tarih",
    cell: ({ row }) => (
      <div className="lowercase">
        {formatDateToDayMonthYear(row.getValue("orderDate"))}
      </div>
    ),
  },
  {
    accessorKey: "statusDetail",
    header: "Durum",
    cell: ({ row }) => {
      const status = row.getValue("statusDetail") as string;

      const translateStatus = (status: string) => {
        switch (status) {
          case "notPerformed":
            return "Bekliyor";
          case "performing":
            return "Devam ediyor";
          case "performed":
            return "Tamamlandı";
          default:
            return status;
        }
      };

      const statusClass = cn(
        status === "notPerformed" && "bg-blue-100 text-blue-800",
        status === "performing" && "bg-green-100 text-green-800",
        status === "performed" && "bg-red-100 text-red-800",
        "rounded-full px-2 py-1 text-sm font-medium capitalize"
      );

      return <Badge className={statusClass}>{translateStatus(status)}</Badge>;
    },
  },
  {
    accessorKey: "paymentType",
    header: "Ödeme Tipi",
    cell: ({ row }) => (
      <div className="lowercase">{row.getValue("paymentType") || "-"}</div>
    ),
  },
  {
    accessorKey: "channel",
    header: "Kanal",
    cell: ({ row }) => (
      <div className="lowercase">{row.getValue("channel") || "-"}</div>
    ),
  },
  {
    accessorKey: "totalAmount",
    header: "Toplam Tutar",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("totalAmount"));
      const formatted = new Intl.NumberFormat("tr-TR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);
      return formatted + " ₺";
    },
  },
  {
    id: "expander",
    header: () => null,
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          e.stopPropagation();
          toggleRowExpanded(row.id);
        }}
        className="h-8 w-8 p-0"
      >
        {expandedRows[row.id] ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </Button>
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50,
  },
];
