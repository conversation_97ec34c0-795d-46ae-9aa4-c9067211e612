import { Star, Users, TrendingUp } from "lucide-react";

const stats = [
  { icon: Users, value: "20+", label: "Aktif Partner" },
  { icon: Star, value: "4.9", label: "Ortalama Puan" },
  { icon: TrendingUp, value: "%65", label: "<PERSON><PERSON><PERSON>" },
];

export function StatsSection() {
  return (
    <section className="py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="flex justify-center mb-2">
                <stat.icon className="w-8 h-8 text-orange-500" />
              </div>
              <div className="text-3xl font-bold text-gray-900">
                {stat.value}
              </div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
