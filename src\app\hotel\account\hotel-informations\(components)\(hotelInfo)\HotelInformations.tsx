"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import Textarea from "@/shared/Textarea";
import { Button } from "@/components/ui/button";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import HotelLogo from "./HotelLogo";
import type { HotelInformationApiTypes } from "@/types/hotel/hotelInformationType";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelInformationsProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const HotelInformations: FC<HotelInformationsProps> = ({
  hotelData,
  hotelToken,
}) => {
  const translate = useTranslations("HotelInformations");
  const initialData = {
    hotelName: hotelData?.hotelName,
    // hotelTaxNumber: hotelData?.taxNumber,
    // hotelTaxOffice: hotelData?.taxOffice,
    hotelDescription: hotelData?.hotelDescription,
  };
  const [HotelInformations, setHotelInformations] =
    useState<HotelInformationApiTypes>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handleHotelInformations } = useHotelInformationsActions();

  const handleChange = (
    event: ChangeEvent<HTMLInputElement> | ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;

    const capitalizeEachWord = (str: string) =>
      str
        .toLocaleLowerCase("tr-TR")
        .replace(/(^\p{L})|(\s\p{L})/gu, (char) =>
          char.toLocaleUpperCase("tr-TR")
        );

    setHotelInformations((prevState) => {
      return {
        ...prevState,
        [name]: name === "hotelName" ? capitalizeEachWord(value) : value,
      };
    });
  };

  return (
    <>
      <form
        className="mb-5"
        onSubmit={(event) => {
          handleHotelInformations(
            event,
            hotelToken,
            HotelInformations,
            setLoading,
            setDisabled
          );
        }}
      >
        <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 md:gap-7">
          <FormItem label={translate("hotelName")}>
            <Input
              name="hotelName"
              onChange={handleChange}
              value={HotelInformations.hotelName}
            />
          </FormItem>
          {/* <FormItem label={translate("hotelCompanyTaxNumber")}>
            <Input
              name="hotelTaxNumber"
              onChange={handleChange}
              type="number"
              value={HotelInformations.hotelTaxNumber}
              className="invalid:!border-red-500"
            />
          </FormItem>
          <FormItem label={translate("hotelCompanyTaxOffice")}>
            <Input
              name="hotelTaxOffice"
              onChange={handleChange}
              value={HotelInformations.hotelTaxOffice}
            />
          </FormItem> */}
          <FormItem label={translate("hotelStory")}>
            <Textarea
              name="hotelDescription"
              onChange={handleChange}
              value={HotelInformations.hotelDescription}
            />
          </FormItem>
        </div>
        <div className="mt-10 flex justify-end">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
            disabled={
              disabled ||
              JSON.stringify(initialData) === JSON.stringify(HotelInformations)
            }
            type="submit"
          >
            {loading ? <LoadingSpinner /> : translate("save")}
          </Button>
        </div>
      </form>
      <HotelLogo hotelData={hotelData} hotelToken={hotelToken} />
    </>
  );
};

export default HotelInformations;
