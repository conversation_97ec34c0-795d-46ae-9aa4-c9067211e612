import React from "react";
import type { FC } from "react";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";
import AccountInformations from "./AccountInformations";
import Membership from "./Membership";
import { CreditCardIcon, User2Icon } from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface MyUserContainerProps {
  membershipData: any;
  hotelNextPaymentDate: any;
  hotelSubscriptionStatus: any;
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
  hotelCards: any;
}

const MyUserContainer: FC<MyUserContainerProps> = ({
  membershipData,
  hotelNextPaymentDate,
  hotelSubscriptionStatus,
  hotelData,
  hotelToken,
  hotelCards,
}) => {
  return (
    <Tabs
      defaultValue="myAccount"
      orientation="vertical"
      className="w-full flex flex-col md:flex-row"
    >
      <TabsList className="text-foreground flex-row md:flex-col gap-1 md:gap-2 rounded-none bg-transparent px-1 py-0 h-full">
        <TabsTrigger
          value="myAccount"
          className="group text-base hover:bg-accent hover:text-foreground data-[state=active]:after:bg-secondary-6000 data-[state=active]:hover:bg-accent relative w-full justify-start after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 md:after:top-0 md:after:bottom-auto md:after:left-0 md:after:h-full md:after:w-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none text-neutral-600 dark:text-neutral-500 data-[state=active]:text-black data-[state=active]:dark:text-white data-[state=active]:font-semibold"
        >
          <User2Icon className="-ms-0.5 me-2" size={25} aria-hidden="true" />
          Hesabım
        </TabsTrigger>
        <TabsTrigger
          value="membership"
          className="group text-base hover:bg-accent hover:text-foreground data-[state=active]:after:bg-secondary-6000 data-[state=active]:hover:bg-accent relative w-full justify-start after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 md:after:top-0 md:after:bottom-auto md:after:left-0 md:after:h-full md:after:w-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none text-neutral-600 dark:text-neutral-500 data-[state=active]:text-black data-[state=active]:dark:text-white data-[state=active]:font-semibold"
        >
          <CreditCardIcon
            className="-ms-0.5 me-2"
            size={25}
            aria-hidden="true"
          />
          Üyelik
        </TabsTrigger>
      </TabsList>
      <div className="mt-5 md:mt-0 md:w-full">
        <TabsContent value="myAccount">
          <AccountInformations hotelData={hotelData} hotelToken={hotelToken} />
        </TabsContent>
        <TabsContent value="membership">
          <Membership
            membershipData={membershipData}
            hotelNextPaymentDate={hotelNextPaymentDate}
            hotelSubscriptionStatus={hotelSubscriptionStatus}
            hotelToken={hotelToken}
            hotelCards={hotelCards}
          />
        </TabsContent>
      </div>
    </Tabs>
  );
};

export default MyUserContainer;
