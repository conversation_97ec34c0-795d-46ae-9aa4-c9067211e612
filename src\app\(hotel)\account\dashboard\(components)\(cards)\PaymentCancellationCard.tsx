import React from "react";
import type { FC } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import IconChart from "@/shared/icons/Chart";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PaymentCancellationCardProps {
  cancelledReservationStats: any;
}

const PaymentCancellationCard: FC<PaymentCancellationCardProps> = ({
  cancelledReservationStats,
}) => {
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger className="cursor-default max-lg:shrink-0 md:basis-1/4 h-full">
            <Card className="px-3 pb-1 border-[#ee3939] min-h-[102px] md:min-h-max h-full">
              <CardHeader className="px-2 pt-2 pb-0">
                <CardTitle className="flex items-center justify-between gap-1.5 text-xs sm:text-sm font-medium text-neutral-500 dark:text-neutral-400 lg:text-base">
                  İptal Edilen
                  <div className="rounded-md bg-[#ee3939] p-1">
                    <IconChart className="size-5 text-white" />
                  </div>
                </CardTitle>
                {/* <CardDescription>Card Description</CardDescription> */}
              </CardHeader>
              <CardContent className="text-start font-medium lg:text-xl pt-0 pb-1 px-2">
                <p>
                  {cancelledReservationStats?.totalCancelledAmount.toLocaleString(
                    "tr-TR"
                  )}
                  ₺
                </p>
              </CardContent>
            </Card>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">
              İptal edilen rezervasyonların parası
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

export default PaymentCancellationCard;
