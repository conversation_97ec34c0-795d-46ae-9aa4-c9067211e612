"use client";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconWarning from "@/shared/icons/Warning";

const BillingWarnModal = () => {
  const [isOpen, setIsOpen] = useState<boolean>(true);

  return (
    <Dialog open={isOpen}>
      <DialogContent
        className="max-w-xl"
        onInteractOutside={() => {
          setIsOpen(false);
        }}
      >
        <DialogHeader>
          <IconWarning className="size-8 w-full" />
          <DialogTitle className="text-center text-xl">
            Bilgilendirme
          </DialogTitle>
          <DialogDescription className="text-center text-lg">
            Diğer sayfaları kullanabilmeniz için ödeme bilgilerinizi eksiksiz
            doldurmanız gerekmektedir.
          </DialogDescription>
        </DialogHeader>
        <DialogClose
          onClick={() => setIsOpen(false)}
          className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <X className="size-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};

export default BillingWarnModal;
