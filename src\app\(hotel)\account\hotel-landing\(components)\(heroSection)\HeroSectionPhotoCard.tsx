"use client";
import type { FC } from "react";
import React, { useState } from "react";
import Image from "next/image";
import IconCancel from "@/shared/icons/Cancel";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";
import { Button } from "@/components/ui/button";

export interface HeroSectionPhotoCard {
  className?: string;
  images: any;
  hotelToken: string | undefined;
  index: number;
}

const HeroSectionPhotoCard: FC<HeroSectionPhotoCard> = ({
  className = "",
  images,
  hotelToken,
  index,
}) => {
  const [deletePhotoIsOpen, setDeletePhotoIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { deleteHeroSectionPhoto } = useHotelLandingActions();
  return (
    <>
      <div
        className={`nc-CardCategory5 group flex flex-col ${className}`}
        data-nc-id="CardCategory5"
      >
        <div
          className={`group aspect-h-3 aspect-w-4 relative h-0 w-full shrink-0 cursor-pointer overflow-hidden rounded-2xl`}
        >
          <Image
            fill
            alt=""
            src={images.src || ""}
            className="size-full rounded-2xl object-cover"
            sizes="(max-width: 400px) 100vw, 400px"
          />
          <span className="absolute inset-0 bg-black bg-opacity-10 opacity-0 transition-opacity group-hover:opacity-100"></span>
        </div>
        <IconCancel
          onClick={() => {
            setDeletePhotoIsOpen(true);
          }}
          className="absolute right-6 top-1 size-6 cursor-pointer rounded-full bg-white text-secondary-6000 duration-200 group-hover:visible group-hover:opacity-100 lg:invisible lg:opacity-0"
        />
      </div>
      <Dialog open={deletePhotoIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            setDeletePhotoIsOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>Fotoğraf Kaldırma</DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteHeroSectionPhoto(
                event,
                hotelToken,
                images._id,
                setLoading,
                setDeletePhotoIsOpen
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Seçili fotoğrafı silmek istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  setDeletePhotoIsOpen(false);
                }}
              >
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Sil"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={() => {
              setDeletePhotoIsOpen(false);
            }}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HeroSectionPhotoCard;
