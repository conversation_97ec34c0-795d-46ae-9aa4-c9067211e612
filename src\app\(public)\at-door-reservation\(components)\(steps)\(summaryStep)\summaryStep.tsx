import React from "react";
import type { FC } from "react";
import CheckoutDiscountInput from "./checkoutDiscountInput";

interface SummaryStepProps {
  orderData: any;
}

const SummaryStep: FC<SummaryStepProps> = ({ orderData }) => {
  return (
    <div className="space-y-10 pt-4 px-0 sm:p-6 xl:p-8">
      <div className="space-y-4">
        <div className="font-semibold capitalize">
          {orderData?.hotel?.hotelName}
        </div>
        <div className="space-y-4">
          {orderData?.reservations?.length > 0 && (
            <div className="space-y-2">
              <div>
                <p className="font-semibold text-sm">Konaklama</p>
                <div className="w-14 border-b mt-1" />
              </div>
              {orderData?.reservations?.map((reservation: any) => {
                return (
                  <div key={reservation._id}>
                    <div className="mt-1 block font-medium capitalize">
                      {reservation?.room?.roomName}
                    </div>
                    <div className="flex text-sm gap-1">
                      <p>Evcil hayvan:</p>
                      <p>{reservation?.hotelPet?.name}</p>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <div>
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(reservation?.avgPrice)) + "₺"}{" "}
                        x {reservation?.nights} gece
                      </div>
                      <div className="font-medium">
                        {new Intl.NumberFormat("tr-TR", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        }).format(Number(reservation?.total)) + "₺"}
                      </div>
                    </div>
                  </div>
                );
              })}
              <div className="w-full border-b mt-3" />
            </div>
          )}
          {orderData?.services?.length > 0 && (
            <div>
              <div>
                <p className="font-semibold text-sm">Hizmet</p>
                <div className="w-14 border-b mt-1" />
              </div>
              {orderData?.services?.map((service: any) => {
                return (
                  <div
                    key={service?._id}
                    className="flex items-center justify-between mt-1 mb-2"
                  >
                    <div>
                      <p className="capitalize font-medium">
                        {service?.serviceName}
                      </p>
                      <div className="flex text-sm gap-1">
                        <p>Evcil hayvan:</p>
                        <p>{service?.hotelPet?.name}</p>
                      </div>
                    </div>
                    <div className="font-medium">
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(service?.total)) + "₺"}
                    </div>
                  </div>
                );
              })}
              <div className="w-full border-b mt-3" />
            </div>
          )}
          {orderData?.subscriptions?.length > 0 && (
            <div>
              <div>
                <p className="font-semibold text-sm">Üyelik Kartı</p>
                <div className="w-14 border-b mt-1" />
              </div>
              {orderData?.subscriptions?.map((subscription: any) => {
                return (
                  <div
                    key={subscription?._id}
                    className="flex items-center justify-between mt-1"
                  >
                    <p className="capitalize">
                      {subscription?.subscriptionName}
                    </p>

                    <div className="font-medium">
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(subscription?.total)) + "₺"}
                    </div>
                  </div>
                );
              })}
              <div className="w-full border-b mt-3" />
            </div>
          )}
        </div>
        {orderData?.discounts?.length > 0 && (
          <div>
            <p className="font-semibold text-sm">İndirim</p>
            <div className="w-14 border-b mt-1" />
            {orderData?.discounts?.map((discount: any) => {
              const discountName =
                discount?.discountReason === "firstReservationDiscount"
                  ? "İlk rezervasyon"
                  : discount?.discountReason === "promoCode"
                    ? `İndirim kuponu - ${discount?.appliedPromoCode}`
                    : "";
              return (
                <div key={discount?._id}>
                  <div className="flex items-center justify-between mt-1">
                    <div className="basis-1/3">{discountName}</div>
                    <p className="capitalize font-medium basis-1/3 text-center">
                      % {discount?.discountRate}
                    </p>

                    <div className="font-medium basis-1/3 text-end">
                      -{" "}
                      {new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(discount?.discountAmount)) + "₺"}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <div className="flex items-center gap-3 justify-end">
        <span className="font-medium">Toplam:</span>
        <span className="text-lg font-semibold">
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(orderData?.totalAmount)) + "₺"}
        </span>
      </div>
      <CheckoutDiscountInput
        orderId={orderData?._id}
        hotel={orderData?.hotel?._id}
      />
    </div>
  );
};

export default SummaryStep;
