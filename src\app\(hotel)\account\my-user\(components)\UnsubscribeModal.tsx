"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useSubscriptionPayment } from "@/hooks/hotel/useSubscriptionPayment";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import LoadingSpinner from "@/shared/icons/Spinner";

export interface UnsubscribeModalProps {
  hotelToken: string | undefined;
}

const UnsubscribeModal: FC<UnsubscribeModalProps> = ({ hotelToken }) => {
  const { unsubscribeHotel } = useSubscriptionPayment();
  const [loading, setLoading] = useState<boolean>(false);
  const [deleteUnsubscribeIsOpen, setDeleteUnsubscribeIsOpen] = useState(false);

  function closeModal() {
    setDeleteUnsubscribeIsOpen(false);
    setLoading(false);
  }

  return (
    <>
      <Button
        onClick={() => setDeleteUnsubscribeIsOpen(true)}
        className="mt-8 w-full text-red-500 hover:text-red-600 bg-neutral-50 dark:bg-slate-800 text-base"
        variant="outline"
      >
        Üyeliği İptal Et
      </Button>
      <Dialog open={deleteUnsubscribeIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Üyeliği iptal et</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              unsubscribeHotel(event, hotelToken, setLoading, closeModal)
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Üyeliği iptal etmek istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UnsubscribeModal;
