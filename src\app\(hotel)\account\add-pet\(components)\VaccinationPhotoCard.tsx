"use client";
import React, { useState } from "react";
import type { FC } from "react";
import type { ImageType } from "@/types/petOwner/petTypes";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import IconCancelSolid from "@/shared/icons/CancelSolid";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import { useHotelPet } from "@/hooks/hotel/useHotelPets";
import LoadingSpinner from "@/shared/icons/Spinner";

interface VaccinationPhotoCardProps {
  document: ImageType;
  hotelToken: string | undefined;
}

const VaccinationPhotoCard: FC<VaccinationPhotoCardProps> = ({
  document,
  hotelToken,
}) => {
  const [isDeleteReportOpen, setIsDeleteReportOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { deleteVaccinationReportPhoto } = useHotelPet();
  const closeModal = () => {
    setIsDeleteReportOpen(false);
    setLoading(false);
  };

  return (
    <div className="relative">
      <Image src={document.src} width={350} height={350} alt="" />
      <IconCancelSolid
        onClick={() => setIsDeleteReportOpen(true)}
        className="absolute right-1 top-1 size-6 cursor-pointer rounded-full bg-white text-secondary-6000"
      />
      <Dialog open={isDeleteReportOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Aşı Karnesi Kaldırma</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form>
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Seçili aşı karnesi fotoğrafını kaldırmak istiyor musunuz?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <ButtonSecondary onClick={closeModal} type="button">
                Vazgeç
              </ButtonSecondary>
              <ButtonPrimary
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="button"
                disabled={disabled}
                onClick={() =>
                  deleteVaccinationReportPhoto(
                    hotelToken,
                    document._id,
                    setLoading,
                    closeModal,
                    setDisabled
                  )
                }
              >
                {loading ? <LoadingSpinner /> : "Uygula"}
              </ButtonPrimary>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VaccinationPhotoCard;
