"use client";

import { useState, useEffect } from "react";
import { Car, Building2 } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";

interface AccountSwitchProps {
  propertyTypes: string[];
  className?: string;
  open?: boolean;
}

export default function AccountSwitch({
  propertyTypes,
  className = "",
  open = true,
}: AccountSwitchProps) {
  const [activeAccount, setActiveAccount] = useState<"petTaxi" | "petHotel">(
    "petHotel"
  );
  const router = useRouter();

  const shouldShowSwitch =
    propertyTypes.includes("petHotel") && propertyTypes.includes("petTaxi");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentPath = window.location.pathname;
      if (currentPath.includes("/petTaxi")) {
        setActiveAccount("petTaxi");
      } else {
        setActiveAccount("petHotel");
      }
    }
  }, []);

  const handleAccountSwitch = (accountType: "petTaxi" | "petHotel") => {
    setActiveAccount(accountType);

    if (accountType === "petHotel") {
      router.push("/hotel/account");
    } else {
      router.push("/petTaxi/account");
    }
  };

  if (!shouldShowSwitch) {
    return null;
  }

  if (!open) {
    return (
      <div className={`relative flex ${className} justify-center`}>
        <button
          className={`flex items-center justify-center rounded-md p-2 bg-neutral-50 dark:bg-gray-800 shadow ${className}`}
        >
          {activeAccount === "petHotel" ? (
            <Building2 className="w-5 h-5 text-secondary-6000" />
          ) : (
            <Car className="w-5 h-5 text-yellow-500" />
          )}
        </button>
      </div>
    );
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          className={`flex items-center justify-center rounded-md p-2 ${
            activeAccount === "petHotel" ? "bg-secondary-6000" : "bg-yellow-500"
          } shadow ${className} justify-center`}
        >
          {activeAccount === "petHotel" ? (
            <div className="flex items-center gap-2">
              <Building2 className="w-5 h-5 text-white" />
              <p className="text-sm font-medium text-white">Otel</p>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Car className="w-5 h-5 text-white" />
              <p className="text-sm font-medium text-white">Taksi</p>
            </div>
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent
        side="top"
        align="start"
        className="p-2 flex flex-row gap-2 w-auto"
      >
        <button
          onClick={() => handleAccountSwitch("petHotel")}
          className={`flex items-center gap-2 p-2 rounded-md transition ${
            activeAccount === "petHotel"
              ? "bg-secondary-6000 text-white"
              : "hover:bg-gray-100 dark:hover:bg-gray-700"
          }`}
        >
          <Building2 className="w-4 h-4" />
          Otel
        </button>
        <button
          onClick={() => handleAccountSwitch("petTaxi")}
          className={`flex items-center gap-2 p-2 rounded-md transition ${
            activeAccount === "petTaxi"
              ? "bg-yellow-500 text-white"
              : "hover:bg-gray-100 dark:hover:bg-gray-700"
          }`}
        >
          <Car className="w-4 h-4" />
          Taksi
        </button>
      </PopoverContent>
    </Popover>
  );
}
