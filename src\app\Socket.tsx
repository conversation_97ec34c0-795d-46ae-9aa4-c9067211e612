"use client";
import type { ReactNode } from "react";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { showToastNotification } from "@/components/ToastNotification";

export interface IncomingMessage {
  type: string;
  [key: string]: any;
}

interface SocketContextType {
  send: (data: any) => void;
  sendChat: (data: any) => void;
  sendTyping: (data: any) => void;
  sendSeen: (data: any) => void;
  sendPush: (data: any) => void;
  setOnNewNotification: React.Dispatch<
    React.SetStateAction<(() => void) | null>
  >;
  connected: boolean;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  token: string | undefined;
  children: ReactNode;
}

export const SocketProvider = ({ token, children }: SocketProviderProps) => {
  const wsRef = useRef<WebSocket | null>(null);
  const [connected, setConnected] = useState(false);
  const [onNewNotification, setOnNewNotification] = useState<
    (() => void) | null
  >(null);

  useEffect(() => {
    if (!token) return;
    let socket;

    if (!connected || !wsRef.current) {
      socket = new WebSocket(`${process.env.NEXT_PUBLIC_SOCKET_URI}/socket`);
    } else {
      socket = wsRef.current;
    }

    socket.onopen = () => {
      console.log("[Socket] Connected");
      setConnected(true);
      socket.send(JSON.stringify({ type: "init", token }));
    };

    socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        // todo redux dispatch
        if (message.type === "push") {
          if (onNewNotification) onNewNotification();
          const audio = new Audio("/sounds/dog.mp3");
          audio.play();
          showToastNotification({
            title: message.title,
            body: message.body,
            data: message.data,
          });
        }
      } catch (err) {
        console.error("Invalid message:", err);
      }
    };

    socket.onclose = (ev) => {
      console.log("[Socket] Disconnected", ev.code, ev.reason, ev);
      setConnected(false);
    };

    wsRef.current = socket;
  }, [token]);

  const send = useCallback((data: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(data));
    } else {
      console.warn("WebSocket not connected yet.");
    }
  }, []);

  const sendChat = useCallback(
    (params: {
      from: string;
      to: string;
      text?: string;
      fileUrl?: string;
      fileType?: string;
    }) => {
      send({ type: "chat", ...params });
    },
    [send]
  );

  // API: sendTyping
  const sendTyping = useCallback(
    (params: { from: string; to: string; isTyping: boolean }) => {
      send({ type: "typing", ...params });
    },
    [send]
  );

  // API: sendSeen
  const sendSeen = useCallback(
    (params: { from: string; to: string; messageId: string }) => {
      send({ type: "seen", ...params });
    },
    [send]
  );

  // API: sendPush
  const sendPush = useCallback(
    (params: { to: string; title: string; data: string; body: string }) => {
      send({ type: "push", ...params });
    },
    [send]
  );

  const contextValue = useMemo(
    () => ({
      sendChat,
      sendTyping,
      sendSeen,
      sendPush,
      send,
      setOnNewNotification,
      connected,
    }),
    [
      sendChat,
      sendTyping,
      sendSeen,
      sendPush,
      send,
      setOnNewNotification,
      connected,
    ]
  );

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};
