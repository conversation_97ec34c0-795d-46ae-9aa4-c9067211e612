import { useEffect, useState } from "react";
import { getLocalStorageItem, setLocalStorageItem } from "@/utils/localStorage";

export const useMobile = (queryParams: any) => {
  const [isMobile, setIsMobile] = useState(() => {
    const savedValue = getLocalStorageItem("isMobile");
    return savedValue || false;
  });

  const [theme, setTheme] = useState(() => {
    const savedValue = getLocalStorageItem("mobileTheme");
    return savedValue || null;
  });

  const [lang, setLang] = useState(() => {
    const savedValue = getLocalStorageItem("mobileLang");
    return savedValue || null;
  });

  const [expoPushToken, setExpoPushToken] = useState(() => {
    const savedValue = getLocalStorageItem("expoPushToken");
    return savedValue || null;
  });

  useEffect(() => {
    const mobileParams = new URLSearchParams(queryParams);
    const mobile = mobileParams.get("mobile");
    const themes = mobileParams.get("theme");
    const langs = mobileParams.get("lang");
    const expoPushTokens = mobileParams.get("expoPushToken");

    console.log("mobile", mobileParams, expoPushTokens);

    if (mobile) {
      setLocalStorageItem("isMobile", mobile);
      setIsMobile(mobile);
    }

    if (themes) {
      setLocalStorageItem("mobileTheme", themes);
      setTheme(themes);
    }

    if (langs) {
      setLocalStorageItem("mobileLang", langs);
      setLang(langs);
    }

    if (expoPushTokens) {
      setLocalStorageItem("expoPushToken", expoPushTokens);
      setExpoPushToken(expoPushTokens);
    }
  }, [queryParams]);

  return { isMobile, lang, expoPushToken, theme };
};
