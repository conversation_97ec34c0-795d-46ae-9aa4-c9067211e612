import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";

function AnimatedTestimonialsHotel() {
  const testimonials = [
    {
      quote:
        "Detaylara gösterilen özen ve yenilikçi özellikler iş akışımızı tamamen dönüştürdü. Tam olarak aradığımız şey buydu.",
      name: "Paws & Claws Pet Hotel",
      designation: "<PERSON><PERSON>",
      src: "https://ogscapital.com/wp-content/uploads/2024/02/pet-hotel.jpg.webp",
    },
    {
      quote:
        "Uygulama sorunsuzdu ve sonuçlar beklentilerimizi aştı. Platformun esnekliği dikkat çekici.",
      name: "Happy Tails Pet Resort",
      designation: "<PERSON><PERSON>",
      src: "https://www.k9resorts.com/cms/thumbnails/34/500x500/images/new%20photography/Favorite-Copy-of-D04_9796.jpg",
    },
    {
      quote:
        "<PERSON>u çözüm ekibimizin verimliliğini önemli ölçüde artırdı. Kullanıcı dostu arayüzü karmaşık görevleri basitleştiriyor.",
      name: "Furry Friends Boarding",
      designation: "Sahibi",
      src: "https://www.ketawahotel.com/images/765.jpg",
    },
    {
      quote:
        "Olağanüstü destek ve sağlam özellikler. Tüm vaatlerini yerine getiren bir ürün bulmak nadirdir.",
      name: "Whiskers & Wags Inn",
      designation: "Sahibi",
      src: "https://d367pvje6v6lu5.cloudfront.net/pictures/images/000/029/301/big_slider_pic/1.jpg?1629799514",
    },
    {
      quote:
        "Ölçeklenebilirlik ve performans organizasyonumuz için oyun değiştirici oldu. Büyüyen her işletmeye şiddetle tavsiye ederim.",
      name: "Pet Paradise Hotel",
      designation: "Sahibi",
      src: "https://image.makewebcdn.com/makeweb/m_1920x0/Mu6KvACen/DefaultData/76710827_1535051379970779_4788777072048734208_n.jpg?v=202405291424",
    },
  ];
  return <AnimatedTestimonials testimonials={testimonials} />;
}

export { AnimatedTestimonialsHotel };
