import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { PartnerStates } from "./partner-auth-types";
import type {
  OwnerTypes,
  PartnerTypes,
  FeatureAndPetTypes,
  StepState,
  FirstStep,
  SecondStep,
  ThirdStep,
  RegisterId,
  MailAuthStep,
  PhoneAuthStep,
} from "./partner-auth-types";

const initialState: PartnerStates = {
  owner: {
    firstName: null,
    lastName: null,
    userName: null,
    email: null,
    password: null,
    phone: null,
    gender: null,
    dateOfBirth: null,
  },

  partner: {
    partnerName: null,
    propertyType: null,
    // taxOffice: null,
    // taxNumber: null,
    streetName: null,
    district: null,
    cityName: null,
    citySubdivisionName: null,
    partnerPhoneNumber: null,
    postbox: null,
    buildingName: null,
    buildingNumber: null,
    partnerDescription: null,
    // legalName: null,
  },
  hotelFeatures: {
    hotelFeaturesArray: [],
    acceptedPetTypes: [],
    hotelCareServices: [],
  },
  stepState: 1,
  firstStep: false,
  secondStep: false,
  thirdStep: false,
  registerId: null,
  mailAuthStep: false,
  phoneAuthStep: false,
};

const partnerAuthSlice = createSlice({
  name: "partnerAuth",
  initialState,
  reducers: {
    setOwner: (state, action: PayloadAction<OwnerTypes>) => {
      state.owner = action.payload;
    },
    setPartner: (state, action: PayloadAction<PartnerTypes>) => {
      state.partner = action.payload;
    },
    setHotelFeatures: (state, action: PayloadAction<FeatureAndPetTypes>) => {
      state.hotelFeatures = action.payload;
    },
    setStepState: (state, action: PayloadAction<StepState>) => {
      state.stepState = action.payload;
    },
    setFirstStep: (state, action: PayloadAction<FirstStep>) => {
      state.firstStep = action.payload;
    },
    setSecondStep: (state, action: PayloadAction<SecondStep>) => {
      state.secondStep = action.payload;
    },
    setThirdStep: (state, action: PayloadAction<ThirdStep>) => {
      state.thirdStep = action.payload;
    },
    setRegisterId: (state, action: PayloadAction<RegisterId>) => {
      state.registerId = action.payload;
    },
    setMailAuthStep: (state, action: PayloadAction<MailAuthStep>) => {
      state.mailAuthStep = action.payload;
    },
    setPhoneAuthStep: (state, action: PayloadAction<PhoneAuthStep>) => {
      state.phoneAuthStep = action.payload;
    },
  },
});

export const {
  setOwner,
  setPartner,
  setHotelFeatures,
  setStepState,
  setRegisterId,
  setMailAuthStep,
  setPhoneAuthStep,
} = partnerAuthSlice.actions;
export default partnerAuthSlice.reducer;
