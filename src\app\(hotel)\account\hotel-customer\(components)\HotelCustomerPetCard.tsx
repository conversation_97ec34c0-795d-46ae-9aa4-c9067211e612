"use client";
import type { FC } from "react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import type { HotelCustomerApiTypes } from "@/types/hotel/hotelCustomerType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { getHotelPetByOwner } from "@/actions/(protected)/hotel/getHotelPetByOwner";
import { petTypes } from "@/types/petOwner/petTypes";
import { useRouter } from "next/navigation";
import IconEdit from "@/shared/icons/Edit";
import DeletePet from "./deletePet";
export interface HotelCustomerPetCardProps {
  rowOriginal: HotelCustomerApiTypes;
  hotelToken: string | undefined;
}

const HotelCustomerPetCard: FC<HotelCustomerPetCardProps> = ({
  hotelToken,
  rowOriginal,
}) => {
  const [IsOpen, setIsOpen] = useState(false);
  const [petData, setPetData] = useState<any>(null);
  const router = useRouter();

  function closeModal() {
    setIsOpen(false);
  }

  const handleHotelCustomer = (hotelCustomerId: string) => {
    router.push(`/account/add-pet?hotelCustomerId=${hotelCustomerId}`);
  };

  const handlePetUpdate = (
    hotelCustomerId: string,
    petId: string,
    petName: string
  ) => {
    router.push(
      `/account/update-pet?hotelCustomerId=${hotelCustomerId}&petId=${petId}&petName=${petName}`
    );
  };

  const handleClick = async () => {
    const params = new URLSearchParams(window.location.search);
    const ownerId = rowOriginal._id;
    params.set("owner", ownerId);
    window.history.pushState(
      {},
      "",
      `${window.location.pathname}?${params.toString()}`
    );

    if (ownerId) {
      const result = await getHotelPetByOwner(ownerId);
      setPetData(result ? result.data : null);
    } else {
      console.error("Owner ID is missing from the search params");
    }

    setIsOpen(true);
  };

  return (
    <>
      <div
        className="text-secondary-6000 hover:text-primary-700 cursor-pointer mr-2 font-medium"
        onClick={handleClick}
      >
        Petleri Görüntüle
      </div>
      <Dialog open={IsOpen} onOpenChange={setIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>
              <span className="font-normal">{rowOriginal?.fullName} </span>
              Petleri
            </DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <div className="w-16">
            <Button
              onClick={() => handleHotelCustomer(rowOriginal._id)}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center"
            >
              Pet Ekle
            </Button>
          </div>
          <div className="">
            <div className={"grid grid-cols-2 gap-5 mt-3"}>
              {petData?.hotelPets?.map((pet: any, index: number) => (
                <div
                  key={index}
                  className="space-y-2 border rounded-xl px-3 py-2 bg-gray-100 dark:bg-neutral-800"
                >
                  <div className="font-semibold capitalize text-neutral-900 dark:text-white text-base">
                    {pet?.name}
                  </div>
                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                    {petTypes.find(
                      (type) =>
                        type.value.toLowerCase() === pet?.kind.toLowerCase()
                    )?.label || ""}
                  </div>
                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                    {pet?.gender === "male" ? "Erkek" : "Dişi"}
                  </div>
                  <div className="text-sm text-neutral-500 dark:text-neutral-400">
                    {pet?.microChipNumber}
                  </div>
                  <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400">
                    <div
                      onClick={() =>
                        handlePetUpdate(rowOriginal._id, pet?._id, pet?.name)
                      }
                    >
                      <IconEdit className="size-5 cursor-pointer duration-200 hover:text-secondary-6000" />
                    </div>
                    <DeletePet
                      hotelToken={hotelToken}
                      petId={pet?._id}
                      petName={pet?.name}
                    />
                  </div>
                </div>
              ))}
            </div>
            {!petData && (
              <div className="text-center text-neutral-500 dark:text-neutral-400">
                Pet bulunamadı.
              </div>
            )}
          </div>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HotelCustomerPetCard;
