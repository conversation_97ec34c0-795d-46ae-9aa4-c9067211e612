import React from "react";
import { cookies } from "next/headers";
import getTaxiUserInformations from "@/actions/(protected)/taxi/getTaxiUserInformation";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";
import TaxiUserTable from "./(components)/TaxiUserTable";
import AddNewTaxiUser from "./(components)/AddNewTaxiUser";

const UserPage = async () => {
  const taxiUserInformations = await getTaxiUserInformations();
  const taxiData = await getMyTaxi();
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;

  return (
    <div className="container pt-8">
      <AddNewTaxiUser petTaxiToken={petTaxiToken} taxiData={taxiData?.data} />
      <TaxiUserTable
        taxiUserInformations={taxiUserInformations?.data.docs}
        petTaxiToken={petTaxiToken}
      />
    </div>
  );
};

export default UserPage;
