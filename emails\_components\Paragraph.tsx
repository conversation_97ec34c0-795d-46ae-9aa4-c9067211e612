import * as React from "react";

import { Text } from "@react-email/components";

interface Props {
  children: React.ReactNode | React.ReactNode[] | string;
  center?: boolean;
}

const Paragraph = ({ children, center = false }: Props) => {
  return (
    <Text
      style={{
        lineHeight: "24px",
        fontSize: "18px",
        color: "#000",
        textAlign: center ? "center" : "left",
      }}
    >
      {children}
    </Text>
  );
};

export default Paragraph;
