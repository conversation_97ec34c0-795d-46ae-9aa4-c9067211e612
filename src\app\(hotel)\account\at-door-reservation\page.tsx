import React from "react";
import { cookies } from "next/headers";
import AtDoorReservationsContainer from "./(components)/atDoorReservationsContainer";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import getAvailableSubscriptions from "@/actions/(protected)/pub/getAvailableSubscriptionsFromHotel";
import getAvailableServices from "@/actions/(protected)/pub/getAvailableServicesFromHotel";

const AtDoorReservationPage = async ({
  searchParams,
}: {
  searchParams: Record<string, string | string[] | undefined>;
}) => {
  const step = searchParams.step || 1;
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const subscriptionData = await getAvailableSubscriptions(hotelData?.data?._id);
  const serviceData = await getAvailableServices(hotelData?.data?._id);

  return (
    <div>
      <AtDoorReservationsContainer
        hotelToken={hotelToken}
        hotelData={hotelData?.data}
        subscriptionData={subscriptionData?.data}
        serviceData={serviceData?.data}
      />
    </div>
  );
};

export default AtDoorReservationPage;
