"use client";
import React, { useState } from "react";
import RoomSelectStep from "./(steps)/(roomSelectStep)/roomSelectStep";

interface AtDoorReservationContainerProps {
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  serviceData: any;
}

const AtDoorReservationContainer: React.FC<AtDoorReservationContainerProps> = ({
  hotelToken,
  hotelData,
  subscriptionData,
  serviceData,
}) => {
  const [step, setStep] = useState<number>(1);

  return <div className="container">{step === 1 && <RoomSelectStep hotelToken={hotelToken} hotelData={hotelData} subscriptionData={subscriptionData} serviceData={serviceData}  />}</div>;
};

export default AtDoorReservationContainer;
