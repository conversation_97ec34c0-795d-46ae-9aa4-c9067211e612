import type { FC } from "react";
import React from "react";
interface IconXMarkProps {
  className?: string;
  strokeWidth?: number;
}

const IconXmark: FC<IconXMarkProps> = ({
  className = "size-6",
  strokeWidth = 1.5,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6 18 18 6M6 6l12 12"
      />
    </svg>
  );
};

export default IconXmark;
