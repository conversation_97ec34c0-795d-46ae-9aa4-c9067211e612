"use client";
import type { FC, ChangeEvent } from "react";
import React, { useState } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { useHotelCalendarActions } from "@/hooks/hotel/useHotelCalendar";
import { useTranslations } from "next-intl";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Checkbox } from "@/components/ui/checkbox";

interface UpdateRoomPriceProps {
  isOpen: boolean;
  closeEditPrice: () => void;
  hotelToken: string | undefined;
  startDateToString: string | undefined;
  endDateToString: string | undefined;
  selectedRoomId: string | string[] | undefined;
  setFetchAgain: React.Dispatch<React.SetStateAction<boolean>>;
}

const UpdateRoomPrice: FC<UpdateRoomPriceProps> = ({
  isOpen,
  closeEditPrice,
  hotelToken,
  startDateToString,
  endDateToString,
  selectedRoomId,
  setFetchAgain,
}) => {
  const translate = useTranslations("UpdateRoomPrice");

  const { setRoomPrice } = useHotelCalendarActions();
  const [priceValue, setPriceValue] = useState<string | undefined>("");
  const [newRoomPrice, setNewRoomPrice] = useState<string | undefined>("");
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const closeModal = () => {
    setNewRoomPrice(undefined);
    setLoading(false);
    closeEditPrice();
    setPriceValue("");
    setIsChecked(false);
  };

  const newRoomPriceHandler = (event: ChangeEvent<HTMLInputElement>) => {
    const price = event.target.value;

    if (+price < 0) {
      return;
    }

    setPriceValue(price);
    if (isChecked) {
      const adjustedPrice = +price / (1 + 18 / 100);
      setNewRoomPrice(adjustedPrice.toString());
    } else {
      setNewRoomPrice(price);
    }
  };

  return (
    <>
      <Dialog open={isOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>{translate("priceUpdate")}</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          {!isChecked ? (
            <FormItem
              className="!text-neutral-700 dark:!text-neutral-400"
              label={translate("newRoomRate")}
            >
              <Input
                onChange={newRoomPriceHandler}
                type="number"
                value={priceValue}
              />
            </FormItem>
          ) : (
            <FormItem
              className="!text-neutral-700 dark:!text-neutral-400"
              label={"Misafirin ödeyeceği fiyatı giriniz"}
            >
              <Input
                onChange={newRoomPriceHandler}
                type="number"
                value={priceValue}
              />
            </FormItem>
          )}
          <div className="flex items-center gap-2">
            <Checkbox
              onClick={() => {
                setIsChecked((prev) => !prev);
                setNewRoomPrice("");
                setPriceValue("");
              }}
              checked={isChecked}
            />
            <span className="text-sm text-neutral-700 dark:text-neutral-400">
              veya misafirin ödeyeceği fiyatı gir
            </span>
          </div>
          <div className="space-y-4">
            <div className="mt-2 flex gap-1 text-sm">
              <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                {translate("price")}
              </div>
              <div className="font-medium">
                {newRoomPrice
                  ? new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(newRoomPrice)) + "₺"
                  : "0₺"}
              </div>
            </div>
            <div className="mt-2 flex gap-1 text-sm">
              <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                {translate("serviceFee")}
              </div>
              <div className="font-medium">
                {newRoomPrice
                  ? new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(newRoomPrice) * 0.18) + "₺"
                  : "0₺"}
              </div>
            </div>
            {!isChecked ? (
              <div className="mt-2 flex gap-1 text-sm">
                <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                  {translate("totalPrice")}
                </div>
                <div className="font-medium">
                  {newRoomPrice
                    ? new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(
                        Number(newRoomPrice) +
                          Number(Number(newRoomPrice) * 0.18)
                      ) + "₺"
                    : "0₺"}
                </div>
              </div>
            ) : (
              <div className="mt-2 flex gap-1 text-sm">
                <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                  Yeni oda fiyatı:
                </div>
                <div className="font-medium">
                  {newRoomPrice
                    ? new Intl.NumberFormat("tr-TR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }).format(Number(priceValue) / 1.18) + "₺"
                    : "0₺"}
                </div>
              </div>
            )}
            <div className="mt-2 flex gap-1 text-sm">
              <div className="font-semibold text-neutral-700 dark:text-neutral-400">
                {translate("yourEarnings")}
              </div>
              <div className="font-medium">
                {newRoomPrice
                  ? new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(newRoomPrice)) + "₺"
                  : "0₺"}
              </div>
            </div>
          </div>
          <div className="mt-5 flex justify-center gap-5">
            <Button
              className="w-1/3"
              type="button"
              variant="outline"
              onClick={closeModal}
            >
              {translate("cancel")}
            </Button>
            <Button
              className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/3"
              disabled={disabled || !newRoomPrice}
              onClick={() => {
                setRoomPrice(
                  hotelToken,
                  startDateToString,
                  endDateToString,
                  selectedRoomId,
                  newRoomPrice,
                  setFetchAgain,
                  closeModal,
                  setLoading,
                  setDisabled
                );
              }}
              type="button"
            >
              {loading ? <LoadingSpinner /> : translate("save")}
            </Button>
          </div>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateRoomPrice;
