import { NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function POST(request: Request) {
  const { tokenName, token, deviceIdName, deviceId } = await request.json();

  const existingCookies = cookies();
  const existingDeviceId = existingCookies.get(deviceIdName)?.value;

  const response = NextResponse.json({ success: true });

  if (!existingDeviceId) {
    response.cookies.set(deviceIdName, deviceId, {
      httpOnly: true,
      path: "/",
      secure: true,
      maxAge: 60 * 60 * 24 * 365,
    });
  }

  response.cookies.set(tokenName, token, {
    httpOnly: true,
    path: "/",
    secure: true,
    maxAge: 60 * 60 * 24 * 365,
  });

  return response;
}
