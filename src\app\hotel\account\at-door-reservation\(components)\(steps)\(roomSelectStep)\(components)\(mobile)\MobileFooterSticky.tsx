"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import type { RootState } from "@/store";
import { useSelector } from "react-redux";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import MobileCalculatedDetail from "./MobileCalculatedDetail";
import LoadingSpinner from "@/shared/icons/Spinner";

export const handleScroll = () => {
  const targetDiv = document.querySelector(".listingSection__wrap");

  if (targetDiv) {
    const targetPosition =
      targetDiv.getBoundingClientRect().top + window.scrollY;

    window.scrollTo({
      top: targetPosition - 50,
      behavior: "smooth",
    });
  }
};

const MobileFooterSticky = ({
  hotelToken,
}: {
  hotelToken: string | undefined;
}) => {
  const { createOrder } = useHotelAtDoorReservation();
  const [loading, setLoading] = useState<boolean>(false);

  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const reservations = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "reservation"
  );
  const subscriptions = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "subscription"
  );
  const services = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "service"
  );

  const hasItemsInCart =
    calculatedRoomData &&
    (subscriptions.length > 0 ||
      reservations.length > 0 ||
      services.length > 0);

  return (
    <div className="fixed inset-x-0 bottom-0 z-10 md:hidden">
      <div className="container block border-t border-neutral-200 bg-white py-2 dark:border-neutral-6000 dark:bg-neutral-800 sm:py-3">
        <div className="mb-2">
          {hasItemsInCart && (
            <div>
              {/* <div className="border-b border-neutral-200 dark:border-neutral-700 mt-1"></div> */}
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center justify-start gap-1">
                  <span className="text-sm font-medium">Toplam</span>
                  <span className="text-lg font-semibold">
                    {new Intl.NumberFormat("tr-TR", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    }).format(Number(calculatedRoomData?.totalOrderPrice)) +
                      "₺"}
                  </span>
                </div>
                <div>
                  <MobileCalculatedDetail hotelToken={hotelToken} />
                </div>
              </div>
            </div>
          )}
        </div>
        {calculatedRoomData &&
          (subscriptions?.length > 0 ||
            reservations?.length > 0 ||
            services?.length > 0) && (
            <Button
              className="mb-2 w-full bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={() =>
                createOrder(
                  hotelToken,
                  calculatedRoomData?.selectedItems,
                  calculatedRoomData?.totalOrderPrice,
                  setLoading
                )
              }
              disabled={loading || !calculatedRoomData}
            >
              {loading ? <LoadingSpinner /> : "Rezervasyon Oluştur"}
            </Button>
          )}
      </div>
    </div>
  );
};

export default MobileFooterSticky;
