"use client";

import React, { useEffect, useState } from "react";
import NotificationDropdown from "./NotificationDropdown";
import { useSocket } from "@/app/Socket";
import getUnseenWebPushes from "@/actions/(protected)/hotel/getUnseenWebPushes";

interface Props {
  hotelToken: string | undefined;
}

const NotificationDropdownWrapper: React.FC<Props> = ({ hotelToken }) => {
  const [unseenWebPushes, setUnseenWebPushes] = useState<any[]>([]);
  const { setOnNewNotification } = useSocket();

  const fetchNotifications = async () => {
    const res = await getUnseenWebPushes();
    setUnseenWebPushes(res?.data || []);
  };

  useEffect(() => {
    fetchNotifications();

    setOnNewNotification(() => fetchNotifications);

    return () => {
      setOnNewNotification(null);
    };
  }, [setOnNewNotification]);

  return (
    <NotificationDropdown
      hotelToken={hotelToken}
      unseenWebPushes={unseenWebPushes}
      refresh={fetchNotifications}
    />
  );
};

export default NotificationDropdownWrapper;
