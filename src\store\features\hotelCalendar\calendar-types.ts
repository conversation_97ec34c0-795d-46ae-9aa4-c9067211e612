export interface ImageVariantType {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface ImageType {
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  img800?: ImageVariantType;
  img400?: ImageVariantType;
  img200?: ImageVariantType;
  img100?: ImageVariantType;
  tags: string;
  createdAt: string;
  updatedAt: string;
}

export interface RoomType {
  _id: string;
  owner: string;
  hotel: string;
  petType: string;
  roomName: string;
  roomType: string;
  roomCapacity: number;
  roomDescription: string;
  roomFeatures: string[];
  images: ImageType[];
  passive: boolean;
  reviews: any[];
  createdAt: string;
  updatedAt: string;
  roomGroupName: string;
}

export interface AllocationType {
  _id: string;
  hotel: string;
  room: RoomType;
  allocationDate: string;
  price: number;
  currency: string;
  avail: boolean;
  capacity: number;
  reserved: number;
  passive: boolean;
  createdAt: string;
  updatedAt: string;
  roomPrice: number;
  serviceFee: number;
  totalPrice: number;
}

export interface AllocationState {
  selectedRoomId: string | string[] | undefined;
  selectedRoomGroupId: string | string[] | undefined;
  rooms: any;
  selectedRoom: any;
  allocations: AllocationType[];
}

// TODO: buradaki anyleri düzelt. review hariç
