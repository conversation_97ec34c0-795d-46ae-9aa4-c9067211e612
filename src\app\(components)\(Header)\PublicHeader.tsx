import type { <PERSON> } from "react";
import React from "react";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import Link from "next/link";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";

export interface PublicHeaderProps {
  className?: string;
}

const PublicHeader: FC<PublicHeaderProps> = ({ className = "" }) => {
  return (
    <div className="nc-Header nc-header-bg sticky inset-x-0 top-0 z-40 w-full shadow-sm dark:border-b dark:border-neutral-700">
      <div className={`MainNav2 relative z-10 ${className}`}>
        <div className="relative flex md:h-20 justify-between px-4 lg:container">
          <div className="hidden justify-start space-x-4 max-lg:flex-1 sm:space-x-10 md:flex">
            <Link className="self-center" href="/">
              <PawBookingLogo className="size-16 self-center" />
            </Link>
          </div>
          <div className="hidden flex-1 shrink-0 justify-end text-neutral-700 dark:text-neutral-100 md:flex lg:flex-none">
            <SwitchDarkMode className="mx-0.5" />
            <div className="hidden space-x-0.5 xl:flex">
              <LangDropdownSingle className="hidden lg:flex" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicHeader;
