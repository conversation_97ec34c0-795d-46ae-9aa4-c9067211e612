"use client";
import React, { useState } from "react";
import type { FC } from "react";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import HotelCustomerByPhoneStep from "./hotelCustomerByPhoneStep";
import HotelCustomerPetSelectStep from "./hotelCustomerPetSelectStep";
import HotelCustomerPaymentStep from "./hotelCustomerPaymentStep";
import { getHotelCustomerByPhone } from "@/actions/(protected)/hotel/getHotelCustomerByPhone";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";

interface HotelReservationContainerProps {
  hotelToken: string | undefined;
}

const HotelReservationContainer: FC<HotelReservationContainerProps> = ({
  hotelToken,
}) => {
  const { Reserve } = useHotelAtDoorReservation();
  const [step, setStep] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [customerData, setCustomerData] = useState<any | null>(null);
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const router = useRouter();
  const [reservationDetails, setReservationDetails] = useState<{
    selectedRoomId: string;
    startDate: string;
    endDate: string;
    selectedPetId: string;
    channel: string;
    paymentType: string;
  }>({
    selectedRoomId: "",
    startDate: "",
    endDate: "",
    selectedPetId: "",
    channel: "",
    paymentType: "",
  });

  const [atDoorReservationData, setAtDoorReservationData] = useState({
    phoneNumber: "",
    petSelect: {
      selectedRoomId: "",
      startDate: "",
      endDate: "",
      selectedPetId: "",
    },
    payment: {
      channel: "",
      paymentType: "",
    },
  });

  const isPetSelectStepComplate = () => {
    if (
      atDoorReservationData.petSelect.selectedRoomId &&
      atDoorReservationData.petSelect.startDate &&
      atDoorReservationData.petSelect.endDate &&
      atDoorReservationData.petSelect.selectedPetId
    ) {
      return true;
    }
  };

  const isStepComplate = () => {
    if (
      atDoorReservationData.petSelect.selectedRoomId &&
      atDoorReservationData.petSelect.startDate &&
      atDoorReservationData.petSelect.endDate &&
      atDoorReservationData.petSelect.selectedPetId &&
      atDoorReservationData.payment.channel &&
      atDoorReservationData.payment.paymentType &&
      customerData?.hotelCustomer?._id
    ) {
      return true;
    }
  };

  const isPetSelectStepValid = isPetSelectStepComplate();
  const isStepValid = isStepComplate();

  const handlePhoneChange = (value: string) => {
    setPhoneNumber(value);
    setAtDoorReservationData((prev) => ({ ...prev, phoneNumber: value }));
  };

  const handleReservationDetails = (
    selectedRoomId: string,
    startDate: string | undefined,
    endDate: string | undefined,
    selectedPetId: string
  ) => {
    const petSelectData = {
      selectedRoomId,
      startDate: startDate || "",
      endDate: endDate || "",
      selectedPetId,
    };
    setAtDoorReservationData((prev) => ({ ...prev, petSelect: petSelectData }));
    setReservationDetails({
      ...reservationDetails,
      ...petSelectData,
    });
  };

  const handlePaymentDetails = (channel: string, paymentType: string) => {
    const paymentData = { channel, paymentType };
    setAtDoorReservationData((prev) => ({ ...prev, payment: paymentData }));
    setReservationDetails((prev) => ({
      ...prev,
      ...paymentData,
    }));
  };

  const handleNextStep = async () => {
    if (step === 1) {
      setLoading(true);
      const result = await getHotelCustomerByPhone(phoneNumber);
      setLoading(false);

      if (result) {
        setCustomerData(result.data);
        setStep(2);
      } else {
        setCustomerData(null);
        setShowAlert(true);
      }
    } else {
      setStep((prev) => prev + 1);
    }
  };

  const closeAddNewPetModal = () => {
    setLoading(false);
    setStep(1);
    router.push("/account/reservations");
  };

  const handleAddCustomer = () => {
    setShowAlert(false);
    router.push("/account/hotel-customer");
  };

  const buttonsConfig: Record<number, { text: string; disabled: boolean }> = {
    1: { text: "İleri", disabled: false },
    2: { text: "İleri", disabled: !isPetSelectStepValid },
    3: { text: "İleri", disabled: !isStepValid },
  };
  return (
    <>
      {step === 1 && (
        <HotelCustomerByPhoneStep
          phoneNumber={atDoorReservationData.phoneNumber}
          setPhoneNumber={handlePhoneChange}
        />
      )}
      {step === 2 && (
        <HotelCustomerPetSelectStep
          customerData={customerData}
          hotelToken={hotelToken}
          onSelectRoom={handleReservationDetails}
          initialValues={atDoorReservationData.petSelect}
        />
      )}
      {step === 3 && (
        <HotelCustomerPaymentStep
          onSelectPaymentDetails={handlePaymentDetails}
          initialValues={atDoorReservationData.payment}
        />
      )}
      {step !== 3 && (
        <div className="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 flex gap-5 justify-end p-5">
          {step > 1 && (
            <Button
              type="button"
              onClick={() => setStep((prev) => prev - 1)}
              variant="outline"
            >
              Geri
            </Button>
          )}
          {buttonsConfig[step] && (
            <Button
              type="button"
              disabled={buttonsConfig[step].disabled || loading}
              onClick={handleNextStep}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            >
              {loading ? <LoadingSpinner /> : buttonsConfig[step].text}
            </Button>
          )}
        </div>
      )}
      {step === 3 && (
        <div className="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 flex gap-5 justify-end p-5">
          <Button
            type="button"
            onClick={() => setStep((prev) => prev - 1)}
            variant="outline"
          >
            Geri
          </Button>
          <Button
            onClick={() =>
              Reserve(
                hotelToken,
                reservationDetails.selectedRoomId,
                customerData?.hotelCustomer?._id,
                reservationDetails.startDate,
                reservationDetails.endDate,
                reservationDetails.selectedPetId,
                reservationDetails.channel,
                reservationDetails.paymentType,
                setLoading,
                closeAddNewPetModal
              )
            }
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            disabled={!isStepValid}
          >
            {loading ? <LoadingSpinner /> : "Rezerve Et"}
          </Button>
        </div>
      )}
      <AlertDialog open={showAlert} onOpenChange={setShowAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-primary-500">
              Müşteri Bulunamadı
            </AlertDialogTitle>
            <AlertDialogDescription>
              Girdiğiniz telefon numarasına kayıtlı bir müşteri bulunamadı.
              Müşteri eklemek ister misiniz?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowAlert(false)}>
              Vazgeç
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              onClick={handleAddCustomer}
            >
              Müşteri Ekle
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default HotelReservationContainer;
