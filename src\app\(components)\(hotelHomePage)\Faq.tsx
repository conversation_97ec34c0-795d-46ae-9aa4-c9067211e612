import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";

const FaqSection: React.FC = () => {
  return (
    <div className="max-w-5xl mx-auto py-10 px-4">
      <h2 className="text-3xl font-bold text-center mb-8 text-gray-800 dark:text-inherit">
        Pet Hotel Sahipleri İçin Sıkça Sorulan Sorular
      </h2>
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="item-1">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Pet Hotelimi Nasıl Kaydedebilirim?
          </AccordionTrigger>
          <AccordionContent>
            PawBooking'de otelinizi kaydetmek için üye olun ve yönetim
            panelinden "Otel Ekle" seçeneğini kullanarak otel detaylarını
            doldurun. Bu işlemden sonra oteliniz onay sürecine alınır.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Odaları Nasıl Eklerim ve Yönetirim?
          </AccordionTrigger>
          <AccordionContent>
            Yönetim paneline gidin. "Odalar" sekmesine tıklayın. Tek tek oda
            ekleyebilir veya grup olarak tanımlayabilirsiniz. Her odanın
            fiyatını, kapasitesini ve aktiflik durumunu ayarlayabilirsiniz.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Rezervasyonlar Nasıl Yönetilir?
          </AccordionTrigger>
          <AccordionContent>
            Rezervasyonlar, yönetim panelindeki "Rezervasyonlar" sekmesinde
            görüntülenebilir. Buradan giriş-çıkış tarihlerini, rezervasyon
            detaylarını ve ödeme durumunu takip edebilirsiniz.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Komisyon Ücretleri Nasıl Hesaplanır?
          </AccordionTrigger>
          <AccordionContent>
            PawBooking, tamamlanan rezervasyonlar üzerinden belirli bir komisyon
            alır. Komisyon oranını öğrenmek için destek ekibimizle iletişime
            geçebilirsiniz.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-5">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Ödemeleri Nasıl Alırım?
          </AccordionTrigger>
          <AccordionContent>
            Tamamlanan rezervasyonlar için ödemeler, belirttiğiniz banka
            hesabına haftalık olarak aktarılır. Detayları "Finans" sekmesinden
            kontrol edebilirsiniz.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-6">
          <AccordionTrigger className="hover:no-underline hover:text-secondary-6000">
            Destek Almak İçin Nereye Başvurabilirim?
          </AccordionTrigger>
          <AccordionContent>
            Herhangi bir sorun veya soru için destek sayfamızı ziyaret edebilir
            veya e-posta yoluyla bizimle iletişime geçebilirsiniz.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default FaqSection;
