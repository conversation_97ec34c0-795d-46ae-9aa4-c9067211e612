"use client";

import type { FC } from "react";
import React from "react";

export interface CheckboxProps {
  label?: string;
  subLabel?: string;
  className?: string;
  name: string;
  defaultChecked?: boolean;
  id: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  inputClass?: string;
}

const Checkbox: FC<CheckboxProps> = ({
  subLabel = "",
  label = "",
  name,
  className = "",
  defaultChecked,
  id,
  onChange,
  checked,
  inputClass = "",
}) => {
  return (
    <div
      className={`flex text-sm sm:text-base ${className} select-none items-center`}
    >
      <input
        id={id}
        name={name}
        type="checkbox"
        className={`size-6 rounded border-neutral-500 border-primary bg-white text-secondary-500 focus:ring-0 dark:bg-neutral-700  dark:checked:bg-primary-500 ${inputClass}`}
        defaultChecked={defaultChecked}
        checked={checked}
        onChange={(e) => onChange && onChange(e)}
      />
      {label && (
        <label
          htmlFor={name}
          className="ml-3.5 flex flex-1 cursor-pointer flex-col justify-center"
        >
          <span className=" text-neutral-900 dark:text-neutral-100">
            {label}
          </span>
          {subLabel && (
            <p className="mt-1 text-sm font-light text-neutral-500 dark:text-neutral-400">
              {subLabel}
            </p>
          )}
        </label>
      )}
    </div>
  );
};

export default Checkbox;
