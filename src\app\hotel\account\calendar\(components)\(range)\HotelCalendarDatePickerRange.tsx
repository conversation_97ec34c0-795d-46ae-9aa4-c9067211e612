import type { FC } from "react";
import React from "react";
import type { AllocationType } from "@/store/features/hotelCalendar/calendar-types";
import { createLocalDate } from "@/utils/createLocalDate";

interface Props {
  dayOfMonth: number;
  date?: Date | undefined;
  selectedRoomCalendarData: AllocationType[];
}

const HotelCalendarDatePickerRange: FC<Props> = ({
  dayOfMonth,
  date,
  selectedRoomCalendarData,
}) => {
  const matchingItem = selectedRoomCalendarData?.find((item: any) => {
    const itemDate = createLocalDate(item.allocationDate);
    const today = new Date();

    if (itemDate) {
      today.setHours(12, 0, 0, 0);
      itemDate.setHours(12, 0, 0, 0);
      return (
        itemDate >= today &&
        itemDate.getFullYear() === date?.getFullYear() &&
        itemDate.getMonth() === date?.getMonth() &&
        itemDate.getDate() === date?.getDate()
      );
    }
  });

  const dateStatus =
    matchingItem?.passive === false
      ? "Aktif"
      : matchingItem?.passive === true
        ? "Pasif"
        : "Fiyat yok";

  const colorStatus = () => {
    if (matchingItem?.passive === true) {
      return "bg-[#6c757d]";
    } else if (
      matchingItem?.passive === false &&
      matchingItem?.avail === false
    ) {
      return "bg-[#FFB3B3]";
    } else if (
      matchingItem?.passive === false &&
      matchingItem?.avail === true
    ) {
      return "bg-[#58d68d]";
    } else {
      return "bg-[#fd7e14]";
    }
  };

  // resets hours to compare without hour
  const resetDate = date && date.setHours(0, 0, 0, 0);
  const today = new Date().setHours(0, 0, 0, 0);

  return (
    <div
      id={`${date && date.toISOString().split("T")[0]}`}
      className={`react-datepicker__day_span relative !h-36 !w-60 !rounded-none max-md:border max-md:border-gray-200 ${
        dateStatus === "Pasif" || dateStatus === "Fiyat yok"
          ? "line-through"
          : "active-date"
      }`}
    >
      <span>{dayOfMonth}</span>
      <span className="absolute bottom-2 right-3 font-medium">
        {matchingItem?.avail === false ? (
          <span className="text-[#FFB3B3]">Reserved</span>
        ) : (
          matchingItem &&
          new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(matchingItem?.roomPrice ?? matchingItem?.price)) +
            "₺"
        )}
      </span>
      <div
        className={`absolute right-3 top-2 size-2 rounded-full text-xs font-medium ${colorStatus()} ${resetDate && resetDate < today && "!bg-transparent"}`}
      ></div>
    </div>
  );
};

export default HotelCalendarDatePickerRange;
