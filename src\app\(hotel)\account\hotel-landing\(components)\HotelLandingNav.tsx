"use client";
import React, { useRef, useEffect } from "react";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { usePathname } from "next/navigation";

const HotelLandingNav = () => {
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  interface NavItem {
    name: string;
    path: string;
    id: number;
    disabled?: boolean;
  }
  const navList: NavItem[] = [
    {
      name: "<PERSON>",
      path: "/account/hotel-landing",
      id: 1,
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      path: "/account/hotel-landing/team-section",
      id: 2,
    },
    {
      name: "<PERSON><PERSON>",
      path: "/account/hotel-landing/customer-review-section",
      id: 3,
    },
  ];

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = navList.findIndex((item) => item.path === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname]);

  return (
    <div
      ref={navContainerRef}
      className="hiddenScrollbar mb-5 flex gap-3 overflow-y-hidden bg-transparent px-1 pb-5 text-sm md:overflow-x-auto md:pb-1.5 md:text-base"
    >
      {navList.map((navItem) => (
        <Link
          href={navItem.path as Route}
          key={navItem.id}
          className={`${pathname === navItem.path ? "bg-secondary-6000 text-white" : "bg-transparent text-neutral-500"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 font-medium`}
        >
          {navItem.name}
        </Link>
      ))}
    </div>
  );
};

export default HotelLandingNav;
