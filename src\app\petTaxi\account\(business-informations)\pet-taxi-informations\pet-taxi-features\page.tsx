import React from "react";
import PetTaxiFeatures from "../(components)/(petTaxiFeatures)/PetTaxiFeatures";
import { cookies } from "next/headers";
import getMyTaxi from "@/actions/(protected)/taxi/getMyTaxi";

const PetTaxiFeaturesPage = async () => {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;
  const taxiData = await getMyTaxi();
  return (
    <>
      {taxiData && (
        <PetTaxiFeatures
          taxiData={taxiData?.data}
          petTaxiToken={petTaxiToken}
        />
      )}
    </>
  );
};

export default PetTaxiFeaturesPage;
