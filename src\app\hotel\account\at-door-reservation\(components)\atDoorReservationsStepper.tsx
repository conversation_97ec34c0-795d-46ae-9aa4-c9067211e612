import {
  Stepper,
  <PERSON>per<PERSON>ndicat<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Stepper<PERSON><PERSON><PERSON>,
} from "@/components/ui/stepper";
import type { FC } from "react";
import { PawPrint } from "lucide-react";
import { DiamondPlus } from "lucide-react";
import { CreditCard } from "lucide-react";
import { FileText } from "lucide-react";
import { User } from "lucide-react";
import { Calendar } from "lucide-react";
import { stepHandler } from "./atDoorReservationsContainer";

interface atDoorReservationsStepperProps {
  stepParams: {
    customer: string;
    pet: string;
    summary: string;
    payment: string;
  };
}

const AtDoorReservationsStepper: FC<atDoorReservationsStepperProps> = ({
  stepParams,
}) => {
  const stepValue = stepHandler(stepParams);

  const steps = [
    {
      step: 1,
      title: "Tarih ve Oda Seçimi",
      Icon: Calendar,
    },
    {
      step: 2,
      title: "<PERSON>üş<PERSON><PERSON> Seçimi",
      Icon: User,
    },
    {
      step: 3,
      title: "<PERSON><PERSON><PERSON><PERSON>",
      Icon: PawPrint,
    },
    {
      step: 4,
      title: "Özet",
      Icon: FileText,
    },
    {
      step: 5,
      title: "Ödeme",
      Icon: CreditCard,
    },
  ];

  return (
    <div className="space-y-8 text-center">
      <Stepper value={stepValue} className="items-start gap-4">
        {steps.map(({ step, title, Icon }) => (
          <StepperItem
            key={step}
            step={step}
            className="flex-1"
            completed={step < stepValue}
          >
            <StepperTrigger className="w-full flex-col items-center gap-2 rounded pointer-events-none">
              <div className="h-10 w-10 relative">
                <StepperIndicator className="bg-border h-10 w-10 [&_span]:sr-only">
                  <span className="sr-only">{step}</span>
                </StepperIndicator>
                <Icon
                  className={`size-5 absolute inset-0 m-auto z-10 ${step === stepValue && "text-white"} ${step < stepValue && "hidden"}`}
                />
              </div>
              <div className="space-y-0.5 max-sm:hidden">
                <StepperTitle>{title}</StepperTitle>
              </div>
            </StepperTrigger>
          </StepperItem>
        ))}
      </Stepper>
    </div>
  );
};

export default AtDoorReservationsStepper;
