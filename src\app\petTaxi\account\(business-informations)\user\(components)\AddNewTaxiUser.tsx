"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useVehicle } from "@/hooks/taxi/vehicles/useVehicle";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Switch } from "@/components/ui/switch";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
import { User } from "lucide-react";
import { MultiSelect } from "@/components/ui/multi-select";
import MultipleUserFiles from "./MultipleUserFiles";

interface AddNewTaxiUserProps {
  taxiData: PetTaxiDataApiTypes;
  petTaxiToken: string | undefined;
}

const AddNewTaxiUser: FC<AddNewTaxiUserProps> = ({
  taxiData,
  petTaxiToken,
}) => {
  const { photoInputHandler } = useVehicle();
  const [vehicle, setVehicle] = useState<any>({
    passive: false,
    vehicleName: "",
    vehiclePlate: "",
    vehicleCapacity: 0,
    vehicleType: "",
    acceptedPetTypes: [],
    cleaningCertificate: null,
    vehicleFeatures: [],
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [open, setOpen] = useState(false);
  const [vehicleFiles, setVehicleFiles] = useState({
    vehicle_license: [] as File[],
    insurance: [] as File[],
    pet_transport_certificate: [] as File[],
  });

  const closeModal = () => {
    setVehicle({
      passive: false,
      vehicleName: "",
      vehiclePlate: "",
      vehicleCapacity: 0,
      vehicleType: "",
      acceptedPetTypes: [],
      cleaningCertificate: null,
      vehicleFeatures: [],
    });
    setLoading(false);
    setDisabled(false);
    setOpen(false);
    setVehicleFiles({
      vehicle_license: [] as File[],
      insurance: [] as File[],
      pet_transport_certificate: [] as File[],
    });
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setVehicle((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const isValid = () => {
    const {
      vehicleName,
      vehiclePlate,
      vehicleCapacity,
      vehicleType,
      acceptedPetTypes,
      cleaningCertificate,
      vehicleFeatures,
    } = vehicle;

    const { vehicle_license, insurance, pet_transport_certificate } =
      vehicleFiles;

    return (
      vehicleName.trim() !== "" &&
      vehiclePlate.trim() !== "" &&
      vehicleCapacity > 0 &&
      vehicleType.trim() !== "" &&
      acceptedPetTypes.length > 0 &&
      cleaningCertificate !== null &&
      vehicleFeatures.length > 0 &&
      vehicle_license.length > 0 &&
      insurance.length > 0 &&
      pet_transport_certificate.length > 0
    );
  };

  const buttonDisabled = isValid();

  const vehicleFeatureOptions = [
    { value: "air_conditioning", label: "Klima" },
    { value: "camera", label: "Kamera" },
    { value: "partitioned_compartment", label: "Kafesli Bölme" },
    { value: "gps_tracking", label: "GPS Takip" },
    { value: "ventilation", label: "Havalandırma" },
    { value: "non_slip_floor", label: "Kaymaz Zemin" },
  ];

  const convertFilesToFileList = (files: File[]): FileList => {
    const dataTransfer = new DataTransfer();
    files.forEach((file) => dataTransfer.items.add(file));
    return dataTransfer.files;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button
        onClick={() => setOpen(true)}
        className="group relative bg-secondary-6000 hover:bg-secondary-500 text-white font-semibold px-3 py-2 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-in-out border-0 overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
        <div className="relative flex items-center gap-2">
          <User className="!size-5 text-white/80 animate-pulse" />
          <span className="text-sm font-bold tracking-wide">
            Kullanıcı Ekle
          </span>
        </div>
        <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
      </Button>
      <DialogContent
        onInteractOutside={closeModal}
        className="max-w-lg max-h-[90vh] overflow-y-auto"
      >
        <DialogHeader>
          <DialogTitle>Kullanıcı Oluşturma</DialogTitle>
          <DialogDescription>Yeni kullanıcı ekleyin.</DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(event) =>
            photoInputHandler(
              event,
              taxiData._id,
              petTaxiToken,
              vehicle,
              {
                vehicle_license: convertFilesToFileList(
                  vehicleFiles.vehicle_license
                ),
                insurance: convertFilesToFileList(vehicleFiles.insurance),
                pet_transport_certificate: convertFilesToFileList(
                  vehicleFiles.pet_transport_certificate
                ),
              },
              setLoading,
              closeModal,
              setDisabled
            )
          }
        >
          <div className="space-y-4">
            <FormItem
              label="Araç Adı*"
              className="w-full !text-black dark:!text-white"
            >
              <Input
                className="rounded-lg"
                placeholder="Araç adını girin"
                name="vehicleName"
                onChange={handleChange}
              />
            </FormItem>
            <FormItem
              label="Araç Plakası*"
              className="w-full !text-black dark:!text-white"
            >
              <Input
                className="rounded-lg"
                placeholder="34 ABC 123"
                name="vehiclePlate"
                onChange={handleChange}
              />
            </FormItem>
            <FormItem
              label="Araç Kapasitesi*"
              className="w-full !text-black dark:!text-white"
            >
              <Select
                value={
                  vehicle.vehicleCapacity === 0
                    ? ""
                    : vehicle.vehicleCapacity.toString()
                }
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleCapacity: +selected,
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Araç kapasitesini seçin" />
                </SelectTrigger>
                <SelectContent>
                  {[...Array(10)].map((_, i) => {
                    const value = (i + 1).toString();
                    return (
                      <SelectItem key={value} value={value}>
                        {value}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem
              label="Araç Tipi*"
              className="w-full !text-black dark:!text-white"
            >
              <Select
                value={vehicle.vehicleType}
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleType: selected,
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Araç tipini seçin" />
                </SelectTrigger>
                <SelectContent className="!text-black">
                  <SelectItem value="panel_van">Panelvan</SelectItem>
                  <SelectItem value="minivan">Minivan</SelectItem>
                  <SelectItem value="small_commercial_van">
                    Küçük Ticari Araç (Küçük Panelvan)
                  </SelectItem>
                  <SelectItem value="passenger_car">
                    Binek Otomobil (Sedan / Hatchback)
                  </SelectItem>
                  <SelectItem value="suv">SUV</SelectItem>
                  <SelectItem value="crossover">Crossover</SelectItem>
                  <SelectItem value="caravan">
                    Karavan / Mobil Klinik Araç
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem
              label="Araç Hijyen Sertifikası"
              className="w-full !text-black dark:!text-white"
            >
              <Select
                value={
                  vehicle.cleaningCertificate === null
                    ? undefined
                    : vehicle.cleaningCertificate
                      ? "true"
                      : "false"
                }
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    cleaningCertificate:
                      selected === "true"
                        ? true
                        : selected === "false"
                          ? false
                          : "false",
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Var/Yok" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Var</SelectItem>
                  <SelectItem value="false">Yok</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem
              label="Araç Özellikleri"
              className="w-full !text-black dark:!text-white"
            >
              <MultiSelect
                options={vehicleFeatureOptions}
                onValueChange={(value) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleFeatures: value,
                  }))
                }
                maxCount={15}
                placeholder="Araç özelliklerini seçin"
              />
            </FormItem>
            <FormItem
              label="Aktiflik Durumu"
              className="w-full !text-black dark:!text-white"
            >
              <div className="flex gap-2">
                <Switch
                  name="passive"
                  checked={!vehicle.passive}
                  onCheckedChange={(checked) =>
                    setVehicle((prev: any) => ({
                      ...prev,
                      passive: !checked,
                    }))
                  }
                  className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                />
                <p
                  className={
                    vehicle.passive ? "text-red-500" : "text-green-500"
                  }
                >
                  {vehicle.passive ? "Pasif" : "Aktif"}
                </p>
              </div>
            </FormItem>
            <FormItem
              label="Belgeler"
              className="w-full !text-black dark:!text-white"
            >
              <MultipleUserFiles
                name="Araç Ruhsatı"
                files={vehicleFiles.vehicle_license}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({
                    ...prev,
                    vehicle_license: files,
                  }))
                }
              />
              <MultipleUserFiles
                name="Sigorta Belgesi"
                files={vehicleFiles.insurance}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({ ...prev, insurance: files }))
                }
              />
              <MultipleUserFiles
                name="Pet Taşıma Uygunluk Belgesi"
                files={vehicleFiles.pet_transport_certificate}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({
                    ...prev,
                    pet_transport_certificate: files,
                  }))
                }
              />
            </FormItem>
          </div>
          <div className="mt-7 flex justify-end gap-5">
            <Button onClick={closeModal} variant="outline" type="button">
              İptal
            </Button>
            <Button
              disabled={!buttonDisabled || disabled}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              type="submit"
            >
              {loading ? <LoadingSpinner /> : "Kaydet"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddNewTaxiUser;
