"use client";
import React from "react";
import SingleHotelFile from "../../../hotel-informations/(components)/(hotelFiles)/SingleHotelFile";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface FileStepData {
  docType: string;
  name: string;
  condition?: (hotelData: HotelDataApiTypes) => boolean;
}

interface FileStepContentProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
  currentStep: number;
  currentStepData?: FileStepData;
  isTransitioning: boolean;
}

const FileStepContent: React.FC<FileStepContentProps> = ({
  hotelData,
  hotelToken,
  currentStep,
  currentStepData,
  isTransitioning,
}) => {
  if (!hotelData || !currentStepData) {
    return null;
  }

  return (
    <div className="relative overflow-hidden">
      <div
        key={currentStep}
        className={`transform transition-all duration-700 ease-out ${
          isTransitioning
            ? "opacity-50 scale-95"
            : "opacity-100 scale-100 animate-in slide-in-from-bottom-8 fade-in duration-700"
        }`}
      >
        <SingleHotelFile
          hotelToken={hotelToken}
          filePath={`hotel/${hotelData._id}/files/`}
          docType={currentStepData.docType}
          name={currentStepData.name}
          hotelData={hotelData}
          fullWidth={true}
        />
      </div>
    </div>
  );
};

export default FileStepContent;
