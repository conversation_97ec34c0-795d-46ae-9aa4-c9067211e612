import {
  veterinaryServiceApiTypes,
  transportationServiceApiTypes,
  groomingServiceApiTypes,
} from "./services/serviceTypes";

interface AddressType {
  country: {
    identificationCode: string;
    name: string;
  };
  streetName: string;
  buildingName: string;
  buildingNumber: string;
  cityName: string;
  postalZone: string;
  region: string;
  district: string;
  blockName: string;
  citySubdivisionName: string;
  postbox: string;
  room: string;
}

interface CampaignType {
  campaignType: string;
  startDate: string | null;
  endDate: string | null;
  isActive: boolean;
  campaignDetails: {
    subscriptionName: string;
    nightsEarned: number;
    total: number;
    subscriptionDuration?: string;
    roomGroups?: string[];
  };
  _id: string;
}

interface SubscriptionType {
  _id: string;
  subscriptionName: string;
  subscriptionDuration: number;
  nightsEarned: number;
  total: number;
  roomGroups: {
    name: string;
    _id: string;
  }[];
  isActive: boolean;
}

interface FileType {
  _id: string;
  src: string;
  size: number;
  mimeType: string;
  originalname: string;
  s3Path: string;
  docType: string;
  createdAt: string;
  updatedAt: string;
}

interface ImageSizeType {
  size: number;
  src: string;
  width: number;
  height: number;
}

export interface ImageType {
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimeType: string;
  fit: string;
  tags: string;
  createdAt: string;
  updatedAt: string;
  processed: boolean;
  img800: ImageSizeType;
  img400: ImageSizeType;
  img200: ImageSizeType;
  img100: ImageSizeType;
}

interface AutoApproveType {
  pawbooking: boolean;
  telephone: boolean;
  whatsapp: boolean;
  instagram: boolean;
  facebook: boolean;
  website: boolean;
  telegram: boolean;
}

interface NotifyHotelOwnerType {
  reservationAddedNew: boolean;
  reservationCancelled: boolean;
}

interface SendReportsType {
  dailyReport: any[];
  weeklyReport: any[];
  monthlyReport: any[];
}

interface HotelSettings {
  autoApprove: AutoApproveType;
  notifyHotelOwner: NotifyHotelOwnerType;
  sendReports: SendReportsType;
}

interface SpecialRuleType {
  title: string;
  rule: string;
  _id: string;
}

interface HotelPolicyType {
  checkInStartTime: string;
  checkInEndTime: string;
  cancellationPolicyType: string;
  checkOutTime: string;
  description: string;
  dateRange: string;
  specialRules: SpecialRuleType[];
}

interface HotelRoomType {
  _id: string;
  hotel: string;
  petType: string[];
  roomName: string;
  roomCapacity: number;
  roomNameStartingNumber: number;
  roomDescription: string;
  roomFeatures: string[];
  nonAcceptedBreedTypes: string[];
  images: ImageType[];
  roomGroup: string;
  passive: boolean;
  reviews: any[];
  createdAt: string;
  updatedAt: string;
}

interface HotelUserPermissionType {
  read: boolean;
  addNew: boolean;
  edit: boolean;
  remove: boolean;
}

interface HotelReservationPermissionType {
  read: boolean;
  addNew: boolean;
  edit: boolean;
  remove: boolean;
  approve: boolean;
  cancel: boolean;
  suspend: boolean;
}

interface HotelInfoPermissionType {
  landingPage: boolean;
  edit: boolean;
}

interface HotelUsersPermissionType {
  read: boolean;
  addNew: boolean;
  edit: boolean;
  remove: boolean;
  editPermissions: boolean;
}

interface HotelUserPermissionsType {
  allocations: HotelUserPermissionType;
  rooms: HotelUserPermissionType;
  petOwnerHistory: HotelUserPermissionType;
  petHistory: HotelUserPermissionType;
  additionalServices: HotelUserPermissionType;
  hotelInfo: HotelInfoPermissionType;
  hotelUsers: HotelUsersPermissionType;
  reservations: HotelReservationPermissionType;
}

interface HotelUserType {
  _id: string;
  hotel: string;
  username: string;
  email: string;
  phone: string;
  fullName: string;
  firstName: string;
  lastName: string;
  role: string;
  gender: string;
  dateOfBirth: string;
  image: string | null;
  bio: string;
  passive: boolean;
  createdAt: string;
  updatedAt: string;
  permissions: HotelUserPermissionsType;
  pushTokenList: string[];
}

export interface HotelDataApiTypes {
  acceptedPetTypes: string[];
  actionRequired: boolean;
  address: AddressType;
  availableCampaigns: CampaignType[];
  availableServices: (
    | veterinaryServiceApiTypes
    | transportationServiceApiTypes
    | groomingServiceApiTypes
  )[];
  availableSubscriptions: SubscriptionType[];
  careServices: string[];
  createdAt: string;
  currency: string;
  files: FileType[];
  googleMapUrl: string;
  googleReviewsSnippet: string;
  googleStar: string;
  hotelDescription: string;
  hotelFeatures: string[];
  hotelName: string;
  hotelPhoneNumber: string;
  hotelSubMerchant: string;
  images: ImageType[];
  legalName: string;
  logo: ImageType;
  owner: string;
  parameters: HotelSettings;
  passive: boolean;
  policy: HotelPolicyType;
  propertyType: string;
  reviewCount: string;
  rooms: HotelRoomType[];
  status: string;
  taxNumber: string;
  taxOffice: string;
  updatedAt: string;
  users: HotelUserType[];
  verifiedDocuments: boolean;
  _id: string;
}
