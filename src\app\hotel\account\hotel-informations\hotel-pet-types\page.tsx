import React from "react";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import HotelPetTypes from "../(components)/(hotelPetTypes)/HotelPetTypes";

const HotelPetTypesPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  return (
    <>
      {hotelData && (
        <HotelPetTypes hotelData={hotelData?.data} hotelToken={hotelToken} />
      )}
    </>
  );
};

export default HotelPetTypesPage;
