"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Star,
  Users,
  Shield,
  TrendingUp,
  Eye,
  CheckCircle,
  Award,
} from "lucide-react";
import { useRouter } from "next/navigation";

export function MarketplaceSection() {
  const router = useRouter();
  const marketplaceStats = [
    { icon: Users, value: "100+", label: "Aktif Pet Sahibi" },
    { icon: Eye, value: "6K+", label: "<PERSON><PERSON><PERSON>k Görüntülenme" },
    { icon: Star, value: "4.8", label: "Ortalama Değerlendirme" },
    { icon: TrendingUp, value: "%95", label: "Doluluk Oranı" },
  ];

  const benefits = [
    {
      icon: Shield,
      title: "Ruhsatlı Oteller",
      description:
        "Sadece resmi ruhsata sahip pet otelleri platformda yer alır",
    },
    {
      icon: Star,
      title: "Yüksek Görünürlük",
      description:
        "Binlerce pet sahibinin günlük ziyaret ettiği platformda öne çıkın",
    },
    {
      icon: MapPin,
      title: "Konum Bazlı Arama",
      description: "Müşteriler size en yakın konumdan kolayca ulaşabilir",
    },
    {
      icon: Award,
      title: "Güvenilir Platform",
      description:
        "Pet sahiplerinin en çok tercih ettiği güvenilir rezervasyon platformu",
    },
  ];

  return (
    <section className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8 bg-orange-50 to-pink-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <Badge
            variant="secondary"
            className="mb-4 bg-orange-100 text-orange-700"
          >
            PawBooking.co Marketplace
          </Badge>
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">
            Türkiye'nin En Büyük Pet Otel Platformunda
            <span className="text-orange-500 block">Yerinizi Alın</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
            Sadece ruhsatlı pet otellerinin yer aldığı platformda işletmenizi
            binlerce pet sahibine ulaştırın
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-12 lg:mb-16">
          {marketplaceStats.map((stat, index) => (
            <Card
              key={index}
              className="text-center border-0 shadow-sm bg-white/70 backdrop-blur-sm"
            >
              <CardContent className="p-4 sm:p-6">
                <div className="flex justify-center mb-2">
                  <stat.icon className="w-6 h-6 sm:w-8 sm:h-8 text-orange-600" />
                </div>
                <div className="text-2xl sm:text-3xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <div className="text-xs sm:text-sm text-gray-600">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {benefits.map((benefit, index) => (
            <Card
              key={index}
              className="border-0 shadow-sm bg-white/70 backdrop-blur-sm hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                    <benefit.icon className="w-5 h-5 text-orange-600" />
                  </div>
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  {benefit.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8 lg:p-12 text-center">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Hemen Platformda Yer Alın
            </h3>
            <p className="text-lg text-gray-600 mb-6">
              Ruhsatlı pet otelinizi PawBooking.co'da listeleyerek daha fazla
              müşteriye ulaşın
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
              <Button
                onClick={() => router.push("/auth/create-partner")}
                size="lg"
                className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4"
              >
                <CheckCircle className="w-5 h-5 mr-2" />
                Ücretsiz Listing Oluştur
              </Button>
              <Button
                onClick={() => window.open("https://pawbooking.co", "_blank")}
                size="lg"
                variant="outline"
                className="px-8 py-4 bg-transparent"
              >
                Platform Örneklerini İncele
              </Button>
            </div>

            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Ruhsat kontrolü ücretsiz</span>
              </div>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Anında yayına alınır</span>
              </div>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Komisyon sadece rezervasyondan</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
