"use server";
import { redirect } from "next/navigation";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import { cookies } from "next/headers";
import { revalidate<PERSON>ath<PERSON>and<PERSON> } from "@/lib/revalidate";

export async function getUpdateActionRequired(actionRequired: boolean) {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  if (hotelToken) {
    try {
      const url = `${HOTEL_API_PATHS.getUpdateActionRequired}/${actionRequired}`;
      const response = await fetch(url, {
        cache: "no-cache",
        headers: {
          hotelToken: hotelToken,
          "Content-Type": "application/json",
        },
      });
      const result = await response.json();

      if (result.status === 401) {
        redirect("/");
      } else if (result.status === 404) {
        redirect("/404");
      }

      if (!response.ok || !result.success) {
        console.error("Network response was not ok");
        return undefined;
      }
      revalidatePathHandler("/hotel/account/reservations");
      return result;
    } catch (err: unknown) {
      console.error("Error fetching data:", err);
      return undefined;
    }
  }
}
