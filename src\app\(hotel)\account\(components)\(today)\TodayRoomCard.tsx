"use client";
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { useWindowSize } from "react-use";
import TodayRoomCardMobile from "./(mobile)/TodayRoomCardMobile";
import RoomGroups from "./RoomGroups";
import RoomInformation from "./RoomInformation";
import PetInformation from "./PetInformation";
import VetInformation from "./VetInformation";
import PetOwnerInformation from "./PetOwnerInformation";
import BalanceInformation from "./Balance";
import IconInfo from "@/shared/icons/Info";
import { But<PERSON> } from "@/components/ui/button";
import HotelReservationContainer from "./(hotelReservation)/hotelReservationContainer";
import PawPlus from "@/shared/PawPlus";
import PlusFeatureModal from "@/components/PlusFeatureModal";

interface TodayRoomCardProps {
  roomGroupData: any;
  hotelToken: string | undefined;
  membershipData: any;
}

export function formatPhoneNumber(phoneNumber: string) {
  return phoneNumber.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})$/, "+$1 $2 $3 $4");
}

const TodayRoomCard: React.FC<TodayRoomCardProps> = ({
  roomGroupData,
  hotelToken,
  membershipData,
}) => {
  const translate = useTranslations("TodayRoomCard");
  const [openItems, setOpenItems] = useState<string[]>([
    roomGroupData[0].roomGroupName,
  ]);
  const [allOpen, setAllOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isReservationOpen, setIsReservationOpen] = useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);

  function IsOpen(room: any) {
    setSelectedRoom(room);
    setIsOpen(true);
  }

  const handleButtonClick = () => {
    if (membershipData?.membershipType === "free") {
      setModalIsVisible(true);
    } else {
      setIsReservationOpen(true);
    }
  };

  const windowWidth = useWindowSize().width;

  useEffect(() => {
    if (windowWidth > 767) {
      setIsMobile(false);
    } else {
      setIsMobile(true);
    }
  }, [windowWidth]);

  // OPENS ALL GROUP ACCORDIONS
  const toggleAllGroups = () => {
    if (allOpen) {
      setOpenItems([]);
    } else {
      setOpenItems(roomGroupData.map((data: any) => data.roomGroupName));
    }
    setAllOpen(!allOpen);
  };

  // IF ALL ACCORDIONS OPEN OR CLOSED SETS SWITCH BUTTON
  useEffect(() => {
    if (openItems.length === roomGroupData.length) {
      setAllOpen(true);
    } else {
      setAllOpen(false);
    }
  }, [openItems, roomGroupData]);

  // use this instead of selectedRoom because selectedRoom does not update the state unless the modal is closed.
  const updatedRoomData = roomGroupData
    ?.flatMap((group: any) => group.rooms)
    ?.find((room: any) => room._id === selectedRoom?._id);

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex max-w-56 items-start justify-start">
          <Button
            className={`${
              membershipData?.membershipType === "free"
                ? "w-44 md:w-auto bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
                : "bg-secondary-6000 hover:bg-secondary-700 text-white"
            }`}
            onClick={handleButtonClick}
          >
            {membershipData?.membershipType === "free" && (
              <PawPlus width="30" height="30" />
            )}
            Rezervasyon Oluştur
          </Button>
        </div>
        <div className="flex justify-end gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <div className="cursor-pointer text-secondary-6000">
                <IconInfo />
              </div>
            </PopoverTrigger>
            <PopoverContent side="bottom" align="start">
              <div className="flex flex-col gap-0.5">
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#FFB3B3]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Dolu Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#FFD59E]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Rezerve Edilmiş Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#C1E1C1]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Rezerve Edilebilir Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#D3D3D3]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Rezervasyona Kapalı Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#B3D9FF]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Bloke Oda
                  </span>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <div className="flex items-center justify-end gap-2">
            <div className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              {allOpen ? translate("closeAll") : translate("openAll")}
            </div>
            <Switch
              name="show-hide-rooms"
              onClick={toggleAllGroups}
              checked={allOpen}
            />
          </div>
        </div>
      </div>
      <RoomGroups
        roomGroupData={roomGroupData}
        IsOpen={IsOpen}
        openItems={openItems}
        setOpenItems={setOpenItems}
      />
      {!isMobile && selectedRoom && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="h-[calc(100vh-50px)] overflow-y-auto max-w-2xl">
            <Tabs defaultValue="roomInformation">
              <TabsList className="mt-2 flex shrink-0 gap-1 bg-white text-black dark:bg-inherit dark:text-inherit">
                <TabsTrigger
                  value="balance"
                  className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                >
                  Ödeme Bilgileri
                </TabsTrigger>
                <TabsTrigger
                  value="roomInformation"
                  className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                >
                  Oda Bilgileri
                </TabsTrigger>
                {(selectedRoom?.reservation?.status === "checkedIn" ||
                  selectedRoom?.reservation?.status === "waitingForCheckIn" ||
                  selectedRoom?.reservation?.status ===
                    "waitingForCheckOut") && (
                  <TabsTrigger
                    value="petInformation"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                  >
                    Pet Bilgileri
                  </TabsTrigger>
                )}
                {(selectedRoom?.reservation?.status === "checkedIn" ||
                  selectedRoom?.reservation?.status === "waitingForCheckIn" ||
                  selectedRoom?.reservation?.status ===
                    "waitingForCheckOut") && (
                  <TabsTrigger
                    value="contactInformation"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                  >
                    İletişim Bilgileri
                  </TabsTrigger>
                )}
              </TabsList>
              <TabsContent value="roomInformation">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <RoomInformation
                    selectedRoom={selectedRoom}
                    hotelToken={hotelToken}
                    setIsOpen={setIsOpen}
                  />
                </div>
              </TabsContent>
              <TabsContent value="balance">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <BalanceInformation
                    selectedRoom={updatedRoomData}
                    hotelToken={hotelToken}
                  />
                </div>
              </TabsContent>
              <TabsContent value="petInformation">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <PetInformation selectedRoom={selectedRoom} />
                </div>
              </TabsContent>
              <TabsContent value="contactInformation">
                <div className="mt-2">
                  <Tabs defaultValue="petOwnerContact">
                    <TabsList className="mt-2 flex shrink-0 gap-1 bg-white text-black dark:bg-inherit dark:text-inherit">
                      <TabsTrigger
                        value="petOwnerContact"
                        className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                      >
                        Pet Sahibi
                      </TabsTrigger>
                      <TabsTrigger
                        value="veterinaryContact"
                        className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                      >
                        Sorumlu Veteriner
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="petOwnerContact">
                      <PetOwnerInformation selectedRoom={selectedRoom} />
                    </TabsContent>
                    <TabsContent value="veterinaryContact">
                      <VetInformation selectedRoom={selectedRoom} />
                    </TabsContent>
                  </Tabs>
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}
      {isMobile && selectedRoom && (
        <TodayRoomCardMobile
          selectedRoom={updatedRoomData}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          hotelToken={hotelToken}
        />
      )}
      <Dialog open={isReservationOpen} onOpenChange={setIsReservationOpen}>
        <DialogContent className="h-[520px] max-w-lg">
          <HotelReservationContainer hotelToken={hotelToken} />
        </DialogContent>
      </Dialog>
      {modalIsVisible && (
        <PlusFeatureModal
          isOpen={modalIsVisible}
          setIsOpen={setModalIsVisible}
          message="Bu özellik sadece Plus üyelikler için geçerlidir."
        />
      )}
    </>
  );
};

export default TodayRoomCard;
