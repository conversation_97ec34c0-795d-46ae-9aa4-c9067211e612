import type { <PERSON> } from "react";
import React from "react";

export interface CommonLayoutProps {
  children?: React.ReactNode;
}

const DashboardLayout: FC<CommonLayoutProps> = ({ children }) => {
  return (
    <div className="nc-CommonLayoutAccount min-h-screen bg-neutral-50 dark:bg-neutral-900 relative">
      <div className="pb-24 lg:pb-32 container">{children}</div>
    </div>
  );
};

export default DashboardLayout;
