"use client";
import React, { useEffect } from "react";
import type { FC } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface RoomGroupsProps {
  roomGroupData: any;
  IsOpen: any;
  openItems: any;
  setOpenItems: any;
}

const RoomGroups: FC<RoomGroupsProps> = ({
  roomGroupData,
  IsOpen,
  openItems,
  setOpenItems,
}) => {
  useEffect(() => {
    if (openItems.length === 0 || !openItems.includes("checkedInRooms")) {
      setOpenItems(["checkedInRooms"]);
    }
  }, []);

  // OPENS ACCORDIONS ONE BY ONE
  const handleAccordionChange = (value: string) => {
    if (openItems.includes(value)) {
      setOpenItems(openItems.filter((item: any) => item !== value));
    } else {
      setOpenItems([...openItems, value]);
    }
  };

  const roomGroupColorHandler = (
    allocation: string | null,
    reservation: { status: string } | null
  ) => {
    if (allocation === null) {
      return "bg-[#D3D3D3]";
    } else if (reservation === null) {
      return "bg-[#C1E1C1]";
    } else if (reservation.status === "confirmed") {
      return "bg-[#FFD59E]";
    } else if (reservation.status === "waitingForCheckIn") {
      return "bg-[#FFD59E]";
    } else if (reservation.status === "checkedIn") {
      return "bg-[#FFB3B3]";
    } else if (reservation.status === "booked") {
      return "bg-[#B3D9FF]";
    } else {
      return "bg-[#ebf782]";
    }
  };

  const checkedInRooms = roomGroupData
    .flatMap((group: any) =>
      group.rooms.filter(
        (room: any) =>
          room?.reservation?.status === "checkedIn" ||
          room?.reservation?.status === "waitingForCheckOut" ||
          room?.reservation?.status === "waitingForCheckIn" ||
          room?.reservation?.status === "confirmed" ||
          room?.reservation?.status === "booked"
      )
    )
    .map((room: any, index: number) => (
      <div
        className={`relative flex h-20 cursor-pointer flex-col justify-between rounded-lg p-3 capitalize text-white brightness-100 drop-shadow-md duration-300 hover:brightness-90 ${roomGroupColorHandler(
          room.allocation,
          room.reservation
        )}`}
        key={`checkedIn-${index}`}
        onClick={() => IsOpen(room)}
      >
        <div>{room.roomName}</div>
        <div>{room?.reservation?.pet?.name}</div>
      </div>
    ));

  return (
    <Accordion type="multiple" value={openItems} className="w-full">
      {checkedInRooms.length > 0 && (
        <AccordionItem value="checkedInRooms">
          <AccordionTrigger
            onClick={() => handleAccordionChange("checkedInRooms")}
            className="capitalize font-semibold text-secondary-6000"
          >
            Dolu odalar
          </AccordionTrigger>
          <AccordionContent>
            <div className="mx-auto grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
              {checkedInRooms}
            </div>
          </AccordionContent>
        </AccordionItem>
      )}
      <div>
        {roomGroupData.map((data: any) => {
          return (
            <div key={data.roomGroupId}>
              <AccordionItem value={data.roomGroupName}>
                <AccordionTrigger
                  onClick={() => handleAccordionChange(data.roomGroupName)}
                  className="capitalize"
                >
                  {data.roomGroupName}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="mx-auto grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                    {data.rooms.map((room: any, index: number) => {
                      const colorValue = roomGroupColorHandler(
                        room.allocation,
                        room.reservation
                      );
                      return (
                        <div
                          className={`relative flex h-20 cursor-pointer flex-col justify-between rounded-lg p-3 capitalize text-white brightness-100 drop-shadow-md duration-300 hover:brightness-90 ${
                            colorValue
                          }`}
                          key={index}
                          onClick={() => IsOpen(room)}
                        >
                          <div>{room.roomName}</div>
                          <div>{room?.reservation?.pet?.name}</div>
                        </div>
                      );
                    })}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </div>
          );
        })}
      </div>
    </Accordion>
  );
};

export default RoomGroups;
