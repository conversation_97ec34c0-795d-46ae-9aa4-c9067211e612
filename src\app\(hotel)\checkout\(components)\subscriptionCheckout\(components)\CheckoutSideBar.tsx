import React from "react";
import type { FC } from "react";
import CheckoutDetail from "./CheckoutDetail";

interface CheckoutSideBarProps {
  selectedTier: any;
}
const CheckoutSideBar: FC<CheckoutSideBarProps> = ({ selectedTier }) => {
  return (
    <div className="flex w-full flex-col space-y-6 border border-neutral-200 p-3 dark:border-neutral-700 sm:space-y-8 sm:rounded-2xl sm:p-4">
      {selectedTier && <CheckoutDetail selectedTier={selectedTier} />}
    </div>
  );
};

export default CheckoutSideBar;
