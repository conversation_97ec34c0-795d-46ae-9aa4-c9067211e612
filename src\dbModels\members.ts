import type { S3Image } from "./s3Image";

export interface Member {
  _id?: string;
  username?: string;
  email?: string;
  phone?: string;
  password?: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  role?:
    | "pet_owner"
    | "hotel_owner"
    | "hotel_user"
    | "blog_editor"
    | "cms_editor"
    | "admin";
  gender?: "" | "male" | "female" | "other";
  dateOfBirth?: string;
  city?: string;
  image?: S3Image | null;
  bio?: string;
  favorites: {
    hotels: [];
    rooms: [];
    roomAllocations: [];
  };
}
