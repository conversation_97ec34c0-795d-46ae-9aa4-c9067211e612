import React from "react";
import { cookies } from "next/headers";
import SubscriptionCard from "./(components)/SubscriptionCard";
import SubscriptionsModal from "./(components)/SubscriptionsModal";
import getSubscriptions from "@/actions/(protected)/hotel/getSubscriptions";
import getRoomGroups from "@/actions/(protected)/hotel/getRoomGroups";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import { redirect } from "next/navigation";

const SubscriptionsPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const Subscriptions = await getSubscriptions();
  const roomGroupData = await getRoomGroups();
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  if (membershipData?.data?.membershipType !== "plus") {
    redirect("/account");
  }

  return (
    <div>
      <SubscriptionsModal
        hotelToken={hotelToken}
        roomGroupData={roomGroupData?.data?.data}
      />
      {Subscriptions?.data?.availableSubscriptions && (
        <>
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Abonelik
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {Subscriptions?.data?.availableSubscriptions &&
              Subscriptions?.data?.availableSubscriptions.map(
                (subscriptions: any, index: number) => (
                  <SubscriptionCard
                    subscriptions={subscriptions}
                    roomGroupData={roomGroupData?.data?.data}
                    key={index}
                    hotelToken={hotelToken}
                  />
                )
              )}
          </div>
        </>
      )}
    </div>
  );
};

export default SubscriptionsPage;
