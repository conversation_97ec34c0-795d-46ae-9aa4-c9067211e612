import {
  Stepper,
  Stepper<PERSON>ndicator,
  <PERSON>per<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>per<PERSON><PERSON><PERSON>,
} from "@/components/ui/stepper";
import type { FC } from "react";
import { PawPrint } from "lucide-react";
import { DiamondPlus } from "lucide-react";
import { CreditCard } from "lucide-react";
import { FileText } from "lucide-react";

interface checkoutStepperProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
}

const CheckoutStepper: FC<checkoutStepperProps> = ({ currentStep, setCurrentStep }) => {
  const steps = [
    {
      step: 1,
      title: "<PERSON><PERSON><PERSON> ve <PERSON>da Seçimi",
      Icon: PawPrint,
    },
    {
      step: 2,
      title: "Müşteri Seçimi",
      Icon: PawPrint,
    },
    {
      step: 3,
      title: "<PERSON>vci<PERSON>",
      Icon: CreditCard,
    },
    {
      step: 4,
      title: "<PERSON><PERSON>",
      Icon: DiamondPlus,
    },
    {
      step: 5,
      title: "<PERSON><PERSON><PERSON>",
      Icon: FileText,
    },
    {
      step: 6,
      title: "<PERSON><PERSON><PERSON>",
      Icon: CreditCard,
    },
  ];

  return (
    <div className="max-w-3xl space-y-8 text-center">
      <Stepper
        value={currentStep}
        onValueChange={setCurrentStep}
        className="items-start gap-4"
      >
        {steps.map(({ step, title, Icon }) => (
          <StepperItem
            key={step}
            step={step}
            className="flex-1"
            completed={step < currentStep}
          >
            <StepperTrigger className="w-full flex-col items-center gap-2 rounded pointer-events-none">
              <div className="h-10 w-10 relative">
                <StepperIndicator className="bg-border h-10 w-10 [&_span]:sr-only">
                  <span className="sr-only">{step}</span>
                </StepperIndicator>
                <Icon
                  className={`size-5 absolute inset-0 m-auto z-10 ${step === currentStep && "text-white"} ${step < currentStep && "hidden"}`}
                />
              </div>
              <div className="space-y-0.5 max-sm:hidden">
                <StepperTitle>{title}</StepperTitle>
              </div>
            </StepperTrigger>
          </StepperItem>
        ))}
      </Stepper>
    </div>
  );
};

export default CheckoutStepper;
