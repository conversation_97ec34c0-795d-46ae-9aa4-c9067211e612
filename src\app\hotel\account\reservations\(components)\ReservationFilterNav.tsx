"use client";
import React, { useRef, useEffect } from "react";
import Link from "next/link";
import type { Route } from "@/routers/types";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";

const ReservationFilterNav = ({
  tab,
  reservationPage,
}: {
  tab: string | string[];
  reservationPage: number;
}) => {
  const translate = useTranslations("HotelInformationsNav");
  const pathname = usePathname();
  const navContainerRef = useRef<HTMLDivElement>(null);
  interface NavItem {
    name: string;
    path: string;
    id: number;
    tabName: string;
  }
  const navList: NavItem[] = [
    {
      name: "Tüm<PERSON>",
      path: "/hotel/account/reservations",
      id: 1,
      tabName: "all",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      path: `/hotel/account/reservations?tab=waitingForAction&page=${reservationPage}`,
      id: 2,
      tabName: "waitingForAction",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      path: `/hotel/account/reservations?tab=currentInHotel&page=${reservationPage}`,
      id: 3,
      tabName: "currentInHotel",
    },
    {
      name: "Yarın Gelecekler",
      path: `/hotel/account/reservations?tab=tomorrowsCheckins&page=${reservationPage}`,
      id: 4,
      tabName: "tomorrowsCheckins",
    },
  ];

  //Determines the scroll position based on the selected nav item when a nav item is clicked.
  useEffect(() => {
    const index = navList.findIndex((item) => item.path === pathname);
    if (index !== -1 && navContainerRef.current) {
      const itemElement = navContainerRef.current.children[
        index
      ] as HTMLElement;
      const containerRect = navContainerRef.current.getBoundingClientRect();
      const itemRect = itemElement.getBoundingClientRect();
      const scrollAmount =
        itemRect.left -
        containerRect.left -
        containerRect.width / 2 +
        itemRect.width / 2;
      navContainerRef.current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  }, [pathname]);

  return (
    <div
      ref={navContainerRef}
      className="hiddenScrollbar flex gap-3 overflow-y-hidden bg-transparent px-1 text-sm md:overflow-x-auto md:pb-1.5 md:text-base"
    >
      {navList.map((navItem) => (
        <Link
          href={navItem.path as Route}
          key={navItem.id}
          className={`${tab === navItem.tabName ? "bg-secondary-6000 text-white font-medium" : "bg-transparent"} shrink-0 cursor-pointer rounded-sm px-3 py-1.5 text-neutral-700 dark:text-neutral-200`}
        >
          {navItem.name}
        </Link>
      ))}
    </div>
  );
};

export default ReservationFilterNav;
