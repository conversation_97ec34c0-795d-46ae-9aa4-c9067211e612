"use client";
import React from "react";
import type { FC } from "react";
import GallerySlider from "@/components/GallerySlider";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useUpdateReservationStatus } from "@/hooks/hotel/useUpdateReservationStatus";
import Image from "next/image";
import cat from "@/images/pets-icon/cat.png";
import smallDogBreed from "@/images/pets-icon/corgi.png";
import mediumDogBreed from "@/images/pets-icon/border-collie.png";
import largeDogBreed from "@/images/pets-icon/german-shepherd.png";
import rabbit from "@/images/pets-icon/rabbit.png";
import turtle from "@/images/pets-icon/turtle.png";
import guineaPig from "@/images/pets-icon/guineapig.png";
import parrot from "@/images/pets-icon/parrot.png";
import hedgehog from "@/images/pets-icon/hedgehog.png";
import horse from "@/images/pets-icon/horse.png";

interface RoomInformationProps {
  selectedRoom: any;
  hotelToken: string | undefined;
  setIsOpen: any;
}

const RoomInformation: FC<RoomInformationProps> = ({
  selectedRoom,
  hotelToken,
  setIsOpen,
}) => {
  const { updateReservationStatusHandler } = useUpdateReservationStatus();

  const handleCheckIn = () => {
    if (selectedRoom?.reservation?._id) {
      updateReservationStatusHandler(
        hotelToken,
        "checkedIn",
        selectedRoom?.reservation?._id
      );
      setIsOpen(false);
    }
  };

  const handleCheckOut = () => {
    if (selectedRoom?.reservation?._id) {
      updateReservationStatusHandler(
        hotelToken,
        "checkedOut",
        selectedRoom?.reservation?._id
      );
      setIsOpen(false);
    }
  };

  const PET_ICONS = {
    cat: { icon: cat, label: "Kedi" },
    smallDogBreed: { icon: smallDogBreed, label: "Köpek (Küçük Irk)" },
    mediumDogBreed: { icon: mediumDogBreed, label: "Köpek (Orta Irk)" },
    largeDogBreed: { icon: largeDogBreed, label: "Köpek (Büyük Irk)" },
    parrot: { icon: parrot, label: "Papağan" },
    rabbit: { icon: rabbit, label: "Tavşan" },
    guineaPig: { icon: guineaPig, label: "Ginepig" },
    hedgehog: { icon: hedgehog, label: "Kirpi" },
    turtle: { icon: turtle, label: "Kaplumbağa" },
    horse: { icon: horse, label: "At" },
  };

  const petTypeList = Array.isArray(selectedRoom.petType)
    ? selectedRoom.petType
    : [selectedRoom.petType];

  const sortedPetTypes = Object.keys(PET_ICONS).filter((key) =>
    petTypeList.includes(key)
  );

  return (
    <div className="mb-4 mt-2 flex flex-col gap-2">
      <div className="my-4 flex w-full justify-center">
        <div className="w-44">
          <GallerySlider
            uniqueID={`StayCard2_${2}`}
            ratioClass="aspect-w-5 aspect-h-2 md:aspect-w-12 md:aspect-h-11"
            galleryImgs={[defaultHotelImage]}
            imageClass="rounded-lg"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 my-3 md:my-7">
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda İsmi</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">{selectedRoom?.roomName}</div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda Kapasitesi</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-sm capitalize">
              {selectedRoom?.roomCapacity}
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
          <div>
            <div className="text-sm font-semibold">Oda Pet Tipi</div>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="capitalize flex flex-wrap text-sm text-center gap-1.5">
              {sortedPetTypes.map((petType: any, i: any) => {
                const pet = PET_ICONS[petType as keyof typeof PET_ICONS];
                if (!pet) return null;

                return (
                  <span
                    key={i}
                    title={pet.label}
                    className="text-xl cursor-default"
                  >
                    <Image
                      src={pet.icon}
                      width={30}
                      height={30}
                      alt={pet.label}
                    />
                  </span>
                );
              })}
            </div>
          </div>
        </div>
        {(selectedRoom?.reservation?.status === "checkedIn" ||
          selectedRoom?.reservation?.status === "waitingForCheckIn" ||
          selectedRoom?.reservation?.status === "waitingForCheckOut" ||
          selectedRoom?.reservation?.status === "confirmed" ||
          selectedRoom?.reservation?.status === "booked") && (
          <>
            <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
              <div>
                <div className="text-sm font-semibold">Check-In</div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-sm capitalize">
                  {new Date(
                    selectedRoom?.reservation?.startDate
                  ).toLocaleDateString()}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center justify-between rounded-lg border py-2 sm:px-4">
              <div>
                <div className="text-sm font-semibold">Check-Out</div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="text-sm capitalize">
                  {new Date(
                    selectedRoom?.reservation?.endDate
                  ).toLocaleDateString()}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      {(selectedRoom?.reservation?.status === "confirmed" ||
        selectedRoom?.reservation?.status === "waitingForCheckIn") && (
        <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleCheckIn}
          >
            Check-In Yap
          </Button>
        </div>
      )}
      {(selectedRoom?.reservation?.status === "checkedIn" ||
        selectedRoom?.reservation?.status === "waitingForCheckOut") && (
        <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white"
            onClick={handleCheckOut}
          >
            Check-Out Yap
          </Button>
        </div>
      )}
      {selectedRoom?.reservation?.status === "booked" && (
        <Link href="/hotel/account/reservations">
          <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
            <Button>Rezervasyonu Onayla</Button>
          </div>
        </Link>
      )}
      <div>
        {selectedRoom?.allocation === null ? (
          <>
            <div className="flex items-center justify-center">
              Bu oda rezervasyona kapalıdır.
            </div>
            <Link href="/hotel/account/calendar">
              <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
                  Rezervasyona Aç
                </Button>
              </div>
            </Link>
          </>
        ) : selectedRoom?.reservation === null ? (
          <>
            <div className="flex items-center justify-center">
              Bu oda rezerve edilebilir.
            </div>
            <Link href="/hotel/account/at-door-reservation">
              <div className="mx-auto mt-3 flex max-w-56 items-center justify-center">
                <Button className="bg-secondary-6000 hover:bg-secondary-700 text-white">
                  Rezervasyon Oluştur
                </Button>
              </div>
            </Link>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default RoomInformation;
