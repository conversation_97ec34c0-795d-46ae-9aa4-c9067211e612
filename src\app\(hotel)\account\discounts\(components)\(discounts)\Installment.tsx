"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import { MultiSelect } from "@/components/ui/multi-select";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelInstallment } from "@/hooks/hotel/discounts/useHotelInstallment";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Switch } from "@/components/ui/switch";
import Checkbox from "@/shared/Checkbox";

interface InstallmentProps {
  hotelToken: string | undefined;
  closeModal: () => void;
}

const Installment: FC<InstallmentProps> = ({ hotelToken, closeModal }) => {
  const { defineInstallment } = useHotelInstallment();
  const [installment, setInstallment] = useState<any>({
    isActive: true,
    cardNames: [],
    maxInstallmentCount: 0,
    startDate: "",
    endDate: "",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setInstallment((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleTodayDateCheckbox = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;

    if (checked) {
      const today = new Date().toISOString().split("T")[0];
      setInstallment((prev: any) => ({
        ...prev,
        startDate: today,
      }));
    } else {
      setInstallment((prev: any) => ({
        ...prev,
        startDate: "",
      }));
    }
  };

  const handleSelectChange = (name: string, value: string | string[]) => {
    setInstallment((prev: any) => ({
      ...prev,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const isValidInstallment = (data: any) => {
    if (!data) return false;

    const { cardNames, maxInstallmentCount, startDate, endDate } = data;

    if (
      !Array.isArray(cardNames) ||
      cardNames.length === 0 ||
      !maxInstallmentCount ||
      maxInstallmentCount <= 0 ||
      !startDate ||
      !endDate
    ) {
      return false;
    }

    return true;
  };

  const buttonDisabled = isValidInstallment(installment);

  const cardBrands = [
    { value: "Axess", label: "Axess" },
    { value: "World", label: "World" },
    { value: "CardFinans", label: "CardFinans" },
    { value: "Bonus", label: "Bonus" },
    { value: "Maximum", label: "Maximum" },
    { value: "SaglamKart", label: "SaglamKart" },
    { value: "Combo", label: "Combo" },
    { value: "Shop&Fly", label: "Shop&Fly" },
    { value: "Paraf", label: "Paraf" },
    { value: "Param", label: "Param" },
  ];

  return (
    <form
      onSubmit={(event) =>
        defineInstallment(
          event,
          hotelToken,
          installment,
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <h2 className="font-medium text-lg mb-3">Taksit</h2>
      <div className="space-y-4">
        <FormItem label="Kart Tipi">
          <MultiSelect
            options={cardBrands}
            onValueChange={(value) => handleSelectChange("cardNames", value)}
            maxCount={10}
            placeholder="Kart Tipi Seçin"
            className="rounded-2xl"
          />
        </FormItem>
        <FormItem label="Maks. Taksit Sayısı" className="w-full">
          <Input
            min={0}
            name="maxInstallmentCount"
            type="number"
            onChange={handleChange}
          />
        </FormItem>
        <FormItem label="Başlangıç Tarihi" className="w-full">
          <Input
            name="startDate"
            type="date"
            onChange={handleChange}
            value={installment.startDate}
          />
          <Checkbox
            id="todayDate"
            label="Bugünün Tarihini Seç"
            name="todayDate"
            checked={
              installment.startDate === new Date().toISOString().split("T")[0]
            }
            onChange={handleTodayDateCheckbox}
            className="mt-2 !text-sm"
            inputClass="!size-5 !rounded-lg !-mr-2"
          />
        </FormItem>
        <FormItem label="Bitiş Tarihi" className="w-full">
          <Input name="endDate" type="date" onChange={handleChange} />
        </FormItem>
        <FormItem label="Aktiflik Durumu">
          <div className="flex gap-2">
            <Switch
              name="isActive"
              checked={installment.isActive}
              onCheckedChange={(checked) =>
                setInstallment((prev: any) => ({
                  ...prev,
                  isActive: checked,
                }))
              }
              className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
            />
            <p
              className={
                installment.isActive ? "text-green-500" : "text-red-500"
              }
            >
              {installment.isActive ? "Aktif" : "Pasif"}
            </p>
          </div>
        </FormItem>
      </div>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default Installment;
