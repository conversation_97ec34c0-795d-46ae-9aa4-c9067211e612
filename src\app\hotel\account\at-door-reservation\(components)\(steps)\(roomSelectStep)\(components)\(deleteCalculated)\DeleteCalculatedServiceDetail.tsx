"use client";
import React, { useState } from "react";
import type { FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X, Trash2 } from "lucide-react";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useHotelAtDoorReservation } from "@/hooks/hotel/useHotelAtDoorReservation";
import LoadingSpinner from "@/shared/icons/Spinner";

interface DeleteCalculatedServiceProps {
  hotelToken: string | undefined;
  removedItemId: string;
  serviceId: string;
}

const DeleteCalculatedServiceDetail: FC<DeleteCalculatedServiceProps> = ({
  hotelToken,
  removedItemId,
  serviceId,
}) => {
  const { removeItemFromCart } = useHotelAtDoorReservation();
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const [deleteServiceIsOpen, setDeleteServiceIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <>
      <Dialog open={deleteServiceIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            setDeleteServiceIsOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>Hizmet Kaldırma</DialogTitle>
            <DialogDescription className="text-base">
              Seçili hizmeti kaldırmak istiyor musun?
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-5">
            <ButtonSecondary
              type="button"
              className="max-sm:basis-1/2"
              onClick={() => setDeleteServiceIsOpen(false)}
            >
              Vazgeç
            </ButtonSecondary>
            <ButtonPrimary
              disabled={loading}
              className="max-sm:basis-1/2"
              onClick={() => {
                removeItemFromCart(
                  hotelToken,
                  removedItemId,
                  calculatedRoomData?.selectedItems,
                  calculatedRoomData?.totalOrderPrice,
                  "service",
                  serviceId,
                  setLoading,
                  setDeleteServiceIsOpen
                );
              }}
            >
              {loading ? <LoadingSpinner /> : "Onayla"}
            </ButtonPrimary>
          </div>
          <DialogClose
            onClick={() => setDeleteServiceIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
      <Trash2
        onClick={() => setDeleteServiceIsOpen(true)}
        className="size-6 cursor-pointer text-xl text-neutral-500 duration-200 hover:text-primary-6000"
      />
    </>
  );
};

export default DeleteCalculatedServiceDetail;
