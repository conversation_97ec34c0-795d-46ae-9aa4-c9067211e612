import React from "react";
import { cookies, headers } from "next/headers";
import { redirect } from "next/navigation";

export default function HomePageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value ?? undefined;
  const headerStore = headers();
  const searchParams = Object.fromEntries(
    new URLSearchParams(headerStore.get("searchParams") ?? "")
  );

  if (token?.includes("HT_")) {
    redirect("/account");
  } else if (searchParams?.mobile === "true") {
    redirect("/login");
  }

  return <>{children}</>;
}
