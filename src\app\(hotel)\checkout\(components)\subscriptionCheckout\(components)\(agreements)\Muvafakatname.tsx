import React from "react";
import type { FC } from "react";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";

interface MuvafakatnameProps {
  checkoutData: any;
}

const Muvafakatname: FC<MuvafakatnameProps> = ({ checkoutData }) => {
  const formattedDate = formatDateToDayMonthYear(checkoutData?.issueDate);

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h3 className="font-medium">
          İş yerinin yetkilisi/sahibi:{" "}
          <span className="capitalize font-normal">
            {checkoutData?.hotel?.legalName}
          </span>
        </h3>
        <h3 className="font-medium">
          İş yerinin adresi:{" "}
          <span className="font-normal">
            {checkoutData?.hotel?.address?.district}{" "}
            {checkoutData?.hotel?.address?.streetName}{" "}
            {checkoutData?.hotel?.address?.buildingName}{" "}
            {checkoutData?.hotel?.address?.buildingNumber}{" "}
            {checkoutData?.hotel?.address?.region}{" "}
            {checkoutData?.hotel?.address?.cityName}{" "}
          </span>
        </h3>
        <h3 className="font-medium">
          İş yerinin faaliyet konusu:{" "}
          <span className="font-normal">Evcil hayvan oteli</span>
        </h3>
        <p>
          Yukarıda faaliyet durumu belirtilmiş, İl Tarım Müdürlüğüne kayıtlı iş
          yerine evcil hayvanımı konaklaması için bırakmış bulunmaktayım.
        </p>
      </div>
      <div>
        <h3 className="mb-3 font-semibold">
          Tarım Bakanlığına bağlı ruhsatlı bir çiftlik olarak onaylamanız
          gereken durumlar aşağıda belirtilmiştir:
        </h3>
        <ul className="list-inside space-y-3 pl-2.5 md:pl-5">
          <li className="text-sm md:text-base">
            1- Evcil hayvanım diğer evcil hayvanlardan bağımsız, yaşam
            standartlarına uygun olacak şekilde tek konaklayacaktır.
          </li>
          <li className="text-sm md:text-base">
            2- Tuvalet ve gezme ihtiyacı için açık gezi alanında yine tek
            olacaktır.
          </li>
          <li className="text-sm md:text-base">
            3- Evcil hayvanımın maması ve mama kabı dışında herhangi bir
            eşyasını teslim etmedim.
          </li>
          <li className="text-sm md:text-base">
            4- Her ne sebeple olursa olsun, almayı taahhüt ettiğim tarihin
            üzerinden 1 hafta (7 gün) geçmesi durumunda iletişim kurmadığım
            takdirde evcil hayvanımın İl Tarım Müdürlüğüne bildirilip cezai
            işlem başlatılacağı ve akabinde barınağa teslim edileceği bilgisi
            tarafıma verilmiştir.
          </li>
          <li className="text-sm md:text-base">
            5- Evcil hayvanımın herhangi bir hastalıkla karşılaşabileceği
            durumunda, evcil hayvanımın hastalanabileceğini bu durumun hayatın
            olağan akışına uygun olduğunu kabul ederim. Böyle bir durumda
            hizmete devam eden{" "}
            <span className="capitalize">
              {checkoutData?.hotel?.hotelName}{" "}
            </span>
            Oteli’nin veterinerinin gözleminde olacağını kabul ederim.
          </li>
          <li className="text-sm md:text-base">
            6- Hayvandan hayvana bulaşan ortak hastalıklar olabileceğini,
            kalıtsal bir hastalığı var ise; herhangi bir hastalıkla
            karşılaşılması durumunda evcil hayvanımın hastalanması ve hayatını
            kaybetmesi durumunda{" "}
            <span className="capitalize">
              {checkoutData?.hotel?.hotelName}{" "}
            </span>{" "}
            Oteli’nin sorumlu olmayacağını kabul, beyan ve taahhüt ederim.
          </li>
          <li className="text-sm md:text-base">
            7- Evcil hayvanımın aşıları tam değilse; parazit kürleri olmayan ne
            tür bir virüs veya parazit taşıdığı belli olmayan bir durumu{" "}
            <span className="capitalize">
              {checkoutData?.hotel?.hotelName}{" "}
            </span>
            Oteli’nin bilemeyeceğini; bu durumdan kaynaklanan bir hastalıkla
            karşılaşıldığında tarafıma bilgilendirme yapılarak veterinere
            götürülebileceğini teyit ederim.
          </li>
          <li className="text-sm md:text-base">
            8- Yukarıdaki tüm maddeleri okudum, anladım, kabul ediyorum.
          </li>
        </ul>
      </div>
      <div className="!my-10 space-y-5">
        <p>Tarih: {formattedDate}</p>
        <p>Adı Soyadı: {checkoutData?.petOwner?.fullName}</p>
        <p>İmza:</p>
      </div>
    </div>
  );
};

export default Muvafakatname;
