"use client";
import React, { useEffect } from "react";
import type { FC } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface RoomGroupsProps {
  roomGroupData: any;
  IsOpen: any;
  openItems: any;
  setOpenItems: any;
  selectedFilter: string;
  onStatusCountsChange: (counts: {
    waitingForApproval: number;
    waitingForCheckIn: number;
    waitingForCheckOut: number;
    checkedOut: number;
    checkedIn: number;
    empty: number;
  }) => void;
}

const RoomGroups: FC<RoomGroupsProps> = ({
  roomGroupData,
  IsOpen,
  openItems,
  setOpenItems,
  selectedFilter,
  onStatusCountsChange,
}) => {
  const handleAccordionChange = (value: string) => {
    if (openItems.includes(value)) {
      setOpenItems(openItems.filter((item: any) => item !== value));
    } else {
      setOpenItems([...openItems, value]);
    }
  };

  const roomGroupColorHandler = (
    allocation: string | null,
    reservation: { status: string } | null
  ) => {
    if (allocation === null) return "bg-[#D2B48C]";
    if (reservation === null) return "bg-[#D3D3D3]";

    switch (reservation.status) {
      case "waitingForApproval":
      case "booked":
        return "bg-[#FFD59E]";
      case "confirmed":
      case "waitingForCheckIn":
        return "bg-[#B3D9FF]";
      case "checkedIn":
        return "bg-[#C1E1C1]";
      case "waitingForCheckOut":
        return "bg-[#FFB3B3]";
      case "checkedOut":
        return "bg-[#FFDB58]";
      default:
        return "bg-[#ebf782]";
    }
  };

  const filteredRoomGroups =
    !selectedFilter || selectedFilter === "all"
      ? roomGroupData
      : roomGroupData
          .map((group: any) => {
            const filteredRooms = group.rooms.filter((room: any) => {
              if (selectedFilter === "empty") {
                return room.reservation === null;
              }

              const status = room?.reservation?.status;

              if (selectedFilter === "waitingForCheckIn") {
                return status === "waitingForCheckIn" || status === "confirmed";
              }

              if (selectedFilter === "waitingForApproval") {
                return status === "waitingForApproval" || status === "booked";
              }

              return status === selectedFilter;
            });

            return { ...group, rooms: filteredRooms };
          })
          .filter((group: any) => group.rooms.length > 0);

  useEffect(() => {
    const statusCounts = roomGroupData.reduce(
      (acc: any, group: any) => {
        group.rooms.forEach((room: any) => {
          const reservation = room.reservation;
          const status = reservation?.status;

          if (!reservation) {
            acc.empty += 1;
          } else {
            if (status === "waitingForApproval" || status === "booked") {
              acc.waitingForApproval += 1;
            }
            if (status === "waitingForCheckIn" || status === "confirmed") {
              acc.waitingForCheckIn += 1;
            }
            if (status === "waitingForCheckOut") {
              acc.waitingForCheckOut += 1;
            }
            if (status === "checkedOut") {
              acc.checkedOut += 1;
            }
            if (status === "checkedIn") {
              acc.checkedIn += 1;
            }
          }
        });
        return acc;
      },
      {
        waitingForApproval: 0,
        waitingForCheckIn: 0,
        waitingForCheckOut: 0,
        checkedOut: 0,
        checkedIn: 0,
        empty: 0,
      }
    );

    onStatusCountsChange(statusCounts);
  }, [roomGroupData]);

  useEffect(() => {
    const firstGroup = filteredRoomGroups?.[0]?.roomGroupName;
    if (firstGroup) {
      setOpenItems([firstGroup]);
    } else {
      setOpenItems([]);
    }
  }, [selectedFilter, roomGroupData]);

  return (
    <Accordion type="multiple" value={openItems} className="w-full mt-5">
      <div>
        {filteredRoomGroups.length === 0 ? (
          <div className="text-center text-gray-500 py-10">
            Seçilen filtrede oda bulunamadı.
          </div>
        ) : (
          filteredRoomGroups.map((data: any) => (
            <div key={data.roomGroupId}>
              <AccordionItem value={data.roomGroupName}>
                <AccordionTrigger
                  onClick={() => handleAccordionChange(data.roomGroupName)}
                  className="capitalize"
                >
                  {data.roomGroupName}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="mx-auto grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                    {data.rooms.map((room: any, index: number) => {
                      const colorValue = roomGroupColorHandler(
                        room.allocation,
                        room.reservation
                      );
                      return (
                        <div
                          className={`relative flex h-20 cursor-pointer flex-col justify-between rounded-lg p-3 capitalize text-white brightness-100 drop-shadow-md duration-300 hover:brightness-90 ${colorValue}`}
                          key={index}
                          onClick={() => IsOpen(room)}
                        >
                          <div>{room.roomName}</div>
                          <div>{room?.reservation?.pet?.name}</div>
                        </div>
                      );
                    })}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </div>
          ))
        )}
      </div>
    </Accordion>
  );
};

export default RoomGroups;
