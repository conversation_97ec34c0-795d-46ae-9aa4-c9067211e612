"use client";
import React, { useState } from "react";
import type { FC } from "react";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useVehicle } from "@/hooks/taxi/vehicles/useVehicle";

interface DeleteVehicleProps {
  petTaxiToken: string | undefined;
  vehicleId: string;
}

const DeleteVehicle: FC<DeleteVehicleProps> = ({ petTaxiToken, vehicleId }) => {
  const { deleteVehicle } = useVehicle();
  const [deleteVehicleIsOpen, setDeleteVehicleIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const closeModal = () => {
    setDeleteVehicleIsOpen(false);
    setLoading(false);
    setTimeout(() => {
      setDisabled(false);
    }, 2000);
  };

  return (
    <div>
      <Button
        size="sm"
        variant="ghost"
        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
        onClick={() => setDeleteVehicleIsOpen(true)}
      >
        <Trash2 className="w-4 h-4" />
      </Button>
      <Dialog open={deleteVehicleIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Araç Kaldırma</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteVehicle(
                event,
                petTaxiToken,
                vehicleId,
                setLoading,
                setDisabled,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Seçili araç kaldırılsın mı?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                disabled={disabled}
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DeleteVehicle;
