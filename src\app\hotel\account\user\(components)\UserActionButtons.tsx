import type { FC } from "react";
import React from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import UpdateHotelUser from "./UpdateHotelUser";
import DeleteHotelUser from "./DeleteHotelUser";
import { useTranslations } from "next-intl";

interface UserActionButtonsProps {
  hotelToken: string | undefined;
  rowOriginal: any;
  index: number;
}

const UserActionButtons: FC<UserActionButtonsProps> = ({
  hotelToken,
  rowOriginal,
  index,
}) => {
  const translate = useTranslations("UserActionButtons");
  return (
    <div className="flex items-center justify-center gap-2 text-neutral-500 dark:text-neutral-400">
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger>
            <UpdateHotelUser
              hotelToken={hotelToken}
              rowOriginal={rowOriginal}
            />
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">{translate("editUser")}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {index !== 0 && (
        <TooltipProvider>
          <Tooltip delayDuration={300}>
            <TooltipTrigger>
              <DeleteHotelUser
                hotelToken={hotelToken}
                rowOriginal={rowOriginal}
              />
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs font-medium">{translate("deleteUser")}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};

export default UserActionButtons;
