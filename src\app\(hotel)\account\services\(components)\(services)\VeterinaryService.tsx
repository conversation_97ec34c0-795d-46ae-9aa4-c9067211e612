"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelService } from "@/hooks/hotel/services/useHotelService";
import LoadingSpinner from "@/shared/icons/Spinner";
import { isServiceValid, timeData } from "@/utils/services/hotelServices";
import type { veterinaryServiceTypes } from "@/types/hotel/services/serviceTypes";
import { Switch } from "@/components/ui/switch";
import { MultiSelect } from "@/components/ui/multi-select";
import { petTypes } from "@/types/petOwner/petTypes";

interface VeterinaryServiceProps {
  hotelToken: string | undefined;
  closeModal: () => void;
}

const VeterinaryService: FC<VeterinaryServiceProps> = ({
  hotelToken,
  closeModal,
}) => {
  const { addService } = useHotelService();
  const [veterinaryService, setVeterinaryService] =
    useState<veterinaryServiceTypes>({
      isActive: true,
      serviceName: "",
      total: "",
      description: "",
      serviceDetails: {
        veterinarianName: "",
        requiredDocuments: "",
        availabilityStart: "",
        availabilityEnd: "",
      },
      petType: [],
    });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    if (
      [
        "veterinarianName",
        "requiredDocuments",
        "availabilityStart",
        "availabilityEnd",
      ].includes(name)
    ) {
      setVeterinaryService((prev) => ({
        ...prev,
        serviceDetails: {
          ...prev.serviceDetails,
          [name]: value,
        },
      }));
    } else {
      setVeterinaryService((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const buttonDisabled = isServiceValid(veterinaryService);

  const handleSelectChange = (name: string, value: string | string[]) => {
    setVeterinaryService((prev) => ({
      ...prev,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const petTypeList = petTypes.map((petType) => ({
    value: petType.value,
    label: petType.label,
  }));

  return (
    <form
      onSubmit={(event) =>
        addService(
          event,
          hotelToken,
          veterinaryService,
          "veterinaryServices",
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <h2 className="font-medium text-lg mb-3">Veteriner Hizmeti</h2>
      <div className="space-y-2">
        <FormItem label="Veteriner Hizmet Adı">
          <Input name="serviceName" onChange={handleChange} />
        </FormItem>
        <FormItem label="Veteriner Adı">
          <Input name="veterinarianName" onChange={handleChange} />
        </FormItem>
        <FormItem label="Gerekli Dosyalar">
          <Input name="requiredDocuments" onChange={handleChange} />
        </FormItem>
        <FormItem label="Fiyat">
          <Input name="total" type="number" onChange={handleChange} />
        </FormItem>
      </div>
      <p className="font-medium text-neutral-700 dark:text-neutral-300 mt-5 mb-2">
        Saat Aralığı
      </p>
      <div className="mb-5">
        <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
          Başlangıç
        </p>
        <Select
          onValueChange={(selected) =>
            setVeterinaryService({
              ...veterinaryService,
              serviceDetails: {
                ...veterinaryService.serviceDetails,
                availabilityStart: selected,
              },
            })
          }
        >
          <SelectTrigger className="max-w-72 rounded-2xl">
            <SelectValue placeholder="Saat Seç" />
          </SelectTrigger>
          <SelectContent>
            {timeData.map((time, index) => {
              return (
                <SelectItem key={index} value={time}>
                  {time}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <div>
        <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
          Bitiş
        </p>
        <Select
          onValueChange={(selected) =>
            setVeterinaryService({
              ...veterinaryService,
              serviceDetails: {
                ...veterinaryService.serviceDetails,
                availabilityEnd: selected,
              },
            })
          }
        >
          <SelectTrigger className="max-w-72 rounded-2xl">
            <SelectValue placeholder="Saat Seç" />
          </SelectTrigger>
          <SelectContent>
            {timeData.map((time, index) => {
              return (
                <SelectItem key={index} value={time}>
                  {time}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <FormItem label="Pet Türü" className="mt-3 w-72">
        <MultiSelect
          options={petTypeList}
          onValueChange={(value) => handleSelectChange("petType", value)}
          maxCount={15}
          placeholder="Pet Türü Seçin"
          className="rounded-2xl"
        />
      </FormItem>
      <FormItem label="Açıklama" className="mt-3">
        <Input name="description" onChange={handleChange} />
      </FormItem>
      <FormItem label="Aktiflik Durumu" className="mt-3">
        <div className="flex gap-2">
          <Switch
            name="isActive"
            checked={veterinaryService.isActive}
            onCheckedChange={(checked) =>
              setVeterinaryService((prev) => ({ ...prev, isActive: checked }))
            }
            className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
          />
          <p
            className={
              veterinaryService.isActive ? "text-green-500" : "text-red-500"
            }
          >
            {veterinaryService.isActive ? "Aktif" : "Pasif"}
          </p>
        </div>
      </FormItem>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default VeterinaryService;
