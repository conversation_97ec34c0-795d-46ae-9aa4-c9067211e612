import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { uploadMultiToS3, ImageResizeFitType } from "@/lib/s3BucketHelper";
import type { updateRoomInputsTypes } from "@/types/hotel/rooms/updateRoomTypes";
import type { RoomImage } from "@/types/hotel/roomGroupType";
import slug from "slug";

export const useUpdateRoom = () => {
  const { toast } = useToast();

  // UPDATES ROOMS
  const updateHandleRoom = async (
    roomImageArray: any,
    hotelToken: string | undefined,
    updateRoomInputs: updateRoomInputsTypes,
    roomId: string,
    setUpdateRoomsIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    isAppend: boolean,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<
      React.SetStateAction<FileList | undefined>
    >,
    setNewImage?: any
  ) => {
    if (hotelToken) {
      const requestBody = {
        roomGroupName: updateRoomInputs.roomGroupName,
        description: updateRoomInputs.roomDescription,
        features: updateRoomInputs.roomFeatures,
        petType: updateRoomInputs.petType,
        roomCapacity: Number(updateRoomInputs.roomCapacity),
        images: roomImageArray,
        appendImages: isAppend,
      };

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/rooms/updateRoomsByRoomGroupId/${roomId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          }
        );
        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setUpdateRoomsIsOpen(false);
          setLoading(false);
          setDisabled(false);
          setPhotoFileObject(undefined);
          if (setNewImage) {
            setNewImage([]);
          }
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3000,
          title: "Oda Grubu Güncelleme",
          description: "Oda grubu güncellendi.",
        });
        setLoading(false);
        setUpdateRoomsIsOpen(false);
        setPhotoFileObject(undefined);
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
        if (setNewImage) {
          setNewImage([]);
        }
        revalidatePathHandler("/hotel/account/rooms");
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // UPDATES ROOM PHOTOS
  const updatePhotoInputHandler = async (
    hotelId: string,
    hotelToken: string | undefined,
    photoFileObject: FileList | undefined,
    updateRoomInputs: updateRoomInputsTypes,
    roomId: string,
    setUpdateRoomsIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setNewImage: any,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    setPhotoFileObject: React.Dispatch<
      React.SetStateAction<FileList | undefined>
    >
  ) => {
    if (photoFileObject && hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await uploadMultiToS3(
          photoFileObject,
          `hotel/${hotelId}/roomPhotos/${slug(updateRoomInputs.roomGroupName)}/`,
          hotelToken,
          ImageResizeFitType.fill
        );

        if (!response.success) {
          const errorMessage = "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setUpdateRoomsIsOpen(false);
          setLoading(false);
          setDisabled(false);
          setPhotoFileObject(undefined);
          throw new Error("Network response was not ok");
        }

        const idsArray = response.data.map((item: any) => item._id);
        updateHandleRoom(
          idsArray,
          hotelToken,
          updateRoomInputs,
          roomId,
          setUpdateRoomsIsOpen,
          setLoading,
          true,
          setDisabled,
          setPhotoFileObject,
          setNewImage
        );
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
    if (
      hotelToken &&
      (photoFileObject === undefined || photoFileObject?.length === 0)
    ) {
      setLoading(true);
      setDisabled(true);
      updateHandleRoom(
        updateRoomInputs.roomImages.map((image) => image._id),
        hotelToken,
        updateRoomInputs,
        roomId,
        setUpdateRoomsIsOpen,
        setLoading,
        false,
        setDisabled,
        setPhotoFileObject
      );
    }
  };

  return { updatePhotoInputHandler };
};
