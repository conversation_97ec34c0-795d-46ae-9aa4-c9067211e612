import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";
import {
  veterinaryServiceTypes,
  transportationServiceTypes,
  groomingServiceTypes,
} from "@/types/hotel/services/serviceTypes";

export const useHotelService = () => {
  const { toast } = useToast();

  const addService = async (
    event: FormEvent,
    hotelToken: string | undefined,
    serviceData:
      | veterinaryServiceTypes
      | transportationServiceTypes
      | groomingServiceTypes,
    serviceType: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.addAdditionalService, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            serviceType: serviceType,
            serviceData: {
              ...serviceData,
            },
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Hizmet Ekleme",
          description: "Hizmet başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/services");
        setLoading(false);
        closeModal();
        setDisabled(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const updateService = async (
    event: FormEvent,
    hotelToken: string | undefined,
    serviceData:
      | veterinaryServiceTypes
      | transportationServiceTypes
      | groomingServiceTypes,
    serviceId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.updateAdditionalService, {
          method: "PUT",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            serviceId: serviceId,
            serviceData: {
              ...serviceData,
            },
          }),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setTimeout(() => {
            setDisabled(false);
          }, 1000);

          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Hizmet Güncelleme",
          description: "Hizmet başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/services");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const deleteService = async (
    event: FormEvent,
    hotelToken: string | undefined,
    serviceId: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.deleteAdditionalService}/${serviceId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Hizmet Kaldırma",
          description: "Hizmet başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/services");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };
  return { addService, updateService, deleteService };
};
