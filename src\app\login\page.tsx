import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import LoginContainer from "../auth/(components)/LoginContainer";
import { useTranslations } from "next-intl";
import TypingAnimation from "@/components/ui/typing-animation";
import PawBooking<PERSON>ogo from "@/shared/PawBookingLogo";
import Link from "next/link";
import SafariComponent from "@/components/SafariComponent";
import Iphone15ProComponent from "@/components/IphoneComponent";
import iphoneImage from "@/images/pawbooking-iphone-calendar-image.png";
import iphoneImage2 from "@/images/pawbooking-iphone-room-image.png";
import DotPatternComponent from "@/components/DotPatternComponent";
import Image from "next/image";
import type { Metadata } from "next";
import { cookies } from "next/headers";

export const metadata: Metadata = {
  title: "PawBooking Otel Giriş | PawBooking Pet Booking System",
};

const HotelLoginPage = () => {
  const translate = useTranslations("LoginPage");
  const cookieStore = cookies();
  const mobileParams = cookieStore.get("mobileParams")?.value || undefined;

  return (
    <div className="relative grid min-h-screen grid-cols-1 overflow-hidden md:grid-cols-2">
      <div className="relative hidden flex-col items-center justify-between bg-gray-100 md:flex">
        <Image
          className="object-cover opacity-15"
          src={
            "https://images.unsplash.com/photo-1498015583783-4abcab4a760a?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          }
          fill
          alt=""
        />
        <div className="mt-5 md:max-w-[350px] lg:max-w-[500px] xl:max-w-[650px] 2xl:max-w-[750px]">
          <SafariComponent />
        </div>
        <div className="absolute -bottom-32 flex max-w-[350px] gap-5 overflow-hidden xl:max-w-[450px] 2xl:max-w-[550px]">
          <Iphone15ProComponent image={iphoneImage2.src} />
          <Iphone15ProComponent image={iphoneImage.src} />
        </div>
      </div>
      <div className="relative flex flex-col items-center justify-center space-y-5 overflow-hidden">
        <div className="absolute top-5 z-50">
          <Link href="/">
            <PawBookingLogo className="size-20 self-center md:size-16" />
          </Link>
        </div>
        <DotPatternComponent />
        <div className="container relative z-50 flex items-center justify-center md:flex-col">
          <TypingAnimation
            className="mb-5 text-3xl font-semibold tracking-normal max-md:hidden"
            text={translate("loginWelcome")}
            duration={80}
          />
          <Card className="h-[425px] w-[350px] md:w-[400px]">
            <CardHeader>
              <CardTitle>{translate("hotelLogin")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <LoginContainer
                role="hotel_owner"
                apiPath="/partner/auth/login"
                queryParams={mobileParams}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default HotelLoginPage;
