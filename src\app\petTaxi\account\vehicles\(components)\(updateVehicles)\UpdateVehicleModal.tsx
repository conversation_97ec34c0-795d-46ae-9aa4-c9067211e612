"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useVehicle } from "@/hooks/taxi/vehicles/useVehicle";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Switch } from "@/components/ui/switch";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
import type {
  VehicleDataApiTypes,
  VehicleDataTypes,
} from "@/types/taxi/vehicleType";
import { Edit3 } from "lucide-react";
import { MultiSelect } from "@/components/ui/multi-select";
import { PET_TYPES } from "@/app/(enums)/enums";
import UpdateVehiclePhotos from "./UpdateVehiclePhotos";
import MultipleVehicleFiles from "../(createVehicles)/MultipleVehicleFiles";

interface UpdateVehicleModalProps {
  taxiData: PetTaxiDataApiTypes;
  vehicleData: VehicleDataApiTypes;
  petTaxiToken: string | undefined;
}

const UpdateVehicleModal: FC<UpdateVehicleModalProps> = ({
  taxiData,
  vehicleData,
  petTaxiToken,
}) => {
  const { updatePhotoInputHandler } = useVehicle();
  const initialData = {
    passive: vehicleData.passive,
    vehicleName: vehicleData.vehicleName,
    vehiclePlate: vehicleData.vehiclePlate,
    vehicleCapacity: vehicleData.vehicleCapacity,
    vehicleType: vehicleData.vehicleType,
    acceptedPetTypes: vehicleData.acceptedPetTypes,
    cleaningCertificate: vehicleData.cleaningCertificate,
    vehicleFeatures: vehicleData.vehicleFeatures,
    images: vehicleData.images,
    documents: vehicleData.documents,
  };
  const [vehicle, setVehicle] = useState<VehicleDataTypes>(initialData);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [open, setOpen] = useState(false);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | undefined>(
    undefined
  );
  const [modalImage, setModalImage] = useState<string[] | []>([]);
  const newVehicleInputRef = React.useRef<HTMLInputElement>(null);
  const [vehicleFiles, setVehicleFiles] = useState({
    vehicle_license: [] as File[],
    insurance: [] as File[],
    pet_transport_certificate: [] as File[],
  });

  const closeModal = () => {
    setVehicle(initialData);
    setLoading(false);
    setDisabled(false);
    setOpen(false);
    setPhotoFileObject(undefined);
    setModalImage([]);
    setVehicleFiles({
      vehicle_license: [] as File[],
      insurance: [] as File[],
      pet_transport_certificate: [] as File[],
    });
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setVehicle((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const isValid = (
    data: any,
    initialData: any,
    vehicleFiles: any,
    photoFileObject: FileList | undefined
  ) => {
    if (!data) return false;

    const {
      vehicleName,
      vehiclePlate,
      vehicleCapacity,
      vehicleType,
      acceptedPetTypes,
      cleaningCertificate,
      vehicleFeatures,
      documents = [],
      images = [],
    } = data;

    const hasVehicleLicense =
      documents.some((doc: any) => doc.doctype === "vehicle_license") ||
      vehicleFiles.vehicle_license.length > 0;

    const hasInsurance =
      documents.some((doc: any) => doc.doctype === "insurance") ||
      vehicleFiles.insurance.length > 0;

    const hasTransportCert =
      documents.some(
        (doc: any) => doc.doctype === "pet_transport_certificate"
      ) || vehicleFiles.pet_transport_certificate.length > 0;

    const requiredFieldsFilled =
      vehicleName.trim() !== "" &&
      vehiclePlate.trim() !== "" &&
      vehicleCapacity > 0 &&
      vehicleType.trim() !== "" &&
      acceptedPetTypes.length > 0 &&
      cleaningCertificate !== null &&
      vehicleFeatures.length > 0 &&
      hasVehicleLicense &&
      hasInsurance &&
      hasTransportCert;

    // değişiklik kontrolü
    const baseChanged =
      JSON.stringify({
        ...initialData,
        documents: initialData.documents?.map((d: any) => d._id),
        images: initialData.images?.map((img: any) => img._id),
      }) !==
      JSON.stringify({
        ...data,
        documents: data.documents?.map((d: any) => d._id),
        images: data.images?.map((img: any) => img._id),
      });

    // yeni dosya/resim var mı?
    const hasNewFiles =
      vehicleFiles.vehicle_license.length > 0 ||
      vehicleFiles.insurance.length > 0 ||
      vehicleFiles.pet_transport_certificate.length > 0;

    const hasNewPhotos = photoFileObject && photoFileObject.length > 0;

    const isChanged = baseChanged || hasNewFiles || hasNewPhotos;

    return requiredFieldsFilled && isChanged;
  };

  const buttonDisabled = isValid(
    vehicle,
    initialData,
    vehicleFiles,
    photoFileObject
  );

  const petTypeOptions = Object.entries(PET_TYPES).map(([value, label]) => ({
    value,
    label,
  }));

  const vehicleFeatureOptions = [
    { value: "air_conditioning", label: "Klima" },
    { value: "camera", label: "Kamera" },
    { value: "partitioned_compartment", label: "Kafesli Bölme" },
    { value: "gps_tracking", label: "GPS Takip" },
    { value: "ventilation", label: "Havalandırma" },
    { value: "non_slip_floor", label: "Kaymaz Zemin" },
  ];

  const convertFilesToFileList = (files: File[]): FileList => {
    const dataTransfer = new DataTransfer();
    files.forEach((file) => dataTransfer.items.add(file));
    return dataTransfer.files;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button
        size="sm"
        variant="ghost"
        className="h-8 w-8 p-0"
        onClick={() => setOpen(true)}
      >
        <Edit3 className="w-4 h-4" />
      </Button>
      <DialogContent
        onInteractOutside={closeModal}
        className="max-w-lg max-h-[90vh] overflow-y-auto"
      >
        <DialogHeader>
          <DialogTitle>Araç Düzenle</DialogTitle>
          <DialogDescription>
            Pet taşımacılığı için araç bilgilerinizi düzenleyin.
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(event) =>
            updatePhotoInputHandler(
              event,
              taxiData._id,
              petTaxiToken,
              vehicle,
              vehicleData._id,
              photoFileObject,
              {
                vehicle_license: convertFilesToFileList(
                  vehicleFiles.vehicle_license
                ),
                insurance: convertFilesToFileList(vehicleFiles.insurance),
                pet_transport_certificate: convertFilesToFileList(
                  vehicleFiles.pet_transport_certificate
                ),
              },
              setLoading,
              closeModal,
              setDisabled
            )
          }
        >
          <div className="space-y-4">
            <FormItem label="Araç Adı*" className="w-full !text-black dark:!text-white">
              <Input
                value={vehicle.vehicleName}
                className="rounded-lg"
                placeholder="Araç adını girin"
                name="vehicleName"
                onChange={handleChange}
              />
            </FormItem>
            <FormItem label="Araç Plakası*" className="w-full !text-black dark:!text-white">
              <Input
                value={vehicle.vehiclePlate}
                className="rounded-lg"
                placeholder="34 ABC 123"
                name="vehiclePlate"
                onChange={handleChange}
              />
            </FormItem>
            <FormItem label="Araç Kapasitesi*" className="w-full !text-black dark:!text-white">
              <Select
                value={
                  vehicle.vehicleCapacity === 0
                    ? ""
                    : vehicle.vehicleCapacity.toString()
                }
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleCapacity: +selected,
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Araç kapasitesini seçin" />
                </SelectTrigger>
                <SelectContent>
                  {[...Array(10)].map((_, i) => {
                    const value = (i + 1).toString();
                    return (
                      <SelectItem key={value} value={value}>
                        {value}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem label="Araç Tipi*" className="w-full !text-black dark:!text-white">
              <Select
                value={vehicle.vehicleType}
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleType: selected,
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Araç tipini seçin" />
                </SelectTrigger>
                <SelectContent className="!text-black">
                  <SelectItem value="panel_van">Panelvan</SelectItem>
                  <SelectItem value="minivan">Minivan</SelectItem>
                  <SelectItem value="small_commercial_van">
                    Küçük Ticari Araç (Küçük Panelvan)
                  </SelectItem>
                  <SelectItem value="passenger_car">
                    Binek Otomobil (Sedan / Hatchback)
                  </SelectItem>
                  <SelectItem value="suv">SUV</SelectItem>
                  <SelectItem value="crossover">Crossover</SelectItem>
                  <SelectItem value="caravan">
                    Karavan / Mobil Klinik Araç
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem
              label="Kabul Edilen Pet Türleri*"
              className="w-full !text-black dark:!text-white"
            >
              <MultiSelect
                options={petTypeOptions}
                onValueChange={(value) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    acceptedPetTypes: value,
                  }))
                }
                defaultValue={vehicle.acceptedPetTypes}
                maxCount={15}
                placeholder="Pet türlerini seçin"
              />
            </FormItem>
            <FormItem
              label="Araç Hijyen Sertifikası"
              className="w-full !text-black dark:!text-white"
            >
              <Select
                value={
                  vehicle.cleaningCertificate === null
                    ? undefined
                    : vehicle.cleaningCertificate
                      ? "true"
                      : "false"
                }
                onValueChange={(selected) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    cleaningCertificate:
                      selected === "true"
                        ? true
                        : selected === "false"
                          ? false
                          : "false",
                  }))
                }
              >
                <SelectTrigger className="w-full !text-gray-500">
                  <SelectValue placeholder="Var/Yok" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Var</SelectItem>
                  <SelectItem value="false">Yok</SelectItem>
                </SelectContent>
              </Select>
            </FormItem>
            <FormItem label="Araç Özellikleri" className="w-full !text-black dark:!text-white">
              <MultiSelect
                options={vehicleFeatureOptions}
                onValueChange={(value) =>
                  setVehicle((prev: any) => ({
                    ...prev,
                    vehicleFeatures: value,
                  }))
                }
                defaultValue={vehicle.vehicleFeatures}
                maxCount={15}
                placeholder="Araç özelliklerini seçin"
              />
            </FormItem>
            <FormItem label="Aktiflik Durumu" className="w-full !text-black dark:!text-white">
              <div className="flex gap-2">
                <Switch
                  name="passive"
                  checked={!vehicle.passive}
                  onCheckedChange={(checked) =>
                    setVehicle((prev: any) => ({
                      ...prev,
                      passive: !checked,
                    }))
                  }
                  className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
                />
                <p
                  className={
                    vehicle.passive ? "text-red-500" : "text-green-500"
                  }
                >
                  {vehicle.passive ? "Pasif" : "Aktif"}
                </p>
              </div>
            </FormItem>
            <FormItem label="Araç Fotoğrafları" className="w-full !text-black dark:!text-white">
              <UpdateVehiclePhotos
                petTaxiToken={petTaxiToken}
                loading={loading}
                newVehicleInputRef={newVehicleInputRef}
                setPhotoFileObject={setPhotoFileObject}
                setModalImage={setModalImage}
                modalImage={modalImage}
                initialInputData={initialData}
              />
            </FormItem>
            <FormItem label="Belgeler" className="w-full !text-black dark:!text-white">
              <MultipleVehicleFiles
                name="Araç Ruhsatı"
                files={vehicleFiles.vehicle_license}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({
                    ...prev,
                    vehicle_license: files,
                  }))
                }
                vehicleData={initialData}
                docType="vehicle_license"
                petTaxiToken={petTaxiToken}
              />
              <MultipleVehicleFiles
                name="Sigorta Belgesi"
                files={vehicleFiles.insurance}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({ ...prev, insurance: files }))
                }
                vehicleData={initialData}
                docType="insurance"
                petTaxiToken={petTaxiToken}
              />
              <MultipleVehicleFiles
                name="Pet Taşıma Uygunluk Belgesi"
                files={vehicleFiles.pet_transport_certificate}
                setFiles={(files) =>
                  setVehicleFiles((prev) => ({
                    ...prev,
                    pet_transport_certificate: files,
                  }))
                }
                vehicleData={initialData}
                docType="pet_transport_certificate"
                petTaxiToken={petTaxiToken}
              />
            </FormItem>
          </div>
          <div className="mt-7 flex justify-end gap-5">
            <Button onClick={closeModal} variant="outline" type="button">
              İptal
            </Button>
            <Button
              disabled={!buttonDisabled || disabled}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white"
              type="submit"
            >
              {loading ? <LoadingSpinner /> : "Kaydet"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateVehicleModal;
