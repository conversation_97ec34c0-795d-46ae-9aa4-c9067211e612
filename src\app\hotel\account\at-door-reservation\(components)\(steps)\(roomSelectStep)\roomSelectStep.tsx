"use client";
import React, { useEffect, useState } from "react";
import SideBar from "./(components)/sideBar";
import DatePickerSection from "./(components)/DatePickerSection";
import ReservationDialog from "./(components)/ReservationDialog";
import { setCalculatedRoomData } from "@/store/features/calculatedRoomData/calculated-room-data-slice";
import { useDispatch } from "react-redux";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";

interface RoomSelectStepProps {
  hotelToken: string | undefined;
  hotelData: any;
  subscriptionData: any;
  serviceData: any;
}

const RoomSelectStep: React.FC<RoomSelectStepProps> = ({
  hotelToken,
  hotelData,
  subscriptionData,
  serviceData,
}) => {
  const dispatch = useDispatch();
  const [startDate, setStartDate] = useState<Date | string | undefined>(
    undefined
  );
  const [endDate, setEndDate] = useState<Date | string | undefined>(undefined);

  const handleDateChange = (
    newStartDate: Date | string | undefined,
    newEndDate: Date | string | undefined
  ) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
  };

  useEffect(() => {
    dispatch(setCalculatedRoomData(null));
  }, [startDate, endDate]);

  useEffect(() => {
    dispatch(
      setCheckoutData({
        hotelCustomer: null,
        pet: [],
        servicePet: [],
        transportationServiceChoice: null,
        channel: "",
        paymentType: "",
      })
    );
  }, []);

  return (
    <div className="flex flex-row gap-4">
      <div className="flex flex-col items-center gap-10 md:gap-4 w-full lg:w-3/5 lg:pr-10 xl:w-2/3">
        <DatePickerSection onDateChange={handleDateChange} />
        <ReservationDialog
          startDate={startDate}
          endDate={endDate}
          hotelToken={hotelToken}
          hotelData={hotelData}
          subscriptionData={subscriptionData}
          serviceData={serviceData}
        />
      </div>
      <div className="mt-14 hidden grow lg:mt-0 lg:block">
        <div className="sticky top-10">
          <SideBar hotelToken={hotelToken} />
        </div>
      </div>
    </div>
  );
};

export default RoomSelectStep;
