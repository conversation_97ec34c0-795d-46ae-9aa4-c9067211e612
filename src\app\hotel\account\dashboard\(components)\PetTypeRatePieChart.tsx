"use client";
import type { <PERSON> } from "react";
import React, { useState, ChangeEvent } from "react";
import { TrendingUp } from "lucide-react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import type { ChartConfig } from "@/components/ui/chart";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import Input from "@/shared/Input";
import Link from "next/link";
import ButtonPrimary from "@/shared/ButtonPrimary";
import { Button } from "@/components/ui/button";
const chartData = [
  { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
  { browser: "firefox", visitors: 187, fill: "var(--color-firefox)" },
  { browser: "edge", visitors: 173, fill: "var(--color-edge)" },
  { browser: "other", visitors: 90, fill: "var(--color-other)" },
];

const petConfig = {
  pets: {
    label: "Pets",
  },
  cat: {
    label: "Kedi",
    color: "hsl(var(--chart-1))",
  },
  dog: {
    label: "Köpek",
    color: "hsl(var(--chart-2))",
  },
  smallDogBreed: {
    label: "Küçük Irk Köpek",
    color: "hsl(var(--chart-2))",
  },
  mediumDogBreed: {
    label: "Orta Irk Köpek",
    color: "hsl(var(--chart-2))",
  },
  largeDogBreed: {
    label: "Büyük Irk Köpek",
    color: "hsl(var(--chart-2))",
  },
  rabbit: {
    label: "Tavşan",
    color: "hsl(var(--chart-3))",
  },
  horse: {
    label: "At",
    color: "hsl(var(--chart-4))",
  },
  turtle: {
    label: "Kaplumbağa",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  chrome: {
    label: "cat",
    color: "hsl(var(--chart-1))",
  },
  safari: {
    label: "dog",
    color: "hsl(var(--chart-2))",
  },
  firefox: {
    label: "Firefox",
    color: "hsl(var(--chart-3))",
  },
  edge: {
    label: "Edge",
    color: "hsl(var(--chart-4))",
  },
  other: {
    label: "Other",
    color: "hsl(var(--chart-5))",
  },
} satisfies ChartConfig;

interface PetTypeRatePieChartProps {
  petTypeData: any;
  startDate: string | string[];
  endDate: string | string[];
}

const PetTypeRatePieChart: FC<PetTypeRatePieChartProps> = ({
  petTypeData,
  startDate,
  endDate,
}) => {
  const updatedPetData = petTypeData.map((pet: any) => ({
    ...pet,
    fill: `var(--color-${pet.petType})`,
  }));

  const formatIsoDate = (isoDate: string | string[]): string => {
    const dateStr = Array.isArray(isoDate) ? isoDate[0] : isoDate;

    const [year, month, day] = dateStr.split("-");
    return `${day}.${month}.${year}`;
  };

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Evcil Hayvan Oranı</CardTitle>
        <CardDescription className="font-medium">
          {formatIsoDate(startDate) + " - " + formatIsoDate(endDate)}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={petConfig}
          className="aspect-square mx-auto h-[297px] [&_.recharts-text]:fill-background"
        >
          <PieChart>
            <ChartTooltip
              content={<ChartTooltipContent nameKey="petType" hideLabel />}
            />
            <Pie data={updatedPetData} dataKey="count" outerRadius="80%">
              <LabelList
                dataKey="petType"
                className="fill-background"
                stroke="none"
                fontSize={10}
                formatter={(value: keyof typeof petConfig) =>
                  petConfig[value]?.label
                }
              />
            </Pie>
            <ChartLegend
              content={({ payload }) => (
                <ul className="flex flex-wrap w-full justify-center gap-2 mt-2">
                  {payload?.map((entry: any, index: number) => (
                    <li
                      key={`item-${index}`}
                      className="flex items-center justify-center gap-1"
                    >
                      <span
                        className="inline-block w-2 h-2"
                        style={{ backgroundColor: entry.color }}
                      ></span>
                      <span className="text-xs">
                        {entry.payload.percentage}
                      </span>
                    </li>
                  ))}
                </ul>
              )}
              className="w-full"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        {/* <div className="flex items-center gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div> */}
      </CardFooter>
    </Card>
  );
};

export default PetTypeRatePieChart;
