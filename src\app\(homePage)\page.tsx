import type { FC } from "react";
import React from "react";
import HotelHeader from "@/app/(components)/(Header)/HotelHeader";
import Layout from "@/app/(components)/(hotelHomePage)/Layout";
import PawBookingLogo from "@/shared/PawBookingLogo";
import { cookies } from "next/headers";

export interface HotelHomePageProps {}

const HotelHomePage: FC<HotelHomePageProps> = async () => {
  const cookieStore = await cookies();
  const mobileParams = cookieStore.get("mobileParams")?.value || undefined;
  const queryParams = new URLSearchParams(mobileParams);
  const mobile = queryParams.get("mobile");

  console.log(mobile, "mobile");

  return (
    <>
      <div className="sticky top-0 z-50 bg-transparent md:block hidden">
        <HotelHeader />
      </div>
      <div className="relative flex justify-center pt-5 md:hidden">
        <PawBookingLogo className="size-20 self-center" />
      </div>
      <div className="z-0">
        <Layout mobile={mobile} />
      </div>
    </>
  );
};

export default HotelHomePage;
