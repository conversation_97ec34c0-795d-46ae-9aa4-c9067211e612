"use client";
import type { FC } from "react";
import React, { useState } from "react";
import HotelPhotos from "./HotelPhotos";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import HotelPanelImageGalleryContainer from "./(hotelPanelImageGallery)/HotelPanelImageGalleryContainer";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelPhotosContainerProps {
  hotelData: HotelDataApiTypes;
  hotelToken: string | undefined;
}

const HotelPhotosContainer: FC<HotelPhotosContainerProps> = ({
  hotelData,
  hotelToken,
}) => {
  const translate = useTranslations("HotelPhotosContainer");

  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | null>(null);
  const [image, setImage] = useState<string[] | []>([]);
  const { photoInputHandler } = useHotelInformationsActions();

  return (
    <div>
      <HotelPanelImageGalleryContainer hotelPhotos={hotelData.images} />
      <HotelPhotos hotelData={hotelData} hotelToken={hotelToken} />
      <div className="mt-5">
        <div className="mt-5">
          <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
            <div className="space-y-1 text-center">
              <svg
                className="mx-auto size-12 text-neutral-400"
                stroke="currentColor"
                fill="none"
                viewBox="0 0 48 48"
                aria-hidden="true"
              >
                <path
                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                ></path>
              </svg>
              <div className="text-sm text-neutral-6000 dark:text-neutral-300">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
                >
                  <span>{translate("hotelUploadPhoto")}</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    disabled={loading}
                    accept="image/*"
                    multiple={true}
                    onChange={(e) => {
                      const fileObject = e.target.files;
                      setPhotoFileObject(fileObject);
                      if (fileObject) {
                        const imageUrls = Array.from(fileObject).map((file) =>
                          URL.createObjectURL(file)
                        );
                        setImage(imageUrls);
                      }
                    }}
                  />
                </label>
                {/* <p className="pl-1">or drag and drop</p> */}
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                PNG, JPG
              </p>
            </div>
          </div>
        </div>
        <div className="mt-2 flex justify-center gap-3">
          {image &&
            image.map((image: any, index: number) => {
              return (
                <div key={index}>
                  <Image src={image} width={150} height={150} alt="" />
                </div>
              );
            })}
        </div>
        <div className="mt-4 flex items-center justify-end gap-4">
          <Button
            className="bg-secondary-6000 hover:bg-secondary-700 text-white text-center w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
            disabled={disabled || image.length === 0 ? true : false}
            onClick={() => {
              photoInputHandler(
                photoFileObject,
                hotelToken,
                hotelData._id,
                setLoading,
                setImage,
                setDisabled,
                setPhotoFileObject
              );
            }}
          >
            {loading ? <LoadingSpinner /> : translate("save")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HotelPhotosContainer;
