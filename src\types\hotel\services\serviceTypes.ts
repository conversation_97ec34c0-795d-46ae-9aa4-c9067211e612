import { PetOwnerApiTypes } from "@/types/petOwner/petOwnerTypes";
import { PetInformationApiTypes } from "@/types/petOwner/petTypes";
export interface serviceSoldListApiTypes {
  _id: string;
  serviceType: string;
  hotel: string;
  serviceHotelId: string;
  petOwner: PetOwnerApiTypes;
  pet: PetInformationApiTypes;
  serviceName: string;
  total: number;
  totalHotel: number;
  description: string;
  note: string;
  issueDate: string;
  serviceDate: string;
  paymentStatus: string;
  status: string;
  serviceDetails:
    | serviceSoldVeterinaryTypes
    | serviceSoldTransportationTypes
    | serviceSoldGroomingTypes;
  createdAt: string;
}

interface serviceSoldVeterinaryTypes {
  veterinarianName: string;
  requiredDocuments: string;
  availabilityStart: string;
  availabilityEnd: string;
}

interface serviceSoldTransportationTypes {
  initialPrice: string;
  maxDistance: string;
  distancePrice: string;
}

interface serviceSoldGroomingTypes {
  duration: string;
}
export interface veterinaryServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    total: string;
    serviceDetails: {
      veterinarianName: string;
      requiredDocuments: string;
      availabilityStart: string;
      availabilityEnd: string;
    };
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface transportationServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    serviceDetails: {
      initialPrice: string;
      maxDistance: string;
      distancePrice: string;
    };
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface groomingServiceApiTypes {
  _id: string;
  serviceData: {
    serviceName: string;
    serviceDetails: {
      duration: string;
    };
    total: string;
    description: string;
    isActive: boolean;
    petType: string[];
  };
}

export interface transportationServiceTypes {
  isActive: boolean;
  serviceName: string;
  description: string;
  serviceDetails: {
    initialPrice: string;
    maxDistance: string;
    distancePrice: string;
  };
  petType: string[];
}

export interface veterinaryServiceTypes {
  isActive: boolean;
  serviceName: string;
  total: string;
  description: string;
  serviceDetails: {
    veterinarianName: string;
    requiredDocuments: string;
    availabilityStart: string;
    availabilityEnd: string;
  };
  petType: string[];
}

export interface groomingServiceTypes {
  isActive: boolean;
  serviceName: string;
  description: string;
  serviceDetails: {
    duration: string;
  };
  total: string;
  petType: string[];
}
