"use client";
import {
  setStepState,
  setRegisterId,
  setOwner,
  setMailAuthStep,
} from "@/store/features/partnerAuth/partner-auth-slice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useToast } from "@/components/ui/use-toast";

export const useMailStepActions = () => {
  const { toast } = useToast();
  const dispatch = useDispatch();

  const stepState = useSelector(
    (state: RootState) => state.partnerAuth.stepState
  );
  const owner = useSelector((state: RootState) => state.partnerAuth.owner);
  const partner = useSelector((state: RootState) => state.partnerAuth.partner);

  const handleCheckEMail = async (
    partnerEmail: string | null,
    setPartnerEmail: React.Dispatch<React.SetStateAction<string | null>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setDisabled(true);
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/check/email/${partnerEmail}/${partner.propertyType}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();

    if (!data.data.inUse) {
      partnerFirstMailStepHandler(partnerEmail, setDisabled);
    } else {
      toast({
        variant: "warning",
        duration: 5000,
        title: "Uyarı",
        description: `${partnerEmail} mail adresi ile açılmış bir kullanıcı var.`,
      });
      setPartnerEmail(null);
      setDisabled(false);
    }
  };

  const partnerFirstMailStepHandler = async (
    partnerEmail: string | null,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/step1`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: partnerEmail,
            propertyType: partner.propertyType,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      dispatch(setRegisterId(data.data.registerId));
      dispatch(setMailAuthStep(true));
      setDisabled(false);
      toast({
        variant: "success",
        duration: 6000,
        title: "Mail Onay",
        description: "Mail adresinize onay kodu gönderildi.",
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const partnerSecondMailStepHandler = async (
    partnerEmail: string | null,
    authCode: string | null,
    setAuthCode: React.Dispatch<React.SetStateAction<string | null>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    try {
      setDisabled(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/step2`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: partnerEmail,
            authCode: authCode,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setAuthCode(null);
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      dispatch(setStepState(stepState + 1));
      dispatch(setOwner({ ...owner, email: partnerEmail }));
      toast({
        variant: "success",
        duration: 6000,
        title: "Mail Onay",
        description: "Mail adresiniz Onaylandı.",
      });
      setDisabled(false);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  return {
    partnerFirstMailStepHandler,
    partnerSecondMailStepHandler,
    handleCheckEMail,
  };
};
