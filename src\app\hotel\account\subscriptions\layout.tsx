import type { <PERSON> } from "react";
import React from "react";
import SubscriptionsNav from "./(components)/SubscriptionsNav";

export interface SubscriptionsLayoutProps {
  children?: React.ReactNode;
}

const SubscriptionsLayout: FC<SubscriptionsLayoutProps> = ({ children }) => {
  return (
    <div className="container pt-8">
      <SubscriptionsNav />
      {children}
    </div>
  );
};

export default SubscriptionsLayout;
