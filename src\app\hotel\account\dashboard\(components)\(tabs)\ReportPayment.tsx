import React from "react";
import type { FC } from "react";
import PaymentTodayCard from "../(cards)/PaymentTodayCard";
import PaymentTomorrowCard from "../(cards)/PaymentTomorrowCard";
import PaymentCancellationCard from "../(cards)/PaymentCancellationCard";
import BalanceTable from "../(table)/BalanceTable";
import LineChartRevenue from "../LineChartRevenue";
import { PaymentTypeComponent } from "../RadialChartPaymentType";

interface ReportPaymentProps {
  depositAmounts: any;
  startDate: string | string[];
  endDate: string | string[];
  ordersBetweenDates: any;
  revenueAnalytics: any;
  cancelledReservationStats: any;
  paymentTypes: any;
}

const ReportPayment: FC<ReportPaymentProps> = ({
  depositAmounts,
  startDate,
  endDate,
  ordersBetweenDates,
  revenueAnalytics,
  cancelledReservationStats,
  paymentTypes,
}) => {
  return (
    <div className="flex flex-col gap-10">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-start">
        <PaymentTomorrowCard
          tomorrowDepositAmount={depositAmounts?.tomorrowDepositAmount}
          tomorrowChangePercent={depositAmounts?.tomorrowChangePercent}
        />
        <PaymentTodayCard
          todayDepositAmount={depositAmounts?.todayDepositAmount}
          todayChangePercent={depositAmounts?.todayChangePercent}
        />
        <PaymentCancellationCard
          cancelledReservationStats={cancelledReservationStats}
        />
        <div className="row-span-2">
          <PaymentTypeComponent
            paymentTypes={paymentTypes}
            startDate={startDate}
            endDate={endDate}
          />
        </div>
        <div className="md:col-span-3">
          <LineChartRevenue
            data={revenueAnalytics}
            startDate={startDate}
            endDate={endDate}
          />
        </div>
      </div>
      <div>
        <BalanceTable orderList={ordersBetweenDates} />
      </div>
    </div>
  );
};

export default ReportPayment;
