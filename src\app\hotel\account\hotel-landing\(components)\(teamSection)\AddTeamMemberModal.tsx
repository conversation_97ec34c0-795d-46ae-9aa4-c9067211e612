"use client";
import type { FC } from "react";
import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import type {
  TeamMemberType,
  TeamSectionType,
} from "@/types/hotel/hotelLandingType";
import Image from "next/image";
import { useHotelLandingActions } from "@/hooks/hotel/useHotelLanding";

interface AddTeamMemberModalProps {
  teamSectionData: TeamSectionType;
  hotelToken: string | undefined;
}

const AddTeamMemberModal: FC<AddTeamMemberModalProps> = ({
  teamSectionData,
  hotelToken,
}) => {
  const { teamMemberPhotoInputHandler } = useHotelLandingActions();
  const [teamMember, setTeamMember] = useState<TeamMemberType>({
    memberName: "",
    memberTitle: "",
    memberImage: null,
  });
  const [photoFileObject, setPhotoFileObject] = useState<FileList | null>(null);
  const [image, setImage] = useState<string[] | []>([]);
  const [addTeamMemberModalIsOpen, setAddTeamMemberModalIsOpen] =
    useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const buttonDisabled =
    teamMember.memberName?.trim() &&
    teamMember.memberTitle?.trim() &&
    image.length > 0;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTeamMember((prev) => ({ ...prev, [name]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileObject = e.target.files;
    setPhotoFileObject(fileObject);
    if (fileObject) {
      const imageUrls = Array.from(fileObject).map((file) =>
        URL.createObjectURL(file)
      );
      setImage(imageUrls);
    }
  };

  const resetForm = () => {
    setAddTeamMemberModalIsOpen(false);
    setTeamMember({
      memberName: "",
      memberTitle: "",
      memberImage: null,
    });
    setImage([]);
  };

  return (
    <>
      <Button
        className="bg-secondary-6000 hover:bg-secondary-700"
        type="button"
        onClick={() => setAddTeamMemberModalIsOpen(true)}
      >
        Yeni Çalışan Ekle
      </Button>
      <Dialog open={addTeamMemberModalIsOpen}>
        <DialogContent
          onInteractOutside={resetForm}
          className="overflow-y-auto max-h-[calc(100vh-50px)] md:max-w-2xl"
        >
          <DialogHeader>
            <DialogTitle>Yeni Çalışan Ekle</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) => {
              teamMemberPhotoInputHandler(
                event,
                teamMember,
                teamSectionData,
                photoFileObject,
                hotelToken,
                setLoading,
                setAddTeamMemberModalIsOpen,
                resetForm
              );
            }}
          >
            <div className="listingSection__wrap_disable">
              <div className="space-y-8">
                <div className="grid gap-3 max-md:space-y-2 md:grid-cols-2">
                  <FormItem label="Çalışan İsmi">
                    <Input
                      type="text"
                      className="mt-1.5"
                      name="memberName"
                      value={teamMember.memberName || ""}
                      onChange={handleInputChange}
                    />
                  </FormItem>
                  <FormItem label="Çalışan Mesleği">
                    <Input
                      type="text"
                      className="mt-1.5"
                      name="memberTitle"
                      value={teamMember.memberTitle || ""}
                      onChange={handleInputChange}
                    />
                  </FormItem>
                </div>
              </div>
              <div className="mt-5">
                <span className="text-lg font-semibold">
                  Çalışan fotoğrafları
                </span>
                <div className="mt-5"></div>
                <div className="mt-1 flex justify-center rounded-md border-2 border-dashed border-neutral-300 px-6 pb-6 pt-5 dark:border-neutral-6000">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto size-12 text-neutral-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      ></path>
                    </svg>
                    <div className="text-sm text-neutral-6000 dark:text-neutral-300">
                      <label
                        htmlFor="team-member-file-upload"
                        className="relative cursor-pointer  rounded-md font-medium text-secondary-6000 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
                      >
                        <span>Fotoğraf yükle</span>
                        <input
                          id="team-member-file-upload"
                          name="team-member-file-upload"
                          type="file"
                          className="sr-only"
                          disabled={loading}
                          accept="image/*"
                          multiple={false}
                          onChange={handleImageChange}
                        />
                      </label>
                      {/* <p className="pl-1">or drag and drop</p> */}
                    </div>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400">
                      PNG, JPG, GIF up to 1MB
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-2 flex justify-center gap-3">
                {image &&
                  image.map((image: any, index: number) => {
                    return (
                      <div key={index}>
                        <Image src={image} width={150} height={150} alt="" />
                      </div>
                    );
                  })}
              </div>
            </div>
            <div className="mt-5 flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={resetForm}>
                Vazgeç
              </Button>
              <Button
                className="bg-secondary-6000 hover:bg-secondary-700"
                // disabled={!buttonDisabled}
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Kaydet"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={resetForm}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddTeamMemberModal;
