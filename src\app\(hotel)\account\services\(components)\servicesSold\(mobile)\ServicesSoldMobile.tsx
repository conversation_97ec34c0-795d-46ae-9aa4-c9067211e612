import React from "react";
import type { FC } from "react";
import {
  Card,
  CardContent,
  CardD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>eader,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import { useTranslations } from "next-intl";
import ServicesSoldActionButtons from "../ServicesSoldActionButtons";
import ServicesSoldDetail from "../ServicesSoldDetail";
import { serviceSoldListApiTypes } from "@/types/hotel/services/serviceTypes";

interface ServicesSoldMobileProps {
  servicesData: serviceSoldListApiTypes;
  hotelToken: string | undefined;
}

export function statusHandler(status: string | undefined) {
  if (!status) return { statusName: status, color: "" };

  const statusColors: Record<string, string> = {
    confirmed: "text-[#2f855a]",
    booked: "text-[#FF9800]",
    checkedIn: "text-[#2196F3]",
    declined: "text-[#c53030]",
    waitingForCheckIn: "text-[#c53030]",
    waitingForCheckOut: "text-[#c53030]",
    waitingForApproval: "text-[#c53030]",
  };

  return {
    color: statusColors[status],
  };
}

const ServicesSoldMobile: FC<ServicesSoldMobileProps> = ({
  servicesData,
  hotelToken,
}) => {
  const ServicesSoldList = useTranslations("ServicesSoldList");
  const translate = useTranslations("ReservationList");
  const { serviceName, createdAt, serviceDate, serviceType, status } =
    servicesData;

  const reservationStatus = statusHandler(status);

  const price =
    new Intl.NumberFormat("tr-TR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(Number(servicesData.totalHotel)) + "₺";

  return (
    <Card>
      <CardHeader className="px-6 py-3">
        <CardTitle className="text-base">{serviceName || "No Name"}</CardTitle>
        <CardDescription>{formatDateToDayMonthYear(createdAt)}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">Hizmet Türü:</p>
          <p>{ServicesSoldList(serviceType)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">Hizmet Tarihi:</p>
          <p>{formatDateToDayMonthYear(serviceDate)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translate("status")}:</p>
          <p className={`${reservationStatus?.color}`}>{translate(status)}</p>
        </div>
        <div className="flex gap-1 text-sm">
          <p className="font-semibold">{translate("total")}:</p>
          <p>{price}</p>
        </div>
      </CardContent>
      <CardFooter className="justify-between">
        <ServicesSoldDetail servicesSoldData={servicesData} />
        <ServicesSoldActionButtons
          rowOriginal={servicesData}
          hotelToken={hotelToken}
        />
      </CardFooter>
    </Card>
  );
};

export default ServicesSoldMobile;
