"use client";
import React from "react";
import type { FC, ChangeEvent } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";

interface PetInformationsProps {
  breedListArray: string[] | [];
  petInformations: PetInformationTypes;
  setPetInformations: React.Dispatch<React.SetStateAction<PetInformationTypes>>;
}

const PetInformations: FC<PetInformationsProps> = ({
  breedListArray,
  petInformations,
  setPetInformations,
}) => {
  const ages = [
    { label: "0-3 aylık", value: "0-3 aylık" },
    { label: "3-6 aylık", value: "3-6 aylık" },
    { label: "6-9 aylık", value: "6-9 aylık" },
    { label: "9-12 aylık", value: "9-12 aylık" },
    { label: "1", value: "1" },
    { label: "2", value: "2" },
    { label: "3", value: "3" },
    { label: "4", value: "4" },
    { label: "5", value: "5" },
    { label: "5+", value: "5+" },
  ];

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPetInformations((prevState) => {
      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  const handleSelectChange = (
    selectedValue: string,
    key: keyof PetInformationTypes
  ) => {
    setPetInformations((prevState) => ({ ...prevState, [key]: selectedValue }));
  };

  return (
    <div className="mt-7">
      <h2 className="font-semibold text-lg mb-2">Evcil Hayvan Bilgileri</h2>
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 mb-10 gap-3">
        <FormItem className="max-w-96" label="Ad*">
          <Input
            name="name"
            onChange={handleChange}
            value={petInformations.name}
          />
        </FormItem>
        <FormItem className="max-w-96" label="Renk*">
          <Input
            name="color"
            onChange={handleChange}
            value={petInformations.color}
          />
        </FormItem>
        <FormItem className="max-w-96" label="Mikroçip Numarası*">
          <Input
            name="microChipNumber"
            onChange={handleChange}
            value={petInformations.microChipNumber}
          />
        </FormItem>
      </div>
      <div className="flex flex-wrap gap-5">
        {breedListArray.length > 0 && (
          <div className="space-y-1">
            <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
              Irk*
            </p>
            <Select
              onValueChange={(selectedValue) =>
                handleSelectChange(selectedValue, "breed")
              }
              value={petInformations.breed}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Irk Seçin" />
              </SelectTrigger>
              <SelectContent>
                {breedListArray.map((breed, index) => {
                  return (
                    <SelectItem key={index} value={breed}>
                      {breed}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        )}
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Yaş*
          </p>
          <Select
            onValueChange={(selectedValue) =>
              handleSelectChange(selectedValue, "age")
            }
            value={petInformations.age}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Yaş Seçin" />
            </SelectTrigger>
            <SelectContent>
              {ages.map((age) => {
                return (
                  <SelectItem key={age.value} value={age.value}>
                    {age.label}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Cinsiyet*
          </p>
          <Select
            onValueChange={(selectedValue) =>
              handleSelectChange(selectedValue, "gender")
            }
            value={petInformations.gender}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Cinsiyet Seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Erkek</SelectItem>
              <SelectItem value="female">Dişi</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Kısırlaştırılmış*
          </p>
          <Select
            onValueChange={(selectedValue) =>
              handleSelectChange(selectedValue, "infertile")
            }
            value={
              petInformations.infertile === true ||
              petInformations.infertile === "true"
                ? "true"
                : petInformations.infertile === false ||
                    petInformations.infertile === "false"
                  ? "false"
                  : ""
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Kısırlaştırılmış" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Evet</SelectItem>
              <SelectItem value="false">Hayır</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default PetInformations;
