"use client";
import { useEffect, useState } from "react";
import Particles from "@/components/ui/particles";
import { useThemeMode } from "@/hooks/useThemeMode";

export default function ParticlesContainer() {
  const [color, setColor] = useState("#ffffff");
  useThemeMode();
  useEffect(() => {
    if (typeof window !== "undefined") {
      const themeColor = localStorage.getItem("theme");
      if (themeColor) {
        if (themeColor === "light") {
          setColor("#000000");
        } else {
          setColor("#ffffff");
        }
      }
    }
  }, []);

  return (
    <div className="particles-element">
      <Particles
        className="absolute inset-0"
        quantity={300}
        ease={80}
        size={0.2}
        color={color}
        refresh
      />
    </div>
  );
}
