{"name": "pawbooking", "version": "0.2.3", "private": true, "engines": {"npm": ">=10.0.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "emails:build": "email build", "emails:dev": "email dev", "emails:export": "email export", "knip": "knip", "prepare": "husky", "tsc": "tsc --noEmit", "lint": "next lint --fix", "trunk": "trunk fmt --all", "format": "prettier --write ."}, "dependencies": {"@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@headlessui/react": "2.2.0", "@heroicons/react": "2.2.0", "@next/third-parties": "14.2.21", "@number-flow/react": "^0.5.4", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-email/components": "0.0.31", "@react-google-maps/api": "^2.20.7", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "2.5.0", "@sentry/nextjs": "8.47.0", "@tabler/icons-react": "^3.28.1", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/forms": "0.5.9", "@tailwindcss/typography": "0.5.15", "@tanstack/react-table": "^8.20.6", "@types/react-payment-inputs": "^1.1.4", "canvas-confetti": "1.9.3", "class-variance-authority": "^0.7.1", "client-only": "0.0.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.12.3", "emoji-regex": "^10.4.0", "framer-motion": "^11.17.0", "google-map-react": "2.2.1", "input-otp": "1.4.1", "lucide-react": "^0.525.0", "mdast-util-directive": "3.0.0", "motion": "^11.17.0", "next": "14.2.21", "next-intl": "3.26.3", "next-pwa": "5.6.0", "rc-slider": "11.1.7", "react": "18.3.1", "react-credit-cards-2": "^1.0.3", "react-datepicker": "7.5.0", "react-day-picker": "^9.7.0", "react-dom": "18.3.1", "react-email": "3.0.4", "react-hooks-global-state": "2.1.0", "react-icons": "5.4.0", "react-payment-inputs": "^1.2.0", "react-redux": "9.2.0", "react-svg-credit-card-payment-icons": "^3.1.1", "react-swipeable": "7.0.2", "react-toastify": "^11.0.5", "react-use": "17.6.0", "react-use-keypress": "1.3.1", "recharts": "2.15.0", "sharp": "0.33.5", "slug": "10.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "1.0.7", "uuid": "11.0.3", "vaul": "0.9.3", "yet-another-react-lightbox": "^3.24.0"}, "devDependencies": {"@trunkio/launcher": "1.3.4", "@types/canvas-confetti": "1.9.0", "@types/google-map-react": "2.1.10", "@types/node": "20.17.10", "@types/react": "18.3.18", "@types/react-datepicker": "6.2.0", "@types/react-dom": "18.3.5", "@types/slug": "5.0.9", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "8.18.2", "@typescript-eslint/parser": "8.18.2", "autoprefixer": "10.4.20", "eslint": "8.57.1", "eslint-config-next": "14.2.21", "eslint-config-prettier": "9.1.0", "eslint-plugin-compat": "6.0.2", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-tailwindcss": "3.17.5", "husky": "9.1.7", "knip": "5.41.1", "postcss": "8.4.49", "prettier": "3.4.2", "sass": "1.83.0", "tailwindcss": "3.4.17", "typescript": "5.7.2"}, "browserslist": [">1%", "not dead", "not op_mini all"]}