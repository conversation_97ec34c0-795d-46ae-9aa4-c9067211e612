"use client";
import React, { useState } from "react";
import type { FC } from "react";
import PetInformations from "../../add-pet/(components)/PetInformations";
import PetHealthInformations from "../../add-pet/(components)/PetHealthInformations";
import PetHabits from "../../add-pet/(components)/PetHabits";
import VaccinationReport from "../../add-pet/(components)/VaccinationReport";
import { breedList } from "@/types/petOwner/petTypes";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";
import type { PetInformationApiTypes } from "@/types/petOwner/petTypes";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import Link from "next/link";
import { useHotelPet } from "@/hooks/hotel/useHotelPets";
import LoadingSpinner from "@/shared/icons/Spinner";
import UpdatePetNav from "./UpdatePetNav";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface UpdatePetContainerProps {
  selectedPet: PetInformationApiTypes;
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
  hotelCustomerId: any;
}

const UpdatePetContainer: FC<UpdatePetContainerProps> = ({
  selectedPet,
  hotelToken,
  hotelData,
  hotelCustomerId,
}) => {
  const initialData = {
    name: selectedPet.name,
    age: selectedPet.age,
    kind: selectedPet.kind,
    breed: selectedPet.breed,
    gender: selectedPet.gender,
    color: selectedPet.color,
    specified: selectedPet.specified,
    infertile: selectedPet.infertile,
    microChipNumber: selectedPet.microChipNumber,
    vaccines: selectedPet.vaccines,
    internalParasiteTreatment: selectedPet.internalParasiteTreatment,
    externalParasiteTreatment: selectedPet.externalParasiteTreatment,
    internalTreatmentDate: selectedPet.internalTreatmentDate?.split("T")[0],
    externalTreatmentDate: selectedPet.externalTreatmentDate?.split("T")[0],
    allergicTo: selectedPet.allergicTo,
    hereditaryDiseases: selectedPet.hereditaryDiseases,
    operationHistory: selectedPet.operationHistory,
    medicine: selectedPet.medicine,
    feedingHabits: selectedPet.feedingHabits,
    description: selectedPet.description,
    documentPhotos: selectedPet.documentPhotos,
  };

  const { updateDocumentPhotosHandler } = useHotelPet();
  const [petInformations, setPetInformations] =
    useState<PetInformationTypes>(initialData);
  const [descriptions, setDescriptions] = useState({
    allergic: selectedPet.allergicTo.length > 0 ? "true" : "false",
    diseases: selectedPet.hereditaryDiseases.length > 0 ? "true" : "false",
    operation: selectedPet.operationHistory.length > 0 ? "true" : "false",
    internalParasite: selectedPet.internalParasiteTreatment.toString(),
    externalParasite: selectedPet.externalParasiteTreatment.toString(),
    medicines: selectedPet.medicine ? "true" : "false",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);
  const [photoFileObject, setPhotoFileObject] = useState<FileList | undefined>(
    undefined
  );
  const [image, setImage] = useState<string[] | []>([]);

  const closeModal = () => {
    setLoading(false);
    setImage([]);
    setPhotoFileObject(undefined);
  };

  const breedListArray = breedList[petInformations.kind] || [];

  const isPetInfoComplete = () => {
    if (breedListArray.length > 0) {
      if (
        petInformations.name.trim() &&
        petInformations.age &&
        petInformations.breed &&
        petInformations.gender &&
        petInformations.color.trim() &&
        petInformations.microChipNumber.trim() &&
        petInformations.infertile?.toString()
      ) {
        return true;
      } else {
        return false;
      }
    } else if (breedListArray.length === 0) {
      if (
        petInformations.name.trim() &&
        petInformations.age &&
        petInformations.gender &&
        petInformations.color.trim() &&
        petInformations.microChipNumber.trim() &&
        petInformations.infertile?.toString()
      ) {
        return true;
      } else {
        return false;
      }
    }
  };

  const isPetHealthInfoComplete = () => {
    if (
      Object.values(descriptions).some((value) => value === "") ||
      (descriptions.internalParasite === "true" &&
        !petInformations.internalTreatmentDate?.trim()) ||
      (descriptions.externalParasite === "true" &&
        !petInformations.externalTreatmentDate?.trim()) ||
      (descriptions.allergic === "true" &&
        petInformations.allergicTo.length === 0) ||
      (descriptions.diseases === "true" &&
        petInformations.hereditaryDiseases.length === 0) ||
      (descriptions.operation === "true" &&
        petInformations.operationHistory.length === 0) ||
      (descriptions.medicines === "true" && !petInformations.medicine.trim())
    ) {
      return false;
    }

    return true;
  };

  const isAllInputsValid = isPetInfoComplete() && isPetHealthInfoComplete();
  const hasImage = image.length > 0;
  const removeKey = (obj: any, key: string) => {
    const { [key]: _, ...rest } = obj;
    return rest;
  };

  const isDataEqual =
    JSON.stringify(removeKey(initialData, "documentPhotos")) ===
    JSON.stringify(removeKey(petInformations, "documentPhotos"));
  const isButtonDisabled = (!hasImage && isDataEqual) || !isAllInputsValid;

  return (
    <form
      onSubmit={(event) =>
        updateDocumentPhotosHandler(
          event,
          photoFileObject,
          hotelToken,
          petInformations,
          closeModal,
          setLoading,
          hotelData?._id,
          hotelCustomerId,
          selectedPet._id,
          setDisabled
        )
      }
      className="space-y-5"
    >
      <UpdatePetNav step={step} setStep={setStep} />
      {step === 1 && (
        <PetInformations
          breedListArray={breedListArray}
          petInformations={petInformations}
          setPetInformations={setPetInformations}
        />
      )}
      {step === 2 && (
        <PetHealthInformations
          petInformations={petInformations}
          setPetInformations={setPetInformations}
          descriptions={descriptions}
          setDescriptions={setDescriptions}
        />
      )}
      {step === 3 && (
        <PetHabits
          petInformations={petInformations}
          setPetInformations={setPetInformations}
        />
      )}
      {step === 4 && (
        <VaccinationReport
          image={image}
          setImage={setImage}
          setPhotoFileObject={setPhotoFileObject}
          initialDocuments={selectedPet.documentPhotos}
          hotelToken={hotelToken}
        />
      )}
      <div className="flex gap-5 justify-end">
        <Link href="/account/hotel-customer">
          <ButtonSecondary type="button">Vazgeç</ButtonSecondary>
        </Link>
        <ButtonPrimary type="submit" disabled={isButtonDisabled || disabled}>
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </ButtonPrimary>
      </div>
    </form>
  );
};

export default UpdatePetContainer;
