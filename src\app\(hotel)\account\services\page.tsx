import React from "react";
import { cookies } from "next/headers";
import VeterinaryCard from "./(components)/(serviceCard)/VeterinaryCard";
import TransportationCard from "./(components)/(serviceCard)/TransportationCard";
import GroomingCard from "./(components)/(serviceCard)/GroomingCard";
import AdditionalServicesModal from "./(components)/AdditionalServicesModal";
import getAdditionalServices from "@/actions/(protected)/hotel/services/getAdditionalServices";
import {
  veterinaryServiceApiTypes,
  transportationServiceApiTypes,
  groomingServiceApiTypes,
} from "@/types/hotel/services/serviceTypes";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";

const ServicesPage = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const additionalServices = await getAdditionalServices();
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  return (
    <div>
      <AdditionalServicesModal
        hotelToken={hotelToken}
        additionalServices={additionalServices}
        membershipData={membershipData?.data}
      />
      {additionalServices?.data?.availableServices?.veterinaryServices?.length >
        0 && (
        <>
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Veteriner Hizmetleri
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {additionalServices?.data?.availableServices?.veterinaryServices &&
              additionalServices?.data?.availableServices?.veterinaryServices.map(
                (service: veterinaryServiceApiTypes) => (
                  <VeterinaryCard
                    service={service}
                    key={service._id}
                    hotelToken={hotelToken}
                  />
                )
              )}
          </div>
        </>
      )}
      {additionalServices?.data?.availableServices?.transportationServices
        ?.length > 0 && (
        <>
          <div className="border-t my-8" />
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Ulaşım Hizmetleri
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {additionalServices?.data?.availableServices
              ?.transportationServices &&
              additionalServices?.data?.availableServices?.transportationServices.map(
                (service: transportationServiceApiTypes) => (
                  <TransportationCard
                    service={service}
                    key={service._id}
                    hotelToken={hotelToken}
                  />
                )
              )}
          </div>
        </>
      )}
      {additionalServices?.data?.availableServices?.groomingServices?.length >
        0 && (
        <>
          <div className="border-t my-8" />
          <h2 className="mb-2 mt-5 text-lg font-semibold capitalize text-neutral-700 dark:text-neutral-200">
            Kuaför Hizmetleri
          </h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 md:gap-7">
            {additionalServices?.data?.availableServices?.groomingServices &&
              additionalServices?.data?.availableServices?.groomingServices.map(
                (service: groomingServiceApiTypes) => (
                  <GroomingCard
                    service={service}
                    key={service._id}
                    hotelToken={hotelToken}
                  />
                )
              )}
          </div>
        </>
      )}
    </div>
  );
};

export default ServicesPage;
