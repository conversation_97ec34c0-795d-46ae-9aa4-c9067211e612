"use client";
import type { FC } from "react";
import React, { useEffect, useState } from "react";
import HeroSectionPhotoCard from "./HeroSectionPhotoCard";
import { AnimatePresence, motion, MotionConfig } from "framer-motion";
import { useSwipeable } from "react-swipeable";
import PrevBtn from "@/components/PrevBtn";
import NextBtn from "@/components/NextBtn";
import { variants } from "@/utils/animationVariants";
import { useWindowSize } from "react-use";
import type { HeroSectionType } from "@/types/hotel/hotelLandingType";

export interface HeroSectionPhotosProps {
  className?: string;
  itemClassName?: string;
  heading?: string;
  subHeading?: string;
  people?: any;
  categoryCardType?: "card3" | "card4" | "card5";
  itemPerRow?: 3 | 4 | 5;
  sliderStyle?: "style1" | "style2";
  heroSectionData: HeroSectionType;
  hotelToken: string | undefined;
}

const HeroSectionPhotos: FC<HeroSectionPhotosProps> = ({
  className = "",
  itemClassName = "",
  itemPerRow = 4,
  heroSectionData,
  hotelToken,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [numberOfItems, setNumberOfitem] = useState(0);

  const windowWidth = useWindowSize().width;
  useEffect(() => {
    if (windowWidth < 320) {
      return setNumberOfitem(1);
    }
    if (windowWidth < 500) {
      return setNumberOfitem(itemPerRow - 3);
    }
    if (windowWidth < 1024) {
      return setNumberOfitem(itemPerRow - 2);
    }
    if (windowWidth < 1280) {
      return setNumberOfitem(itemPerRow - 1);
    }

    setNumberOfitem(itemPerRow);
  }, [itemPerRow, windowWidth]);

  function changeItemId(newVal: number) {
    if (newVal > currentIndex) {
      setDirection(1);
    } else {
      setDirection(-1);
    }
    setCurrentIndex(newVal);
  }

  const handlers = useSwipeable({
    onSwipedLeft: () => {
      if (heroSectionData?.images.length)
        if (currentIndex < heroSectionData?.images.length - 1) {
          changeItemId(currentIndex + 1);
        }
    },
    onSwipedRight: () => {
      if (currentIndex > 0) {
        changeItemId(currentIndex - 1);
      }
    },
    trackMouse: true,
  });

  if (!numberOfItems) return null;

  return (
    <div className={`nc-SectionSliderNewCategories ${className}`}>
      <MotionConfig
        transition={{
          x: { type: "spring", stiffness: 300, damping: 30 },
          opacity: { duration: 0.2 },
        }}
      >
        <div className={`relative flow-root`} {...handlers}>
          <div className={`flow-root overflow-hidden rounded-xl`}>
            <motion.ul
              initial={false}
              className="relative -mx-2 whitespace-nowrap xl:-mx-4"
            >
              <AnimatePresence initial={false} custom={direction}>
                {heroSectionData?.images?.map((item: any, indx: number) => (
                  <motion.li
                    className={`relative inline-block px-2 xl:px-4 ${itemClassName}`}
                    custom={direction}
                    initial={{
                      x: `${(currentIndex - 1) * -100}%`,
                    }}
                    animate={{
                      x: `${currentIndex * -100}%`,
                    }}
                    variants={variants(200, 1)}
                    key={indx}
                    style={{
                      width: `calc(1/${numberOfItems} * 100%)`,
                    }}
                  >
                    <HeroSectionPhotoCard
                      images={item}
                      hotelToken={hotelToken}
                      index={indx}
                    />
                  </motion.li>
                ))}
              </AnimatePresence>
            </motion.ul>
          </div>

          {currentIndex ? (
            <PrevBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex - 1)}
              className="absolute -left-3 top-1/3 z-[1] size-9 -translate-y-1/2 text-lg xl:-left-6 xl:size-12"
            />
          ) : null}

          {heroSectionData?.images?.length > currentIndex + numberOfItems ? (
            <NextBtn
              style={{ transform: "translate3d(0, 0, 0)" }}
              onClick={() => changeItemId(currentIndex + 1)}
              className="absolute -right-3 top-1/3 z-[1] size-9 -translate-y-1/2 text-lg xl:-right-6 xl:size-12"
            />
          ) : null}
        </div>
      </MotionConfig>
    </div>
  );
};

export default HeroSectionPhotos;
