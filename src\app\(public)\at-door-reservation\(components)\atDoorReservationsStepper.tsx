import {
  Stepper,
  Stepper<PERSON>ndicator,
  Stepper<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Stepper<PERSON>rigger,
} from "@/components/ui/stepper";
import type { FC } from "react";
import { PawPrint } from "lucide-react";
import { FileText } from "lucide-react";
import { CreditCard } from "lucide-react";
import { step<PERSON><PERSON><PERSON> } from "./atDoorReservationsContainer";

interface atDoorReservationsStepperProps {
  stepParams: {
    summary: string;
    payment: string;
  };
}

const AtDoorReservationsStepper: FC<atDoorReservationsStepperProps> = ({
  stepParams,
}) => {
  const stepValue = stepHandler(stepParams);

  const steps = [
    {
      step: 1,
      title: "<PERSON>vci<PERSON>",
      Icon: PawPrint,
    },
    {
      step: 2,
      title: "Özet",
      Icon: FileText,
    },
    {
      step: 3,
      title: "Ödeme",
      Icon: CreditCard,
    },
  ];

  return (
    <div className="space-y-8 text-center">
      <Stepper value={stepValue} className="items-start gap-4">
        {steps.map(({ step, title, Icon }) => (
          <StepperItem
            key={step}
            step={step}
            className="flex-1"
            completed={step < stepValue}
          >
            <StepperTrigger className="w-full flex-col items-center gap-2 rounded pointer-events-none">
              <div className="h-10 w-10 relative">
                <StepperIndicator className="bg-border h-10 w-10 [&_span]:sr-only">
                  <span className="sr-only">{step}</span>
                </StepperIndicator>
                <Icon
                  className={`size-5 absolute inset-0 m-auto z-10 ${step === stepValue && "text-white"} ${step < stepValue && "hidden"}`}
                />
              </div>
              <div className="space-y-0.5">
                <StepperTitle>{title}</StepperTitle>
              </div>
            </StepperTrigger>
          </StepperItem>
        ))}
      </Stepper>
    </div>
  );
};

export default AtDoorReservationsStepper;
