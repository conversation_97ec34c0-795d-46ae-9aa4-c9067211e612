"use client";
import { useState, useEffect } from "react";
import {
  GoogleMap,
  LoadScript,
  Autocomplete,
  <PERSON><PERSON>,
  <PERSON>yline,
} from "@react-google-maps/api";
import Input from "@/shared/Input";
import { MapPin } from "lucide-react";

const libraries: "places"[] = ["places"];
const API_KEY = "AIzaSyCXi01Rpagu_wbKsoLUMW0UwWNhfn7xg6A";
const mapContainerStyle = {
  width: "100%",
  height: "400px",
};
const center = {
  lat: 39.92077, // Ankara
  lng: 32.85411,
};

interface DistanceCalculatorProps {
  onDistanceChange: (distance: string | null) => void;
  onLocationsChange: (start: string, end: string) => void;
  hotelLocation: string;
  setFormData: any;
}

const DistanceCalculator: React.FC<DistanceCalculatorProps> = ({
  onDistanceChange,
  onLocationsChange,
  hotelLocation,
  setFormData,
}) => {
  const [startLocation, setStartLocation] = useState<string>("");
  const [endLocation, setEndLocation] = useState<string>("");
  const [startCoords, setStartCoords] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const [endCoords, setEndCoords] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const [hotelAddress, setHotelAddress] = useState<string>("");
  const [startAutocomplete, setStartAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [endAutocomplete, setEndAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);

  const onLoadStart = (autocomplete: google.maps.places.Autocomplete) => {
    setStartAutocomplete(autocomplete);
  };
  const onLoadEnd = (autocomplete: google.maps.places.Autocomplete) => {
    setEndAutocomplete(autocomplete);
  };

  const onPlaceChangedStart = () => {
    if (startAutocomplete) {
      const place = startAutocomplete.getPlace();
      if (place.geometry) {
        const formattedAddress = place.formatted_address || "";
        setStartLocation(place.formatted_address || "");
        setStartCoords({
          lat: place.geometry.location?.lat() || 0,
          lng: place.geometry.location?.lng() || 0,
        });

        onLocationsChange(formattedAddress, endLocation);
      }
    }
  };

  // const onPlaceChangedEnd = () => {
  //   if (endAutocomplete) {
  //     const place = endAutocomplete.getPlace();
  //     if (place.geometry) {
  //       const formattedAddress = place.formatted_address || "";
  //       setEndLocation(place.formatted_address || "");
  //       setEndCoords({
  //         lat: place.geometry.location?.lat() || 0,
  //         lng: place.geometry.location?.lng() || 0,
  //       });

  //       onLocationsChange(startLocation, formattedAddress);
  //     }
  //   }
  // };

  const getDistance = async () => {
    if (!startLocation || !endLocation) {
      return;
    }
    try {
      const response = await fetch(
        `/api/distance?start=${encodeURIComponent(startLocation)}&end=${encodeURIComponent(endLocation)}`
      );
      const data = await response.json();
      if (data.rows[0].elements[0].status === "OK") {
        // Mesafeden sadece sayısal kısmı alma
        const distanceText = data.rows[0].elements[0].distance.text; // Örneğin: "9.5 km"
        const distance = parseFloat(distanceText); // Sadece 9.5 kısmını alır
        onDistanceChange(distance.toString());
      } else {
        alert("Mesafe hesaplanamadı!");
        onDistanceChange(null);
      }
    } catch (error) {
      console.error("Fetch Error:", error);
    }
  };

  useEffect(() => {
    if (startCoords && endCoords) {
      getDistance(); // Mesafeyi otomatik hesapla
    }
  }, [startCoords, endCoords]); // Konumlar değiştiğinde mesafe hesaplanır

  // find hotel address from embed url
  useEffect(() => {
    const placeNameMatch = hotelLocation.match(/!2s([^!]+)/);
    const placeName = placeNameMatch
      ? decodeURIComponent(placeNameMatch[1])
      : null;

    const latMatch = hotelLocation.match(/!3d([0-9.-]+)/);
    const lngMatch = hotelLocation.match(/!2d([0-9.-]+)/);
    const lat = latMatch ? parseFloat(latMatch[1]) : null;
    const lng = lngMatch ? parseFloat(lngMatch[1]) : null;

    const geocoder = new window.google.maps.Geocoder();

    const isValidPlaceName =
      placeName && placeName.length > 3 && placeName.toLowerCase() !== "tr";

    if (isValidPlaceName) {
      // Yer ismine göre geocode
      geocoder.geocode({ address: placeName }, (results, status) => {
        if (status === "OK" && results?.[0]) {
          const location = results[0]?.geometry?.location;
          const address = results[0]?.formatted_address;
          setEndLocation(address);
          setHotelAddress(address);
          setEndCoords({ lat: location.lat(), lng: location.lng() });
        } else {
          console.error("place name hata:", status);
        }
      });
    } else if (lat !== null && lng !== null) {
      // Koordinat ile ters geocode
      const coords = { lat, lng };
      setEndCoords(coords);

      geocoder.geocode({ location: coords }, (results, status) => {
        if (status === "OK" && results?.[0]) {
          setHotelAddress(results[0].formatted_address);
          setEndLocation(results[0].formatted_address);
        } else {
          console.error("Koordinattan adres çözümlenemedi:", status);
        }
      });
    } else {
      console.warn("Adres veya koordinat bulunamadı.");
    }
  }, [hotelLocation]);

  const findUserLocationHandler = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          console.log("Konum alındı:", latitude, longitude);

          const geocoder = new window.google.maps.Geocoder();
          const location = { lat: latitude, lng: longitude };

          geocoder.geocode({ location }, (results, status) => {
            if (status === "OK" && results?.[0]) {
              setStartLocation(results[0].formatted_address);
              setStartCoords({ lat: latitude, lng: longitude });
              setFormData((prev: any) => ({
                ...prev,
                startLocation: results[0].formatted_address,
                endLocation: endLocation,
              }));
              console.log("Adres:", results[0].formatted_address);
            } else {
              console.error("Adres çözümlenemedi:", status);
            }
          });
        },
        (error) => {
          console.error("Konum alınamadı:", error.message);
        }
      );
    } else {
      alert("Tarayıcınız konum özelliğini desteklemiyor.");
    }
  };

  return (
    <div>
      <div
        style={{
          position: "relative",
          zIndex: 1000,
          overflow: "visible", // Autocomplete dropdown'ının görünmesini sağlar
        }}
      >
        <p className="font-medium text-sm mb-1">Başlangıç Noktası</p>
        <Autocomplete
          onLoad={onLoadStart}
          onPlaceChanged={onPlaceChangedStart}
          options={{ componentRestrictions: { country: "tr" } }}
        >
          <Input
            type="text"
            placeholder="Başlangıç Noktası"
            value={startLocation}
            onChange={(e) => setStartLocation(e.target.value)}
            className="w-full p-2 border rounded mb-2"
            style={{
              zIndex: 2000,
              position: "relative",
            }}
          />
        </Autocomplete>
        <div className="flex justify-end">
          <button
            className="text-xs sm:text-sm flex items-center justify-end font-medium hover:text-primary-700 duration-200"
            type="button"
            onClick={findUserLocationHandler}
          >
            <span className="relative flex h-2.5 w-2.5 mr-1">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-700 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2.5 w-2.5 bg-primary-6000"></span>
            </span>
            <MapPin className="size-4" /> Konumumu Kullan
          </button>
        </div>
        {startCoords?.lat && startCoords.lng && (
          <div className="my-5">
            <iframe
              width="100%"
              height="300"
              loading="lazy"
              allowFullScreen
              referrerPolicy="no-referrer-when-downgrade"
              src={`https://maps.google.com/maps?q=${startCoords?.lat},${startCoords?.lng}&z=15&output=embed`}
            />
            <p className="text-xs sm:text-sm font-medium text-red-500 mt-2">
              Konum küçük sapmalar içerebilir ve fiyat hesaplamasında
              kullanılır. Araç yönlendirmesi için açık adres esas alınır.
            </p>
          </div>
        )}
      </div>
      <div
        style={{
          position: "relative",
          zIndex: 1000,
          overflow: "visible", // Autocomplete dropdown'ının görünmesini sağlar
        }}
      >
        <p className="font-medium text-sm mb-1 mt-3">Varış Noktası (Otel)</p>
        <Autocomplete
          onLoad={onLoadEnd}
          // onPlaceChanged={onPlaceChangedEnd}
          className="z-50"
          options={{ componentRestrictions: { country: "tr" } }}
        >
          <Input
            type="text"
            placeholder="Varış Noktası"
            value={hotelAddress}
            // value={endLocation}
            // onChange={(e) => setEndLocation(e.target.value)}
            className="w-full p-2 border rounded mb-2"
            disabled
            style={{
              zIndex: 1000,
              position: "relative",
              pointerEvents: "auto", // Bu, input elemanının tıklanabilirliğini sağlıyor
            }}
          />
        </Autocomplete>
      </div>

      {/* <div className="mt-4">
          <GoogleMap
            mapContainerStyle={mapContainerStyle}
            center={startCoords || center}
            zoom={startCoords ? 10 : 5}
          >
            {startCoords && <Marker position={startCoords} />}
            {endCoords && <Marker position={endCoords} />}
            {startCoords && endCoords && (
              <Polyline
                path={[startCoords, endCoords]}
                options={{ strokeColor: "#FF0000", strokeWeight: 3 }}
              />
            )}
          </GoogleMap>
        </div> */}
    </div>
  );
};

export default DistanceCalculator;
