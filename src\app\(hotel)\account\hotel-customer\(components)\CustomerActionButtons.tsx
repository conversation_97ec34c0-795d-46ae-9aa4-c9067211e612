import type { <PERSON> } from "react";
import React from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import UpdateCustomer from "./updateCustomer";
import DeleteCustomer from "./deleteCustomer";
import HotelCustomerPetCard from "./HotelCustomerPetCard";

interface UserActionButtonsProps {
  hotelToken: string | undefined;
  rowOriginal: any;
}

const CustomerActionButtons: FC<UserActionButtonsProps> = ({
  hotelToken,
  rowOriginal,
}) => {
  return (
    <div className="flex items-center justify-center gap-2 text-neutral-500 dark:text-neutral-400">
      <HotelCustomerPetCard hotelToken={hotelToken} rowOriginal={rowOriginal} />
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger>
            <UpdateCustomer hotelToken={hotelToken} rowOriginal={rowOriginal} />
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium"><PERSON>üş<PERSON><PERSON>yi düzenle</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger>
            <DeleteCustomer hotelToken={hotelToken} rowOriginal={rowOriginal} />
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs font-medium">Müşteriyi sil</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};

export default CustomerActionButtons;
