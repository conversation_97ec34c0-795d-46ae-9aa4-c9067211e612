"use client";
import React, { useRef, useEffect } from "react";
import type { RootState } from "@/store";
import { useSelector } from "react-redux";

const ChatBottomRef = () => {
  const bottomRef = useRef<HTMLDivElement | null>(null);
  const chatData = useSelector(
    (state: RootState) => state.liveChatText.chatData
  );

  const scrollToBottom = () => {
    const parent = bottomRef?.current?.parentElement;
    if (!parent) return;

    bottomRef?.current?.scrollIntoView({ behavior: "auto", block: "nearest" });

    parent.scrollTop += 20;
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatData]);

  return <div ref={bottomRef} />;
};

export default ChatBottomRef;
