[{"index": 1, "id": "9e3e3994-a3ed-47ca-a014-d4483884cfe2", "featuredImage": "https://images.pexels.com/photos/3935702/pexels-photo-3935702.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Lenovo’s smarter devices stoke professional passions ", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 11, "viewdCount": 2504, "readingTime": 2, "bookmark": {"count": 3007, "isBookmarked": false}, "like": {"count": 3366, "isLiked": true}, "authorId": 3, "categoriesId": [3, 12], "postType": "standard"}, {"index": 2, "id": "af92a665-4a4d-4cff-9e17-89456df21fb5", "featuredImage": "https://images.pexels.com/photos/3218718/pexels-photo-3218718.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "How AI and Teams are benefitting the littlest of patients ", "desc": "In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 13, "viewdCount": 1646, "readingTime": 5, "bookmark": {"count": 3751, "isBookmarked": false}, "like": {"count": 3024, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "video"}, {"index": 3, "id": "dffe0224-ebff-4803-bb66-e8d128656284", "featuredImage": "https://images.pexels.com/photos/3155666/pexels-photo-3155666.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "DIYer and TV host <PERSON><PERSON>’s journey through gaming keeps evolving", "desc": "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 33, "viewdCount": 4031, "readingTime": 2, "bookmark": {"count": 76, "isBookmarked": false}, "like": {"count": 222, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "standard"}, {"index": 4, "id": "5ac7cb90-4694-42e8-8883-16372711eaa8", "featuredImage": "https://images.pexels.com/photos/8241135/pexels-photo-8241135.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "New tools for Black pregnant and postpartum mothers to save lives", "desc": "Fusce consequat. Nulla nisl. Nunc nisl. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 49, "viewdCount": 1632, "readingTime": 6, "bookmark": {"count": 264, "isBookmarked": true}, "like": {"count": 3735, "isLiked": true}, "authorId": 5, "categoriesId": [5, 14], "postType": "standard"}, {"index": 5, "id": "f82a8455-3a14-4af9-b6d2-ac6cd74e007c", "featuredImage": "https://images.pexels.com/photos/4226100/pexels-photo-4226100.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "People who inspired us in 2019 ", "desc": "Aliquam quis turpis eget elit sodales scelerisque. <PERSON><PERSON><PERSON> sit amet eros. Suspendisse accumsan tortor quis turpis.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 9, "viewdCount": 3338, "readingTime": 5, "bookmark": {"count": 733, "isBookmarked": false}, "like": {"count": 3569, "isLiked": true}, "authorId": 10, "categoriesId": [10, 19], "postType": "video"}, {"index": 6, "id": "54c11c92-049f-4353-8cdb-f7ec70d3ae75", "featuredImage": "https://images.pexels.com/photos/3225528/pexels-photo-3225528.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "How architects visualize design for world’s biggest airport", "desc": "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 13, "viewdCount": 4475, "readingTime": 2, "bookmark": {"count": 199, "isBookmarked": false}, "like": {"count": 3052, "isLiked": true}, "authorId": 9, "categoriesId": [9, 18], "postType": "standard"}, {"index": 7, "id": "1c40aaaf-0077-4f54-9b34-e90d95e17c7e", "featuredImage": "https://images.pexels.com/photos/2507007/pexels-photo-2507007.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Take a 3D tour through a Microsoft datacenter", "desc": "Vestibulum ac est lacinia nisi venenatis tristique. <PERSON><PERSON><PERSON> congue, diam id ornare imperdiet, sapien urna pretium nisl, ut volutpat sapien arcu sed augue. Aliquam erat volutpat.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 20, "viewdCount": 2714, "readingTime": 4, "bookmark": {"count": 1321, "isBookmarked": true}, "like": {"count": 3309, "isLiked": true}, "authorId": 2, "categoriesId": [2, 11], "postType": "video"}, {"index": 8, "id": "9f1739eb-8f00-4c99-97f4-63544b6a2d12", "featuredImage": "https://images.pexels.com/photos/1955986/pexels-photo-1955986.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Mind games: How gaming can play a positive role in mental health", "desc": "<PERSON>is bibendum, felis sed interdum venenatis, turpis enim blandit mi, in porttitor pede justo eu massa. Donec dapibus. Duis at velit eu est congue elementum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 18, "viewdCount": 3800, "readingTime": 5, "bookmark": {"count": 1168, "isBookmarked": false}, "like": {"count": 1255, "isLiked": true}, "authorId": 4, "categoriesId": [4, 13], "postType": "audio"}, {"index": 9, "id": "0991ab0b-696f-4d7f-afe7-9c624eb8c050", "featuredImage": "https://images.pexels.com/photos/4009464/pexels-photo-4009464.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Microsoft announces a five-year commitment to create bigger opportunities for people with disabilities", "desc": "Duis bibendum. Morbi non quam nec dui luctus rutrum. Nulla tellus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 19, "viewdCount": 4515, "readingTime": 3, "bookmark": {"count": 3463, "isBookmarked": true}, "like": {"count": 2586, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "standard"}, {"index": 10, "id": "eae0e85d-db11-44fa-ac32-6c192f687e0c", "featuredImage": "https://images.pexels.com/photos/4871012/pexels-photo-4871012.jpeg?auto=compress&cs=tinysrgb&dpr=3&h=750&w=1260", "title": "360-degree video: How Microsoft deployed a datacenter to the bottom of the ocean", "desc": "We’re an online magazine dedicated to covering the best in international product design. We started as a little blog back in 2002 covering student work and over time", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 14, "viewdCount": 2378, "readingTime": 6, "bookmark": {"count": 3502, "isBookmarked": false}, "like": {"count": 773, "isLiked": true}, "authorId": 9, "categoriesId": [9, 18], "postType": "standard"}, {"index": 11, "id": "10f85e8a-a75a-485a-a1af-7555193fae1a", "featuredImage": "https://images.pexels.com/photos/5092763/pexels-photo-5092763.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "To cool datacenter servers, Microsoft turns to boiling liquid", "desc": "Nam ultrices, libero non mattis pulvinar, nulla pede ullamcorper augue, a suscipit nulla elit ac nulla. Sed vel enim sit amet nunc viverra dapibus. Nulla suscipit ligula in lacus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 39, "viewdCount": 1460, "readingTime": 3, "bookmark": {"count": 732, "isBookmarked": false}, "like": {"count": 1917, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "video"}, {"index": 12, "id": "f924ccfb-2622-497d-8072-df373c73cbaa", "featuredImage": "https://images.pexels.com/photos/5043948/pexels-photo-5043948.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Xbox connects people to help through Crisis Text Line", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 2, "viewdCount": 3418, "readingTime": 7, "bookmark": {"count": 1502, "isBookmarked": false}, "like": {"count": 913, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "standard"}, {"index": 13, "id": "6fa9d0b2-d48b-48f3-b862-cb38a4b6c755", "featuredImage": "https://images.pexels.com/photos/8065131/pexels-photo-8065131.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Unusual ‘machine in the woods’ taps clean energy deep underground for new Microsoft campus", "desc": "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 5, "viewdCount": 3894, "readingTime": 4, "bookmark": {"count": 3384, "isBookmarked": true}, "like": {"count": 1731, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "audio"}, {"index": 14, "id": "95de5119-776e-4c7f-9ff3-52482f9a2081", "featuredImage": "https://images.pexels.com/photos/346885/pexels-photo-346885.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "WNBA’s <PERSON> teams up with Xbox to empower young girls to pursue their hoop dreams ", "desc": "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 27, "viewdCount": 1038, "readingTime": 4, "bookmark": {"count": 3818, "isBookmarked": false}, "like": {"count": 1160, "isLiked": false}, "authorId": 2, "categoriesId": [2, 11], "postType": "standard"}, {"index": 15, "id": "499ea541-5d70-4978-8b41-eeb3987024bd", "featuredImage": "https://images.pexels.com/photos/1371360/pexels-photo-1371360.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "New tools and advocacy support Black pregnant and postpartum mothers to save lives ", "desc": "In sagittis dui vel nisl. Duis ac nibh. Fusce lacus purus, aliquet at, feugiat non, pretium quis, lectus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 3, "viewdCount": 584, "readingTime": 5, "bookmark": {"count": 1308, "isBookmarked": false}, "like": {"count": 2182, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "audio"}, {"index": 16, "id": "20992b33-1bae-4867-b0fb-dec76ac1e0da", "featuredImage": "https://images.pexels.com/photos/2174656/pexels-photo-2174656.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "New Orleans Game Camp creates opportunities for a more diverse pipeline into the gaming industry ", "desc": "Quisque porta volutpat erat. Quisque erat eros, viverra eget, congue eget, semper rutrum, nulla. Nunc purus.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 19, "viewdCount": 52, "readingTime": 5, "bookmark": {"count": 482, "isBookmarked": true}, "like": {"count": 627, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "video"}, {"index": 17, "id": "71fde139-b176-44e3-8514-2296f1d888d3", "featuredImage": "https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Unusual ‘machine in the woods’ taps clean energy deep underground for new Microsoft campus ", "desc": "<PERSON>sce posuere felis sed lacus. Morbi sem mauris, la<PERSON><PERSON> ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 1, "viewdCount": 2996, "readingTime": 2, "bookmark": {"count": 2515, "isBookmarked": false}, "like": {"count": 1277, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "standard"}, {"index": 18, "id": "f63fbb28-94a6-4a87-bfe4-5ac6a9e7990b", "featuredImage": "https://images.pexels.com/photos/2132126/pexels-photo-2132126.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "New ideas and energized employees fuel Microsoft’s ongoing efforts toward racial equity ", "desc": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 1, "viewdCount": 129, "readingTime": 7, "bookmark": {"count": 3976, "isBookmarked": false}, "like": {"count": 3126, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "standard"}, {"index": 19, "id": "2980bc99-74c7-4507-a28d-78966ec71acf", "featuredImage": "https://images.pexels.com/photos/2106776/pexels-photo-2106776.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "With lessons rooted in social justice movements, Minecraft’s Good Trouble aims to help build a better world.", "desc": "<PERSON>ulla ut erat id mauris vulputate elementum. Nullam varius. Nulla facilisi.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 45, "viewdCount": 575, "readingTime": 7, "bookmark": {"count": 2264, "isBookmarked": true}, "like": {"count": 1642, "isLiked": false}, "authorId": 7, "categoriesId": [7, 16], "postType": "standard"}, {"index": 20, "id": "c8f8e679-d434-4a1c-9448-efc44814d11e", "featuredImage": "https://images.pexels.com/photos/6300606/pexels-photo-6300606.jpeg?auto=compress&cs=tinysrgb&dpr=2&h=750&w=1260", "title": "Early community involvement has ‘meant a lot’ in Atlanta as Microsoft plans to expand its presence ", "desc": "Mae<PERSON>nas leo odio, condimentum id, luctus nec, molestie sed, justo. Pellentesque viverra pede ac diam. Cras pellentesque volutpat dui.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 36, "viewdCount": 2783, "readingTime": 4, "bookmark": {"count": 1653, "isBookmarked": true}, "like": {"count": 505, "isLiked": false}, "authorId": 10, "categoriesId": [10, 19], "postType": "standard"}, {"index": 21, "id": "293b349d-5112-4eb4-a28b-5db8dbd51aed", "featuredImage": ".", "title": "Xbox Ambassadors build community, reinforce inclusion and positive gaming ", "desc": "Morbi porttitor lorem id ligula. Suspendisse ornare consequat lectus. In est risus, auctor sed, tristique in, tempus sit amet, sem.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 6, "viewdCount": 979, "readingTime": 6, "bookmark": {"count": 3728, "isBookmarked": true}, "like": {"count": 1234, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "standard"}, {"index": 22, "id": "fa96a568-e4ce-4beb-a2ac-120a164e0e4a", "featuredImage": ".", "title": "New tools help companies pinpoint — and fix — the unexpected things that make remote work so draining ", "desc": "Curabitur in libero ut massa volutpat convallis. Morbi odio odio, elementum eu, interdum eu, tincidunt in, leo. Maecenas pulvinar lobortis est.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 7, "viewdCount": 1501, "readingTime": 6, "bookmark": {"count": 3278, "isBookmarked": false}, "like": {"count": 1803, "isLiked": false}, "authorId": 7, "categoriesId": [7, 16], "postType": "standard"}, {"index": 23, "id": "0e48196f-644b-46d4-99f8-9ece6761218c", "featuredImage": ".", "title": "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.", "desc": "Aliquam quis turpis eget elit sodales scelerisque. <PERSON><PERSON><PERSON> sit amet eros. Suspendisse accumsan tortor quis turpis.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 14, "viewdCount": 3004, "readingTime": 7, "bookmark": {"count": 2090, "isBookmarked": false}, "like": {"count": 2811, "isLiked": true}, "authorId": 4, "categoriesId": [4, 13], "postType": "standard"}, {"index": 24, "id": "5d0ffbb2-81f2-4de2-90de-808dbab4707f", "featuredImage": ".", "title": "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.", "desc": "Phasellus sit amet erat. Nulla tempus. Vivamus in felis eu sapien cursus vestibulum.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 5, "viewdCount": 2580, "readingTime": 6, "bookmark": {"count": 449, "isBookmarked": false}, "like": {"count": 507, "isLiked": true}, "authorId": 3, "categoriesId": [3, 12], "postType": "standard"}, {"index": 25, "id": "89ed1f1c-897a-41e6-81f0-cd1f1f7349e4", "featuredImage": ".", "title": "In hac habitasse platea dictumst. Etiam faucibus cursus urna. Ut tellus.", "desc": "Integer tincidunt ante vel ipsum. Praesent blandit lacinia erat. Vestibulum sed magna at nunc commodo placerat.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 43, "viewdCount": 204, "readingTime": 5, "bookmark": {"count": 1381, "isBookmarked": true}, "like": {"count": 3503, "isLiked": false}, "authorId": 5, "categoriesId": [5, 14], "postType": "video"}, {"index": 26, "id": "060aaba3-5f51-461c-a137-6ebdfbb04448", "featuredImage": ".", "title": "Aenean fermentum. Donec ut mauris eget massa tempor convallis. <PERSON>ulla neque libero, convallis eget, elei<PERSON>d luctus, ultricies eu, nibh.", "desc": "Phasellus in felis. Donec semper sapien a libero. Nam dui.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 24, "viewdCount": 2592, "readingTime": 5, "bookmark": {"count": 224, "isBookmarked": true}, "like": {"count": 2125, "isLiked": false}, "authorId": 4, "categoriesId": [4, 13], "postType": "standard"}, {"index": 27, "id": "42d59591-7104-4fef-a3b6-9c1cb63d3317", "featuredImage": ".", "title": "Mae<PERSON>nas leo odio, condimentum id, luctus nec, molestie sed, justo. Pellentesque viverra pede ac diam. Cras pellentesque volutpat dui.", "desc": "<PERSON>sce posuere felis sed lacus. Morbi sem mauris, la<PERSON><PERSON> ut, rhoncus aliquet, pulvinar sed, nisl. Nunc rhoncus dui vel sem.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 28, "viewdCount": 1031, "readingTime": 3, "bookmark": {"count": 840, "isBookmarked": false}, "like": {"count": 2454, "isLiked": false}, "authorId": 3, "categoriesId": [3, 12], "postType": "standard"}, {"index": 28, "id": "b8c19738-84c1-4c7a-81b6-3f1d29cb87a6", "featuredImage": ".", "title": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.", "desc": "<PERSON><PERSON>s enim leo, rhoncus sed, vestibulum sit amet, cursus id, turpis. <PERSON><PERSON><PERSON> aliquet, massa id lobortis convallis, tortor risus dapibus augue, vel accumsan tellus nisi eu orci. Mauris lacinia sapien quis libero.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 7, "viewdCount": 2399, "readingTime": 3, "bookmark": {"count": 567, "isBookmarked": false}, "like": {"count": 1340, "isLiked": false}, "authorId": 5, "categoriesId": [5, 14], "postType": "video"}, {"index": 29, "id": "51da9fcf-1f42-4713-bb2c-142914ca38b4", "featuredImage": ".", "title": "Pellentesque at nulla. Suspendisse potenti. Cras in purus eu magna vulputate luctus.", "desc": "Proin interdum mauris non ligula pellentesque ultrices. Phasellus id sapien in sapien iaculis congue. Vivamus metus arcu, adipiscing molestie, hendrerit at, vulputate vitae, nisl.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 7, "viewdCount": 4216, "readingTime": 6, "bookmark": {"count": 357, "isBookmarked": false}, "like": {"count": 2709, "isLiked": false}, "authorId": 1, "categoriesId": [1, 10], "postType": "standard"}, {"index": 30, "id": "2ef4f6f6-c812-4768-98a6-8d3b9e8c4606", "featuredImage": ".", "title": "Praesent blandit. Nam nulla. Integer pede justo, lacinia eget, tin<PERSON><PERSON> eget, tempus vel, pede.", "desc": "Curabitur gravida nisi at nibh. In hac habitasse platea dictumst. Aliquam augue quam, sollicitudin vitae, consectetuer eget, rutrum at, lorem.", "date": "May 20, 2021", "href": "/blog/single", "commentCount": 10, "viewdCount": 2809, "readingTime": 5, "bookmark": {"count": 1598, "isBookmarked": true}, "like": {"count": 327, "isLiked": false}, "authorId": 9, "categoriesId": [9, 18], "postType": "audio"}]