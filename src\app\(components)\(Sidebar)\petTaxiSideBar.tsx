"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  Sidebar,
  SidebarBody,
  SidebarLink,
} from "@/components/ui/sidebar-menu";
import {
  Home,
  Building2,
  MapPin,
  Settings2,
  PawPrint,
  ImageIcon,
  FileText,
  Truck,
  CalendarCheck2,
  LogOut,
  Car,
  RefreshCw,
  CreditCard,
  User,
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import PawBookingLogo from "@/shared/PawBookingLogo";
import Image from "next/image";
import LanguageWithFlags from "../(Header)/LanguageWithFlags";
import SwitchDarkLightMode from "@/shared/SwitchDarkLightMode";
import AccountSwitch from "../(Header)/AccountSwitch";

interface AppSidebarProps {
  children: React.ReactNode;
  defaultOpen?: boolean;
  taxiData: any;
  propertyTypes: string[];
}

export default function AppSidebar({
  children,
  defaultOpen = false,
  taxiData,
  propertyTypes,
}: AppSidebarProps) {
  const links = useMemo(
    () => [
      {
        label: "Bugün",
        href: "/petTaxi/account",
        icon: <Home className="h-5 w-5 text-muted-foreground" />,
      },
      {
        label: "İşletme Bilgileri",
        href: "/petTaxi/account/pet-taxi-informations",
        icon: <Building2 className="h-5 w-5 text-muted-foreground" />,
        subItems: [
          {
            label: "Taksi Bilgileri",
            href: "/petTaxi/account/pet-taxi-informations",
            icon: <Car className="h-4 w-4 text-muted-foreground" />,
          },
          {
            label: "Ödeme Bilgileri",
            href: "/petTaxi/account/billing",
            icon: <CreditCard className="h-4 w-4 text-muted-foreground" />,
          },
          {
            label: "Politika ve Sözleşmeler",
            href: "/petTaxi/account/policy",
            icon: <FileText className="h-4 w-4 text-muted-foreground" />,
          },
          {
            label: "Kullanıcı Bilgileri",
            href: "/petTaxi/account/user",
            icon: <User className="h-4 w-4 text-muted-foreground" />,
          },
        ],
      },
      {
        label: "Araçlar",
        href: "/petTaxi/account/vehicles",
        icon: <Truck className="h-5 w-5 text-muted-foreground" />,
      },
      {
        label: "Rezevasyonlar",
        href: "/petTaxi/account/reservations",
        icon: <CalendarCheck2 className="h-5 w-5 text-muted-foreground" />,
      },
    ],
    []
  );

  const [open, setOpen] = useState(defaultOpen);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();

  const getActiveParent = () => {
    return links.find(
      (link) =>
        link.subItems?.some((subItem) => subItem.href === pathname) ||
        link.href === pathname
    );
  };

  useEffect(() => {
    const activeParent = getActiveParent();

    if (activeParent && activeParent.subItems) {
      if (open) {
        if (!expandedItems.includes(activeParent.label)) {
          setExpandedItems([activeParent.label]);
        }
      } else {
        setExpandedItems((prev) =>
          prev.filter((item) => item === activeParent.label)
        );
      }
    } else if (!open) {
      setExpandedItems([]);
    }
  }, [pathname, open, links]);

  const handleSidebarToggle = (newOpen: boolean) => {
    setOpen(newOpen);

    if (!newOpen) {
      const activeParent = getActiveParent();
      if (activeParent && activeParent.subItems) {
        setExpandedItems([activeParent.label]);
      } else {
        setExpandedItems([]);
      }
    }
  };

  const isActive = (href: string) => pathname === href;

  const hasActiveChild = (link: any) => {
    return link.subItems?.some((subItem: any) => isActive(subItem.href));
  };

  return (
    <>
      {taxiData?.status === "approved" ? (
        <div className="flex flex-col md:flex-row h-screen w-full">
          <Sidebar open={open} setOpen={handleSidebarToggle}>
            <SidebarBody className="justify-between gap-10">
              <div
                className={cn(
                  "flex flex-1 flex-col",
                  open ? "overflow-x-hidden overflow-y-auto" : "overflow-hidden"
                )}
              >
                {open ? <Logo open={open} animate={true} /> : <LogoIcon />}
                <div className="mt-8 flex flex-col gap-1">
                  {links.map((link, idx) => (
                    <div key={idx}>
                      <SidebarLink
                        link={{
                          ...link,
                          isActive: isActive(link.href) || hasActiveChild(link),
                        }}
                        expandedItems={expandedItems}
                        setExpandedItems={setExpandedItems}
                        isActive={isActive(link.href) || hasActiveChild(link)}
                        pathname={pathname}
                      />
                    </div>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <AccountSwitch propertyTypes={propertyTypes} open={open} />
                <SidebarLink
                  link={{
                    label: taxiData?.petTaxiName,
                    href: "/profile",
                    icon: (
                      <Image
                        src={taxiData?.logo?.src}
                        className="h-16 w-16 shrink-0 rounded-full"
                        width={100}
                        height={100}
                        alt="taxi-logo"
                      />
                    ),
                  }}
                  pathname={pathname}
                />
              </div>
            </SidebarBody>
          </Sidebar>
          <main className="flex-1 overflow-auto bg-neutral-50 dark:bg-neutral-900">
            <div className="p-4">{children}</div>
          </main>
        </div>
      ) : (
        <main className="flex-1 overflow-auto bg-neutral-50 dark:bg-neutral-900">
          <div className="p-4">{children}</div>
        </main>
      )}
    </>
  );
}

export const Logo = ({
  open,
  animate,
}: {
  open: boolean;
  animate: boolean;
}) => {
  return (
    <div className="flex items-center justify-between">
      <a
        href="/petTaxi/account"
        className="relative z-20 flex items-center py-1 text-sm font-normal text-black w-full"
      >
        <motion.span
          initial={false}
          animate={{
            opacity: animate ? (open ? 1 : 0) : 1,
            x: animate ? (open ? 0 : -20) : 0,
          }}
          transition={{
            duration: 0.25,
            ease: [0.4, 0, 0.2, 1],
          }}
          className="font-medium text-black dark:text-white"
        >
          <PawBookingLogo className="size-12" />
        </motion.span>
      </a>
      <div className="flex items-center gap-3">
        <LanguageWithFlags />
        <div className="hidden h-5 self-center border-l border-neutral-300 dark:border-neutral-500 lg:block"></div>
        <SwitchDarkLightMode />
      </div>
    </div>
  );
};

export const LogoIcon = () => {
  return (
    <div className="flex items-center justify-center">
      <PawBookingLogo className="size-12 self-center" />
    </div>
  );
};
