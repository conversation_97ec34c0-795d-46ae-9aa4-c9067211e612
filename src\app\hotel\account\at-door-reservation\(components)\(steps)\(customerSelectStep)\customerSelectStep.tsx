import React, { useState, useEffect } from "react";
import Input from "@/shared/Input";
import Label from "@/components/Label";
import AddCustomerModal from "@/app/hotel/account/hotel-customer/(components)/addCustomer";
import { setCheckoutData } from "@/store/features/checkout/checkout-data-slice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";

interface Customer {
  _id: string;
  fullName: string;
  email: string;
  phone: string;
}

interface CustomerSelectStepProps {
  hotelToken: string | undefined;
  hotelData: any;
  allHotelCustomers: Customer[];
}

const CustomerSelectStep: React.FC<CustomerSelectStepProps> = ({
  hotelToken,
  hotelData,
  allHotelCustomers,
}) => {
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );

  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const normalizePhone = (phone: string) => phone.replace(/^0+/, "");

  useEffect(() => {
    if (checkoutData.hotelCustomer) {
      const customer = allHotelCustomers.find(
        (c) => c._id === checkoutData.hotelCustomer
      );
      if (customer) {
        setSelectedCustomer(customer);
      }
    }
  }, [checkoutData.hotelCustomer, allHotelCustomers]);

  useEffect(() => {
    if (searchTerm.trim().length < 3) {
      setFilteredCustomers([]);
      return;
    }
    const normalizedSearchTerm = normalizePhone(searchTerm.toLowerCase());

    const filtered = allHotelCustomers.filter((customer) => {
      const normalizedPhone = normalizePhone(customer.phone.toLowerCase());
      return (
        customer.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        normalizedPhone.includes(normalizedSearchTerm)
      );
    });

    setFilteredCustomers(filtered);
    setCurrentPage(1);
  }, [searchTerm, allHotelCustomers]);

  const paginatedCustomers = filteredCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  useEffect(() => {
    if (selectedCustomer) {
      dispatch(
        setCheckoutData({
          hotelCustomer: selectedCustomer._id,
          pet: [],
          servicePet: [],
          transportationServiceChoice: null,
          channel: "",
          paymentType: "",
        })
      );
    }
  }, [selectedCustomer]);

  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col-reverse md:flex-row md:justify-between md:items-center">
        <div className="flex flex-col gap-4">
          <Label>Müşteri Ara</Label>
          <Input
            name="customer"
            type="text"
            placeholder="İsim, Telefon veya E-posta"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              if (e.target.value.trim().length < 3) {
                setSelectedCustomer(null);
                dispatch(
                  setCheckoutData({
                    hotelCustomer: null,
                    pet: [],
                    servicePet: [],
                    transportationServiceChoice: null,
                    channel: "",
                    paymentType: "",
                  })
                );
              }
            }}
            className="!w-56"
          />
          {searchTerm.trim().length >= 1 && searchTerm.trim().length < 3 && (
            <p className="text-xs text-red-500 -mt-2">
              Arama yapmak için en az 3 karakter giriniz.
            </p>
          )}
          {searchTerm.trim().length >= 3 && filteredCustomers.length === 0 && (
            <p className="text-xs text-red-500 -mt-2">Müşteri bulunamadı.</p>
          )}
        </div>
        <div className="flex gap-2 mb-5 md:mb-0">
          <AddCustomerModal hotelToken={hotelToken} hotelData={hotelData} />
        </div>
      </div>

      {selectedCustomer && !searchTerm.trim() && (
        <div className="p-4 border-2 border-secondary-6000 bg-secondary-50 dark:bg-neutral-700 rounded-lg mt-4">
          <div className="flex justify-between items-center">
            <div>
              <div className="font-semibold text-gray-800 dark:text-white">
                {selectedCustomer.fullName}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {selectedCustomer.phone}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {selectedCustomer.email}
              </div>
            </div>
            <span className="px-3 py-1 text-sm font-medium text-white bg-secondary-6000 rounded-lg shadow">
              Seçili
            </span>
          </div>
        </div>
      )}

      {searchTerm.trim().length >= 3 && filteredCustomers.length > 0 && (
        <>
          <ul className="mt-4 space-y-2">
            {paginatedCustomers.map((customer) => (
              <li
                key={customer._id}
                className={`flex justify-between items-center p-4 border rounded-lg cursor-pointer hover:bg-blue-50 dark:hover:bg-neutral-700 transition ${
                  selectedCustomer?._id === customer._id
                    ? "border-2 border-secondary-6000 bg-secondary-50 dark:bg-neutral-700"
                    : "border-gray-300"
                }`}
                onClick={() => setSelectedCustomer(customer)}
              >
                <div>
                  <div className="font-semibold text-gray-800 dark:text-white">
                    {customer.fullName}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {customer.phone}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {customer.email}
                  </div>
                </div>
                {selectedCustomer?._id === customer._id && (
                  <div className="flex items-center px-3 py-1 text-sm font-medium text-white bg-secondary-6000 rounded-lg shadow">
                    Seçildi
                  </div>
                )}
              </li>
            ))}
          </ul>

          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              {currentPage > 1 && (
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  Önceki
                </button>
              )}
              <div>
                Sayfa {currentPage} / {totalPages}
              </div>
              {currentPage < totalPages && (
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
                >
                  Sonraki
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CustomerSelectStep;
