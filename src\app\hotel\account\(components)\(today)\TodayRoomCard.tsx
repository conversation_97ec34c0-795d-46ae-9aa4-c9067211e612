"use client";
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { useWindowSize } from "react-use";
import TodayRoomCardMobile from "./(mobile)/TodayRoomCardMobile";
import RoomGroups from "./RoomGroups";
import RoomInformation from "./RoomInformation";
import PetInformation from "./PetInformation";
import VetInformation from "./VetInformation";
import PetOwnerInformation from "./PetOwnerInformation";
import BalanceInformation from "./Balance";
import IconInfo from "@/shared/icons/Info";
import { But<PERSON> } from "@/components/ui/button";
import PawPlus from "@/shared/PawPlus";
import PlusFeatureModal from "@/components/PlusFeatureModal";
import { useRouter } from "next/navigation";
import FilterTabs from "./FilterTabs";
import ImageModal from "@/components/ImageModal";

interface TodayRoomCardProps {
  roomGroupData: any;
  hotelToken: string | undefined;
  membershipData: any;
}

export function formatPhoneNumber(phoneNumber: string) {
  return phoneNumber.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})$/, "+$1 $2 $3 $4");
}

const TodayRoomCard: React.FC<TodayRoomCardProps> = ({
  roomGroupData,
  hotelToken,
  membershipData,
}) => {
  const translate = useTranslations("TodayRoomCard");
  const [openItems, setOpenItems] = useState<string[]>([
    roomGroupData[0].roomGroupName,
  ]);
  const [selectedFilter, setSelectedFilter] = useState<string>("all");
  const [statusCounts, setStatusCounts] = useState({
    waitingForApproval: 0,
    waitingForCheckIn: 0,
    waitingForCheckOut: 0,
    checkedOut: 0,
    checkedIn: 0,
    empty: 0,
  });
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const router = useRouter();
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | string[]>("");
  const [imageFileName, setImageFileName] = useState("photo");
  const [activeTab, setActiveTab] = useState("roomInformation");

  function IsOpen(room: any) {
    setSelectedRoom(room);
    setIsOpen(true);
  }

  const handleButtonClick = () => {
    if (membershipData?.membershipType === "free") {
      setModalIsVisible(true);
    } else {
      router.push("/hotel/account/at-door-reservation");
    }
  };

  const windowWidth = useWindowSize().width;

  useEffect(() => {
    if (windowWidth > 767) {
      setIsMobile(false);
    } else {
      setIsMobile(true);
    }
  }, [windowWidth]);

  // use this instead of selectedRoom because selectedRoom does not update the state unless the modal is closed.
  const updatedRoomData = roomGroupData
    ?.flatMap((group: any) => group.rooms)
    ?.find((room: any) => room._id === selectedRoom?._id);

  const handleOpenImageModal = (urls: string | string[], fileName: string) => {
    setImageUrl(urls);
    setImageFileName(fileName || "photo");
    setImageModalOpen(true);
  };

  const handleModalIsOpen = (open: boolean) => {
    setIsOpen(open);
    setActiveTab("petInformation");
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FilterTabs
            selected={selectedFilter}
            onSelect={setSelectedFilter}
            statusCounts={statusCounts}
          />
          <Popover>
            <PopoverTrigger asChild>
              <div className="cursor-pointer text-secondary-6000">
                <IconInfo />
              </div>
            </PopoverTrigger>
            <PopoverContent side="bottom" align="start">
              <div className="flex flex-col gap-0.5">
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#C1E1C1]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Dolu Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#D3D3D3]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Boş Oda
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#FFD59E]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Onay Bekliyor
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#B3D9FF]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Bugün Check-in
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#FFB3B3]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Bugün Check-out
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#FFDB58]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Temizlik Bekliyor
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="size-2.5 rounded-full bg-[#D2B48C]"></span>
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-400">
                    Rezervasyona Kapalı Oda
                  </span>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
        <div className="flex justify-end items-center gap-2">
          <div className="flex max-w-56 items-start justify-start">
            <Button
              className={`${
                membershipData?.membershipType === "free"
                  ? "w-44 md:w-auto bg-neutral-50 dark:bg-neutral-900 hover:bg-secondary-6000 text-secondary-6000 dark:text-white hover:text-white border-2 border-secondary-6000 hover:border-white"
                  : "bg-secondary-6000 hover:bg-secondary-700 text-white"
              }`}
              onClick={handleButtonClick}
            >
              {membershipData?.membershipType === "free" && (
                <PawPlus width="30" height="30" />
              )}
              Rezervasyon Oluştur
            </Button>
          </div>
        </div>
      </div>
      <RoomGroups
        roomGroupData={roomGroupData}
        IsOpen={IsOpen}
        openItems={openItems}
        setOpenItems={setOpenItems}
        selectedFilter={selectedFilter}
        onStatusCountsChange={setStatusCounts}
      />
      {!isMobile && selectedRoom && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="h-[calc(100vh-50px)] overflow-y-auto max-w-2xl">
            <Tabs
              defaultValue="roomInformation"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="mt-2 flex shrink-0 gap-1 bg-white text-black dark:bg-inherit dark:text-inherit">
                {(selectedRoom?.reservation?.status === "confirmed" ||
                  selectedRoom?.reservation?.status === "waitingForCheckIn" ||
                  selectedRoom?.reservation?.status === "checkedIn" ||
                  selectedRoom?.reservation?.status === "waitingForCheckOut" ||
                  selectedRoom?.reservation?.status === "checkedOut") && (
                  <TabsTrigger
                    value="balance"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                  >
                    Ödeme Bilgileri
                  </TabsTrigger>
                )}
                <TabsTrigger
                  value="roomInformation"
                  className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                >
                  Oda Bilgileri
                </TabsTrigger>
                {(selectedRoom?.reservation?.status === "confirmed" ||
                  selectedRoom?.reservation?.status === "waitingForCheckIn" ||
                  selectedRoom?.reservation?.status === "checkedIn" ||
                  selectedRoom?.reservation?.status === "waitingForCheckOut" ||
                  selectedRoom?.reservation?.status === "checkedOut") && (
                  <TabsTrigger
                    value="petInformation"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                  >
                    Pet Bilgileri
                  </TabsTrigger>
                )}
                {(selectedRoom?.reservation?.status === "confirmed" ||
                  selectedRoom?.reservation?.status === "waitingForCheckIn" ||
                  selectedRoom?.reservation?.status === "checkedIn" ||
                  selectedRoom?.reservation?.status === "waitingForCheckOut" ||
                  selectedRoom?.reservation?.status === "checkedOut") && (
                  <TabsTrigger
                    value="contactInformation"
                    className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                  >
                    İletişim Bilgileri
                  </TabsTrigger>
                )}
              </TabsList>
              <TabsContent value="roomInformation">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <RoomInformation
                    selectedRoom={selectedRoom}
                    hotelToken={hotelToken}
                    setIsOpen={setIsOpen}
                  />
                </div>
              </TabsContent>
              <TabsContent value="balance">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <BalanceInformation
                    selectedRoom={updatedRoomData}
                    hotelToken={hotelToken}
                  />
                </div>
              </TabsContent>
              <TabsContent value="petInformation">
                <div className="mt-4">
                  <DialogHeader>
                    <DialogTitle className="sr-only"></DialogTitle>
                    <DialogDescription className="sr-only"></DialogDescription>
                  </DialogHeader>
                  <Separator className="my-2" />
                  <PetInformation
                    selectedRoom={selectedRoom}
                    onImageClick={(url, name) =>
                      handleOpenImageModal(url, name)
                    }
                  />
                </div>
              </TabsContent>
              <TabsContent value="contactInformation">
                <div className="mt-2">
                  <Tabs defaultValue="petOwnerContact">
                    <TabsList className="mt-2 flex shrink-0 gap-1 bg-white text-black dark:bg-inherit dark:text-inherit">
                      <TabsTrigger
                        value="petOwnerContact"
                        className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                      >
                        Pet Sahibi
                      </TabsTrigger>
                      <TabsTrigger
                        value="veterinaryContact"
                        className="data-[state=active]:bg-secondary-6000 data-[state=active]:text-white"
                      >
                        Sorumlu Veteriner
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="petOwnerContact">
                      <PetOwnerInformation selectedRoom={selectedRoom} />
                    </TabsContent>
                    <TabsContent value="veterinaryContact">
                      <VetInformation selectedRoom={selectedRoom} />
                    </TabsContent>
                  </Tabs>
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}
      <ImageModal
        isOpen={imageModalOpen}
        setIsOpen={setImageModalOpen}
        imageUrl={imageUrl}
        fileName={imageFileName}
        setModalIsOpen={handleModalIsOpen}
      />
      {isMobile && selectedRoom && (
        <TodayRoomCardMobile
          selectedRoom={updatedRoomData}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          hotelToken={hotelToken}
          onImageClick={(url, name) => handleOpenImageModal(url, name)}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      )}
      {modalIsVisible && (
        <PlusFeatureModal
          isOpen={modalIsVisible}
          setIsOpen={setModalIsVisible}
          message="Bu özellik sadece Plus üyelikler için geçerlidir."
        />
      )}
    </>
  );
};

export default TodayRoomCard;
