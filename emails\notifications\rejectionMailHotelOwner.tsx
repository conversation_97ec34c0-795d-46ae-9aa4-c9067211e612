import React, { ReactNode } from "react";
import mailImage from "../PetOwnerConfirmation.png";
import { Img, Link, Section } from "@react-email/components";
const Layout: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <div
      style={{
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#fff7f3",
        padding: "20px",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          maxWidth: "600px",
          background: "#ffffff",
          padding: "20px",
          borderRadius: "10px",
          textAlign: "center",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        {children}
      </div>
    </div>
  );
};

const rejectionMailHotelOwner: React.FC<{ username: string }> = ({
  username,
}) => {
  return (
    <Layout>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "0px",
          backgroundColor: "white",
          borderRadius: "10px",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div
          style={{
            position: "relative",
          }}
        >
          <Img
            src="https://i.hizliresim.com/2ujdd3k.png"
            style={{ width: "100%", height: "auto" }}
          />
          <div
            style={{
              position: "absolute",
              top: "40%",
              left: "65px",
              transform: "translateY(-50%)",
            }}
          >
            <h2
              style={{
                color: "#333",
                top: "50%",
                left: "50px",
              }}
            >
              Merhaba,
            </h2>
            <p>
              Yapılan değerlendirme sonucunda başvurunuzun olumsuz
              <br />
              sonuçlandığını üzülerek bildiriyoruz.
              <br />
              İlginiz için teşekkür ederiz.
            </p>
            <p>
              Sevgiler,
              <br /> <strong>PawBooking Ekibi 🧡</strong>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default rejectionMailHotelOwner;
