import React from "react";
import type { FC } from "react";
import DeleteHotelService from "../DeleteHotelService";
import UpdateVeterinaryService from "../(updateService)/UpdateVeterinaryService";
import { veterinaryServiceApiTypes } from "@/types/hotel/services/serviceTypes";

export interface VeterinaryCardProps {
  service: veterinaryServiceApiTypes;
  hotelToken: string | undefined;
}
const VeterinaryCard: FC<VeterinaryCardProps> = ({ service, hotelToken }) => {
  const { serviceName, total, isActive } = service.serviceData;
  const { veterinarianName } = service.serviceData.serviceDetails;
  const { _id } = service;

  return (
    <div className="border bg-secondary-6000/5 dark:bg-neutral-800 rounded-[45px] p-5 shadow-sm hover:border-secondary-6000/45 duration-200">
      <div className="flex justify-between items-center">
        <div className="flex">
          <span className="relative flex h-2.5 w-2.5 mt-[7px] mr-2">
            <span
              className={`animate-ping absolute inline-flex h-full w-full rounded-full ${
                isActive ? "bg-green-700" : "bg-red-700"
              } opacity-75`}
            ></span>
            <span
              className={`relative inline-flex rounded-full h-2.5 w-2.5 ${
                isActive ? "bg-green-600" : "bg-red-600"
              }`}
            ></span>
          </span>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <h2 className="font-semibold capitalize text-neutral-900 dark:text-white">
                <span className="line-clamp-1">{serviceName}</span>
              </h2>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Veteriner Adı:</div>
              <div> {veterinarianName}</div>
            </div>
            <div className="flex items-center space-x-1.5 text-sm text-neutral-500 dark:text-neutral-400">
              <div>Fiyat:</div>
              <div> {total}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-3 text-sm text-neutral-500 dark:text-neutral-400 mt-4">
        <UpdateVeterinaryService service={service} hotelToken={hotelToken} />
        <DeleteHotelService
          hotelToken={hotelToken}
          serviceId={_id}
          serviceName={serviceName}
        />
      </div>
    </div>
  );
};

export default VeterinaryCard;
