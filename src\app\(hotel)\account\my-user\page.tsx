import React from "react";
import { cookies } from "next/headers";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import MyUserContainer from "./(components)/MyUserContainer";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import getHotelNextPaymentDate from "@/actions/(protected)/hotel/getHotelNextPaymentDate";
import getHotelSubscriptionStatus from "@/actions/(protected)/hotel/getHotelSubscriptionStatus";
import getHotelCards from "@/actions/(protected)/hotel/getHotelCards";

const MyUser = async () => {
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);
  const hotelNextPaymentDate = await getHotelNextPaymentDate();
  const hotelSubscriptionStatus = await getHotelSubscriptionStatus();
  const hotelCards = await getHotelCards();

  return (
    <div className="min-h-screen container">
      <MyUserContainer
        membershipData={membershipData?.data}
        hotelNextPaymentDate={hotelNextPaymentDate?.data?.nextPaymentDate}
        hotelSubscriptionStatus={hotelSubscriptionStatus?.data?.status}
        hotelData={hotelData?.data}
        hotelToken={hotelToken}
        hotelCards={hotelCards?.data?.data}
      />
    </div>
  );
};

export default MyUser;
