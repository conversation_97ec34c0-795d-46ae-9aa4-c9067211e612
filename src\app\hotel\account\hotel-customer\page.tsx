import React from "react";
import { cookies } from "next/headers";
import AddCustomerModal from "./(components)/addCustomer";
import HotelCustomerTable from "./(components)/HotelCustomerTable";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import getAllHotelCustomers from "@/actions/(protected)/hotel/getAllHotelCustomers";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import { redirect } from "next/navigation";

const HotelCustomerPage = async () => {
  const allHotelCustomers = await getAllHotelCustomers();
  const cookieStore = cookies();
  const hotelToken = cookieStore.get("token")?.value || undefined;
  const hotelData = await getMyHotel();
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  if (membershipData?.data?.membershipType !== "plus") {
    redirect("/hotel/account");
  }

  return (
    <div className="container pt-8">
      <AddCustomerModal hotelToken={hotelToken} hotelData={hotelData?.data} />
      <HotelCustomerTable
        allHotelCustomers={allHotelCustomers?.data?.hotelCustomers}
        hotelToken={hotelToken}
      />
    </div>
  );
};

export default HotelCustomerPage;
