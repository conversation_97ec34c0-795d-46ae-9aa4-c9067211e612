"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { getUpdateActionRequired } from "@/actions/(protected)/hotel/getUpdateActionRequired";
import Link from "next/link";

const ReservationsButton = () => {
  const handleClick = async () => {
    await getUpdateActionRequired(false);
  };

  return (
    <Link href="/account/reservations?tab=waitingForAction&page=1">
      <Button
        className="bg-secondary-700 hover:bg-secondary-700 text-white max-sm:text-xs"
        onClick={handleClick}
      >
        Rezervasyonlara Git
        <span className="relative flex h-3 w-3 sm:h-3.5 sm:w-3.5">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-300 opacity-75"></span>
          <span className="relative inline-flex rounded-full h-3 w-3 sm:h-3.5 sm:w-3.5 bg-green-300"></span>
        </span>
      </Button>
    </Link>
  );
};

export default ReservationsButton;
