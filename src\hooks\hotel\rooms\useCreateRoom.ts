import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";
import { uploadMultiToS3, ImageResizeFitType } from "@/lib/s3BucketHelper";
import type { roomInputsTypes } from "@/types/hotel/rooms/createRoomTypes";
import slug from "slug";

export const useCreateRoom = () => {
  const { toast } = useToast();

  // CREATES NEW ROOMS
  const handleRoom = async (
    roomImageArray: string[],
    hotelToken: string | undefined,
    roomInputs: roomInputsTypes,
    setModalImage: React.Dispatch<React.SetStateAction<string[]>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setCreateRoomsIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    resetForm: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (hotelToken) {
      const requestBody = {
        roomName: roomInputs.roomGroupName,
        roomCapacity: Number(roomInputs.roomCapacity),
        roomGroupList: {
          roomCount: Number(roomInputs.roomCount) || 1,
          roomGroupName: roomInputs.roomGroupName,
        },
        roomNameStartingNumber: Number(roomInputs.roomNameStartingNumber),
        petType: roomInputs.petType,
        roomDescription: roomInputs.roomDescription,
        roomFeatures: roomInputs.roomFeatures,
        images: roomImageArray,
      };

      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/rooms`,
          {
            method: "POST",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          }
        );
        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setCreateRoomsIsOpen(false);
          resetForm();
          setLoading(false);
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Oda Ekleme",
          description: `${roomInputs.roomCount} oda eklendi.`,
        });
        setModalImage([]);
        setLoading(false);
        setCreateRoomsIsOpen(false);
        resetForm();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
        revalidatePathHandler("/hotel/account/rooms");
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  // UPLOADS PHOTOS OF ROOMS
  const photoInputHandler = async (
    hotelId: string,
    hotelToken: string | undefined,
    roomInputs: roomInputsTypes,
    photoFileObject: FileList | undefined,
    setModalImage: React.Dispatch<React.SetStateAction<string[]>>,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setCreateRoomsIsOpen: React.Dispatch<React.SetStateAction<boolean>>,
    resetForm: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      // Resim yoksa oda ekleme işlemine devam et
      if (!photoFileObject || photoFileObject?.length === 0) {
        return handleRoom(
          [],
          hotelToken,
          roomInputs,
          setModalImage,
          setLoading,
          setCreateRoomsIsOpen,
          resetForm,
          setDisabled
        );
      }

      try {
        const response = await uploadMultiToS3(
          photoFileObject!,
          `hotel/${hotelId}/roomPhotos/${slug(roomInputs.roomGroupName)}/`,
          hotelToken,
          ImageResizeFitType.fill
        );

        if (!response.success) {
          const errorMessage = "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setCreateRoomsIsOpen(false);
          setLoading(false);
          setDisabled(false);
          resetForm();
          throw new Error("Network response was not ok");
        }

        const idsArray = response.data.map((item: any) => item._id);
        handleRoom(
          idsArray,
          hotelToken,
          roomInputs,
          setModalImage,
          setLoading,
          setCreateRoomsIsOpen,
          resetForm,
          setDisabled
        );
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };
  return { photoInputHandler };
};
