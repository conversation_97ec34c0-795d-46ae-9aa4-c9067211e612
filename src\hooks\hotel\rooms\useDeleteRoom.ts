import type { FormEvent } from "react";
import { useToast } from "@/components/ui/use-toast";
import { revalidatePathHandler } from "@/lib/revalidate";

export const useDeleteRoom = () => {
  const { toast } = useToast();

  const deleteRoomHandler = async (
    event: FormEvent,
    hotelToken: string | undefined,
    roomGroupId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URI}/partner/hotel/account/rooms/deleteRoomsByRoomGroupId/${roomGroupId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();
        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setDisabled(false);
          closeModal();
          throw new Error("Network response was not ok");
        }

        toast({
          variant: "success",
          duration: 3500,
          title: "Oda Kaldırma",
          description: "Oda kaldırıldı.",
        });
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 2000);
        revalidatePathHandler("/hotel/account/rooms");
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
  };

  return { deleteRoomHandler };
};
