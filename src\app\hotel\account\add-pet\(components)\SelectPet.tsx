"use client";
import React from "react";
import type { FC } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Label from "@/components/Label";
import Image from "next/image";
import catImage from "@/images/pets/cat.png";
import dogImage from "@/images/pets/dog.png";
import dogSmallImage from "@/images/pets/dog-small.png";
import dogBigImage from "@/images/pets/dog-big.png";
import rabbitImage from "@/images/pets/rabbit.png";
import guineaPigImage from "@/images/pets/guinea-pig.png";
import parrotImage from "@/images/pets/parrot.png";
import turtleImage from "@/images/pets/turtle.png";
import horseImage from "@/images/pets/horse.png";
import hedgehogImage from "@/images/pets/hedgehog.png";
import type { PetInformationTypes } from "@/types/petOwner/petTypes";

interface SelectPetProps {
  petInformations: PetInformationTypes;
  setPetInformations: React.Dispatch<React.SetStateAction<PetInformationTypes>>;
  isCheckout?: boolean;
  isPublic?: boolean;
  selectedPetTypes?: string[];
}

const SelectPet: FC<SelectPetProps> = ({
  petInformations,
  setPetInformations,
  isCheckout = false,
  isPublic = false,
  selectedPetTypes = [],
}) => {
  const petArray = [
    { id: 1, value: "cat", label: "Kedi", image: catImage },
    {
      id: 2,
      value: "smallDogBreed",
      label: "Köpek (Küçük Irk)",
      image: dogSmallImage,
    },
    {
      id: 3,
      value: "mediumDogBreed",
      label: "Köpek (Orta Irk)",
      image: dogImage,
    },
    {
      id: 4,
      value: "largeDogBreed",
      label: "Köpek (Büyük Irk)",
      image: dogBigImage,
    },
    { id: 5, value: "rabbit", label: "Tavşan", image: rabbitImage },
    { id: 6, value: "guineaPig", label: "Ginepig", image: guineaPigImage },
    { id: 7, value: "parrot", label: "Papağan", image: parrotImage },
    { id: 8, value: "turtle", label: "Kaplumbağa", image: turtleImage },
    { id: 9, value: "horse", label: "At", image: horseImage },
    { id: 10, value: "hedgehog", label: "Kirpi", image: hedgehogImage },
  ];

  const shouldFilter = isCheckout || isPublic;

  const filteredPetArray = shouldFilter
    ? petArray.filter((pet) => selectedPetTypes.includes(pet.value))
    : petArray;
  return (
    <div className="mt-7">
      <h2 className="font-semibold text-lg mb-2">Evcil Hayvan Seçimi</h2>
      <RadioGroup
        onValueChange={(selected) =>
          setPetInformations((prevState) => ({
            ...prevState,
            kind: selected,
          }))
        }
        value={petInformations.kind}
        className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-6 gap-4"
      >
        {filteredPetArray.map((pet) => {
          return (
            <div key={pet.id} className="hover:scale-105 duration-200">
              <RadioGroupItem
                value={pet.value}
                id={pet.value}
                className="peer sr-only"
              />
              <Label
                htmlFor={pet.value}
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-secondary-6000 [&:has([data-state=checked])]:border-primary !cursor-pointer"
              >
                <Image src={pet.image} width={150} height={150} alt="" />
                {pet.label}
              </Label>
            </div>
          );
        })}
      </RadioGroup>
    </div>
  );
};

export default SelectPet;
