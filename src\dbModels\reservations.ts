import type { Hotel } from "./hotels";
import type { Room } from "./rooms";
import type { Member } from "./members";
import type { Pet } from "./pets";
import type { RoomAllocation } from "./room-allocations";

export interface AllocationType {
  roomAllocation?: RoomAllocation;
  room?: Room;
  allocationDate?: string;
  price?: number;
  currency?: "USD" | "EUR" | "TRY" | "GBP";
}
export interface Reservation {
  room?: Room;
  _id?: string;
  hotel?: Hotel;
  petOwner?: Member;
  pet?: Pet;
  issueDate?: string;
  allocations: AllocationType[];
  startDate?: string;
  endDate?: string;
  hotelSubMerchantGuid?: string;
  status?:
    | ""
    | "waitingForBook"
    | "waitingForConfirm"
    | "booked"
    | "waitingForApprove"
    | "confirmed"
    | "approved"
    | "declined"
    | "cancelled"
    | "checkedIn"
    | "checkedOut"
    | undefined;
  checkedIn?: string;
  checkedOut?: string;
  total?: number;
  channel?: string;
  receiptNo?: string;
  currency?: "USD" | "EUR" | "TRY" | "GBP";
  paymentStatus?: "" | "paid" | "atHotel" | "creditCard";
  createdAt: string;
}
