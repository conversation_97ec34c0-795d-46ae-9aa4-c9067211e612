import React from "react";
import type { FC } from "react";
import SelectPetCheckout from "./SelectPetCheckout";
import { Separator } from "@/components/ui/separator";
import { getSelectedPetIds } from "../../atDoorReservationsContainer";

interface MultipleSelectPetContainerProps {
  orderData: any;
  hotelCustomerPetData: any;
  hotelCustomerId: string | null;
}

const MultipleSelectPetContainer: FC<MultipleSelectPetContainerProps> = ({
  orderData,
  hotelCustomerPetData,
  hotelCustomerId,
}) => {
  // Creates an array of selected pet IDs from reservations, filtering out any null or undefined values
  const reservationSelectedPetArray = getSelectedPetIds(
    orderData?.reservations
  );
  return (
    <div>
      <p className="font-medium mb-2">
        Lütfen rezervasyon yapmak istediğiniz tüm odalar için evcil hayvan
        seçiniz.
      </p>
      <Separator className="mt-1 mb-2 w-20 dark:bg-white" />
      <div className="space-y-10">
        {orderData?.reservations?.map((reservation: any, index: number) => {
          return (
            <div key={reservation._id}>
              <div className="flex items-center gap-1 font-semibold">
                <p className="font-semibold">Oda adı:</p>
                <p className="text-lg font-semibold">
                  {reservation.room.roomName}
                </p>
              </div>
              <Separator className="mt-1 mb-2 w-20 dark:bg-white" />
              <SelectPetCheckout
                hotelCustomerPetData={hotelCustomerPetData}
                orderId={orderData?._id}
                hotelCustomerId={hotelCustomerId}
                petTypes={reservation?.room?.petType}
                multiple
                index={index}
                reservationSelectedPetArray={reservationSelectedPetArray}
              />
              <Separator className="mt-5" />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MultipleSelectPetContainer;
