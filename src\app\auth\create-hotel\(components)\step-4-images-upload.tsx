"use client";
import type { <PERSON> } from "react";
import React from "react";
import { useTranslations } from "next-intl";

export interface PageAddListing4Props {}

const Step4ImagesUpload: FC<PageAddListing4Props> = () => {
  const translate = useTranslations("Step4ImagesUpload");
  return (
    <>
      <h2 className="text-2xl font-semibold">{translate("hotelImages")}</h2>
    </>
  );
};

export default Step4ImagesUpload;
