import type { ChangeEvent, KeyboardEvent } from "react";
import type { PET_TYPES } from "@/app/(enums)/enums";

export interface RenderRadioProps {
  name: string;
  id: string;
  label: string;
  defaultChecked?: boolean;
  onChange: (id: string) => void;
}

export interface CreateRoomInputsProps {
  roomInputs: {
    roomName: string;
    roomCapacity: number | string;
    petType: string[];
    roomCount: number;
    roomGroupName: string;
    roomNameStartingNumber: number;
    roomFeatures: string[] | [];
  };
  handleChange: (event: ChangeEvent<HTMLInputElement>) => void;
  hotelFeaturesRemoveHandler: (index: number) => void;
  roomFeaturesHandler: (event: KeyboardEvent<HTMLInputElement>) => void;
  acceptedPetTypes: Partial<keyof typeof PET_TYPES>[];
  handlePetTypeCheckboxChange: (event: ChangeEvent<HTMLInputElement>) => void;
  roomFeaturesNameHandler: (event: ChangeEvent<HTMLInputElement>) => void;
  roomFeatureNames: string;
  handleRoomFeatures: () => void;
  textAreaHandleChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
}

export interface CreateRoomPhotosProps {
  loading: boolean;
  newRoomInputRef: React.RefObject<HTMLInputElement>;
  setPhotoFileObject: React.Dispatch<
    React.SetStateAction<FileList | undefined>
  >;
  setModalImage: React.Dispatch<React.SetStateAction<string[] | []>>;
  modalImage: string[] | [];
}

export interface CreateNewRoomModalProps {
  hotelToken: string | undefined;
  hotel: any;
}

export interface roomInputsTypes {
  roomName: string;
  roomCapacity: number | string;
  petType: string[];
  roomCount: number;
  roomGroupName: string;
  roomNameStartingNumber: number;
  roomDescription: string;
  roomFeatures: string[] | [];
}
