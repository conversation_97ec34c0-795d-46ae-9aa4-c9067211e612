// Converts a given date (either Date object or string in "YYYY-MM-DD" format) to a short string format in Turkish
// (e.g., "12 Ara" for 12th December), handling both string and Date input types.
const convertOneDateToString = (
  date: Date | string | undefined,
  year: boolean,
  language: string
) => {
  if (!date) return "";

  const monthLanguage = language === "tr" ? "tr-TR" : "en-US";

  // If the date is a string in "YYYY-MM-DD" format, convert it to a Date object
  if (typeof date === "string") {
    const [year, month, day] = date.split("-").map(Number);
    date = new Date(year, month - 1, day); // Months are 0-indexed
  }
  if (year) {
    // Return the date in a short format suitable for the locale
    return date.toLocaleDateString(monthLanguage, {
      month: "short",
      day: "2-digit",
      year: "numeric",
    });
  } else {
    // Return the date in a short format suitable for the locale
    return date.toLocaleDateString(monthLanguage, {
      month: "short",
      day: "2-digit",
    });
  }
};

export default convertOneDateToString;
