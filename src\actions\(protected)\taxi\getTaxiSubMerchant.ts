"use server";
import { redirect } from "next/navigation";
import { PET_TAXI_API_PATHS } from "@/utils/apiUrls";
import { cookies } from "next/headers";

export default async function getTaxiSubMerchant() {
  const cookieStore = cookies();
  const petTaxiToken = cookieStore.get("token")?.value || undefined;

  if (petTaxiToken) {
    try {
      const response = await fetch(PET_TAXI_API_PATHS.subMerchant, {
        cache: "no-cache",
        headers: {
          petTaxiToken: petTaxiToken,
          "Content-Type": "application/json",
        },
      });
      const result = await response.json();

      if (result.status === 401) {
        redirect("/");
      } else if (result.status === 404) {
        redirect("/404");
      }

      if (!response.ok || !result.success) {
        console.error("Network response was not ok");
        return undefined;
      }
      return result;
    } catch (err: unknown) {
      console.error("Error fetching data:", err);
      return undefined;
    }
  }
}
