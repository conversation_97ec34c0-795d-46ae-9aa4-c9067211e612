import type { FC } from "react";
import React from "react";

interface IconChevronLeftProps {
  className?: string;
  strokeWidth?: number;
  onClick?: () => void;
}

const IconChevronLeft: FC<IconChevronLeftProps> = ({
  className = "size-6",
  strokeWidth = 2.8,
  onClick,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={strokeWidth}
      stroke="currentColor"
      className={className}
      onClick={onClick}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M15.75 19.5 8.25 12l7.5-7.5"
      />
    </svg>
  );
};

export default IconChevronLeft;
