import React, { useState } from "react";
import type { FC } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import LoadingSpinner from "@/shared/icons/Spinner";
import { X } from "lucide-react";
import { useHotelCustomers } from "@/hooks/hotel/useHotelCustomers";

interface SendSmsContainerProps {
  hotelCustomerId: string | null;
  orderData: any;
  hotelToken: string | undefined;
}

const SendSmsContainer: FC<SendSmsContainerProps> = ({
  hotelCustomerId,
  orderData,
  hotelToken,
}) => {
  const { sendSmsToHotelCustomer } = useHotelCustomers();
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <div className="flex">
      <Dialog open={modalIsOpen}>
        <DialogContent
          onInteractOutside={() => {
            setModalIsOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>Sms Gönderme</DialogTitle>
            <DialogDescription className="text-base">
              Seçili müşteriye sms göndermek istiyor musunuz?
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-5">
            <Button
              type="button"
              variant="outline"
              className="max-sm:basis-1/2"
              onClick={() => setModalIsOpen(false)}
            >
              Vazgeç
            </Button>
            <Button
              disabled={loading}
              className="bg-secondary-6000 hover:bg-secondary-700 text-white max-sm:basis-1/2"
              onClick={() => {
                sendSmsToHotelCustomer(
                  hotelToken,
                  hotelCustomerId,
                  orderData?._id,
                  setLoading,
                  () => setModalIsOpen(false)
                );
              }}
            >
              {loading ? <LoadingSpinner /> : "Gönder"}
            </Button>
          </div>
          <DialogClose
            onClick={() => setModalIsOpen(false)}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
      <div>
        <h2 className="font-semibold text-lg">Sms İle Evcil Hayvan Bilgisi</h2>
        <p className="text-sm mb-2">
          Müşteriye sms göndererek evcil hayvan eklemesini sağlayabilirsiniz.
        </p>
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          onClick={() => setModalIsOpen(true)}
        >
          Sms Gönder
        </Button>
      </div>
    </div>
  );
};

export default SendSmsContainer;
