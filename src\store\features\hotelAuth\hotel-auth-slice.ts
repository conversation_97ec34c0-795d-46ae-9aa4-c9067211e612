import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { HotelStates } from "./hotel-auth-types";
import type {
  OwnerTypes,
  HotelTypes,
  FeatureAndPetTypes,
  StepState,
  FirstStep,
  SecondStep,
  ThirdStep,
  RegisterId,
  MailAuthStep,
  PhoneAuthStep,
} from "./hotel-auth-types";

const initialState: HotelStates = {
  owner: {
    firstName: null,
    lastName: null,
    userName: null,
    email: null,
    password: null,
    phone: null,
    gender: null,
    dateOfBirth: null,
  },

  hotel: {
    hotelName: null,
    propertyType: null,
    // taxOffice: null,
    // taxNumber: null,
    streetName: null,
    district: null,
    cityName: null,
    citySubdivisionName: null,
    hotelPhoneNumber: null,
    postbox: null,
    buildingName: null,
    buildingNumber: null,
    hotelDescription: null,
    // legalName: null,
  },
  hotelFeatures: {
    hotelFeaturesArray: [],
    acceptedPetTypes: [],
    hotelCareServices: [],
  },
  stepState: 1,
  firstStep: false,
  secondStep: false,
  thirdStep: false,
  registerId: null,
  mailAuthStep: false,
  phoneAuthStep: false,
};

const hotelAuthSlice = createSlice({
  name: "hotelAuth",
  initialState,
  reducers: {
    setOwner: (state, action: PayloadAction<OwnerTypes>) => {
      state.owner = action.payload;
    },
    setHotel: (state, action: PayloadAction<HotelTypes>) => {
      state.hotel = action.payload;
    },
    setHotelFeatures: (state, action: PayloadAction<FeatureAndPetTypes>) => {
      state.hotelFeatures = action.payload;
    },
    setStepState: (state, action: PayloadAction<StepState>) => {
      state.stepState = action.payload;
    },
    setFirstStep: (state, action: PayloadAction<FirstStep>) => {
      state.firstStep = action.payload;
    },
    setSecondStep: (state, action: PayloadAction<SecondStep>) => {
      state.secondStep = action.payload;
    },
    setThirdStep: (state, action: PayloadAction<ThirdStep>) => {
      state.thirdStep = action.payload;
    },
    setRegisterId: (state, action: PayloadAction<RegisterId>) => {
      state.registerId = action.payload;
    },
    setMailAuthStep: (state, action: PayloadAction<MailAuthStep>) => {
      state.mailAuthStep = action.payload;
    },
    setPhoneAuthStep: (state, action: PayloadAction<PhoneAuthStep>) => {
      state.phoneAuthStep = action.payload;
    },
  },
});

export const {
  setOwner,
  setHotel,
  setHotelFeatures,
  setStepState,
  setRegisterId,
  setMailAuthStep,
  setPhoneAuthStep,
} = hotelAuthSlice.actions;
export default hotelAuthSlice.reducer;
