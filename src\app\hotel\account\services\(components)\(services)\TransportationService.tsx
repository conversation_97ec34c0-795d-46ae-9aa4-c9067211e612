"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useHotelService } from "@/hooks/hotel/services/useHotelService";
import type { transportationServiceTypes } from "@/types/hotel/services/serviceTypes";
import { isServiceValid } from "@/utils/services/hotelServices";
import { Switch } from "@/components/ui/switch";
import { MultiSelect } from "@/components/ui/multi-select";
import { petTypes } from "@/types/petOwner/petTypes";

interface TransportationServiceProps {
  hotelToken: string | undefined;
  closeModal: () => void;
}

const TransportationService: FC<TransportationServiceProps> = ({
  hotelToken,
  closeModal,
}) => {
  const { addService } = useHotelService();
  const [transportationService, setTransportationService] =
    useState<transportationServiceTypes>({
      isActive: true,
      serviceName: "",
      description: "",
      serviceDetails: {
        initialPrice: "",
        maxDistance: "",
        distancePrice: "",
      },
      petType: [],
    });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const formFields = [
    { label: "Taşıma Hizmet Adı", name: "serviceName" },
    { label: "Başlangıç Fiyatı", name: "initialPrice", type: "number" },
    { label: "Maksimum Mesafe KM", name: "maxDistance", type: "number" },
    { label: "KM Başı Fiyat", name: "distancePrice", type: "number" },
    { label: "Açıklama", name: "description" },
  ];

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;

    if (["initialPrice", "maxDistance", "distancePrice"].includes(name)) {
      setTransportationService((prev) => ({
        ...prev,
        serviceDetails: {
          ...prev.serviceDetails,
          [name]: value,
        },
      }));
    } else {
      setTransportationService((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  const buttonDisabled = isServiceValid(transportationService);

  const handleSelectChange = (name: string, value: string | string[]) => {
    setTransportationService((prev) => ({
      ...prev,
      [name]: Array.isArray(value) ? value : Number(value),
    }));
  };

  const petTypeList = petTypes.map((petType) => ({
    value: petType.value,
    label: petType.label,
  }));

  return (
    <form
      onSubmit={(event) =>
        addService(
          event,
          hotelToken,
          transportationService,
          "transportationServices",
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <h2 className="font-medium text-lg mb-3">Ulaşım Hizmeti</h2>
      <div className="space-y-2">
        {formFields.map(({ label, name, type = "text" }) => (
          <div key={name}>
            {name === "description" && (
              <FormItem label="Pet Türü">
                <MultiSelect
                  options={petTypeList}
                  onValueChange={(value) =>
                    handleSelectChange("petType", value)
                  }
                  maxCount={15}
                  placeholder="Pet Türü Seçin"
                  className="rounded-2xl mb-2 min-h-11"
                />
              </FormItem>
            )}
            <FormItem label={label}>
              <Input
                name={name}
                type={type}
                value={
                  ["initialPrice", "maxDistance", "distancePrice"].includes(
                    name
                  )
                    ? String(
                        transportationService.serviceDetails[
                          name as keyof typeof transportationService.serviceDetails
                        ] || ""
                      )
                    : String(
                        transportationService[
                          name as keyof transportationServiceTypes
                        ] || ""
                      )
                }
                onChange={handleChange}
              />
            </FormItem>
          </div>
        ))}
        <FormItem label="Aktiflik Durumu" className="mt-3">
          <div className="flex gap-2">
            <Switch
              name="isActive"
              checked={transportationService.isActive}
              onCheckedChange={(checked) =>
                setTransportationService((prev) => ({
                  ...prev,
                  isActive: checked,
                }))
              }
              className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
            />
            <p
              className={
                transportationService.isActive
                  ? "text-green-500"
                  : "text-red-500"
              }
            >
              {transportationService.isActive ? "Aktif" : "Pasif"}
            </p>
          </div>
        </FormItem>
      </div>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default TransportationService;
