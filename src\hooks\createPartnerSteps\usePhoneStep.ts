"use client";
import {
  setStepState,
  setOwner,
  setPhoneAuthStep,
} from "@/store/features/partnerAuth/partner-auth-slice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useToast } from "@/components/ui/use-toast";

export const usePhoneStepActions = () => {
  const { toast } = useToast();
  const dispatch = useDispatch();

  const stepState = useSelector(
    (state: RootState) => state.partnerAuth.stepState
  );
  const owner = useSelector((state: RootState) => state.partnerAuth.owner);
  const registerId = useSelector(
    (state: RootState) => state.partnerAuth.registerId
  );
  const partner = useSelector((state: RootState) => state.partnerAuth.partner);

  const handleCheckPhone = async (
    partnerPhone: string | null,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    setPartnerPhone: React.Dispatch<React.SetStateAction<string | null>>
  ) => {
    setDisabled(true);
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/check/phone/${partnerPhone}/${partner.propertyType}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    const data = await response.json();

    if (!data.data.inUse) {
      partnerFirstPhoneStepHandler(partnerPhone, closeModal, setDisabled);
    } else {
      closeModal();
      setDisabled(false);
      setPartnerPhone(null);
      toast({
        variant: "warning",
        duration: 5000,
        title: "Uyarı",
        description: `${partnerPhone} telefon numarası ile açılmış bir kullanıcı var.`,
      });
    }
  };

  const partnerFirstPhoneStepHandler = async (
    partnerPhone: string | null,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/step3`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            registerId: registerId,
            phone: partnerPhone,
            propertyType: partner.propertyType,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Bilinmeyen bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        closeModal();
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      closeModal();
      dispatch(setPhoneAuthStep(true));
      setDisabled(false);
      toast({
        variant: "success",
        duration: 6000,
        title: "Telefon Onay",
        description: "Telefonunuza onay kodu gönderildi.",
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const partnerSecondPhoneStepHandler = async (
    partnerPhone: string | null,
    authCode: string | null,
    setAuthCode: React.Dispatch<React.SetStateAction<string | null>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    try {
      setDisabled(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URI}/partner/auth/register/step4`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            registerId: registerId,
            phone: partnerPhone,
            authCode: authCode,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Bilinmeyen bir hata oluştu";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: `${errorMessage}`,
        });
        setAuthCode(null);
        setDisabled(false);
        throw new Error("Network response was not ok");
      }
      dispatch(setStepState(stepState + 1));
      dispatch(setOwner({ ...owner, phone: partnerPhone }));
      toast({
        variant: "success",
        duration: 6000,
        title: "Telefon Onay",
        description: "Telefon numaranız Onaylandı.",
      });
      setDisabled(false);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  return {
    partnerFirstPhoneStepHandler,
    partnerSecondPhoneStepHandler,
    handleCheckPhone,
  };
};
