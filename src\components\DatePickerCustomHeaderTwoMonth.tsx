import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import React from "react";
import type { ReactDatePickerCustomHeaderProps } from "react-datepicker";

const DatePickerCustomHeaderTwoMonth = ({
  monthDate,
  customHeaderCount,
  decreaseMonth,
  increaseMonth,
}: ReactDatePickerCustomHeaderProps) => {
  const today = new Date();
  const visibleStartMonth = new Date(
    monthDate.getFullYear(),
    monthDate.getMonth() - customHeaderCount
  );

  const isPreviousMonthDisabled =
    visibleStartMonth.getMonth() <= today.getMonth() &&
    visibleStartMonth.getFullYear() <= today.getFullYear();

  return (
    <div>
      <button
        aria-label="Previous Month"
        className={
          "react-datepicker__navigation react-datepicker__navigation--previous absolute -top-1 left-0 flex items-center justify-center rounded-full p-2 hover:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-400 dark:hover:bg-gray-700"
        }
        style={customHeaderCount === 1 ? { visibility: "hidden" } : {}}
        onClick={decreaseMonth}
        type="button"
        disabled={isPreviousMonthDisabled}
      >
        <span className="react-datepicker__navigation-icon react-datepicker__navigation-icon--previous">
          <ChevronLeftIcon className="size-5" />
        </span>
      </button>
      <span className="react-datepicker__current-month">
        {monthDate.toLocaleString("tr-TR", {
          month: "long",
          year: "numeric",
        })}
      </span>
      <button
        aria-label="Next Month"
        className="react-datepicker__navigation react-datepicker__navigation--next absolute -right-0 -top-1 flex items-center justify-center rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700"
        style={customHeaderCount === 0 ? { visibility: "hidden" } : {}}
        type="button"
        onClick={increaseMonth}
      >
        <span className="react-datepicker__navigation-icon react-datepicker__navigation-icon--next">
          <ChevronRightIcon className="size-5" />
        </span>
      </button>
    </div>
  );
};

export default DatePickerCustomHeaderTwoMonth;
