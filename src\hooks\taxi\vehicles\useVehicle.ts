import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { PET_TAXI_API_PATHS } from "@/utils/apiUrls";
import { uploadMultiToS3, ImageResizeFitType } from "@/lib/s3BucketHelper";
import slug from "slug";

export const useVehicle = () => {
  const { toast } = useToast();

  const photoInputHandler = async (
    event: FormEvent,
    petTaxiId: string,
    petTaxiToken: string | undefined,
    vehicle: any,
    photoFileObject: FileList | undefined,
    vehicleFiles: {
      vehicle_license: FileList | undefined;
      insurance: FileList | undefined;
      pet_transport_certificate: FileList | undefined;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();

    if (!petTaxiToken) return;

    setLoading(true);
    setDisabled(true);

    try {
      const vehicleImageArray: string[] = [];
      const vehicleFilesArray: string[] = [];

      if (photoFileObject && photoFileObject.length > 0) {
        const response = await uploadMultiToS3(
          photoFileObject,
          `petTaxi/${petTaxiId}/vehiclePhotos/${slug(vehicle.vehicleName)}/`,
          petTaxiToken,
          ImageResizeFitType.fill
        );

        if (!response.success) throw new Error("Fotoğraf yüklenemedi");

        vehicleImageArray.push(...response.data.map((item: any) => item._id));
      }

      for (const [docType, files] of Object.entries(vehicleFiles) as [
        string,
        FileList | undefined,
      ][]) {
        if (files && files.length > 0) {
          const response = await uploadMultiToS3(
            files,
            `petTaxi/${petTaxiId}/vehicleDocuments/${slug(vehicle.vehicleName)}/${docType}/`,
            petTaxiToken,
            undefined,
            docType
          );

          if (!response.success) throw new Error(`${docType} yüklenemedi`);

          vehicleFilesArray.push(...response.data.map((item: any) => item._id));
        }
      }

      await addVehicle(
        vehicleImageArray,
        vehicleFilesArray,
        event,
        petTaxiToken,
        vehicle,
        setLoading,
        closeModal,
        setDisabled
      );
    } catch (error) {
      console.error("Dosya yükleme hatası:", error);
      toast({
        variant: "error",
        duration: 4000,
        title: "Hata",
        description: "Dosya yükleme sırasında hata oluştu.",
      });
      setLoading(false);
      setDisabled(false);
    }
  };

  const addVehicle = async (
    vehicleImageArray: string[],
    vehicleFilesArray: string[],
    event: FormEvent,
    petTaxiToken: string | undefined,
    vehicle: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (!petTaxiToken) return;

    setLoading(true);
    setDisabled(true);

    try {
      const bodyData = {
        ...vehicle,
        images: vehicleImageArray,
        documents: vehicleFilesArray,
      };

      const response = await fetch(PET_TAXI_API_PATHS.createVehicle, {
        method: "POST",
        headers: {
          petTaxiToken: petTaxiToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(bodyData),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: errorMessage,
        });
        setLoading(false);
        closeModal();
        setDisabled(false);
        throw new Error(errorMessage);
      }

      toast({
        variant: "success",
        duration: 6000,
        title: "Araç Ekleme",
        description: "Araç başarıyla eklendi.",
      });

      revalidatePathHandler("/petTaxi/account/vehicles");
      setLoading(false);
      closeModal();
      setDisabled(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
      setDisabled(false);
    }
  };

  const updatePhotoInputHandler = async (
    event: FormEvent,
    petTaxiId: string,
    petTaxiToken: string | undefined,
    vehicle: any,
    vehicleId: string,
    photoFileObject: FileList | undefined,
    vehicleFiles: {
      vehicle_license: FileList | undefined;
      insurance: FileList | undefined;
      pet_transport_certificate: FileList | undefined;
    },
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();

    if (!petTaxiToken) return;

    setLoading(true);
    setDisabled(true);

    try {
      const vehicleImageArray: string[] = [];
      const vehicleFilesArray: string[] = [];

      if (photoFileObject && photoFileObject.length > 0) {
        const response = await uploadMultiToS3(
          photoFileObject,
          `petTaxi/${petTaxiId}/vehiclePhotos/${slug(vehicle.vehicleName)}/`,
          petTaxiToken,
          ImageResizeFitType.fill
        );

        if (!response.success) throw new Error("Fotoğraf yüklenemedi");

        vehicleImageArray.push(...response.data.map((item: any) => item._id));
      }

      for (const [docType, files] of Object.entries(vehicleFiles) as [
        string,
        FileList | undefined,
      ][]) {
        if (files && files.length > 0) {
          const response = await uploadMultiToS3(
            files,
            `petTaxi/${petTaxiId}/vehicleDocuments/${slug(vehicle.vehicleName)}/${docType}/`,
            petTaxiToken,
            undefined,
            docType
          );

          if (!response.success) throw new Error(`${docType} yüklenemedi`);

          vehicleFilesArray.push(...response.data.map((item: any) => item._id));
        }
      }

      await updateVehicle(
        vehicleImageArray,
        vehicleFilesArray,
        event,
        petTaxiToken,
        vehicle,
        vehicleId,
        setLoading,
        true,
        true,
        closeModal,
        setDisabled
      );
    } catch (error) {
      console.error("Dosya yükleme hatası:", error);
      toast({
        variant: "error",
        duration: 4000,
        title: "Hata",
        description: "Dosya yükleme sırasında hata oluştu.",
      });
      setLoading(false);
      setDisabled(false);
    }
  };

  const updateVehicle = async (
    vehicleImageArray: string[],
    vehicleFilesArray: string[],
    event: FormEvent,
    petTaxiToken: string | undefined,
    vehicle: any,
    vehicleId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    isAppendImages: boolean,
    isAppendDocuments: boolean,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (!petTaxiToken) return;

    setLoading(true);
    setDisabled(true);

    try {
      const bodyData = {
        ...vehicle,
        images: vehicleImageArray,
        documents: vehicleFilesArray,
        appendImages: isAppendImages,
        appendDocuments: isAppendDocuments,
      };

      const response = await fetch(
        `${PET_TAXI_API_PATHS.updateVehicle}/${vehicleId}`,
        {
          method: "PUT",
          headers: {
            petTaxiToken: petTaxiToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(bodyData),
        }
      );

      const data = await response.json();

      if (!response.ok || !data.success) {
        const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
        toast({
          variant: "error",
          duration: 5000,
          title: "Hata",
          description: errorMessage,
        });
        setLoading(false);
        closeModal();
        setDisabled(false);
        throw new Error(errorMessage);
      }

      toast({
        variant: "success",
        duration: 6000,
        title: "Araç Güncelleme",
        description: "Araç başarıyla güncellendi.",
      });
      setLoading(false);
      closeModal();
      setDisabled(false);
      revalidatePathHandler("/petTaxi/account/vehicles");
    } catch (error) {
      console.error(error);
      setLoading(false);
      setDisabled(false);
    }
  };

  const deleteVehicle = async (
    event: FormEvent,
    petTaxiToken: string | undefined,
    vehicleId: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (petTaxiToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${PET_TAXI_API_PATHS.deleteVehicle}/${vehicleId}`,
          {
            method: "DELETE",
            headers: {
              petTaxiToken: petTaxiToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 5000,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 6000,
          title: "Araç Kaldırma",
          description: "Araç başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/petTaxi/account/vehicles");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };
  return { photoInputHandler, updatePhotoInputHandler, deleteVehicle };
};
