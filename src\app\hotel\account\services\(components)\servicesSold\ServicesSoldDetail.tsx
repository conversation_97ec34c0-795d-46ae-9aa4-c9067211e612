import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import VeterinaryDetail from "./(servicesSoldDetail)/VeterinaryDetail";
import TransportationDetail from "./(servicesSoldDetail)/TransportationDetail";
import GroomingDetail from "./(servicesSoldDetail)/GroomingDetail";
import ChatButton from "../../../(components)/(today)/ChatButton";

const ServicesSoldDetail = ({
  servicesSoldData,
}: {
  servicesSoldData: any;
}) => {
  const translate = useTranslations("ReservationDetail");
  return (
    <Sheet>
      <SheetTrigger className="font-medium">
        {translate("details")}
      </SheetTrigger>
      <SheetContent className="overflow-y-auto p-6 text-gray-800 dark:text-gray-200 dark:bg-gray-900">
        <SheetHeader>
          <SheetTitle className="text-xl font-bold mb-6 dark:text-white">
            📋 Hizmet Detayı
          </SheetTitle>
        </SheetHeader>
        <div className="space-y-6 text-sm leading-relaxed">
          {servicesSoldData?.serviceType === "veterinaryServices" && (
            <VeterinaryDetail servicesSoldData={servicesSoldData} />
          )}
          {servicesSoldData?.serviceType === "transportationServices" && (
            <TransportationDetail servicesSoldData={servicesSoldData} />
          )}
          {servicesSoldData?.serviceType === "groomingServices" && (
            <GroomingDetail servicesSoldData={servicesSoldData} />
          )}
          <section>
            <h3 className="text-lg font-medium mb-2 dark:text-white">
              🐶 {translate("petInformation")}
            </h3>
            <ul className="space-y-1">
              <li>
                {translate("petName")}:{" "}
                <span className="font-semibold dark:text-gray-300">
                  {servicesSoldData?.pet?.name ||
                    servicesSoldData?.hotelPet?.name}
                </span>
              </li>
              <li>
                {translate("petAge")}:{" "}
                <span className="font-semibold dark:text-gray-300">
                  {servicesSoldData?.pet?.age ||
                    servicesSoldData?.hotelPet?.age}
                </span>
              </li>
              <li>
                {translate("petKind")}:{" "}
                <span className="font-semibold dark:text-gray-300">
                  {servicesSoldData?.pet?.kind
                    ? translate(servicesSoldData.pet.kind)
                    : servicesSoldData?.hotelPet?.kind
                      ? translate(servicesSoldData.hotelPet.kind)
                      : "-"}
                </span>
              </li>
              <li>
                {translate("petBreed")}:{" "}
                <span className="font-semibold dark:text-gray-300">
                  {servicesSoldData?.pet?.breed ||
                    servicesSoldData?.hotelPet?.breed}
                </span>
              </li>
            </ul>

            {(servicesSoldData?.pet?.images?.length > 0 ||
              servicesSoldData?.hotelPet?.images?.length > 0) && (
              <div className="grid grid-cols-2 gap-4 mt-4">
                {(
                  servicesSoldData?.pet?.images ||
                  servicesSoldData?.hotelPet?.images
                )?.map((image: any) => (
                  <div
                    key={image._id}
                    className="bg-gray-100 p-2 rounded shadow dark:bg-gray-800"
                  >
                    <Image
                      src={image?.img800 || image?.src}
                      width={150}
                      height={150}
                      alt="pet"
                      className="rounded"
                    />
                    <a href={image?.src} target="_blank" rel="noreferrer">
                      <Button variant="outline" className="w-full mt-2 text-xs">
                        {translate("showPhoto")}
                      </Button>
                    </a>
                  </div>
                ))}
              </div>
            )}

            {(servicesSoldData?.pet?.documentPhotos?.length > 0 ||
              servicesSoldData?.hotelPet?.documentPhotos?.length > 0) && (
              <>
                <Separator className="my-4 dark:border-gray-700" />
                <h4 className="font-medium text-base mb-2 dark:text-white">
                  💉 {translate("vaccinationReport")}
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  {(
                    servicesSoldData?.pet?.documentPhotos ||
                    servicesSoldData?.hotelPet?.documentPhotos
                  )?.map((image: any) => (
                    <div
                      key={image._id}
                      className="bg-gray-100 p-2 rounded shadow dark:bg-gray-800"
                    >
                      <Image
                        src={image?.img800 || image?.src}
                        width={150}
                        height={150}
                        alt="report"
                        className="rounded"
                      />
                      <a href={image?.src} target="_blank" rel="noreferrer">
                        <Button
                          variant="outline"
                          className="w-full mt-2 text-xs"
                        >
                          {translate("showPhoto")}
                        </Button>
                      </a>
                    </div>
                  ))}
                </div>
              </>
            )}
          </section>
          <Separator className="dark:border-gray-700" />
          <section>
            <h3 className="text-lg font-medium mb-2 dark:text-white">
              👤 {translate("petOwnerInformation")}
            </h3>
            <ul className="space-y-1">
              <li>
                {translate("petOwnerName")}:{" "}
                <span className="font-semibold capitalize dark:text-gray-300">
                  {servicesSoldData?.petOwner?.fullName ||
                    servicesSoldData?.hotelCustomer?.fullName}
                </span>
              </li>
              <li>
                {translate("phone")}:{" "}
                <a
                  href={`tel:${servicesSoldData?.petOwner?.phone || servicesSoldData?.hotelCustomer?.phone}`}
                  className="font-semibold text-blue-600 hover:underline dark:text-blue-400"
                >
                  {servicesSoldData?.petOwner?.phone ||
                    servicesSoldData?.hotelCustomer?.phone}
                </a>
              </li>
              <li>
                {translate("email")}:{" "}
                <a
                  href={`mailto:${servicesSoldData?.petOwner?.email || servicesSoldData?.hotelCustomer?.email}`}
                  className="font-semibold text-blue-600 hover:underline dark:text-blue-400"
                >
                  {servicesSoldData?.petOwner?.email ||
                    servicesSoldData?.hotelCustomer?.email}
                </a>
              </li>
              {servicesSoldData?.petOwner && (
                <li>
                  <div className="mt-5">
                    <ChatButton petOwnerId={servicesSoldData?.petOwner?._id} />
                  </div>
                </li>
              )}
            </ul>
          </section>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ServicesSoldDetail;
