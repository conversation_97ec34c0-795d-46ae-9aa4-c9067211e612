import React from "react";
import type { FC } from "react";
import { formatDateToDayMonthYear } from "@/utils/formatDateToDayMonthYear";
import { PET_TYPES } from "@/app/(enums)/enums";

interface TaahhütnameProps {
  checkoutData: any;
}

const Taahhütname: FC<TaahhütnameProps> = ({ checkoutData }) => {
  const formattedDate = formatDateToDayMonthYear(
    checkoutData?.allocations[0]?.allocationDate
  );

  const petTypeName =
    PET_TYPES[checkoutData?.pet?.kind as keyof typeof PET_TYPES];

  return (
    <div className="space-y-6">
      <p className="text-sm md:text-base">
        <PERSON>i olduğum; {formattedDate} tarihinde iş yerinize getirdiğim{" "}
        {checkoutData?.pet?.name} adlı, {checkoutData?.pet?.age} yaşında,{" "}
        {checkoutData?.pet?.breed} I<PERSON><PERSON>, dişi/erkek {petTypeName}.{" "}
        Barındırılması/eğitimi için aşağıdaki şartları şimdiden kabul, beyan ve
        taahhüt ederim.
      </p>
      <ul className="list-inside space-y-2 pl-2.5">
        <li className="text-sm md:text-base">
          1- Evcil hayvanımın, günlüğü{" "}
          {new Intl.NumberFormat("tr-TR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }).format(Number(checkoutData?.avgPrice)) + "₺"}{" "}
          (Türk Lirası) ücretle, barınma/eğitim amacıyla iş yerinizde
          bulunmasına tam muvafakatim vardır. Toplam giderlerin tarafımca
          ödeneceğini kabul, beyan ve taahhüt ederim.
        </li>
        <li className="text-sm md:text-base">
          2- Evcil hayvanımın barınması/eğitimi sırasında hastalanması
          durumunda;
          <span className="my-2 block pl-2">
            a- Ek tutanakta belirttiğim Özel Veteriner Hekime başvurulmasını,
            aksi durumlarda soreumlu veteriner hekiminiz tarafından muayene
            edilmesini,
          </span>
          <span className="block pl-2">
            b- Sorumlu veteriner hekimi tarafından muayene edilmesini, tüm
            tedavi masrafları ve giderlerinden mesul bulunduğumu, işbu borcumu
            herhangi bir ihtara gerek kalmaksızın nakden ödeyeceğimi kabul,
            beyan ve taahhüt ederim.
          </span>
        </li>
        <li className="text-sm md:text-base">
          3- Tarafımdan ödenmesi gereken borcumu ödemediğim takdirde aylık %10
          temerrüt faizi ile birlikte ödeyeceğimi kabul, beyan ve taahhüt
          ederim.
        </li>
        <li className="text-sm md:text-base">
          4- Hayvanımı, tutanakta belirttiğim tarihte almadığım takdirde alınan
          günlük ücretin %..... oranında fazla ücret ödeyeceğimi kabul, beyan ve
          taahhüt ederim.
        </li>
        <li className="text-sm md:text-base">
          5- Aşağıda bildirdiğim; kimlijk, işyeri ve ikamet adresi ile iletişim
          bilgilerimim gerçek ve geçerli olduğunu beyanla işbu adreslere
          yapılacak her türlü tebligatın muteber olacağını, adres değişikliği
          durumunda bunu derhal 1 hafta içerisinde yazılı olarak tarafınıza
          bildireceğimi, aksi takdirde Tebligat Kanununun 21.maddesine göre
          yapılacak tebliği kabul, beyan ve taahhüt ederim.
        </li>
        <li className="text-sm md:text-base">
          6- İş yeri sahibi ve personeli ile aramızda çıkacak her türlü ihtilaf
          hallerinde Gaziantep İcra Daireleri ve Mahkemelerinin yetkili olduğunu
          şimdiden kabul, beyan ve taahhüt ederim.
        </li>
      </ul>

      <div>
        <p className="mb-2">Evcil hayvan sahibinin</p>
        <div className="space-y-2">
          <p>Adı Soyadı: {checkoutData?.petOwner?.fullName}</p>
          <p>Ev Adresi:</p>
          <p>İş Adresi:</p>
          <p>Cep Telefonu: {checkoutData?.petOwner?.phone}</p>
          <p>T.C.K.N.:</p>
        </div>
      </div>
      <div className="space-y-2">
        <p>Tarih: {formattedDate}</p>
        <p>İmza:</p>
      </div>
    </div>
  );
};

export default Taahhütname;
