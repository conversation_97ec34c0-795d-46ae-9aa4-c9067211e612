import React from "react";
import type { FC } from "react";
import SelectPetServiceCheckout from "./SelectPetServiceCheckout";
import { Separator } from "@/components/ui/separator";
import { getSelectedPetIds } from "../../atDoorReservationsContainer";

interface MultipleSelectPetServiceContainerProps {
  orderData: any;
  hotelCustomerPetData: any;
  hotelCustomerId: string | null;
}

const MultipleSelectPetServiceContainer: FC<
  MultipleSelectPetServiceContainerProps
> = ({ orderData, hotelCustomerPetData, hotelCustomerId }) => {
  // Creates an array of selected pet IDs from services, filtering out any null or undefined values
  const serviceSelectedPetArray = getSelectedPetIds(orderData?.services);

  return (
    <div>
      <p className="font-medium mb-2 text-lg">
        Lütfen tüm hizmetler için evcil hayvan se<PERSON>iniz.
      </p>
      <Separator className="mt-1 mb-2 w-20 dark:bg-white" />
      <div className="space-y-10">
        {orderData?.services?.map((service: any, index: number) => {
          return (
            <div key={service._id}>
              <div className="flex items-center gap-1 font-semibold">
                <p className="font-semibold">Hizmet adı:</p>
                <p className="text-lg font-semibold capitalize">
                  {service?.serviceName}
                </p>
              </div>
              <Separator className="mt-1 mb-2 w-20 dark:bg-white" />
              <SelectPetServiceCheckout
                hotelCustomerPetData={hotelCustomerPetData}
                orderId={orderData?._id}
                hotelCustomerId={hotelCustomerId}
                petTypes={service?.petType}
                multiple
                index={index}
                serviceSelectedPetArray={serviceSelectedPetArray}
              />
              <Separator className="mt-5" />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MultipleSelectPetServiceContainer;
