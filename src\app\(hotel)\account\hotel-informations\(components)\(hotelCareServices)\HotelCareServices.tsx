"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import Checkbox from "@/shared/Checkbox";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelCareServicesProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

interface CareService {
  label: string;
  name: string;
  id: string;
}

const HotelCareServices: FC<HotelCareServicesProps> = ({
  hotelToken,
  hotelData,
}) => {
  const translate = useTranslations("HotelAdditionalServices");
  const [hotelCareServices, setHotelCareServices] = useState<string[]>(
    hotelData.careServices || []
  );
  const [loading, setLoading] = useState<boolean>(false);
  const { handleHotelCareServices } = useHotelInformationsActions();

  const careServices: CareService[] = [
    { label: translate("veterinary"), name: "veterinary", id: "veterinary-id" },
    {
      label: translate("specialNutrition"),
      name: "specialNutrition",
      id: "special-nutrition",
    },
    { label: translate("grooming"), name: "grooming", id: "grooming-id" },
    { label: translate("camera"), name: "camera", id: "camera-id" },
    {
      label: translate("photoInfoServices"),
      name: "photoInfoServices",
      id: "photo-info-services",
    },
    {
      label: translate("specialCondition"),
      name: "specialCondition",
      id: "special-condition",
    },
  ];

  // adds hotel care services to an array and removes
  const handleCareServices = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      setHotelCareServices([...hotelCareServices, name]);
    } else {
      setHotelCareServices(
        hotelCareServices.filter((careService) => careService !== name)
      );
    }
  };

  const sortedInitial = [...hotelData.careServices].sort();
  const sortedCurrent = [...hotelCareServices].sort();

  return (
    <form
      className="mt-8"
      onSubmit={(event) => {
        handleHotelCareServices(
          event,
          hotelToken,
          hotelCareServices,
          setLoading
        );
      }}
    >
      <label className="text-lg font-semibold">
        {translate("hotelAdditionalServices")}
      </label>
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {careServices.map((service) => (
          <Checkbox
            key={service.id}
            label={service.label}
            name={service.name}
            id={service.id}
            defaultChecked={hotelCareServices.includes(service.name)}
            onChange={handleCareServices}
          />
        ))}
      </div>
      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            JSON.stringify(sortedInitial) === JSON.stringify(sortedCurrent)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </form>
  );
};

export default HotelCareServices;
