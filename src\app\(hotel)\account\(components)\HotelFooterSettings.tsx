"use client";
import React from "react";
import SwitchDarkMode from "@/shared/SwitchDarkMode";
import LangDropdownSingle from "@/app/(components)/(Header)/LangDropdownSingle";
import { useLogin } from "@/hooks/useLogin";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useTranslations } from "next-intl";

const HotelFooterSettings = () => {
  const { logoutUser } = useLogin();
  const translate = useTranslations("NavMobile");

  return (
    <div className="bg-neutral-50 dark:bg-neutral-900 flex flex-col gap-5 items-center md:hidden pb-10">
      <div className="flex space-x-2 lg:space-x-1">
        <SwitchDarkMode className="mx-0.5" />
        <LangDropdownSingle />
      </div>
      <Link
        href="/account/my-user"
        className="bg-transparent w-60 flex justify-center border border-red rounded p-3 py-2"
      >
        {translate("myAccount")}
      </Link>
      <Button
        onClick={logoutUser}
        className="bg-transparent w-60"
        variant="outline"
      >
        {translate("logout")}
      </Button>
    </div>
  );
};

export default HotelFooterSettings;
