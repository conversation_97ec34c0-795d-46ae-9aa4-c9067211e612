"use client";
import React from "react";
import type { FC } from "react";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  setText,
  setOldText,
  setIsEdit,
  setMessageId,
} from "@/store/features/liveChat/live-chat-slice";
import { useDispatch } from "react-redux";
import { useSocket } from "@/app/Socket";

interface MessageOptionsProps {
  text: string;
  messageId: string;
  selectedId: string | string[] | undefined;
  hotelId: string;
  isOptionOpen: boolean;
  setIsOptionOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isHotel: boolean;
}

const MessageOptions: FC<MessageOptionsProps> = ({
  text,
  messageId,
  selectedId,
  hotelId,
  isOptionOpen,
  setIsOptionOpen,
  isHotel,
}) => {
  const dispatch = useDispatch();
  const { sendDeleted } = useSocket();

  const deleteMessageHandler = () => {
    sendDeleted({
      from: hotelId,
      to: selectedId,
      messageId: messageId,
    });
  };

  return (
    <DropdownMenu open={isOptionOpen} onOpenChange={setIsOptionOpen}>
      <DropdownMenuTrigger
        onClick={() => setIsOptionOpen(true)}
        className="absolute right-1 top-2 bg-neutral-200 cursor-pointer rounded-md invisible group-hover:visible max-md:opacity-0"
      >
        <ChevronDown className="size-4 text-neutral-800" />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem
          onClick={() => {
            navigator.clipboard.writeText(text);
          }}
        >
          Kopyala
        </DropdownMenuItem>
        {isHotel && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                dispatch(setText(text));
                dispatch(setOldText(text));
                dispatch(setMessageId(messageId));
                dispatch(setIsEdit(true));
              }}
            >
              Düzenle
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={deleteMessageHandler}>
              Sil
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MessageOptions;
