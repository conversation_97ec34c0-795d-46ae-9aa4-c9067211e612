import React from "react";
import type { FC } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Users,
  Heart,
  Settings,
  Edit3,
  Trash2,
  CheckCircle,
  XCircle,
  Calendar,
} from "lucide-react";
import Image from "next/image";
import defaultHotelImage from "@/images/default-hotel-photo.jpg";
import type { VehicleDataApiTypes } from "@/types/taxi/vehicleType";
import { PET_TYPES } from "@/app/(enums)/enums";
import type { PetTaxiDataApiTypes } from "@/types/taxi/taxiDataType";
import DeleteVehicleModal from "./(deleteVehicles)/DeleteVehicleModal";
import UpdateVehicleModal from "./(updateVehicles)/UpdateVehicleModal";

export interface PetTaxiVehicleCardProps {
  taxiData: PetTaxiDataApiTypes;
  vehicle: VehicleDataApiTypes;
  petTaxiToken: string | undefined;
}

const PetTaxiVehicleCard: FC<PetTaxiVehicleCardProps> = ({
  taxiData,
  vehicle,
  petTaxiToken,
}) => {
  const {
    _id,
    acceptedPetTypes,
    vehicleFeatures,
    vehicleName,
    vehiclePlate,
    vehicleCapacity,
    vehicleDescription,
    images,
    passive,
    cleaningCertificate,
    vehicleType,
  } = vehicle;

  const vehicleImage =
    images?.length > 0 && images[0]?.src ? images[0]?.src : defaultHotelImage;

  const vehicleFeatureLabels: Record<string, string> = {
    air_conditioning: "Klima",
    camera: "Kamera",
    partitioned_compartment: "Kafesli Bölme",
    gps_tracking: "GPS Takip",
    ventilation: "Havalandırma",
    non_slip_floor: "Kaymaz Zemin",
  };

  const vehicleTypeLabels: Record<string, string> = {
    panel_van: "Panelvan",
    minivan: "Minivan",
    small_commercial_van: "Küçük Ticari Araç (Küçük Panelvan)",
    passenger_car: "Binek Otomobil (Sedan / Hatchback)",
    suv: "SUV",
    crossover: "Crossover",
    caravan: "Karavan / Mobil Klinik Araç",
  };

  return (
    <Card
      key={_id}
      className="group hover:shadow-xl transition-all duration-300 border-0 shadow-md overflow-hidden bg-white dark:bg-gray-800"
    >
      <div className="relative">
        <Image
          src={vehicleImage}
          alt={vehicleName}
          width={500}
          height={300}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 left-4">
          <Badge
            variant={passive ? "secondary" : "default"}
            className={
              passive
                ? "bg-gray-500 dark:bg-gray-700 dark:text-white"
                : "bg-emerald-600 dark:bg-emerald-700 dark:text-white"
            }
          >
            {passive ? "Pasif" : "Aktif"}
          </Badge>
        </div>
        <div className="absolute top-4 right-4">
          <Badge
            className={
              cleaningCertificate
                ? "bg-blue-600 dark:bg-blue-700 dark:text-white"
                : "bg-white/90 dark:bg-gray-800 dark:text-white"
            }
            variant={cleaningCertificate ? undefined : "outline"}
          >
            {cleaningCertificate ? (
              <CheckCircle className="w-3 h-3 mr-1" />
            ) : (
              <XCircle className="w-3 h-3 mr-1" />
            )}
            Temizlik Sertifikası
          </Badge>
        </div>
      </div>

      <CardContent className="p-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-1">
              {vehicleName}
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
              <span className="font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                {vehiclePlate}
              </span>
              <Badge
                variant="outline"
                className="text-xs dark:border-gray-600 dark:text-gray-300"
              >
                {vehicleTypeLabels[vehicleType] || vehicleType}
              </Badge>
            </div>
          </div>

          <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
            {vehicleDescription}
          </p>

          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1 text-gray-700 dark:text-gray-300">
              <Users className="w-4 h-4" />
              <span className="font-medium">{vehicleCapacity}</span>
            </div>
          </div>

          <Separator className="dark:bg-gray-700" />

          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-200 mb-2 flex items-center gap-1">
                <Heart className="w-4 h-4" />
                Kabul Edilen Evcil Hayvanlar
              </h4>
              <div className="flex flex-wrap gap-1">
                {acceptedPetTypes.map((pet, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs dark:bg-gray-700 dark:text-gray-200"
                  >
                    {PET_TYPES[pet as keyof typeof PET_TYPES] || pet}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-200 mb-2 flex items-center gap-1">
                <Settings className="w-4 h-4" />
                Özellikler
              </h4>
              <div className="flex flex-wrap gap-1">
                {vehicleFeatures.slice(0, 3).map((feature, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs dark:border-gray-600 dark:text-gray-300"
                  >
                    {vehicleFeatureLabels[feature] || feature}
                  </Badge>
                ))}
                {vehicleFeatures.length > 3 && (
                  <Badge
                    variant="outline"
                    className="text-xs dark:border-gray-600 dark:text-gray-300"
                  >
                    +{vehicleFeatures.length - 3} daha
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <Separator className="dark:bg-gray-700" />

          <div className="flex items-center justify-between pt-2">
            <Button
              onClick={() => console.log("planla")}
              className="group relative bg-secondary-6000 hover:bg-secondary-500 text-white font-semibold px-2 h-9 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out border-0 overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
              <div className="relative flex items-center gap-1.5">
                <Calendar className="w-4 h-4 text-white/80 animate-pulse" />
                <span className="text-xs font-bold tracking-wide">
                  Seyehati Planla
                </span>
              </div>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
            </Button>

            <div className="flex items-center gap-2">
              <UpdateVehicleModal
                taxiData={taxiData}
                vehicleData={vehicle}
                petTaxiToken={petTaxiToken}
              />
              <DeleteVehicleModal petTaxiToken={petTaxiToken} vehicleId={_id} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PetTaxiVehicleCard;
