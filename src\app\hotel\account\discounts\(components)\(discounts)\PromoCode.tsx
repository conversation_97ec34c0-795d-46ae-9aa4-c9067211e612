"use client";
import React, { useState } from "react";
import type { ChangeEvent, FC } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import { useHotelPromo } from "@/hooks/hotel/discounts/useHotelPromo";
import LoadingSpinner from "@/shared/icons/Spinner";
import { Switch } from "@/components/ui/switch";
import Checkbox from "@/shared/Checkbox";

interface PromoCodeProps {
  hotelToken: string | undefined;
  closeModal: () => void;
}

const PromoCode: FC<PromoCodeProps> = ({ hotelToken, closeModal }) => {
  const { createPromo } = useHotelPromo();
  const [promoCode, setPromoCode] = useState<any>({
    isActive: true,
    code: "",
    discount: 0,
    startDate: "",
    endDate: "",
    usageLimit: 0,
    minimumConditionValue: 0,
    discountType: "",
    promoType: "",
    condition: "none",
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setPromoCode((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleConditionChange = (selected: string) => {
    setPromoCode((prev: any) => ({
      ...prev,
      condition: selected,
      ...(selected === "none" && { minimumConditionValue: 0 }),
    }));
  };

  const handleTodayDateCheckbox = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;

    if (checked) {
      const today = new Date().toISOString().split("T")[0];
      setPromoCode((prev: any) => ({
        ...prev,
        startDate: today,
      }));
    } else {
      setPromoCode((prev: any) => ({
        ...prev,
        startDate: "",
      }));
    }
  };

  const isValid = () => {
    const {
      code,
      discount,
      startDate,
      endDate,
      usageLimit,
      condition,
      minimumConditionValue,
    } = promoCode;

    if (
      condition !== "none" &&
      (!minimumConditionValue || minimumConditionValue <= 0)
    ) {
      return false;
    }

    return (
      code.trim() !== "" &&
      discount > 0 &&
      startDate.trim() !== "" &&
      endDate.trim() !== "" &&
      usageLimit > 0 &&
      promoCode.discountType.trim() !== "" &&
      promoCode.promoType.trim() !== ""
    );
  };

  const buttonDisabled = isValid();

  return (
    <form
      onSubmit={(event) =>
        createPromo(
          event,
          hotelToken,
          promoCode,
          setLoading,
          closeModal,
          setDisabled
        )
      }
    >
      <h2 className="font-medium text-lg mb-3">Promosyon Kodu</h2>
      <div className="space-y-4">
        <FormItem label="Promosyon Kodu" className="w-full">
          <Input name="code" onChange={handleChange} />
        </FormItem>
        <FormItem label="İndirim Tipi">
          <Select
            onValueChange={(selected) =>
              setPromoCode((prev: any) => ({
                ...prev,
                discountType: selected,
              }))
            }
          >
            <SelectTrigger className="w-full rounded-2xl">
              <SelectValue placeholder="İndirim Tipi Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="percentage">Yüzde</SelectItem>
              <SelectItem value="fixedAmount">Sabit Tutar</SelectItem>
              <SelectItem value="freeNights">Ücretsiz Gece</SelectItem>
              <SelectItem value="freeService">Ücretsiz Hizmet</SelectItem>
              <SelectItem value="custom">Özel</SelectItem>
            </SelectContent>
          </Select>
        </FormItem>
        <FormItem label="Promosyon Tipi">
          <Select
            onValueChange={(selected) =>
              setPromoCode((prev: any) => ({
                ...prev,
                promoType: selected,
              }))
            }
          >
            <SelectTrigger className="w-full rounded-2xl">
              <SelectValue placeholder="Promosyon Tipi Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="reservationDiscount">
                Konaklama İndirimi
              </SelectItem>
              <SelectItem value="serviceDiscount">Hizmet İndirimi</SelectItem>
              <SelectItem value="subscriptionDiscount">
                Üyelik Kartı İndirimi
              </SelectItem>
              <SelectItem value="orderDiscount">
                Rezervasyon İndirimi
              </SelectItem>
              <SelectItem value="firstReservation">İlk Rezervasyon</SelectItem>
            </SelectContent>
          </Select>
        </FormItem>
        <FormItem label="İndirim Tutarı" className="w-full">
          <Input
            min={0}
            placeholder={
              promoCode.discountType === "percentage"
                ? "%"
                : promoCode.discountType === "fixedAmount"
                  ? "₺"
                  : promoCode.discountType === "freeNights"
                    ? "Gece"
                    : promoCode.discountType === "freeService"
                      ? "Hizmet"
                      : ""
            }
            name="discount"
            type="number"
            onChange={handleChange}
          />
        </FormItem>
        <FormItem label="Başlangıç Tarihi" className="w-full">
          <Input
            name="startDate"
            type="date"
            onChange={handleChange}
            value={promoCode.startDate}
          />
          <Checkbox
            id="todayDate"
            label="Bugünün Tarihini Seç"
            name="todayDate"
            checked={
              promoCode.startDate === new Date().toISOString().split("T")[0]
            }
            onChange={handleTodayDateCheckbox}
            className="mt-2 !text-sm"
            inputClass="!size-5 !rounded-lg !-mr-2"
          />
        </FormItem>
        <FormItem label="Bitiş Tarihi" className="w-full">
          <Input name="endDate" type="date" onChange={handleChange} />
        </FormItem>
        <FormItem label="Kullanım Limiti" className="w-full">
          <Input
            min={1}
            name="usageLimit"
            type="number"
            onChange={handleChange}
          />
        </FormItem>
        <FormItem label="Kullanım Koşulu">
          <Select
            value={promoCode.condition}
            onValueChange={(selected) => handleConditionChange(selected)}
          >
            <SelectTrigger className="w-full rounded-2xl">
              <SelectValue placeholder="Kullanım Koşulu Seç" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Yok</SelectItem>
              <SelectItem value="totalReservationNights">
                Konaklama gece sayısı (Bir rezervasyondaki minimum konaklama
                sayısı)
              </SelectItem>
              <SelectItem value="totalOrderPrice">
                Toplam Rezervasyon Fiyatı
              </SelectItem>
              <SelectItem value="reservationCount">
                Rezervasyon sayısı (Tek seferde minimum rezervasyon sayısı)
              </SelectItem>
              <SelectItem value="serviceCount">
                Hizmet sayısı (Tek seferde minimum hizmet sayısı)
              </SelectItem>
              <SelectItem value="firstReservation">İlk rezervasyon</SelectItem>
              <SelectItem value="betweenDates">
                Belirli tarihler arası
              </SelectItem>{" "}
            </SelectContent>
          </Select>
        </FormItem>
        {promoCode.condition !== "none" && (
          <FormItem label="Minimum Kullanım Koşulu" className="w-full">
            <Input
              name="minimumConditionValue"
              type="number"
              min={0}
              onChange={handleChange}
            />
          </FormItem>
        )}
        <FormItem label="Aktiflik Durumu">
          <div className="flex gap-2">
            <Switch
              name="isActive"
              checked={promoCode.isActive}
              onCheckedChange={(checked) =>
                setPromoCode((prev: any) => ({
                  ...prev,
                  isActive: checked,
                }))
              }
              className="data-[state=unchecked]:bg-red-500 data-[state=checked]:bg-green-500"
            />
            <p
              className={promoCode.isActive ? "text-green-500" : "text-red-500"}
            >
              {promoCode.isActive ? "Aktif" : "Pasif"}
            </p>
          </div>
        </FormItem>
      </div>
      <div className="mt-7 flex justify-end gap-5">
        <Button onClick={closeModal} variant="outline" type="button">
          İptal
        </Button>
        <Button
          disabled={!buttonDisabled || disabled}
          className="bg-secondary-6000 hover:bg-secondary-700 text-white"
          type="submit"
        >
          {loading ? <LoadingSpinner /> : "Kaydet"}
        </Button>
      </div>
    </form>
  );
};

export default PromoCode;
