export interface PetInformationApiTypes {
  _id: string;
  petOwner: string;
  name: string;
  age: string;
  kind: string;
  breed: string;
  gender: "male" | "female" | string;
  color: string;
  specified: string;
  infertile: boolean;
  microChipNumber: string;
  vaccines: Vaccine[];
  internalParasiteTreatment: boolean;
  externalParasiteTreatment: boolean;
  internalTreatmentDate: string;
  externalTreatmentDate: string;
  treatmentDate: string;
  allergicTo: string[];
  hereditaryDiseases: string[];
  operationHistory: string[];
  medicine: string;
  feedingHabits: string;
  description: string;
  images: ImageType[];
  documentPhotos: ImageType[];
  passive: boolean;
}
export interface ImageType {
  img800: Img800;
  img400: Img400;
  img200: Img200;
  img100: Img100;
  _id: string;
  src: string;
  width: number;
  height: number;
  size: number;
  alt: string;
  mimetype: string;
  fit: string;
  tags: string;
  createdDate: string;
}

export interface Img800 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img400 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img200 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Img100 {
  src: string;
  width: number;
  height: number;
  size: number;
}

export interface Vaccine {
  vaccineName: string;
  treatmentDate: string;
}

export interface PetInformationTypes {
  name: string; //
  age: string; //
  kind: string; //
  breed: string; //
  gender: string; //
  color: string; //
  specified: string;
  infertile: boolean | null | string; //
  microChipNumber: string; //
  vaccines: Vaccine[];
  internalParasiteTreatment: boolean;
  externalParasiteTreatment: boolean;
  internalTreatmentDate: string;
  externalTreatmentDate: string;
  allergicTo: string[];
  hereditaryDiseases: string[];
  operationHistory: string[];
  medicine: string;
  feedingHabits: string;
  description: string;
  documentPhotos: ImageType[];
}

export interface updatePetInformationTypes {
  _id: string;
  name: string; //
  age: string; //
  kind: string; //
  breed: string; //
  gender: string; //
  color: string; //
  specified: string;
  infertile: boolean; //
  microChipNumber: string; //
  vaccines: Vaccine[];
  internalParasiteTreatment: boolean;
  externalParasiteTreatment: boolean;
  treatmentDate: string;
  allergicTo: string[];
  hereditaryDiseases: string[];
  operationHistory: string[];
  medicine: string;
  feedingHabits: string;
  description: string;
}

export type PetKind =
  | "dog"
  | "cat"
  | "horse"
  | "turtle"
  | "rabbit"
  | "hamster"
  | "snake"
  | "iguana"
  | "monkey"
  | "rooster"
  | "other";

export type BreedList = Record<string, string[]>;

export type VaccineList = Record<string, string[]>;

export const breedList: BreedList = {
  smallDogBreed: [
    "Pomeranian",
    "French Bulldog",
    "Yorkshire Terrier",
    "Shih Tzu",
    "Maltese Terrier",
    "Chihuahua",
    "Dachshund",
    "Miniature Schnauzer",
    "Cavalier King Charles Spaniel",
    "Pug",
    "Boston Terrier",
    "Bichon Frise",
    "Papillon",
    "Jack Russell Terrier",
    "Italian Greyhound",
    "Lhasa Apso",
    "Havanese",
    "Toy Poodle",
    "Pekingese",
    "Brussels Griffon",
    "Japanese Chin",
    "Scottish Terrier",
    "Welsh Corgi (Pembroke)",
    "Chinese Crested",
    "Australian Terrier",
    "West Highland White Terrier (Westie)",
    "Silky Terrier",
    "Coton de Tulear",
    "Norfolk Terrier",
    "Norwich Terrier",
    "Teacup Pomeranian",
    "Teacup Chihuahua",
    "Miniature Pinscher",
    "Mix (Kırma)",
  ],
  mediumDogBreed: [
    "Border Collie",
    "Bulldog",
    "Standard Schnauzer",
    "American Staffordshire Terrier",
    "Australian Shepherd",
    "Beagle",
    "Basset Hound",
    "Cocker Spaniel",
    "Whippet",
    "English Springer Spaniel",
    "Samoyed",
    "Shetland Sheepdog",
    "Finnish Spitz",
    "Portuguese Water Dog",
    "Shiba Inu",
    "Mix (Kırma)",
  ],
  largeDogBreed: [
    "Golden Retriever",
    "Labrador Retriever",
    "German Shepherd",
    "Rottweiler",
    "Bernese Mountain Dog",
    "Great Dane",
    "Saint Bernard",
    "Alaskan Malamute",
    "Siberian Husky",
    "Newfoundland",
    "Doberman Pinscher",
    "Akita",
    "Belgian Malinois",
    "Irish Wolfhound",
    "English Mastiff",
    "Bullmastiff",
    "Boxer",
    "Weimaraner",
    "Rhodesian Ridgeback",
    "Great Pyrenees",
    "Anatolian Shepherd Dog",
    "Cane Corso",
    "Leonberger",
    "Kuvasz",
    "Dogue de Bordeaux",
    "Tibetan Mastiff",
    "Afghan Hound",
    "Borzoi",
    "Irish Setter",
    "Giant Schnauzer",
    "Komondor",
    "Black Russian Terrier",
    "Caucasian Shepherd Dog",
    "Mix (Kırma)",
  ],
  cat: [
    "Siamese",
    "Persian",
    "Maine Coon",
    "Bengal",
    "Sphynx",
    "British Shorthair",
    "Abyssinian",
    "Ragdoll",
    "Scottish Fold",
    "Savannah",
    "Norwegian Forest Cat",
    "Turkish Angora",
    "Turkish Van",
    "Cornish Rex",
    "Exotic Shorthair",
    "Russian Blue",
    "Tekir",
  ],
  horse: [
    "Arabian",
    "Thoroughbred",
    "Quarter Horse",
    "Clydesdale",
    "Shetland Pony",
    "Friesian",
    "Appaloosa",
    "Shire Horse",
    "Mustang",
  ],
  turtle: [
    "Red-Eared Slider",
    "Eastern Box Turtle",
    "Russian Tortoise",
    "Greek Tortoise",
    "Sulcata Tortoise",
    "Painted Turtle",
    "Snapping Turtle",
  ],
  rabbit: [
    "Netherland Dwarf",
    "Holland Lop",
    "Lionhead",
    "Flemish Giant",
    "Mini Rex",
    "Angora",
    "French Lop",
    "English Spot",
    "Harlequin",
  ],
  hamster: [
    "Syrian Hamster",
    "Dwarf Campbell Russian Hamster",
    "Dwarf Winter White Russian Hamster",
    "Roborovski Hamster",
    "Chinese Hamster",
  ],
  snake: [
    "Ball Python",
    "Corn Snake",
    "King Snake",
    "Garter Snake",
    "Boa Constrictor",
    "Milk Snake",
    "Western Hognose Snake",
    "Green Tree Python",
  ],
  iguana: [
    "Green Iguana",
    "Red Iguana",
    "Blue Iguana",
    "Desert Iguana",
    "Fiji Banded Iguana",
    "Spiny-Tailed Iguana",
  ],
  monkey: [
    "Capuchin Monkey",
    "Squirrel Monkey",
    "Marmoset",
    "Tamarin",
    "Spider Monkey",
    "Howler Monkey",
    "Macaque",
  ],
  rooster: [
    "Leghorn",
    "Rhode Island Red",
    "Sussex",
    "Plymouth Rock",
    "Silkie",
    "Brahma",
    "Orpington",
    "Cochin",
  ],
  other: [],
};
export const vaccineList: VaccineList = {
  smallDogBreed: [
    "rabies",
    "distemper",
    "parvovirus",
    "adenovirus",
    "leptospirosis",
    "bordetella",
    "lyme",
    "influenza",
    "canine influenza",
    "coronavirus",
  ],
  mediumDogBreed: [
    "rabies",
    "distemper",
    "parvovirus",
    "adenovirus",
    "leptospirosis",
    "bordetella",
    "lyme",
    "influenza",
    "canine influenza",
    "coronavirus",
  ],
  largeDogBreed: [
    "rabies",
    "distemper",
    "parvovirus",
    "adenovirus",
    "leptospirosis",
    "bordetella",
    "lyme",
    "influenza",
    "canine influenza",
    "coronavirus",
  ],
  cat: [
    "feline rhinotracheitis",
    "calicivirus",
    "panleukopenia",
    "rabies",
    "feline leukemia virus (FeLV)",
    "chlamydia",
    "feline immunodeficiency virus (FIV)",
  ],
  horse: [
    "tetanus",
    "equine influenza",
    "west nile virus",
    "rabies",
    "equine herpesvirus (EHV)",
    "strangles",
    "potomac horse fever",
    "equine viral arteritis (EVA)",
    "rotavirus",
    "EIA (Equine Infectious Anemia)",
  ],
  parrot: [
    "Psittacine Beak and Feather Disease (PBFD)",
    "Avian Influenza",
    "Newcastle Disease",
    "Chlamydiosis",
  ],
  canary: ["Chlamydiosis", "Avian Influenza", "Newcastle Disease"],
  budgerigar: [
    "Psittacosis (Chlamydiosis)",
    "Avian Influenza",
    "Newcastle Disease",
  ],
  cockatiel: [
    "Psittacosis (Chlamydiosis)",
    "Avian Influenza",
    "Newcastle Disease",
  ],
  lovebird: [
    "Psittacosis (Chlamydiosis)",
    "Avian Influenza",
    "Newcastle Disease",
  ],
  finch: ["Avian Influenza", "Newcastle Disease"],
  dove: ["Psittacosis (Chlamydiosis)", "Avian Influenza", "Newcastle Disease"],
  rabbit: ["Rabbit Hemorrhagic Disease (RHD)", "Myxomatosis"],
  ferret: ["rabies", "canine distemper"],
  monkey: [
    "rabies",
    "tetanus",
    "hepatitis A",
    "hepatitis B",
    "measles",
    "polio",
    "influenza",
  ],
  rooster: [
    "newcastle disease",
    "infectious bronchitis",
    "fowl pox",
    "avian influenza",
    "marek's disease",
  ],
  other: [],
};

export const petTypes = [
  { value: "cat", label: "Kedi" },
  { value: "smallDogBreed", label: "Köpek (Küçük Irk)" },
  { value: "mediumDogBreed", label: "Köpek (Orta Irk)" },
  { value: "largeDogBreed", label: "Köpek (Büyük Irk)" },
  { value: "parrot", label: "Papağan" },
  { value: "canary", label: "Kanarya" },
  { value: "budgerigar", label: "Muhabbet kuşu" },
  { value: "cockatiel", label: "Sultan Papağanı" },
  { value: "lovebird", label: "Sevda Papağanı" },
  { value: "finch", label: "İspinoz" },
  { value: "dove", label: "Güvercin (Evcil Tür)" },
  { value: "rabbit", label: "Tavşan" },
  { value: "hamster", label: "Hamster" },
  { value: "guineaPig", label: "Ginepig" },
  { value: "ferret", label: "Gelincik" },
  { value: "chinchilla", label: "Şinşila" },
  { value: "hedgehog", label: "Kirpi" },
  { value: "turtle", label: "Kaplumbağa" },
  { value: "iguana", label: "İguana" },
  { value: "snake", label: "Yılan" },
  { value: "fish", label: "Balık (Akvaryum Türleri)" },
  { value: "horse", label: "At" },
  { value: "pony", label: "Midilli" },
  { value: "donkey", label: "Eşek" },
  { value: "goat", label: "Keçi" },
  { value: "sheep", label: "Koyun" },
  { value: "alpaca", label: "Alpaka" },
  { value: "llama", label: "Lama" },
  { value: "other", label: "Diğer" },
];
