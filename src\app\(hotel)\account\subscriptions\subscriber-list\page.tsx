import React from "react";
import getMyHotel from "@/actions/(protected)/hotel/getMyHotel";
import { getHotelsSubscribers } from "@/actions/(protected)/hotel/getHotelsSubscribers";
import { getMembershipByHotel } from "@/actions/(protected)/hotel/getMembershipByHotel";
import SubscriberList from "../(components)/SubscriberList";
import { redirect } from "next/navigation";

const SubscriberListPage = async () => {
  const hotelData = await getMyHotel();
  const subscriberData = await getHotelsSubscribers(hotelData?.data?._id);
  const membershipData = await getMembershipByHotel(hotelData?.data?._id);

  if (membershipData?.data?.membershipType !== "plus") {
    redirect("/account");
  }

  return (
    <div className="mt-5">
      <SubscriberList
        subscriberData={subscriberData?.data?.hotelsSubscribers}
      />
    </div>
  );
};

export default SubscriberListPage;
