import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(req: NextRequest) {
  const imageUrl = req.nextUrl.searchParams.get("url");
  const filename = req.nextUrl.searchParams.get("filename") || "chat-file.jpg";

  if (!imageUrl) {
    return new NextResponse("URL param is required", { status: 400 });
  }

  const response = await fetch(imageUrl);
  const arrayBuffer = await response.arrayBuffer();

  const res = new NextResponse(new Uint8Array(arrayBuffer));
  res.headers.set("Content-Disposition", `attachment; filename=${filename}`);
  res.headers.set(
    "Content-Type",
    response.headers.get("Content-Type") || "application/octet-stream"
  );

  return res;
}
