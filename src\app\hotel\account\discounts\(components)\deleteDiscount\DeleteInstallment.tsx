"use client";
import React, { useState } from "react";
import type { FC } from "react";
import IconDelete from "@/shared/icons/Delete";
import LoadingSpinner from "@/shared/icons/Spinner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useHotelInstallment } from "@/hooks/hotel/discounts/useHotelInstallment";

interface DeleteInstallmentProps {
  hotelToken: string | undefined;
  installmentId: string;
}

const DeleteInstallment: FC<DeleteInstallmentProps> = ({
  hotelToken,
  installmentId,
}) => {
  const { deleteInstallment } = useHotelInstallment();
  const [deleteInstallmentIsOpen, setDeleteInstallmentIsOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);

  const closeModal = () => {
    setDeleteInstallmentIsOpen(false);
    setLoading(false);
    setTimeout(() => {
      setDisabled(false);
    }, 2000);
  };

  return (
    <div>
      <IconDelete
        onClick={() => setDeleteInstallmentIsOpen(true)}
        className="size-5 cursor-pointer duration-200 hover:text-secondary-6000 text-neutral-500 dark:text-neutral-400"
      />
      <Dialog open={deleteInstallmentIsOpen}>
        <DialogContent onInteractOutside={closeModal}>
          <DialogHeader>
            <DialogTitle>Taksit Kampanya Kaldırma</DialogTitle>
            <DialogDescription className="sr-only"></DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(event) =>
              deleteInstallment(
                event,
                hotelToken,
                installmentId,
                setLoading,
                setDisabled,
                closeModal
              )
            }
          >
            <div className="mb-4 mt-2">
              <p className="text-gray-500 dark:text-neutral-200">
                Seçili taksit kampanyası kaldırılsın mı?
              </p>
            </div>
            <div className="flex justify-end gap-5">
              <Button variant="outline" type="button" onClick={closeModal}>
                Vazgeç
              </Button>
              <Button
                disabled={disabled}
                className="bg-secondary-6000 hover:bg-secondary-700 text-white"
                type="submit"
              >
                {loading ? <LoadingSpinner /> : "Onayla"}
              </Button>
            </div>
          </form>
          <DialogClose
            onClick={closeModal}
            className="absolute right-4 top-4 z-20 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="size-4" />
            <span className="sr-only">Close</span>
          </DialogClose>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DeleteInstallment;
