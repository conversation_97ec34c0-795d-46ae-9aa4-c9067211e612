import React from "react";
import type { <PERSON> } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";
import { avatarColors } from "@/contains/contants";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface HotelDetailProps {
  contactDetail: any;
}

const HotelDetail: FC<HotelDetailProps> = ({ contactDetail }) => {
  const translate = useTranslations("AddPet");
  const _setBgColor = (name: string) => {
    const backgroundIndex = Math.floor(
      name.charCodeAt(0) % avatarColors.length
    );
    return avatarColors[backgroundIndex];
  };

  return (
    <div className="w-full space-y-5 p-2">
      <div className="p-3 text-center border-b border-gray-200">
        <Avatar className="w-16 h-16 mx-auto mb-3">
          <AvatarImage src={contactDetail?.images?.logo?.src} />
          <AvatarFallback className="bg-blue-100 text-blue-600 text-xl uppercase">
            {contactDetail?.hotelName?.slice(0, 1)}
          </AvatarFallback>
        </Avatar>
        <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-1 capitalize">
          {contactDetail?.hotelName}
        </h2>

        <p className="text-sm text-neutral-500 dark:text-neutral-400">
          {contactDetail?.address?.cityName}, Türkiye
        </p>
      </div>
      <div className="rounded-lg overflow-hidden">
        {contactDetail?.images?.gallery?.length > 0 && (
          <Carousel>
            <CarouselContent>
              {contactDetail?.images?.gallery?.map((image: any) => {
                return (
                  <CarouselItem className="h-72 relative" key={image?._id}>
                    <Image
                      className="object-cover"
                      src={image?.img800?.src || image?.src}
                      fill
                      alt={contactDetail?.hotelName}
                    />
                  </CarouselItem>
                );
              })}
            </CarouselContent>
            <CarouselPrevious className="left-2 cursor-pointer p-3" />
            <CarouselNext className="right-2 cursor-pointer p-3" />
          </Carousel>
        )}
      </div>
      <div className="space-y-5">
        <div>
          <h4 className="text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-1">
            Kabul Edilen Türler
          </h4>
          <Separator className="w-20 mb-3" />
          <div className="flex flex-wrap gap-3">
            {contactDetail?.acceptedPetTypes?.map((petType: any) => {
              return (
                <Badge
                  className="font-medium pointer-events-none text-white dark:text-neutral-100"
                  style={{
                    backgroundColor: _setBgColor(petType),
                  }}
                  key={petType}
                >
                  {translate(petType)}
                </Badge>
              );
            })}
          </div>
        </div>
        <div className="space-y-5">
          <div>
            <h4 className="text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-1">
              Adres
            </h4>
            <Separator className="w-20 mb-3" />
            <span className="block mt-2 text-neutral-500 dark:text-neutral-400 capitalize text-sm">
              {contactDetail?.address?.streetName},{" "}
              {contactDetail?.address?.buildingName}{" "}
              {contactDetail?.address?.buildingNumber},{" "}
              {contactDetail?.address?.district},{" "}
              {contactDetail?.address?.region}/
              {contactDetail?.address?.cityName}
            </span>
          </div>
          {contactDetail?.googleMapUrl && (
            <div className="aspect-w-5 aspect-h-5 ring-1 ring-black/10 rounded-xl z-0">
              <div className="rounded-xl overflow-hidden z-0">
                <iframe
                  width="100%"
                  height="100%"
                  loading="lazy"
                  allowFullScreen
                  referrerPolicy="no-referrer-when-downgrade"
                  src={contactDetail?.googleMapUrl}
                />
              </div>
            </div>
          )}
          <div className="h-10">{/* do not remove */}</div>
        </div>
      </div>
    </div>
  );
};

export default HotelDetail;
