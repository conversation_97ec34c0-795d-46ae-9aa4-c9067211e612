"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState } from "react";
import Checkbox from "@/shared/Checkbox";
import { useHotelInformationsActions } from "@/hooks/hotel/useHotelInformations";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useTranslations } from "next-intl";
import type { HotelDataApiTypes } from "@/types/hotel/hotelDataType";

interface HotelPetTypesProps {
  hotelToken: string | undefined;
  hotelData: HotelDataApiTypes;
}

interface PetTypes {
  label: string;
  name: string;
  id: string;
}

const HotelPetTypes: FC<HotelPetTypesProps> = ({ hotelData, hotelToken }) => {
  const translate = useTranslations("HotelPetTypes");
  const [hotelAcceptedPetTypes, setHotelAcceptedPetTypes] = useState<string[]>(
    hotelData?.acceptedPetTypes || []
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(false);
  const { handleHotelPetTypes } = useHotelInformationsActions();

  const petTypes: PetTypes[] = [
    { id: "cat-id", name: "cat", label: translate("cat") }, // Kedi
    {
      id: "smallDogBreed-id",
      name: "smallDogBreed",
      label: translate("smallDogBreed"),
    }, // Köpek (Küçük Irk)
    {
      id: "mediumDogBreed-id",
      name: "mediumDogBreed",
      label: translate("mediumDogBreed"),
    }, // Köpek (Orta Irk)
    {
      id: "largeDogBreed-id",
      name: "largeDogBreed",
      label: translate("largeDogBreed"),
    }, // Köpek (Büyük Irk)
    { id: "parrot-id", name: "parrot", label: translate("parrot") }, // Papağan
    { id: "canary-id", name: "canary", label: translate("canary") }, // Kanarya
    { id: "budgerigar-id", name: "budgerigar", label: translate("budgerigar") }, // Muhabbet kuşu
    { id: "cockatiel-id", name: "cockatiel", label: translate("cockatiel") }, // Sultan Papağanı
    { id: "lovebird-id", name: "lovebird", label: translate("lovebird") }, // Sevda Papağanı
    { id: "finch-id", name: "finch", label: translate("finch") }, // İspinoz
    { id: "dove-id", name: "dove", label: translate("dove") }, // Güvercin (Evcil Tür)
    { id: "rabbit-id", name: "rabbit", label: translate("rabbit") }, // Tavşan
    { id: "hamster-id", name: "hamster", label: translate("hamster") }, // Hamster
    { id: "guineaPig-id", name: "guineaPig", label: translate("guineaPig") }, // Gine Domuzu
    { id: "ferret-id", name: "ferret", label: translate("ferret") }, // Gelincik
    { id: "chinchilla-id", name: "chinchilla", label: translate("chinchilla") }, // Şinşila
    { id: "hedgehog-id", name: "hedgehog", label: translate("hedgehog") }, // Kirpi
    { id: "turtle-id", name: "turtle", label: translate("turtle") }, // Kaplumbağa
    { id: "iguana-id", name: "iguana", label: translate("iguana") }, // İguana
    { id: "snake-id", name: "snake", label: translate("snake") }, // Yılan
    { id: "fish-id", name: "fish", label: translate("fish") }, // Balık (Akvaryum Türleri)
    { id: "horse-id", name: "horse", label: translate("horse") }, // At
    { id: "pony-id", name: "pony", label: translate("pony") }, // Midilli
    { id: "donkey-id", name: "donkey", label: translate("donkey") }, // Eşek
    { id: "goat-id", name: "goat", label: translate("goat") }, // Keçi
    { id: "sheep-id", name: "sheep", label: translate("sheep") }, // Koyun
    { id: "alpaca-id", name: "alpaca", label: translate("alpaca") }, // Alpaka
    { id: "llama-id", name: "llama", label: translate("llama") }, // Lama
    { id: "other-id", name: "other", label: translate("other") }, // Diğer
  ];

  // adds hotel accepted pet types to an array and removes
  const handleAcceptedPetTypes = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;

    if (checked) {
      setHotelAcceptedPetTypes([...hotelAcceptedPetTypes, name]);
    } else {
      setHotelAcceptedPetTypes(
        hotelAcceptedPetTypes.filter((pet) => pet !== name)
      );
    }
  };

  const sortedInitial = [...hotelData.acceptedPetTypes].sort();
  const sortedCurrent = [...hotelAcceptedPetTypes].sort();

  return (
    <form
      className="mt-8"
      onSubmit={(event) => {
        handleHotelPetTypes(
          event,
          hotelToken,
          hotelAcceptedPetTypes,
          setLoading,
          setDisabled
        );
      }}
    >
      <div>
        <h2 className="text-2xl font-semibold leading-8">
          {translate("hotelAcceptedPetTypes")}
        </h2>
        <span className="text-sm text-neutral-500 dark:text-neutral-300">
          Bu alanda seçilen Evcil hayvan türleri açabileceğiniz oda türlerini ve
          satışlarınıza doğrudan etki eden bir alandır.
        </span>
      </div>
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {petTypes.map((petType) => (
          <Checkbox
            key={petType.id}
            label={petType.label}
            name={petType.name}
            id={petType.id}
            defaultChecked={hotelAcceptedPetTypes.includes(petType.name)}
            onChange={handleAcceptedPetTypes}
          />
        ))}
      </div>
      <div className="mt-10 flex justify-end">
        <Button
          className="bg-secondary-6000 hover:bg-secondary-700 text-white w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/6"
          disabled={
            disabled ||
            JSON.stringify(sortedInitial) === JSON.stringify(sortedCurrent)
          }
          type="submit"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </div>
    </form>
  );
};

export default HotelPetTypes;
