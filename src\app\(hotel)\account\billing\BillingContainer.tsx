"use client";
import type { ChangeEvent, FC } from "react";
import React, { useState, useEffect } from "react";
import FormItem from "@/shared/FormItem";
import Input from "@/shared/Input";
import { Button } from "@/components/ui/button";
import Select from "@/shared/Select";
import Textarea from "@/shared/Textarea";
import citiesData from "@/data/jsons/mernis_city_district.json";
import { useHotelBilling } from "@/hooks/hotel/useBillingInformation";
import { useTranslations } from "next-intl";
import LoadingSpinner from "@/shared/icons/Spinner";

export interface SubMerchant {
  companyType: number;
  fullName: string;
  alias: string;
  identityNumber: string;
  birthDate: string;
  gsmNumber: string;
  ibanNo: string;
  ibanAlias: string;
  address: string;
  city: number;
  district: number;
  email: string;
  website: string;
  mcc_code: string | null;
  authorizedPersonIdentityNumber: string;
  authorizedPersonBirthDate: string;
  taxOffice: string;
  hotelParamGuid?: string | null;
}

interface BillingContainerProps {
  hotelToken: string | undefined;
  hotelSubMerchantData: any;
}

const validateIban = (value: string) => {
  // Remove all whitespaces
  const cleanValue = value.replace(/\s+/g, "");

  // Check if the IBAN starts with 'TR' and is 26 characters long
  if (!/^TR\d{24}$/.test(cleanValue)) {
    return false;
  }

  return true;
};

const formatIban = (value: string) => {
  // Remove all non-alphanumeric characters
  const cleanValue = value.replace(/[^a-zA-Z0-9]/g, "");

  // Insert spaces after every 4 characters, keeping 'TR' at the start
  return cleanValue.replace(/(.{4})(?!$)/g, "$1 ");
};

const BillingContainer: FC<BillingContainerProps> = ({
  hotelToken,
  hotelSubMerchantData,
}) => {
  const initialState: SubMerchant = {
    companyType: 2,
    fullName: "",
    alias: "",
    identityNumber: "",
    birthDate: "",
    gsmNumber: "",
    ibanNo: "",
    ibanAlias: "",
    address: "",
    city: 0,
    district: 0,
    email: "",
    website: "",
    mcc_code: "1",
    authorizedPersonIdentityNumber: "_",
    authorizedPersonBirthDate: "_",
    taxOffice: "",
  };
  const translate = useTranslations("BillingContainer");
  const [error, setError] = useState("");
  const { createSubMerchant, updateSubMerchant, addSubMerchantToParam } =
    useHotelBilling();
  const [initialBillingData, setInitialBillingData] = useState<SubMerchant>(
    (hotelSubMerchantData && hotelSubMerchantData[0]) || initialState
  );
  const [billingData, setBillingData] = useState<SubMerchant>(
    (hotelSubMerchantData && hotelSubMerchantData[0]) || initialState
  );
  const [disabled, setDisabled] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);

  const filteredCityDistrict = citiesData.find(
    (city) => city.ilkodu === Number(billingData.city)
  );

  const setField = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setBillingData({ ...billingData, [name]: value });
  };

  const handleAddress = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    setBillingData({ ...billingData, [name]: value });
  };

  const handleSelectChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = event.target;
    setBillingData({ ...billingData, [name]: value });
  };

  const handleIbanChange = (event: ChangeEvent<HTMLInputElement>) => {
    const rawValue = event.target.value;

    // Format the IBAN
    const formattedValue = formatIban(rawValue);

    // Validate the IBAN
    if (
      formattedValue.startsWith("TR") &&
      !validateIban(formattedValue.replace(/\s+/g, ""))
    ) {
      setError("Geçersiz IBAN formatı");
    } else {
      setError("");
    }

    // Update the state
    const { name, value } = event.target;
    setBillingData({ ...billingData, [name]: value });
  };

  // TODO: form validation yap ve htmlfor ekle @emresarigul

  // activates the button when a change is detected
  useEffect(() => {
    if (JSON.stringify(initialBillingData) !== JSON.stringify(billingData)) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [initialBillingData, billingData]);

  // updates the initial state when the data is re-fetched due to a change
  useEffect(() => {
    if (hotelSubMerchantData) {
      setInitialBillingData(hotelSubMerchantData[0]);
      setBillingData(hotelSubMerchantData[0]);
    }
  }, [hotelSubMerchantData]);

  return (
    <div className="container">
      <form
        className="max-w-[720px] space-y-5"
        onSubmit={(event) =>
          !initialBillingData.hotelParamGuid
            ? createSubMerchant(event, billingData, hotelToken, setLoading)
            : updateSubMerchant(event, billingData, hotelToken, setLoading)
        }
      >
        <div className="my-8">
          <h2 className="text-[22px] font-semibold leading-8">
            Şirket Bilgileri
          </h2>
          <span className="text-gray-400 leading-8 text-[14px]">
            Şirket türünüze göre zorunlu alanlar olacaktır
          </span>
        </div>
        <FormItem label={translate("companyType")}>
          <Select
            value={billingData?.companyType}
            onChange={handleSelectChange}
            name="companyType"
            required
          >
            <option value={2}>{translate("soleProprietorship")}</option>
            <option value={3}>{translate("stockCompany")}</option>
          </Select>
        </FormItem>
        {billingData.companyType == 3 && (
          <>
            <FormItem label={translate("authorizedPersonIdentityNumber")}>
              <Input
                name="authorizedPersonIdentityNumber"
                onChange={setField}
                value={billingData.authorizedPersonIdentityNumber}
              />
            </FormItem>
            <FormItem label={translate("authorizedPersonBirthDate")}>
              <Input
                name="authorizedPersonBirthDate"
                type="date"
                onChange={setField}
                value={billingData.authorizedPersonBirthDate}
              />
            </FormItem>
          </>
        )}
        <FormItem label={translate("taxOffice")}>
          <Input
            required={billingData.companyType == 3}
            name="taxOffice"
            onChange={setField}
            value={billingData.taxOffice}
          />
        </FormItem>
        {billingData.companyType == 2 ? (
          <FormItem label={translate("identityNumber")}>
            <Input
              name="identityNumber"
              onChange={setField}
              value={billingData.identityNumber}
            />
          </FormItem>
        ) : (
          <FormItem label={translate("taxNumber")}>
            <Input
              name="identityNumber"
              onChange={setField}
              value={billingData.identityNumber}
            />
          </FormItem>
        )}
        <FormItem label={translate("alias")}>
          <Input name="alias" onChange={setField} value={billingData.alias} />
        </FormItem>
        <FormItem label={translate("fullName")}>
          <Input
            name="fullName"
            onChange={setField}
            value={billingData.fullName}
          />
        </FormItem>
        <FormItem label={translate("birthDate")}>
          <Input
            name="birthDate"
            type={"date"}
            onChange={setField}
            value={billingData.birthDate}
          />
        </FormItem>
        <div className="!mt-20">
          <h2 className="text-[22px] font-semibold leading-8">
            Banka Bilgileri
          </h2>
          <span className="text-gray-400 leading-8 text-[14px]">
            Banka bilgisi adımı işlemler sonucunda oluşan ödemelerin hangi
            hesaba gideceğini belirlemek içindir
          </span>
        </div>
        <FormItem label="IBAN">
          <Input
            name="ibanNo"
            maxLength={32}
            onChange={(event) => handleIbanChange(event)}
            value={formatIban(billingData.ibanNo)}
            className={error ? "border-red-500" : ""}
            placeholder="TR00 0000 0000 0000 0000 0000"
          />
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </FormItem>
        <FormItem label={translate("ibanTitle")}>
          <Input
            name="ibanAlias"
            onChange={setField}
            value={billingData.ibanAlias}
          />
        </FormItem>
        <div className="!mt-20">
          <h2 className="text-[22px] font-semibold leading-8">
            Adres Bilgileri
          </h2>
          <span className="text-gray-400 leading-8 text-[14px]">
            Bu adım gelecek olan şifreler ile alt üye işyeri hesabınızın
            takibini yapmak için önemlidir
          </span>
        </div>
        <FormItem label={translate("address")}>
          <Textarea
            name="address"
            onChange={handleAddress}
            value={billingData.address}
          />
        </FormItem>
        <FormItem label={translate("city")}>
          <Select
            defaultValue={billingData.city}
            name="city"
            onChange={handleSelectChange}
            required
          >
            <option value="">{translate("selectCity")}</option>
            {citiesData.map((city) => (
              <option key={city.ilkodu} value={city.ilkodu}>
                {city.iladi}
              </option>
            ))}
          </Select>
        </FormItem>
        <FormItem label={translate("district")}>
          <Select
            defaultValue={billingData.district}
            name="district"
            onChange={handleSelectChange}
            required
          >
            <option value="">{translate("selectDistrict")}</option>
            {filteredCityDistrict?.ilceler?.map((district) => (
              <option key={district.ilcekodu} value={district.ilcekodu}>
                {district.ilceadi}
              </option>
            ))}
          </Select>
        </FormItem>
        <div className="!mt-20">
          <h2 className="text-[22px] font-semibold leading-8">
            İletişim Bilgileri
          </h2>
          <span className="text-gray-400 leading-8 text-[14px]">
            Bu adım gelecek olan şifreler ile alt üye işyeri hesabınızın
            takibini yapmak için önemlidir
          </span>
        </div>
        <FormItem label={translate("gsmNumber")}>
          <Input
            name="gsmNumber"
            onChange={setField}
            required
            value={billingData.gsmNumber}
            maxLength={10}
          />
        </FormItem>
        <FormItem label={translate("email")}>
          <Input name="email" onChange={setField} value={billingData.email} />
        </FormItem>
        <FormItem label="Website">
          <Input
            name="website"
            onChange={setField}
            value={billingData.website}
          />
        </FormItem>
        <Button
          type="submit"
          className="w-full bg-secondary-6000 hover:bg-secondary-700 text-white !mt-12"
        >
          {loading ? <LoadingSpinner /> : translate("save")}
        </Button>
      </form>
    </div>
  );
};

export default BillingContainer;
