import { useToast } from "@/components/ui/use-toast";
import type { FormEvent } from "react";
import { revalidatePathHandler } from "@/lib/revalidate";
import { HOTEL_API_PATHS } from "@/utils/apiUrls";

export const useHotelPromo = () => {
  const { toast } = useToast();

  const createPromo = async (
    event: FormEvent,
    hotelToken: string | undefined,
    promoData: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(HOTEL_API_PATHS.createPromo, {
          method: "POST",
          headers: {
            hotelToken: hotelToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(promoData),
        });
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata olu<PERSON>.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          setLoading(false);
          closeModal();
          setDisabled(false);
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Promosyon Kodu Ekleme",
          description: "Promosyon Kodu başarıyla eklendi.",
        });
        revalidatePathHandler("/hotel/account/discounts");
        setLoading(false);
        closeModal();
        setDisabled(false);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const updatePromo = async (
    event: FormEvent,
    hotelToken: string | undefined,
    promoData: any,
    promoId: string,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.updatePromo}/${promoId}`,
          {
            method: "PUT",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(promoData),
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          setTimeout(() => {
            setDisabled(false);
          }, 1000);

          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Promosyon Kodu Güncelleme",
          description: "Promosyon Kodu başarıyla güncellendi.",
        });
        revalidatePathHandler("/hotel/account/discounts");
        closeModal();
        setTimeout(() => {
          setDisabled(false);
        }, 1000);
      } catch (error) {
        console.log(error);
      }
    }
  };

  const removePromo = async (
    event: FormEvent,
    hotelToken: string | undefined,
    promoId: any,
    setLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setDisabled: React.Dispatch<React.SetStateAction<boolean>>,
    closeModal: () => void
  ) => {
    event.preventDefault();
    if (hotelToken) {
      setLoading(true);
      setDisabled(true);
      try {
        const response = await fetch(
          `${HOTEL_API_PATHS.removePromo}/${promoId}`,
          {
            method: "DELETE",
            headers: {
              hotelToken: hotelToken,
              "Content-Type": "application/json",
            },
          }
        );
        const data = await response.json();

        if (!response.ok || !data.success) {
          const errorMessage = data.error || "Beklenmedik bir hata oluştu.";
          toast({
            variant: "error",
            duration: 3500,
            title: "Hata",
            description: `${errorMessage}`,
          });
          closeModal();
          throw new Error("Network response was not ok");
        }
        toast({
          variant: "success",
          duration: 3500,
          title: "Promosyon Kodu Kaldırma",
          description: "Promosyon Kodu başarıyla kaldırıldı.",
        });
        revalidatePathHandler("/hotel/account/discounts");
        closeModal();
      } catch (error) {
        console.log(error);
      }
    }
  };

  return { createPromo, updatePromo, removePromo };
};
