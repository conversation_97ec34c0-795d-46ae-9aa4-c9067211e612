"use client";
import React, { useState } from "react";
import type { FC } from "react";
import Link from "next/link";
import { useHotelAtDoorReservationPublic } from "@/hooks/public/useHotelAtDoorReservationPublic";
import finishOrderPublic from "@/actions/(protected)/pub/finishOrderPublic";
import ButtonPrimary from "@/shared/ButtonPrimary";
import ButtonSecondary from "@/shared/ButtonSecondary";
import type { Route } from "@/routers/types";
import LoadingSpinner from "@/shared/icons/Spinner";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";
import { useRouter } from "next/navigation";

interface FooterLoggedButtonsProps {
  orderData: any;
  buttonValue: number;
  reservationPetData: any;
  servicePetData: any;
}

const FooterLoggedInButtons: FC<FooterLoggedButtonsProps> = ({
  orderData,
  buttonValue,
  reservationPetData,
  servicePetData,
}) => {
  const { updateReservation, updateService } =
    useHotelAtDoorReservationPublic();
  const [loading, setLoading] = useState<boolean>(false);
  const [secondLoading, setSecondLoading] = useState<boolean>(false);
  const [backLoading, setBackLoading] = useState<boolean>(false);
  const checkoutData = useSelector(
    (state: RootState) => state.checkoutData.checkoutData
  );
  const router = useRouter();

  const loadingHandler = () => {
    setSecondLoading(true);

    setTimeout(() => {
      setSecondLoading(false);
    }, 800);
  };

  const backLoadingHandler = () => {
    setBackLoading(true);

    setTimeout(() => {
      setBackLoading(false);
    }, 800);
  };

  const backButtonHandler = () => {
    if (buttonValue === 2)
      return `/at-door-reservation?orderId=${orderData?._id}`;
    if (buttonValue === 3)
      return `/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`;
    return `/at-door-reservation?orderId=${orderData?._id}`;
  };
  const backButtonValue = backButtonHandler();

  const handleFinishOrder = async () => {
    setLoading(true);

    try {
      const result = await finishOrderPublic(orderData?._id, {
        channel: checkoutData.channel,
        paymentType: checkoutData.paymentType,
      });

      if (result?.success) {
        setLoading(false);
        router.push("/at-door-reservation/reservation-success");
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error("An error occurred while finishing the order:", error);
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2 sm:gap-5">
      {buttonValue > 1 && (
        <Link href={backButtonValue as Route}>
          <ButtonSecondary disabled={backLoading} onClick={backLoadingHandler}>
            {backLoading ? <LoadingSpinner /> : "Geri"}
          </ButtonSecondary>
        </Link>
      )}
      {buttonValue === 1 &&
        orderData?.reservations?.length > 0 &&
        orderData?.services?.length === 0 && (
          <ButtonPrimary
            onClick={() =>
              updateReservation({
                updates: reservationPetData,
                setLoading,
                route: `/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              })
            }
            disabled={
              !(checkoutData.pet.length === orderData?.reservations?.length) ||
              loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 1 &&
        orderData?.services?.length > 0 &&
        orderData?.reservations?.length === 0 && (
          <ButtonPrimary
            onClick={() =>
              updateService({
                updates: servicePetData,
                setLoading,
                route: `/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              })
            }
            disabled={
              !(
                checkoutData.servicePet.filter(Boolean).length ===
                orderData?.services?.length
              ) || loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 1 &&
        orderData?.services?.length > 0 &&
        orderData?.reservations?.length > 0 && (
          <ButtonPrimary
            onClick={() => {
              updateReservation({
                updates: reservationPetData,
                setLoading,
                route: `/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              });
              updateService({
                updates: servicePetData,
                setLoading,
                route: `/at-door-reservation?orderId=${orderData?._id}&summaryStep=true`,
              });
            }}
            disabled={
              !(
                checkoutData.pet.length === orderData?.reservations?.length &&
                checkoutData.servicePet.filter(Boolean).length ===
                  orderData?.services?.length
              ) || loading
            }
          >
            {loading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        )}
      {buttonValue === 2 && (
        <Link
          href={`/at-door-reservation?orderId=${orderData?._id}&paymentStep=true`}
        >
          <ButtonPrimary disabled={secondLoading} onClick={loadingHandler}>
            {secondLoading ? <LoadingSpinner /> : "Devam Et"}
          </ButtonPrimary>
        </Link>
      )}
      {buttonValue === 3 && (
        <ButtonPrimary
          disabled={
            checkoutData.channel === "" ||
            checkoutData.paymentType === "" ||
            !checkoutData.agreementsChecked ||
            loading
          }
          onClick={handleFinishOrder}
        >
          {loading ? <LoadingSpinner /> : "Oluştur"}
        </ButtonPrimary>
      )}
    </div>
  );
};

export default FooterLoggedInButtons;
