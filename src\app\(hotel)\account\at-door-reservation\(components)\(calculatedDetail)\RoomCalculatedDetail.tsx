import React from "react";
// import DeleteCalculatedRoomDetail from "../DeleteCalculatedRoomDetail";
import { useSelector } from "react-redux";
import type { RootState } from "@/store";

const RoomCalculatedDetail = () => {
  const calculatedRoomData = useSelector(
    (state: RootState) => state.calculatedRoomData.calculatedRoomData
  );
  const reservations = calculatedRoomData?.selectedItems.filter(
    (item: any) => item.itemType === "reservation"
  );

  return (
    <>
      {reservations?.map((reservation: any) => {
        return (
          <div key={reservation.id}>
            {reservation?.itemData?.allocations
              .slice(0, 1)
              .map((allocation: any) => (
                <div key={allocation._id} className="space-y-4">
                  <div className="flex justify-between items-end">
                    <div className="space-y-4">
                      <div className="font-semibold capitalize">
                        {allocation?.roomGroupDetails?.roomGroupName} (Oda)
                      </div>
                      <div className="flex justify-between">
                        <span>
                          {new Intl.NumberFormat("tr-TR", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(Number(allocation?.totalPrice)) + "₺"}
                          {""} x {reservations[0]?.itemData?.nights} gece
                        </span>
                      </div>
                      <div className="flex justify-start font-semibold">
                        <span>
                          {new Intl.NumberFormat("tr-TR", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }).format(Number(reservation?.itemData?.total)) + "₺"}
                        </span>
                      </div>
                    </div>
                    {/* <DeleteCalculatedRoomDetail
                      removedItemId={reservation?.id}
                      roomId={reservation?.itemData?.room}
                    /> */}
                  </div>
                  <div className="border-b border-neutral-200 dark:border-neutral-700"></div>
                </div>
              ))}
          </div>
        );
      })}
    </>
  );
};

export default RoomCalculatedDetail;
